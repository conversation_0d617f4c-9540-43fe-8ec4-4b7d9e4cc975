import 'package:flutter/material.dart';
import '../models/backup_settings_isar.dart';
import '../services/farm_setup_repository.dart';
import '../services/google_drive_auth_service.dart';
import '../screens/data_backup_screen.dart';
import '../../../constants/app_colors.dart';

/// Widget that displays cloud backup status and provides quick access to backup settings
class CloudBackupStatusCard extends StatefulWidget {
  final FarmSetupRepository farmSetupRepository;

  const CloudBackupStatusCard({
    Key? key,
    required this.farmSetupRepository,
  }) : super(key: key);

  @override
  State<CloudBackupStatusCard> createState() => _CloudBackupStatusCardState();
}

class _CloudBackupStatusCardState extends State<CloudBackupStatusCard> {
  BackupSettingsIsar? _backupSettings;
  bool _isLoading = true;
  bool _isGoogleDriveAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _loadBackupStatus();
  }

  Future<void> _loadBackupStatus() async {
    try {
      final settings = await widget.farmSetupRepository.getBackupSettings();
      final isAuthenticated = settings.isGoogleDriveSelected
          ? await widget.farmSetupRepository.isCloudAuthenticated(BackupStorageProvider.googleDrive)
          : false;

      if (mounted) {
        setState(() {
          _backupSettings = settings;
          _isGoogleDriveAuthenticated = isAuthenticated;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (_backupSettings == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getStatusIcon(),
                  color: _getStatusColor(),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Cloud Backup Status',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStatusText(),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: _getStatusColor(),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: _openBackupSettings,
                  icon: const Icon(Icons.settings),
                  tooltip: 'Backup Settings',
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildStatusDetails(),
            const SizedBox(height: 12),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusDetails() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          _buildDetailRow(
            'Storage Provider',
            _backupSettings!.storageProviderDisplayName,
            _backupSettings!.isCloudStorageEnabled ? Icons.cloud : Icons.phone_android,
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            'Auto Backup',
            _backupSettings!.autoBackupEnabled ? 'Enabled' : 'Disabled',
            _backupSettings!.autoBackupEnabled ? Icons.check_circle : Icons.cancel,
          ),
          if (_backupSettings!.autoBackupEnabled) ...[
            const SizedBox(height: 8),
            _buildDetailRow(
              'Frequency',
              'Every ${_backupSettings!.autoBackupFrequency} days',
              Icons.schedule,
            ),
          ],
          if (_backupSettings!.lastBackupDate != null) ...[
            const SizedBox(height: 8),
            _buildDetailRow(
              'Last Backup',
              _formatDate(_backupSettings!.lastBackupDate!),
              Icons.backup,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: Colors.grey[700]),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    if (_backupSettings!.isCloudStorageEnabled && !_isGoogleDriveAuthenticated) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _authenticateGoogleDrive,
          icon: const Icon(Icons.login),
          label: const Text('Authenticate Google Drive'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
        ),
      );
    }

    if (!_backupSettings!.isCloudStorageEnabled) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _enableCloudBackup,
          icon: const Icon(Icons.cloud_upload),
          label: const Text('Enable Cloud Backup'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
            foregroundColor: Colors.white,
          ),
        ),
      );
    }

    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _createBackupNow,
            icon: const Icon(Icons.backup),
            label: const Text('Backup Now'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _openBackupSettings,
            icon: const Icon(Icons.settings),
            label: const Text('Settings'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  IconData _getStatusIcon() {
    if (_backupSettings!.isCloudStorageEnabled && _isGoogleDriveAuthenticated) {
      return Icons.cloud_done;
    } else if (_backupSettings!.isCloudStorageEnabled) {
      return Icons.cloud_off;
    } else {
      return Icons.phone_android;
    }
  }

  Color _getStatusColor() {
    if (_backupSettings!.isCloudStorageEnabled && _isGoogleDriveAuthenticated) {
      return AppColors.success;
    } else if (_backupSettings!.isCloudStorageEnabled) {
      return AppColors.warning;
    } else {
      return AppColors.info;
    }
  }

  String _getStatusText() {
    if (_backupSettings!.isCloudStorageEnabled && _isGoogleDriveAuthenticated) {
      return 'Cloud backup active';
    } else if (_backupSettings!.isCloudStorageEnabled) {
      return 'Authentication required';
    } else {
      return 'Local backup only';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '$difference.inDays days ago';
    } else {
      return '$date.day/$date.month/$date.year';
    }
  }

  Future<void> _openBackupSettings() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DataBackupScreen(),
      ),
    );

    if (result == true) {
      _loadBackupStatus(); // Refresh status after settings change
    }
  }

  Future<void> _enableCloudBackup() async {
    _openBackupSettings();
  }

  Future<void> _authenticateGoogleDrive() async {
    try {
      final authService = GoogleDriveAuthService();
      final success = await authService.signIn();

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Google Drive authentication successful!'),
            backgroundColor: AppColors.success,
          ),
        );
        _loadBackupStatus(); // Refresh status
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Google Drive authentication failed'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Authentication error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _createBackupNow() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Creating backup...')),
      );

      final result = await widget.farmSetupRepository.createBackup();

      if (mounted) {
        if (result.success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Backup created successfully: $result.message'),
              backgroundColor: AppColors.success,
            ),
          );
          _loadBackupStatus(); // Refresh to show new backup date
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Backup failed: $result.message'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Backup error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
