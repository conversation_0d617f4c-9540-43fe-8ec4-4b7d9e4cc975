import 'package:isar/isar.dart';

part 'milk_sale_isar.g.dart';

@collection
class MilkSaleIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  DateTime? date;

  double? quantity;

  double? ratePerLiter;

  double? totalAmount;

  double? calfUsage;

  double? homeUsage;

  bool? isPaid;

  String? notes;

  DateTime? createdAt;

  DateTime? updatedAt;

  // Additional properties for compatibility
  String? buyer;
  String? paymentMethod;
  String? paymentStatus;
  double? price;
  double? pricePerLiter;
  String? saleId;
  double? total;

  MilkSaleIsar();

  factory MilkSaleIsar.create({
    String? businessId,
    DateTime? date,
    double? quantity,
    double? ratePerLiter,
    double? totalAmount,
    double? calfUsage,
    double? homeUsage,
    bool? isPaid,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? buyer,
    String? paymentMethod,
    String? paymentStatus,
    double? price,
    double? pricePerLiter,
    String? saleId,
    double? total,
  }) {
    return MilkSaleIsar()
      ..businessId = businessId
      ..date = date ?? DateTime.now()
      ..quantity = quantity
      ..ratePerLiter = ratePerLiter
      ..totalAmount = totalAmount
      ..calfUsage = calfUsage
      ..homeUsage = homeUsage
      ..isPaid = isPaid
      ..notes = notes
      ..createdAt = createdAt ?? DateTime.now()
      ..updatedAt = updatedAt ?? DateTime.now()
      ..buyer = buyer
      ..paymentMethod = paymentMethod
      ..paymentStatus = paymentStatus
      ..price = price
      ..pricePerLiter = pricePerLiter
      ..saleId = saleId
      ..total = total;
  }

  Map<String, dynamic> toMap() {
    return {
      'businessId': businessId,
      'date': date?.toIso8601String(),
      'quantity': quantity,
      'ratePerLiter': ratePerLiter,
      'totalAmount': totalAmount,
      'calfUsage': calfUsage,
      'homeUsage': homeUsage,
      'isPaid': isPaid,
      'notes': notes,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'buyer': buyer,
      'paymentMethod': paymentMethod,
      'paymentStatus': paymentStatus,
      'price': price,
      'pricePerLiter': pricePerLiter,
      'saleId': saleId,
      'total': total,
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory MilkSaleIsar.fromMap(Map<String, dynamic> map) {
    return MilkSaleIsar()
      ..businessId = map['businessId'] as String?
      ..date = map['date'] != null ? DateTime.parse(map['date'] as String) : null
      ..quantity = map['quantity'] as double?
      ..ratePerLiter = map['ratePerLiter'] as double?
      ..totalAmount = map['totalAmount'] as double?
      ..calfUsage = map['calfUsage'] as double?
      ..homeUsage = map['homeUsage'] as double?
      ..isPaid = map['isPaid'] as bool?
      ..notes = map['notes'] as String?
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : null
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : null
      ..buyer = map['buyer'] as String?
      ..paymentMethod = map['paymentMethod'] as String?
      ..paymentStatus = map['paymentStatus'] as String?
      ..price = map['price'] as double?
      ..pricePerLiter = map['pricePerLiter'] as double?
      ..saleId = map['saleId'] as String?
      ..total = map['total'] as double?;
  }

  factory MilkSaleIsar.fromJson(Map<String, dynamic> json) => MilkSaleIsar.fromMap(json);

  MilkSaleIsar copyWith({
    String? businessId,
    DateTime? date,
    double? quantity,
    double? ratePerLiter,
    double? totalAmount,
    double? calfUsage,
    double? homeUsage,
    bool? isPaid,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? buyer,
    String? paymentMethod,
    String? paymentStatus,
    double? price,
    double? pricePerLiter,
    String? saleId,
    double? total,
  }) {
    return MilkSaleIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..date = date ?? this.date
      ..quantity = quantity ?? this.quantity
      ..ratePerLiter = ratePerLiter ?? this.ratePerLiter
      ..totalAmount = totalAmount ?? this.totalAmount
      ..calfUsage = calfUsage ?? this.calfUsage
      ..homeUsage = homeUsage ?? this.homeUsage
      ..isPaid = isPaid ?? this.isPaid
      ..notes = notes ?? this.notes
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? this.updatedAt
      ..buyer = buyer ?? this.buyer
      ..paymentMethod = paymentMethod ?? this.paymentMethod
      ..paymentStatus = paymentStatus ?? this.paymentStatus
      ..price = price ?? this.price
      ..pricePerLiter = pricePerLiter ?? this.pricePerLiter
      ..saleId = saleId ?? this.saleId
      ..total = total ?? this.total;
  }
} 