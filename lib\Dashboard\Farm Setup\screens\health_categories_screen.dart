import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../Transactions/models/category_isar.dart';
import '../services/farm_setup_repository.dart';
import 'package:get_it/get_it.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_tabs.dart';

// Define primary color as a constant for easier maintenance
const Color kPrimaryAppColor = Color(0xFF2E7D32); // Green to match app standard

// Define common spacing constants
const double kSpacing = 8.0;
const double kSpacingMedium = 16.0;
const double kSpacingLarge = 24.0;

class HealthCategoriesScreen extends StatefulWidget {
  const HealthCategoriesScreen({super.key});

  @override
  State<HealthCategoriesScreen> createState() => _HealthCategoriesScreenState();
}

class _HealthCategoriesScreenState extends State<HealthCategoriesScreen>
    with TickerProviderStateMixin {
  final List<CategoryIsar> _healthIssues = [];
  final List<CategoryIsar> _treatments = [];
  final List<CategoryIsar> _vaccines = [];

  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  bool _isLoading = true;
  late AnimationController _animationController;
  late TabController _tabController;
  UniversalTabManager? _tabManager;
  bool _isInitialLoad = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _tabController = TabController(length: 3, vsync: this);
    _loadCategories();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() => _isLoading = true);
    try {
      final results = await Future.wait([
        _farmSetupRepository.getHealthIssueCategories(),
        _farmSetupRepository.getTreatmentCategories(),
        _farmSetupRepository.getVaccineCategories(),
      ]);

      // Check if categories are empty and create defaults
      if (results[0].isEmpty) {
        await _createDefaultHealthIssues();
      }
      if (results[1].isEmpty) {
        await _createDefaultTreatments();
      }
      if (results[2].isEmpty) {
        await _createDefaultVaccines();
      }

      // Reload categories after creating defaults
      final updatedResults = await Future.wait([
        _farmSetupRepository.getHealthIssueCategories(),
        _farmSetupRepository.getTreatmentCategories(),
        _farmSetupRepository.getVaccineCategories(),
      ]);

      setState(() {
        _healthIssues.clear();
        _treatments.clear();
        _vaccines.clear();

        _healthIssues.addAll(updatedResults[0]);
        _treatments.addAll(updatedResults[1]);
        _vaccines.addAll(updatedResults[2]);

        _isLoading = false;
      });

      // Play animation only on initial load
      if (_isInitialLoad && mounted) {
        _animationController.forward();
        _isInitialLoad = false;
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load categories: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _createDefaultHealthIssues() async {
    final defaultHealthIssues = [
      {'name': 'Mastitis', 'description': 'Inflammation of the mammary gland', 'icon': Icons.medical_information},
      {'name': 'Lameness', 'description': 'Difficulty in walking or standing', 'icon': Icons.accessibility},
      {'name': 'Respiratory Issues', 'description': 'Breathing and lung problems', 'icon': Icons.air},
      {'name': 'Digestive Problems', 'description': 'Stomach and intestinal issues', 'icon': Icons.restaurant},
      {'name': 'Reproductive Issues', 'description': 'Breeding and fertility problems', 'icon': Icons.pregnant_woman},
      {'name': 'Skin Conditions', 'description': 'External skin problems and infections', 'icon': Icons.healing},
      {'name': 'Eye Infections', 'description': 'Ocular diseases and infections', 'icon': Icons.visibility},
    ];

    for (final issue in defaultHealthIssues) {
      final category = CategoryIsar()
        ..categoryId = const Uuid().v4()
        ..name = issue['name'] as String
        ..description = '${issue['description']}|color:0x${kPrimaryAppColor.toARGB32().toRadixString(16)}'
        ..type = 'HealthIssue'
        ..iconCodePoint = (issue['icon'] as IconData).codePoint
        ..iconFontFamily = 'MaterialIcons'
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _farmSetupRepository.createCategory(category);
    }
  }

  Future<void> _createDefaultTreatments() async {
    final defaultTreatments = [
      {'name': 'Antibiotics', 'description': 'Bacterial infection treatment', 'icon': Icons.medication},
      {'name': 'Anti-inflammatory', 'description': 'Reduces inflammation and pain', 'icon': Icons.healing},
      {'name': 'Vaccination', 'description': 'Preventive immunization', 'icon': Icons.vaccines},
      {'name': 'Nutritional Supplement', 'description': 'Dietary supplements and vitamins', 'icon': Icons.restaurant},
      {'name': 'Surgical Procedure', 'description': 'Surgical intervention', 'icon': Icons.medical_services},
      {'name': 'Topical Treatment', 'description': 'External application medicines', 'icon': Icons.brush},
      {'name': 'Pain Management', 'description': 'Pain relief medications', 'icon': Icons.local_hospital},
    ];

    for (final treatment in defaultTreatments) {
      final category = CategoryIsar()
        ..categoryId = const Uuid().v4()
        ..name = treatment['name'] as String
        ..description = '${treatment['description']}|color:0x${kPrimaryAppColor.toARGB32().toRadixString(16)}'
        ..type = 'Treatment'
        ..iconCodePoint = (treatment['icon'] as IconData).codePoint
        ..iconFontFamily = 'MaterialIcons'
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _farmSetupRepository.createCategory(category);
    }
  }

  Future<void> _createDefaultVaccines() async {
    final defaultVaccines = [
      {'name': 'BVDV', 'description': 'Bovine Viral Diarrhea Virus vaccine', 'icon': Icons.vaccines},
      {'name': 'IBR', 'description': 'Infectious Bovine Rhinotracheitis vaccine', 'icon': Icons.vaccines},
      {'name': 'PI3', 'description': 'Parainfluenza-3 virus vaccine', 'icon': Icons.vaccines},
      {'name': 'BRSV', 'description': 'Bovine Respiratory Syncytial Virus vaccine', 'icon': Icons.vaccines},
      {'name': 'Clostridial', 'description': 'Clostridial disease prevention vaccine', 'icon': Icons.vaccines},
      {'name': 'Leptospirosis', 'description': 'Leptospira bacteria vaccine', 'icon': Icons.vaccines},
      {'name': 'Brucellosis', 'description': 'Brucella abortus vaccine', 'icon': Icons.vaccines},
      {'name': 'Anthrax', 'description': 'Bacillus anthracis vaccine', 'icon': Icons.vaccines},
    ];

    for (final vaccine in defaultVaccines) {
      final category = CategoryIsar()
        ..categoryId = const Uuid().v4()
        ..name = vaccine['name'] as String
        ..description = '${vaccine['description']}|color:0x${kPrimaryAppColor.toARGB32().toRadixString(16)}'
        ..type = 'Vaccine'
        ..iconCodePoint = (vaccine['icon'] as IconData).codePoint
        ..iconFontFamily = 'MaterialIcons'
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _farmSetupRepository.createCategory(category);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialize tab manager
    _tabManager ??= UniversalTabManager.threeTabs(
      controller: _tabController,
      tabViews: [
        _buildCategoryList(_healthIssues, 'HealthIssue', 'health issue'),
        _buildCategoryList(_treatments, 'Treatment', 'treatment'),
        _buildCategoryList(_vaccines, 'Vaccine', 'vaccine'),
      ],
      labels: const ['Health Issues', 'Treatments', 'Vaccines'],
      icons: const [Icons.medical_information, Icons.medication, Icons.vaccines],
      showFABs: const [true, true, true], // FAB on all tabs
    );

    return UniversalLayout.tabScreen(
      title: 'Health Categories',
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _tabManager!,
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          return _tabManager?.getCurrentFAB(
            onPressed: _showAddCategoryDialog,
            tooltip: 'Add Category') ?? const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildCategoryList(List<CategoryIsar> categories, String type, String typeName) {
    if (categories.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getTypeIcon(type),
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: kSpacingMedium),
            Text(
              'No ${typeName}s yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: kSpacing),
            Text(
              'Add your first $typeName to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: kSpacingLarge),
            ElevatedButton.icon(
              onPressed: _showAddCategoryDialog,
              icon: const Icon(Icons.add),
              label: Text('Add $typeName'),
              style: ElevatedButton.styleFrom(
                backgroundColor: kPrimaryAppColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _animationController,
      child: ListView.builder(
        padding: const EdgeInsets.only(bottom: 80),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return _buildCategoryCard(category);
        },
      ),
    );
  }

  Widget _buildCategoryCard(CategoryIsar category) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: kSpacingMedium,
        vertical: kSpacing,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getCategoryColor(category),
          child: Icon(
            _getCategoryIcon(category),
            color: Colors.white,
          ),
        ),
        title: Text(
          category.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: category.description.isNotEmpty
            ? Text(category.description.split('|')[0]) // Remove color info from display
            : const Text('No description'),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditCategoryDialog(category);
                break;
              case 'delete':
                _showDeleteConfirmation(category);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('Edit'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Delete', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'HealthIssue':
        return Icons.medical_information;
      case 'Treatment':
        return Icons.medication;
      case 'Vaccine':
        return Icons.vaccines;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(CategoryIsar category) {
    // Try to extract color from description field (same pattern as expense categories)
    if (category.description.contains('|color:')) {
      try {
        final colorString = category.description.split('|color:')[1].split('|')[0];
        return Color(int.parse(colorString));
      } catch (e) {
        return kPrimaryAppColor;
      }
    }
    return kPrimaryAppColor;
  }

  IconData _getCategoryIcon(CategoryIsar category) {
    if (category.iconCodePoint != null) {
      return IconData(category.iconCodePoint!, fontFamily: category.iconFontFamily);
    }
    return _getTypeIcon(category.type);
  }

  String _getCurrentType() {
    switch (_tabController.index) {
      case 0:
        return 'HealthIssue';
      case 1:
        return 'Treatment';
      case 2:
        return 'Vaccine';
      default:
        return 'HealthIssue';
    }
  }

  String _getTypeName(String type) {
    switch (type) {
      case 'HealthIssue':
        return 'Health Issue';
      case 'Treatment':
        return 'Treatment';
      case 'Vaccine':
        return 'Vaccine';
      default:
        return 'Category';
    }
  }

  void _showAddCategoryDialog() {
    final currentType = _getCurrentType();
    _showAddEditCategoryDialog(currentType);
  }

  void _showEditCategoryDialog(CategoryIsar category) {
    _showAddEditCategoryDialog(category.type, category);
  }

  void _showDeleteConfirmation(CategoryIsar category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "$category.name"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteCategory(category);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showAddEditCategoryDialog(String type, [CategoryIsar? category]) {
    final bool isEditing = category != null;
    final typeName = _getTypeName(type);

    final nameController = TextEditingController(text: category?.name ?? '');
    final descriptionController = TextEditingController(text: category?.description.split('|')[0] ?? '');
    Color selectedColor = _getCategoryColor(category ?? CategoryIsar());
    IconData selectedIcon = _getCategoryIcon(category ?? CategoryIsar());

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('${isEditing ? 'Edit' : 'Add'} $typeName'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: '$typeName Name',
                    border: const OutlineInputBorder(),
                  ),
                  textCapitalization: TextCapitalization.words,
                ),
                const SizedBox(height: kSpacingMedium),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: kSpacingMedium),
                // Color and icon selection (simplified for now)
                Row(
                  children: [
                    Expanded(
                      child: Text('Color: ', style: Theme.of(context).textTheme.bodyMedium),
                    ),
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: selectedColor,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.grey),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => _saveCategory(
                type,
                nameController.text,
                descriptionController.text,
                selectedColor,
                selectedIcon,
                category,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: kPrimaryAppColor,
                foregroundColor: Colors.white,
              ),
              child: Text(isEditing ? 'Update' : 'Add'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveCategory(
    String type,
    String name,
    String description,
    Color color,
    IconData icon,
    CategoryIsar? existingCategory,
  ) async {
    if (name.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Category name is required')),
      );
      return;
    }

    try {
      final categoryToSave = existingCategory ?? CategoryIsar();

      categoryToSave.categoryId = existingCategory?.categoryId ?? const Uuid().v4();
      categoryToSave.name = name.trim();
      categoryToSave.description = '${description.trim()}|color:0x${color.toARGB32().toRadixString(16)}';
      categoryToSave.type = type;
      categoryToSave.iconCodePoint = icon.codePoint;
      categoryToSave.iconFontFamily = icon.fontFamily ?? 'MaterialIcons';
      categoryToSave.updatedAt = DateTime.now();

      if (existingCategory == null) {
        categoryToSave.createdAt = DateTime.now();
        await _farmSetupRepository.createCategory(categoryToSave);
      } else {
        await _farmSetupRepository.updateCategory(categoryToSave);
      }

      Navigator.of(context).pop();
      await _loadCategories();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_getTypeName(type)} ${existingCategory == null ? 'added' : 'updated'} successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save category: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteCategory(CategoryIsar category) async {
    try {
      await _farmSetupRepository.deleteCategory(category.categoryId);
      await _loadCategories();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$category.name deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete category: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}