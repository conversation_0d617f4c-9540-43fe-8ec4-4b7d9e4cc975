import 'notification_priority.dart';
import 'notification_status.dart';

/// Filter model for querying notifications
class NotificationFilter {
  /// Filter by notification status
  final NotificationStatus? status;
  
  /// Filter by notification category
  final String? category;
  
  /// Filter by notification type
  final String? type;
  
  /// Filter by notification priority
  final NotificationPriority? priority;
  
  /// Filter by related cattle ID
  final String? cattleId;
  
  /// Filter by date range (from)
  final DateTime? fromDate;
  
  /// Filter by date range (to)
  final DateTime? toDate;
  
  /// Search query for text search
  final String? searchQuery;
  
  /// Filter by specific business IDs
  final List<String>? businessIds;

  /// Filter by event ID
  final String? eventId;

  /// Filter by user ID
  final String? userId;

  /// Constructor
  NotificationFilter({
    this.status,
    this.category,
    this.type,
    this.priority,
    this.cattleId,
    this.fromDate,
    this.toDate,
    this.searchQuery,
    this.businessIds,
    this.eventId,
    this.userId,
  });

  /// Convert to a map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'status': status?.index,
      'category': category,
      'type': type,
      'priority': priority?.index,
      'cattleId': cattleId,
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
      'searchQuery': searchQuery,
      'businessIds': businessIds,
    };
  }

  /// Create from a map (JSON deserialization)
  factory NotificationFilter.fromMap(Map<String, dynamic> map) {
    return NotificationFilter(
      status: map['status'] != null 
          ? NotificationStatus.values[map['status']] 
          : null,
      category: map['category'],
      type: map['type'],
      priority: map['priority'] != null 
          ? NotificationPriority.values[map['priority']] 
          : null,
      cattleId: map['cattleId'],
      fromDate: map['fromDate'] != null 
          ? DateTime.parse(map['fromDate']) 
          : null,
      toDate: map['toDate'] != null 
          ? DateTime.parse(map['toDate']) 
          : null,
      searchQuery: map['searchQuery'],
      businessIds: map['businessIds'] != null 
          ? List<String>.from(map['businessIds']) 
          : null,
    );
  }
}