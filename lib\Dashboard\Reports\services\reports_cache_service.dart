import 'dart:convert';
import 'dart:developer' as developer;
import '../models/report_models.dart';

/// Reports Cache Service
/// 
/// Provides intelligent caching for report data to improve performance.
/// Implements cache invalidation strategies and memory management.
class ReportsCacheService {
  static final ReportsCacheService _instance = ReportsCacheService._internal();
  factory ReportsCacheService() => _instance;
  ReportsCacheService._internal();

  // Cache storage
  final Map<String, _CacheEntry> _cache = {};
  
  // Cache configuration
  static const Duration _defaultTtl = Duration(minutes: 15);
  static const int _maxCacheSize = 50;

  /// Get cached report data
  ReportData? getCachedReport(ReportType type, FilterState filter) {
    final key = _generateCacheKey(type, filter);
    final entry = _cache[key];
    
    if (entry == null) return null;
    
    // Check if cache entry is expired
    if (DateTime.now().isAfter(entry.expiresAt)) {
      _cache.remove(key);
      return null;
    }
    
    // Update last accessed time
    entry.lastAccessed = DateTime.now();
    return entry.data;
  }

  /// Cache report data
  void cacheReport(ReportType type, FilterState filter, ReportData data, {Duration? ttl}) {
    final key = _generateCacheKey(type, filter);
    final expiresAt = DateTime.now().add(ttl ?? _defaultTtl);
    
    _cache[key] = _CacheEntry(
      data: data,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      lastAccessed: DateTime.now(),
    );
    
    // Clean up cache if it's getting too large
    _cleanupCache();
  }

  /// Check if report is cached and valid
  bool isCached(ReportType type, FilterState filter) {
    final key = _generateCacheKey(type, filter);
    final entry = _cache[key];
    
    if (entry == null) return false;
    return DateTime.now().isBefore(entry.expiresAt);
  }

  /// Invalidate cache for specific report type
  void invalidateReportType(ReportType type) {
    final keysToRemove = _cache.keys
        .where((key) => key.startsWith('${type.name}_'))
        .toList();

    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    developer.log(
      'Cache invalidated for ${type.name}: ${keysToRemove.length} entries removed',
      name: 'Reports.Cache',
    );
  }

  /// Selective cache invalidation based on filter changes
  void invalidateSelective(ReportType type, FilterState filter) {
    final keysToRemove = <String>[];

    _cache.forEach((key, entry) {
      // Check if this cache entry is affected by the filter change
      if (_isFilterAffected(key, filter) || key.startsWith('${type.name}_')) {
        keysToRemove.add(key);
      }
    });

    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    developer.log(
      'Selective cache invalidation: ${keysToRemove.length} entries removed',
      name: 'Reports.Cache',
    );
  }

  /// Check if a cache key is affected by filter changes
  bool _isFilterAffected(String cacheKey, FilterState filter) {
    // Parse the cache key to understand what filters it was created with
    // This is a simplified implementation - in practice, you'd store filter info

    // If filter has date range, invalidate dashboard and time-sensitive reports
    if (filter.startDate != null || filter.endDate != null) {
      return cacheKey.contains('dashboard') ||
             cacheKey.contains('milk') ||
             cacheKey.contains('financial');
    }

    // If filter has cattle IDs, invalidate cattle-related reports
    if (filter.cattleIds?.isNotEmpty == true) {
      return cacheKey.contains('cattle') ||
             cacheKey.contains('health') ||
             cacheKey.contains('breeding') ||
             cacheKey.contains('weight');
    }

    return false;
  }

  /// Invalidate all cache entries
  void invalidateAll() {
    final count = _cache.length;
    _cache.clear();

    developer.log(
      'All cache cleared: $count entries removed',
      name: 'Reports.Cache',
    );
  }

  /// Invalidate cache entries older than specified duration
  void invalidateOlderThan(Duration duration) {
    final cutoff = DateTime.now().subtract(duration);
    final keysToRemove = <String>[];
    
    _cache.forEach((key, entry) {
      if (entry.createdAt.isBefore(cutoff)) {
        keysToRemove.add(key);
      }
    });
    
    for (final key in keysToRemove) {
      _cache.remove(key);
    }
  }

  /// Get cache statistics
  CacheStats getCacheStats() {
    final now = DateTime.now();
    int validEntries = 0;
    int expiredEntries = 0;
    
    _cache.forEach((key, entry) {
      if (now.isBefore(entry.expiresAt)) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    });
    
    return CacheStats(
      totalEntries: _cache.length,
      validEntries: validEntries,
      expiredEntries: expiredEntries,
      memoryUsageEstimate: _estimateMemoryUsage(),
    );
  }

  /// Preload commonly used reports
  Future<void> preloadCommonReports() async {
    // This would typically preload dashboard and recent reports
    // Implementation depends on usage patterns
    
    // Common filters for preloading (implementation placeholder)
    // final commonFilters = [
    //   const FilterState(), // All time
    //   FilterState(
    //     startDate: DateTime.now().subtract(const Duration(days: 30)),
    //     endDate: DateTime.now(),
    //   ), // Last 30 days
    //   FilterState(
    //     startDate: DateTime.now().subtract(const Duration(days: 7)),
    //     endDate: DateTime.now(),
    //   ), // Last 7 days
    // ];
    
    // Note: Actual preloading would require ReportsService instance
    // This is a placeholder for the preloading strategy
  }

  // Private methods

  String _generateCacheKey(ReportType type, FilterState filter) {
    final filterData = {
      'startDate': filter.startDate?.millisecondsSinceEpoch,
      'endDate': filter.endDate?.millisecondsSinceEpoch,
      'cattleIds': filter.cattleIds,
      'categories': filter.categories,
      'searchQuery': filter.searchQuery,
      'customFilters': filter.customFilters,
    };
    
    final filterJson = jsonEncode(filterData);
    final filterHash = filterJson.hashCode;
    
    return '${type.name}_$filterHash';
  }

  void _cleanupCache() {
    if (_cache.length <= _maxCacheSize) return;
    
    // Remove expired entries first
    final now = DateTime.now();
    final expiredKeys = _cache.entries
        .where((entry) => now.isAfter(entry.value.expiresAt))
        .map((entry) => entry.key)
        .toList();
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }
    
    // If still over limit, remove least recently used entries
    if (_cache.length > _maxCacheSize) {
      final sortedEntries = _cache.entries.toList()
        ..sort((a, b) => a.value.lastAccessed.compareTo(b.value.lastAccessed));
      
      final entriesToRemove = sortedEntries.length - _maxCacheSize;
      for (int i = 0; i < entriesToRemove; i++) {
        _cache.remove(sortedEntries[i].key);
      }
    }
  }

  int _estimateMemoryUsage() {
    // Rough estimate of memory usage in bytes
    int totalSize = 0;
    
    _cache.forEach((key, entry) {
      // Key size
      totalSize += key.length * 2; // UTF-16 encoding
      
      // Data size estimation
      totalSize += _estimateReportDataSize(entry.data);
      
      // Metadata size
      totalSize += 100; // Rough estimate for timestamps and other metadata
    });
    
    return totalSize;
  }

  int _estimateReportDataSize(ReportData data) {
    int size = 0;
    
    // Title and subtitle
    size += (data.title.length + data.subtitle.length) * 2;
    
    // Metrics
    size += data.metrics.length * 200; // Rough estimate per metric
    
    // Chart data
    size += data.chartData.length * 100; // Rough estimate per chart point
    
    // Table data
    for (final row in data.tableData) {
      row.forEach((key, value) {
        size += (key.length + value.length) * 2;
      });
    }
    
    // Insights
    for (final insight in data.insights) {
      size += insight.length * 2;
    }
    
    return size;
  }
}

/// Cache entry wrapper
class _CacheEntry {
  final ReportData data;
  final DateTime createdAt;
  final DateTime expiresAt;
  DateTime lastAccessed;

  _CacheEntry({
    required this.data,
    required this.createdAt,
    required this.expiresAt,
    required this.lastAccessed,
  });
}

/// Cache statistics
class CacheStats {
  final int totalEntries;
  final int validEntries;
  final int expiredEntries;
  final int memoryUsageEstimate;

  const CacheStats({
    required this.totalEntries,
    required this.validEntries,
    required this.expiredEntries,
    required this.memoryUsageEstimate,
  });

  double get hitRatio => totalEntries > 0 ? validEntries / totalEntries : 0.0;
  
  String get memoryUsageFormatted {
    if (memoryUsageEstimate < 1024) {
      return '${memoryUsageEstimate}B';
    } else if (memoryUsageEstimate < 1024 * 1024) {
      return '${(memoryUsageEstimate / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(memoryUsageEstimate / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  @override
  String toString() {
    return 'CacheStats(total: $totalEntries, valid: $validEntries, '
           'expired: $expiredEntries, memory: $memoryUsageFormatted, '
           'hit ratio: ${(hitRatio * 100).toStringAsFixed(1)}%)';
  }
}