import 'package:isar/isar.dart';

part 'treatment_isar.g.dart';

@collection
class TreatmentIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? cattleTagId;

  String? treatment;
  String? condition;
  String? notes;

  @Index()
  DateTime? date;

  String? followUpDate;
  String? cost;
  String? veterinarian;
  String? outcome;
  DateTime? createdAt;
  DateTime? updatedAt;
  bool? isArchived;
  String? status;
  String? dosage;

  // Backward compatibility getters and setters
  String? get recordId => businessId;
  set recordId(String? value) => businessId = value;

  String? get cattleId => cattleTagId;
  set cattleId(String? value) => cattleTagId = value;

  String? get cattleBusinessId => cattleTagId;
  set cattleBusinessId(String? value) => cattleTagId = value;

  TreatmentIsar();

  /// Generate a formatted ID for treatment records in the format 'cattleTagId-Treatment-sequenceNumber'
  static String generateFormattedId(String cattleTagId, int sequenceNumber) {
    return '$cattleTagId-Treatment-$sequenceNumber';
  }

  /// Generate a deterministic business ID for treatment records to ensure consistency
  /// across app reinstallations. Based on cattle tag ID, date, and treatment.
  static String generateBusinessId(
      String cattleTagId, DateTime date, String treatment) {
    // Format the date in a consistent way (YYYYMMDD)
    final dateStr =
        "$date.year${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}";

    // Normalize treatment (lowercase, remove spaces)
    final normalizedTreatment = treatment.toLowerCase().replaceAll(' ', '_');

    // Create a unique ID combining cattle tag ID, date and treatment
    final uniqueKey = "$cattleTagId-$dateStr-$normalizedTreatment";

    // Return with a prefix to distinguish treatment records
    return "treatment_$uniqueKey";
  }

  factory TreatmentIsar.create({
    required String cattleTagId,
    required String treatment,
    required String condition,
    required DateTime date,
    String? notes,
    String? followUpDate,
    String? cost,
    String? veterinarian,
    String? outcome,
    String? status,
    String? dosage,
    String? businessId,
    bool isArchived = false,
  }) {
    return TreatmentIsar()
      ..businessId = businessId ?? generateBusinessId(cattleTagId, date, treatment)
      ..cattleTagId = cattleTagId
      ..treatment = treatment
      ..condition = condition
      ..date = date
      ..notes = notes
      ..followUpDate = followUpDate
      ..cost = cost
      ..veterinarian = veterinarian
      ..outcome = outcome
      ..status = status ?? 'Active'
      ..dosage = dosage
      ..isArchived = isArchived
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleTagId': cattleTagId,
      'treatment': treatment,
      'condition': condition,
      'notes': notes,
      'date': date?.toIso8601String(),
      'followUpDate': followUpDate,
      'cost': cost,
      'veterinarian': veterinarian,
      'outcome': outcome,
      'status': status,
      'dosage': dosage,
      'isArchived': isArchived,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory TreatmentIsar.fromMap(Map<String, dynamic> map) {
    return TreatmentIsar()
      ..businessId = map['id'] as String? ?? map['businessId'] as String?
      ..cattleTagId = map['cattleTagId'] as String? ?? map['cattleId'] as String? ?? map['cattleBusinessId'] as String?
      ..treatment = map['treatment'] as String?
      ..condition = map['condition'] as String?
      ..notes = map['notes'] as String?
      ..date = map['date'] != null ? DateTime.parse(map['date'].toString()) : null
      ..followUpDate = map['followUpDate'] as String?
      ..cost = map['cost'] as String?
      ..veterinarian = map['veterinarian'] as String?
      ..outcome = map['outcome'] as String?
      ..status = map['status'] as String?
      ..dosage = map['dosage'] as String?
      ..isArchived = map['isArchived'] as bool? ?? false
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'].toString())
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'].toString())
          : DateTime.now();
  }

  Map<String, dynamic> toJson() => toMap();

  TreatmentIsar copyWith({
    String? businessId,
    String? cattleTagId,
    String? treatment,
    String? condition,
    String? notes,
    DateTime? date,
    String? followUpDate,
    String? cost,
    String? veterinarian,
    String? outcome,
    String? status,
    String? dosage,
    bool? isArchived,
  }) {
    return TreatmentIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..cattleTagId = cattleTagId ?? this.cattleTagId
      ..treatment = treatment ?? this.treatment
      ..condition = condition ?? this.condition
      ..notes = notes ?? this.notes
      ..date = date ?? this.date
      ..followUpDate = followUpDate ?? this.followUpDate
      ..cost = cost ?? this.cost
      ..veterinarian = veterinarian ?? this.veterinarian
      ..outcome = outcome ?? this.outcome
      ..status = status ?? this.status
      ..dosage = dosage ?? this.dosage
      ..isArchived = isArchived ?? this.isArchived
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }
}