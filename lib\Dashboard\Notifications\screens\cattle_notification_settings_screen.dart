import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/notification_settings_controller.dart';
import '../models/notification_priority.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/app_colors.dart';

/// Screen for managing cattle-specific notification settings
class CattleNotificationSettingsScreen extends StatefulWidget {
  final CattleIsar cattle;

  const CattleNotificationSettingsScreen({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<CattleNotificationSettingsScreen> createState() => _CattleNotificationSettingsScreenState();
}

class _CattleNotificationSettingsScreenState extends State<CattleNotificationSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationSettingsController>().loadSettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.cattle.name} Notifications'),
      ),
      body: Consumer<NotificationSettingsController>(
        builder: (context, controller, child) {
          if (controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: controller.loadSettings,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final settings = controller.settings;
          if (settings == null) {
            return const Center(child: Text('No settings available'));
          }

          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Cattle Info Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        child: Text(
                          widget.cattle.name?.substring(0, 1).toUpperCase() ?? 'C',
                          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.cattle.name ?? 'Unknown',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            Text(
                              'Tag: ${widget.cattle.tagNumber ?? 'N/A'}',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.notificationStatusColors['read']!.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Category-specific settings for this cattle
              _buildSectionHeader('Notification Categories'),
              ...settings.getCategoryEnabled().entries.map((entry) =>
                _buildCattleCategoryTile(
                  controller,
                  entry.key,
                  entry.value,
                  settings.getCategoryPriorities()[entry.key] ?? NotificationPriority.medium,
                ),
              ),

              const SizedBox(height: 24),

              // Custom reminder settings
              _buildSectionHeader('Custom Reminders'),
              ListTile(
                title: const Text('Health Check Reminder'),
                subtitle: const Text('Set custom health check intervals'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showHealthReminderDialog(context, controller),
              ),
              ListTile(
                title: const Text('Breeding Reminder'),
                subtitle: const Text('Set breeding-related reminders'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showBreedingReminderDialog(context, controller),
              ),

              const SizedBox(height: 24),

              // Priority Override
              _buildSectionHeader('Priority Override'),
              ListTile(
                title: const Text('Override Global Priority'),
                subtitle: const Text('Set higher priority for this cattle'),
                trailing: Switch(
                  value: false, // This would come from cattle-specific settings
                  onChanged: (value) {
                    // Implement cattle-specific priority override
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.notificationHeader,
        ),
      ),
    );
  }

  Widget _buildCattleCategoryTile(
    NotificationSettingsController controller,
    String category,
    bool enabled,
    NotificationPriority priority,
  ) {
    return Card(
      child: ExpansionTile(
        title: Text('${category.toUpperCase()} Notifications'),
        subtitle: Text(enabled ? 'Enabled for ${widget.cattle.name}' : 'Disabled'),
        leading: Switch(
          value: enabled,
          onChanged: (value) => controller.updateCategorySetting(category, 'enabled', value),
        ),
        children: [
          if (enabled) ...[
            ListTile(
              title: const Text('Priority Level'),
              subtitle: Text(priority.name.toUpperCase()),
              trailing: DropdownButton<NotificationPriority>(
                value: priority,
                items: NotificationPriority.values.map((p) => DropdownMenuItem(
                  value: p,
                  child: Text(p.name.toUpperCase()),
                )).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.updateCategorySetting(category, 'priority', value);
                  }
                },
              ),
            ),
            ListTile(
              title: const Text('Custom Message Template'),
              subtitle: const Text('Customize notification messages for this cattle'),
              trailing: const Icon(Icons.edit),
              onTap: () => _showMessageTemplateDialog(context, category),
            ),
          ],
        ],
      ),
    );
  }

  void _showHealthReminderDialog(BuildContext context, NotificationSettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Health Check Reminder'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Set custom health check intervals for ${widget.cattle.name}'),
            const SizedBox(height: 16),
            // Add form fields for custom health reminders
            const TextField(
              decoration: InputDecoration(
                labelText: 'Reminder Interval (days)',
                hintText: 'e.g., 30',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Save custom health reminder
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showBreedingReminderDialog(BuildContext context, NotificationSettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Breeding Reminder'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Set breeding reminders for ${widget.cattle.name}'),
            const SizedBox(height: 16),
            // Add form fields for breeding reminders
            const TextField(
              decoration: InputDecoration(
                labelText: 'Heat Cycle Reminder (days)',
                hintText: 'e.g., 21',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Save breeding reminder
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showMessageTemplateDialog(BuildContext context, String category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$category Message Template'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Customize $category notification messages for ${widget.cattle.name}'),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Message Template',
                hintText: 'e.g., {cattle_name} needs {action}',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            Text(
              'Available variables: {cattle_name}, {tag_number}, {action}, {date}',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.notificationStatusColors['read']!.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Save message template
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}