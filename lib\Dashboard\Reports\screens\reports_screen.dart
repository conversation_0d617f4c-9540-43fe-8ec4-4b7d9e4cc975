import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/report_models.dart';
import '../../../Dashboard/widgets/universal_info_card.dart';
import '../controllers/reports_controller.dart';
import '../widgets/universal_chart.dart';
import '../dialogs/export_dialog.dart';
import '../widgets/filter_bar.dart';

/// Unified Reports Screen
///
/// Modern tab-based interface with Dashboard, Reports, and Export tabs.
/// Leverages existing UniversalInfoCard for consistent metric display.
///
/// Follows Provider pattern with ChangeNotifierProvider for controller lifecycle.
class ReportsScreen extends StatelessWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ReportsController()..initialize(),
      child: const _ReportsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _ReportsScreenContent extends StatelessWidget {
  const _ReportsScreenContent();

  @override
  Widget build(BuildContext context) {
    return Consumer<ReportsController>(
      builder: (context, controller, child) {
        return DefaultTabController(
          length: 3,
          child: Scaffold(
            appBar: AppBar(
              title: const Text('Reports'),
              backgroundColor: const Color(0xFF2E7D32),
              foregroundColor: Colors.white,
              bottom: const TabBar(
                indicatorColor: Colors.white,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                tabs: [
                  Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
                  Tab(icon: Icon(Icons.bar_chart), text: 'Reports'),
                  Tab(icon: Icon(Icons.file_download), text: 'Export'),
                ],
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: () => _showFilterDialog(context),
                  tooltip: 'Filter Reports',
                ),
                IconButton(
                  icon: const Icon(Icons.date_range),
                  onPressed: () => _showDateRangePicker(context),
                  tooltip: 'Select Date Range',
                ),
              ],
            ),
            body: TabBarView(
              children: [
                _buildDashboardTab(context, controller),
                _buildReportsTab(context, controller),
                _buildExportTab(context, controller),
              ],
            ),
            floatingActionButton: FloatingActionButton(
              onPressed: () => _showExportDialog(context),
              backgroundColor: const Color(0xFF2E7D32),
              tooltip: 'Quick Export',
              child: const Icon(Icons.file_download, color: Colors.white),
            ),
          ),
        );
      },
    );
  }

  /// Build Dashboard Tab - Overview with key metrics
  Widget _buildDashboardTab(BuildContext context, ReportsController controller) {
    return RefreshIndicator(
      onRefresh: () => controller.refreshDashboard(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Filter status bar
            if (controller.currentFilter.hasFilters)
              FilterStatusBar(
                filter: controller.currentFilter,
                onClearFilters: controller.clearFilters,
              ),

            // Dashboard metrics using UniversalInfoCard
            _buildDashboardMetrics(context, controller),

            const SizedBox(height: 24.0),

            // Dashboard charts
            _buildDashboardCharts(context, controller),

            const SizedBox(height: 24.0),

            // Quick insights
            _buildQuickInsights(context, controller),
          ],
        ),
      ),
    );
  }

  /// Build Reports Tab - Detailed reports by category
  Widget _buildReportsTab(BuildContext context, ReportsController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter status bar
          if (controller.currentFilter.hasFilters)
            FilterStatusBar(
              filter: controller.currentFilter,
              onClearFilters: controller.clearFilters,
            ),

          // Report type selector
          _buildReportTypeSelector(context, controller),

          const SizedBox(height: 16.0),

          // Current report content
          _buildCurrentReport(context, controller),
        ],
      ),
    );
  }

  /// Build Export Tab - Export options and history
  Widget _buildExportTab(BuildContext context, ReportsController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick export options
          _buildQuickExportOptions(context, controller),

          const SizedBox(height: 24.0),

          // Export history (placeholder)
          _buildExportHistory(context, controller),
        ],
      ),
    );
  }

  /// Build dashboard metrics using UniversalInfoCard
  Widget _buildDashboardMetrics(BuildContext context, ReportsController controller) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final dashboardData = controller.dashboardData;
    if (dashboardData == null || !dashboardData.hasData) {
      return _buildEmptyState('No dashboard data available');
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: _getCrossAxisCount(context),
      crossAxisSpacing: 16.0,
      mainAxisSpacing: 16.0,
      childAspectRatio: 1.2,
      children: dashboardData.metrics.values.map((metric) {
        // Use UniversalInfoCard factory methods based on metric type
        switch (metric.type) {
          case MetricType.kpi:
            return UniversalInfoCard.kpi(
              title: metric.title,
              value: metric.value,
              icon: metric.icon,
              color: metric.color,
              subtitle: metric.subtitle,
              onTap: metric.onTap,
            );
          case MetricType.metric:
            return UniversalInfoCard.metric(
              title: metric.title,
              value: metric.value,
              icon: metric.icon,
              color: metric.color,
              subtitle: metric.subtitle,
              badge: metric.badge,
              onTap: metric.onTap,
            );
          case MetricType.insight:
            return UniversalInfoCard.insight(
              title: metric.title,
              value: metric.value,
              icon: metric.icon,
              color: metric.color,
              subtitle: metric.subtitle,
              insight: metric.insight,
              onTap: metric.onTap,
            );
        }
      }).toList(),
    );
  }

  /// Build dashboard charts
  Widget _buildDashboardCharts(BuildContext context, ReportsController controller) {
    final dashboardData = controller.dashboardData;
    if (dashboardData == null || dashboardData.chartData.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Trends',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16.0),
        UniversalChart.line(
          data: dashboardData.chartData,
          title: 'Farm Performance Trend',
          primaryColor: const Color(0xFF2E7D32),
          isLoading: controller.isLoading,
        ),
      ],
    );
  }

  /// Build quick insights
  Widget _buildQuickInsights(BuildContext context, ReportsController controller) {
    final dashboardData = controller.dashboardData;
    if (dashboardData == null || dashboardData.insights.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Insights',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16.0),
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: const Color(0xFF2E7D32).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(
              color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: dashboardData.insights.map((insight) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.lightbulb_outline,
                    size: 16,
                    color: Color(0xFF2E7D32),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      insight,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  /// Build report type selector
  Widget _buildReportTypeSelector(BuildContext context, ReportsController controller) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: ReportType.values.map((type) {
          final isSelected = controller.selectedReportType == type;
          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: FilterChip(
              label: Text(type.displayName),
              avatar: Icon(type.icon, size: 16),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  controller.selectReportType(type);
                }
              },
              selectedColor: type.color.withValues(alpha: 0.2),
              checkmarkColor: type.color,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Build current report content
  Widget _buildCurrentReport(BuildContext context, ReportsController controller) {
    if (controller.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    final reportData = controller.currentReportData;
    if (reportData == null || !reportData.hasData) {
      return _buildEmptyState('No data available for selected report');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Report header
        Text(
          reportData.title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          reportData.subtitle,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 16.0),

        // Report metrics
        if (reportData.metrics.isNotEmpty) ...[
          _buildReportMetrics(context, reportData.metrics),
          const SizedBox(height: 24.0),
        ],

        // Report charts
        if (reportData.chartData.isNotEmpty) ...[
          _buildReportCharts(context, reportData),
          const SizedBox(height: 24.0),
        ],

        // Report insights
        if (reportData.insights.isNotEmpty) ...[
          _buildReportInsights(context, reportData.insights),
        ],
      ],
    );
  }

  /// Build report metrics
  Widget _buildReportMetrics(BuildContext context, Map<String, ReportMetric> metrics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: _getCrossAxisCount(context),
      crossAxisSpacing: 16.0,
      mainAxisSpacing: 16.0,
      childAspectRatio: 1.2,
      children: metrics.values.map((metric) {
        switch (metric.type) {
          case MetricType.kpi:
            return UniversalInfoCard.kpi(
              title: metric.title,
              value: metric.value,
              icon: metric.icon,
              color: metric.color,
              subtitle: metric.subtitle,
              onTap: metric.onTap,
            );
          case MetricType.metric:
            return UniversalInfoCard.metric(
              title: metric.title,
              value: metric.value,
              icon: metric.icon,
              color: metric.color,
              subtitle: metric.subtitle,
              badge: metric.badge,
              onTap: metric.onTap,
            );
          case MetricType.insight:
            return UniversalInfoCard.insight(
              title: metric.title,
              value: metric.value,
              icon: metric.icon,
              color: metric.color,
              subtitle: metric.subtitle,
              insight: metric.insight,
              onTap: metric.onTap,
            );
        }
      }).toList(),
    );
  }

  /// Build report charts
  Widget _buildReportCharts(BuildContext context, ReportData reportData) {
    return UniversalChart.bar(
      data: reportData.chartData,
      title: '${reportData.title} - Data Visualization',
      primaryColor: reportData.type.color,
    );
  }

  /// Build report insights
  Widget _buildReportInsights(BuildContext context, List<String> insights) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Insights',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        ...insights.map((insight) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                Icons.insights,
                size: 16,
                color: Color(0xFF2E7D32),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  insight,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  /// Build quick export options
  Widget _buildQuickExportOptions(BuildContext context, ReportsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Export',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16.0),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16.0,
          mainAxisSpacing: 16.0,
          childAspectRatio: 2,
          children: [
            _buildExportButton(
              context,
              'Export Dashboard PDF',
              Icons.picture_as_pdf,
              Colors.red,
              () async {
                final error = await controller.exportDashboardPDF();
                if (error != null && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(error)),
                  );
                }
              },
            ),
            _buildExportButton(
              context,
              'Export Dashboard Excel',
              Icons.table_chart,
              Colors.green,
              () async {
                final error = await controller.exportDashboardExcel();
                if (error != null && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(error)),
                  );
                }
              },
            ),
            _buildExportButton(
              context,
              'Export Current Report',
              Icons.file_download,
              const Color(0xFF2E7D32),
              () => _showExportDialog(context),
            ),
            _buildExportButton(
              context,
              'Batch Export All',
              Icons.folder_zip,
              Colors.orange,
              () async {
                final result = await controller.batchExportAll();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(result ?? 'Export completed successfully')),
                  );
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  /// Build export button
  Widget _buildExportButton(BuildContext context, String title, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.0),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build export history placeholder
  Widget _buildExportHistory(BuildContext context, ReportsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Exports',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16.0),
        Container(
          padding: const EdgeInsets.all(32.0),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: const Center(
            child: Text(
              'Export history will appear here',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build empty state
  Widget _buildEmptyState(String message) {
    return Container(
      padding: const EdgeInsets.all(32.0),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16.0),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods

  int _getCrossAxisCount(BuildContext context) {
    return MediaQuery.of(context).size.width > 600 ? 3 : 2;
  }

  void _showFilterDialog(BuildContext context) {
    // Placeholder for filter dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Reports'),
        content: const Text('Filter options will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker(BuildContext context) {
    final controller = context.read<ReportsController>();
    showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: controller.currentFilter.startDate != null &&
                       controller.currentFilter.endDate != null
          ? DateTimeRange(
              start: controller.currentFilter.startDate!,
              end: controller.currentFilter.endDate!,
            )
          : null,
    ).then((range) {
      if (range != null) {
        controller.updateDateRange(range.start, range.end);
      }
    });
  }

  void _showExportDialog(BuildContext context) {
    final controller = context.read<ReportsController>();
    showDialog(
      context: context,
      builder: (context) => ExportDialog(
        reportData: controller.currentReportData ??
                   controller.dashboardData ??
                   ReportData.empty(ReportType.dashboard),
      ),
    );
  }
}