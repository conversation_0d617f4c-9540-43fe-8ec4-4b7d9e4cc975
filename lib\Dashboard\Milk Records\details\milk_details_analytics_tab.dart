import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/milk_record_isar.dart';
import '../controllers/milk_details_controller.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../widgets/universal_info_card.dart';

/// Data class for analytics info cards to provide type safety
class AnalyticsCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String? insight;

  const AnalyticsCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.insight,
  });
}


class MilkDetailsAnalyticsTab extends StatefulWidget {
  const MilkDetailsAnalyticsTab({Key? key}) : super(key: key);

  @override
  State<MilkDetailsAnalyticsTab> createState() => _MilkDetailsAnalyticsTabState();
}

class _MilkDetailsAnalyticsTabState extends State<MilkDetailsAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  // Toggle state for chart view
  bool _showPieChart = true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Consumer<MilkDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final milkRecords = controller.milkRecords;

        if (milkRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Analytics Data',
            message: 'Add more milk production records for ${cattle?.name ?? 'this cattle'} to see detailed analytics.',
            tabColor: AppColors.milkHeader,
            tabIndex: 0, // Analytics tab
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(kPaddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProductionMetrics(milkRecords),
              const SizedBox(height: kSpacingLarge),
              _buildSessionAnalytics(milkRecords),
              const SizedBox(height: kSpacingLarge),
              _buildTrendAnalytics(milkRecords),
            ],
          ),
        );
      },
    );
  }

  /// Build production metrics section
  Widget _buildProductionMetrics(List<MilkRecordIsar> milkRecords) {
    final cardData = _buildProductionCards(milkRecords);
    return _buildAnalyticsSection(
      title: 'Production Analytics',
      subtitle: 'Milk production performance and trends',
      icon: Icons.water_drop,
      headerColor: AppColors.milkHeader,
      cardData: cardData,
    );
  }

  /// Build production metric cards with type safety and accurate calculations
  List<AnalyticsCardData> _buildProductionCards(List<MilkRecordIsar> milkRecords) {
    // Calculate total production using totalYield (computed from session amounts)
    final totalProduction = milkRecords.fold<double>(
      0.0, (sum, record) => sum + record.totalYield);

    // Calculate daily average by grouping records by date
    final dailyTotals = <String, double>{};
    for (final record in milkRecords) {
      if (record.date != null) {
        final dateKey = '${record.date!.year}-${record.date!.month}-${record.date!.day}';
        dailyTotals[dateKey] = (dailyTotals[dateKey] ?? 0.0) + record.totalYield;
      }
    }

    final averageDaily = dailyTotals.isNotEmpty
        ? dailyTotals.values.fold(0.0, (sum, daily) => sum + daily) / dailyTotals.length
        : 0.0;

    // Find peak daily production
    final peakDaily = dailyTotals.isNotEmpty
        ? dailyTotals.values.reduce((a, b) => a > b ? a : b)
        : 0.0;

    final productionDays = dailyTotals.length;

    return [
      AnalyticsCardData(
        title: 'Total Production',
        value: '${totalProduction.toStringAsFixed(1)}L',
        subtitle: 'Lifetime milk yield',
        icon: Icons.water_drop,
        color: AppColors.milkKpiColors[0],
        insight: 'Total milk produced by this cattle',
      ),
      AnalyticsCardData(
        title: 'Daily Average Yield',
        value: '${averageDaily.toStringAsFixed(1)}L',
        subtitle: 'Average per day',
        icon: Icons.trending_up,
        color: AppColors.milkKpiColors[1],
        insight: 'Average daily production across all production days',
      ),
      AnalyticsCardData(
        title: 'Peak Daily Yield',
        value: '${peakDaily.toStringAsFixed(1)}L',
        subtitle: 'Best single day',
        icon: Icons.star,
        color: AppColors.milkKpiColors[2],
        insight: 'Highest daily production recorded',
      ),
      AnalyticsCardData(
        title: 'Production Days',
        value: '$productionDays',
        subtitle: 'Total milking days',
        icon: Icons.calendar_today,
        color: AppColors.milkKpiColors[3],
        insight: 'Number of days with recorded production',
      ),
    ];
  }

  /// Build session analytics section
  Widget _buildSessionAnalytics(List<MilkRecordIsar> milkRecords) {
    final cardData = _buildSessionCards(milkRecords);
    return _buildAnalyticsSection(
      title: 'Session Analytics',
      subtitle: 'Morning, afternoon, and evening production patterns',
      icon: Icons.schedule,
      headerColor: AppColors.milkKpiColors[1],
      cardData: cardData,
    );
  }

  /// Build session metric cards with type safety
  List<AnalyticsCardData> _buildSessionCards(List<MilkRecordIsar> milkRecords) {
    final morningTotal = milkRecords.fold<double>(
      0.0, (sum, record) => sum + (record.morningAmount ?? 0.0));
    final afternoonTotal = milkRecords.fold<double>(
      0.0, (sum, record) => sum + (record.afternoonAmount ?? 0.0));
    final eveningTotal = milkRecords.fold<double>(
      0.0, (sum, record) => sum + (record.eveningAmount ?? 0.0));

    return [
      AnalyticsCardData(
        title: 'Morning Yield',
        value: '${morningTotal.toStringAsFixed(1)}L',
        subtitle: 'Morning sessions',
        icon: Icons.wb_sunny,
        color: AppColors.milkKpiColors[0],
        insight: 'Total morning production',
      ),
      AnalyticsCardData(
        title: 'Afternoon Yield',
        value: '${afternoonTotal.toStringAsFixed(1)}L',
        subtitle: 'Afternoon sessions',
        icon: Icons.wb_cloudy,
        color: AppColors.milkKpiColors[1],
        insight: 'Total afternoon production',
      ),
      AnalyticsCardData(
        title: 'Evening Yield',
        value: '${eveningTotal.toStringAsFixed(1)}L',
        subtitle: 'Evening sessions',
        icon: Icons.nights_stay,
        color: AppColors.milkKpiColors[2],
        insight: 'Total evening production',
      ),
    ];
  }

  /// Build trend analytics section with toggle charts
  Widget _buildTrendAnalytics(List<MilkRecordIsar> milkRecords) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Trend Analytics',
          subtitle: 'Production trends and session distribution',
          icon: Icons.timeline,
          color: AppColors.milkKpiColors[2],
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        // Toggle buttons
        _buildChartToggle(),
        const SizedBox(height: kSpacingMedium),
        // Single chart display based on toggle
        _showPieChart
          ? _buildSessionDistributionChart(milkRecords)
          : _buildProductionTrendChart(milkRecords),
      ],
    );
  }

  /// Build chart toggle buttons
  Widget _buildChartToggle() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildToggleButton(
              label: 'Session Distribution',
              icon: Icons.pie_chart,
              isSelected: _showPieChart,
              onTap: () => setState(() => _showPieChart = true),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildToggleButton(
              label: 'Production Trend',
              icon: Icons.show_chart,
              isSelected: !_showPieChart,
              onTap: () => setState(() => _showPieChart = false),
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual toggle button
  Widget _buildToggleButton({
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    // Define colors for each button
    final bool isPieChart = label == 'Session Distribution';
    final Color activeColor = isPieChart ? Colors.blue : Colors.purple;
    final Color inactiveColor = isPieChart ? Colors.blue[300]! : Colors.purple[300]!;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: kPaddingMedium,
          vertical: kPaddingMedium,
        ),
        decoration: BoxDecoration(
          color: isSelected ? activeColor : inactiveColor,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: Colors.white,
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 13,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build session distribution pie chart
  Widget _buildSessionDistributionChart(List<MilkRecordIsar> milkRecords) {
    final morningTotal = milkRecords.fold<double>(0.0, (sum, record) => sum + (record.morningAmount ?? 0.0));
    final afternoonTotal = milkRecords.fold<double>(0.0, (sum, record) => sum + (record.afternoonAmount ?? 0.0));
    final eveningTotal = milkRecords.fold<double>(0.0, (sum, record) => sum + (record.eveningAmount ?? 0.0));

    final total = morningTotal + afternoonTotal + eveningTotal;
    if (total == 0) {
      return _buildEmptyChart('No session data available');
    }

    return Card(
      elevation: 2,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(kPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: kPaddingSmall),
              child: Text(
                'Session Distribution',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.milkKpiColors[2],
                  fontSize: 18,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: kSpacingLarge),
            Center(
              child: SizedBox(
                height: 280,
                width: 280,
                child: PieChart(
                  PieChartData(
                    sections: [
                      if (morningTotal > 0)
                        PieChartSectionData(
                          value: morningTotal,
                          title: '${((morningTotal / total) * 100).toStringAsFixed(1)}%',
                          color: AppColors.milkKpiColors[0],
                          radius: 100,
                          titleStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      if (afternoonTotal > 0)
                        PieChartSectionData(
                          value: afternoonTotal,
                          title: '${((afternoonTotal / total) * 100).toStringAsFixed(1)}%',
                          color: AppColors.milkKpiColors[1],
                          radius: 100,
                          titleStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      if (eveningTotal > 0)
                        PieChartSectionData(
                          value: eveningTotal,
                          title: '${((eveningTotal / total) * 100).toStringAsFixed(1)}%',
                          color: AppColors.milkKpiColors[2],
                          radius: 100,
                          titleStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                    ],
                    centerSpaceRadius: 50,
                    sectionsSpace: 3,
                  ),
                ),
              ),
            ),
            const SizedBox(height: kSpacingLarge),
            _buildChartLegend([
              if (morningTotal > 0) {'label': 'Morning', 'color': AppColors.milkKpiColors[0], 'value': '${morningTotal.toStringAsFixed(1)}L'},
              if (afternoonTotal > 0) {'label': 'Afternoon', 'color': AppColors.milkKpiColors[1], 'value': '${afternoonTotal.toStringAsFixed(1)}L'},
              if (eveningTotal > 0) {'label': 'Evening', 'color': AppColors.milkKpiColors[2], 'value': '${eveningTotal.toStringAsFixed(1)}L'},
            ]),
          ],
        ),
      ),
    );
  }

  /// Build production trend line chart
  Widget _buildProductionTrendChart(List<MilkRecordIsar> milkRecords) {
    if (milkRecords.length < 2) {
      return _buildEmptyChart('Need more records for trend analysis');
    }

    // Sort records by date
    final sortedRecords = List<MilkRecordIsar>.from(milkRecords)
      ..sort((a, b) => (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now()));

    // Find max value for better scaling
    final maxValue = sortedRecords.map((r) => r.totalYield).reduce((a, b) => a > b ? a : b);
    final yAxisMax = (maxValue * 1.2).ceilToDouble(); // Add 20% padding

    return Card(
      elevation: 2,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(kPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: kPaddingSmall),
              child: Text(
                'Production Trend',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.milkKpiColors[2],
                  fontSize: 18,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: kSpacingLarge),
            SizedBox(
              height: 280,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    horizontalInterval: yAxisMax / 5,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Colors.grey[300]!,
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: Colors.grey[300]!,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 50,
                        interval: yAxisMax / 5,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            '${value.toStringAsFixed(1)}L',
                            style: const TextStyle(fontSize: 12),
                          );
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= 0 && value.toInt() < sortedRecords.length) {
                            final date = sortedRecords[value.toInt()].date;
                            return date != null
                                ? Padding(
                                    padding: const EdgeInsets.only(top: 8),
                                    child: Text(
                                      DateFormat('MM/dd').format(date),
                                      style: const TextStyle(fontSize: 11),
                                    ),
                                  )
                                : const Text('');
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: Colors.grey[400]!, width: 1),
                  ),
                  minX: 0,
                  maxX: (sortedRecords.length - 1).toDouble(),
                  minY: 0,
                  maxY: yAxisMax,
                  lineBarsData: [
                    LineChartBarData(
                      spots: List.generate(sortedRecords.length, (index) {
                        return FlSpot(index.toDouble(), sortedRecords[index].totalYield);
                      }),
                      isCurved: true,
                      color: AppColors.milkHeader,
                      barWidth: 4,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 6,
                            color: AppColors.milkHeader,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.milkHeader.withAlpha(51),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build empty chart placeholder
  Widget _buildEmptyChart(String message) {
    return Card(
      elevation: 2,
      child: Container(
        width: double.infinity,
        height: 350,
        padding: const EdgeInsets.all(kPaddingLarge),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _showPieChart ? Icons.pie_chart : Icons.show_chart,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: kSpacingMedium),
              Text(
                message,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build chart legend
  Widget _buildChartLegend(List<Map<String, dynamic>> items) {
    return Center(
      child: Wrap(
        spacing: kSpacingMedium,
        runSpacing: kSpacingSmall,
        alignment: WrapAlignment.center,
        children: items.map((item) {
          return Container(
            padding: const EdgeInsets.symmetric(
              horizontal: kPaddingSmall,
              vertical: 4,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 14,
                  height: 14,
                  decoration: BoxDecoration(
                    color: item['color'] as Color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  '${item['label']}: ${item['value']}',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: item['color'] as Color,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Build analytics section with consistent styling
  Widget _buildAnalyticsSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<AnalyticsCardData> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        ResponsiveGrid.cards(
          children: cardData.map((data) => _buildMetricCard(data)).toList(),
        ),
      ],
    );
  }

  /// Build individual metric card from typed data
  Widget _buildMetricCard(AnalyticsCardData data) {
    return UniversalInfoCard(
      title: data.title,
      value: data.value,
      subtitle: data.subtitle,
      icon: data.icon,
      color: data.color,
      insight: data.insight,
    );
  }

}
