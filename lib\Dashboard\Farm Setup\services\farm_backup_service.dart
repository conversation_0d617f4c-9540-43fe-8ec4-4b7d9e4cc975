import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';

import '../models/backup_settings_isar.dart';
import 'cloud_backup_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Farm Backup Service
/// 
/// Handles all backup-related operations for farm setup following established patterns.
/// Pure business logic service that orchestrates backup operations using CloudBackupService.
/// Follows single responsibility principle - only handles backup logic.
class FarmBackupService {
  static final Logger _logger = Logger('FarmBackupService');

  // Services accessed through dependency injection
  static CloudBackupService get _cloudBackupService => GetIt.instance<CloudBackupService>();

  /// Create a backup using the configured storage provider
  static Future<CloudBackupResult> createBackup(BackupSettingsIsar settings, String farmId) async {
    try {
      _logger.info('Creating backup for farm: $farmId using provider: ${settings.storageProvider.name}');

      final result = await _cloudBackupService.createCloudBackup(farmId, settings.storageProvider);

      if (result.success) {
        _logger.info('Backup created successfully: ${result.backupId}');
      } else {
        _logger.warning('Backup failed: ${result.message}');
      }

      return result;
    } catch (e) {
      _logger.severe('Error creating backup: $e');
      return CloudBackupResult.error('Error creating backup: $e');
    }
  }

  /// Restore from a backup using the specified provider
  static Future<CloudBackupResult> restoreFromBackup(
    String backupId, 
    BackupStorageProvider provider
  ) async {
    try {
      _logger.info('Restoring from backup: $backupId using provider: ${provider.name}');

      final result = await _cloudBackupService.restoreFromCloudBackup(backupId, provider);

      if (result.success) {
        _logger.info('Backup restored successfully: $backupId');
      } else {
        _logger.warning('Restore failed: ${result.message}');
      }

      return result;
    } catch (e) {
      _logger.severe('Error restoring from backup: $e');
      return CloudBackupResult.error('Error restoring from backup: $e');
    }
  }

  /// Update backup settings with last backup date
  static BackupSettingsIsar updateBackupSettingsAfterSuccess(
    BackupSettingsIsar settings,
    bool isCloudBackup
  ) {
    final now = DateTime.now();
    
    return settings.copyWith(
      lastBackupDate: now,
      lastCloudBackupDate: isCloudBackup ? now : settings.lastCloudBackupDate,
    );
  }

  /// Check if backup is needed based on settings
  static bool isBackupNeeded(BackupSettingsIsar settings) {
    if (!settings.autoBackupEnabled) {
      return false;
    }

    if (settings.lastBackupDate == null) {
      return true;
    }

    final daysSinceLastBackup = DateTime.now().difference(settings.lastBackupDate!).inDays;
    return daysSinceLastBackup >= settings.autoBackupFrequency;
  }

  /// Get backup status information
  static Map<String, dynamic> getBackupStatus(BackupSettingsIsar settings) {
    final now = DateTime.now();
    final lastBackup = settings.lastBackupDate;
    final lastCloudBackup = settings.lastCloudBackupDate;

    int? daysSinceLastBackup;
    int? daysSinceLastCloudBackup;

    if (lastBackup != null) {
      daysSinceLastBackup = now.difference(lastBackup).inDays;
    }

    if (lastCloudBackup != null) {
      daysSinceLastCloudBackup = now.difference(lastCloudBackup).inDays;
    }

    return {
      'autoBackupEnabled': settings.autoBackupEnabled,
      'backupFrequency': settings.autoBackupFrequency,
      'storageProvider': settings.storageProvider.name,
      'lastBackupDate': lastBackup?.toIso8601String(),
      'lastCloudBackupDate': lastCloudBackup?.toIso8601String(),
      'daysSinceLastBackup': daysSinceLastBackup,
      'daysSinceLastCloudBackup': daysSinceLastCloudBackup,
      'isBackupNeeded': isBackupNeeded(settings),
      'isCloudStorageEnabled': settings.isCloudStorageEnabled,
    };
  }

  /// Validate backup settings
  static void validateBackupSettings(BackupSettingsIsar settings) {
    if (settings.farmBusinessId == null || settings.farmBusinessId!.isEmpty) {
      throw ValidationException('Farm business ID is required for backup settings');
    }

    if (settings.autoBackupFrequency < 1) {
      throw ValidationException('Auto backup frequency must be at least 1 day');
    }

    if (settings.autoBackupFrequency > 365) {
      throw ValidationException('Auto backup frequency cannot exceed 365 days');
    }

    _logger.info('Backup settings validation passed');
  }

  /// Get recommended backup frequency based on farm activity
  static int getRecommendedBackupFrequency(int cattleCount, int dailyTransactions) {
    // High activity farms should backup more frequently
    if (cattleCount > 100 || dailyTransactions > 20) {
      return 1; // Daily
    } else if (cattleCount > 50 || dailyTransactions > 10) {
      return 3; // Every 3 days
    } else if (cattleCount > 20 || dailyTransactions > 5) {
      return 7; // Weekly
    } else {
      return 14; // Bi-weekly
    }
  }

  /// Create default backup settings for a farm
  static BackupSettingsIsar createDefaultBackupSettings(String farmBusinessId) {
    return BackupSettingsIsar.create(
      farmBusinessId: farmBusinessId,
      backupLocation: 'local',
      autoBackupEnabled: true,
      autoBackupFrequency: 7, // Weekly by default
    );
  }
}
