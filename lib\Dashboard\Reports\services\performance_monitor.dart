import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Performance monitoring service for Reports system
/// 
/// Tracks load times, memory usage, and performance metrics to ensure
/// the reports system meets the <2s dashboard load time requirement.
class PerformanceMonitor {
  static const int _targetLoadTimeMs = 2000; // 2 seconds
  static const int _targetMemoryLimitMB = 100; // 100 MB
  static const int _maxChartPoints = 1000; // Maximum chart data points

  /// Measure operation load time and log if it exceeds target
  static Future<T> measureLoadTime<T>(
    String operation,
    Future<T> Function() task,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await task();
      final duration = stopwatch.elapsedMilliseconds;
      
      // Log performance metrics
      _logPerformance(operation, duration);
      
      // Warn if exceeds target
      if (duration > _targetLoadTimeMs) {
        developer.log(
          'PERFORMANCE WARNING: $operation took ${duration}ms (target: <$_targetLoadTimeMs ms)',
          name: 'Reports.Performance',
          level: 900, // Warning level
        );
      }
      
      return result;
    } catch (error) {
      final duration = stopwatch.elapsedMilliseconds;
      developer.log(
        'PERFORMANCE ERROR: $operation failed after ${duration}ms - $error',
        name: 'Reports.Performance',
        level: 1000, // Error level
      );
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  /// Measure synchronous operation performance
  static T measureSyncOperation<T>(
    String operation,
    T Function() task,
  ) {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = task();
      final duration = stopwatch.elapsedMilliseconds;
      
      _logPerformance(operation, duration);
      
      if (duration > 500) { // 500ms threshold for sync operations
        developer.log(
          'SYNC PERFORMANCE WARNING: $operation took ${duration}ms',
          name: 'Reports.Performance',
          level: 900,
        );
      }
      
      return result;
    } finally {
      stopwatch.stop();
    }
  }

  /// Check if chart data exceeds recommended limits
  static bool shouldPaginateChartData(int dataPointCount) {
    if (dataPointCount > _maxChartPoints) {
      developer.log(
        'CHART DATA WARNING: $dataPointCount points exceeds limit of $_maxChartPoints',
        name: 'Reports.Performance',
        level: 900,
      );
      return true;
    }
    return false;
  }

  /// Log memory usage warning if available
  static void checkMemoryUsage(String context) {
    if (kDebugMode) {
      // In debug mode, we can check memory usage
      developer.log(
        'Memory check: $context',
        name: 'Reports.Memory',
        level: 800, // Info level
      );
    }
  }

  /// Get performance recommendations based on data size
  static List<String> getPerformanceRecommendations(int dataPoints, int chartCount) {
    final recommendations = <String>[];
    
    if (dataPoints > _maxChartPoints) {
      recommendations.add('Consider data pagination for $dataPoints data points');
    }
    
    if (chartCount > 5) {
      recommendations.add('Consider lazy loading for $chartCount charts');
    }
    
    if (dataPoints > 5000) {
      recommendations.add('Consider data aggregation for large datasets');
    }
    
    return recommendations;
  }

  /// Create performance report for debugging
  static Map<String, dynamic> createPerformanceReport({
    required int totalDataPoints,
    required int chartCount,
    required int cacheHitCount,
    required int cacheMissCount,
    required List<int> loadTimes,
  }) {
    final avgLoadTime = loadTimes.isEmpty ? 0 : 
        loadTimes.reduce((a, b) => a + b) / loadTimes.length;
    
    final maxLoadTime = loadTimes.isEmpty ? 0 : loadTimes.reduce((a, b) => a > b ? a : b);
    
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'performance': {
        'averageLoadTime': avgLoadTime.round(),
        'maxLoadTime': maxLoadTime,
        'targetLoadTime': _targetLoadTimeMs,
        'meetsTarget': maxLoadTime <= _targetLoadTimeMs,
      },
      'data': {
        'totalDataPoints': totalDataPoints,
        'chartCount': chartCount,
        'exceedsChartLimit': totalDataPoints > _maxChartPoints,
      },
      'cache': {
        'hitCount': cacheHitCount,
        'missCount': cacheMissCount,
        'hitRate': cacheHitCount + cacheMissCount > 0 
            ? (cacheHitCount / (cacheHitCount + cacheMissCount) * 100).round()
            : 0,
      },
      'recommendations': getPerformanceRecommendations(totalDataPoints, chartCount),
    };
  }

  /// Log performance metrics
  static void _logPerformance(String operation, int durationMs) {
    if (kDebugMode) {
      final level = durationMs > _targetLoadTimeMs ? 900 : 800; // Warning or Info
      developer.log(
        '$operation completed in ${durationMs}ms',
        name: 'Reports.Performance',
        level: level,
      );
    }
  }

  /// Benchmark chart rendering performance
  static Future<Map<String, int>> benchmarkChartRendering(
    List<Function> chartBuilders,
  ) async {
    final results = <String, int>{};
    
    for (int i = 0; i < chartBuilders.length; i++) {
      final chartName = 'Chart_$i';
      await measureLoadTime(
        'Render $chartName',
        () async {
          chartBuilders[i]();
          // Simulate rendering time
          await Future.delayed(const Duration(milliseconds: 10));
          return true;
        },
      );
      results[chartName] = 10; // Placeholder duration
    }
    
    return results;
  }

  /// Performance constants for easy access
  static const performanceTargets = {
    'dashboardLoadTime': _targetLoadTimeMs,
    'chartRenderTime': 1000, // 1 second
    'exportGenerationTime': 30000, // 30 seconds
    'memoryLimit': _targetMemoryLimitMB,
    'maxChartPoints': _maxChartPoints,
  };
}

/// Performance metrics data class
class PerformanceMetrics {
  final String operation;
  final int durationMs;
  final DateTime timestamp;
  final bool meetsTarget;
  final Map<String, dynamic>? metadata;

  const PerformanceMetrics({
    required this.operation,
    required this.durationMs,
    required this.timestamp,
    required this.meetsTarget,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'operation': operation,
      'durationMs': durationMs,
      'timestamp': timestamp.toIso8601String(),
      'meetsTarget': meetsTarget,
      'metadata': metadata,
    };
  }
}

/// Chart data paginator for performance optimization
class ChartDataPaginator {
  /// Paginate chart data to improve rendering performance
  static List<T> paginateForChart<T>(
    List<T> data, {
    int maxPoints = 1000,
  }) {
    if (data.length <= maxPoints) return data;
    
    // Smart sampling - keep first, last, and evenly distributed points
    final step = data.length / maxPoints;
    final sampled = <T>[];
    
    // Always include first point
    sampled.add(data.first);
    
    // Add evenly distributed points
    for (int i = 1; i < maxPoints - 1; i++) {
      final index = (i * step).round();
      if (index < data.length && index > 0) {
        sampled.add(data[index]);
      }
    }
    
    // Always include last point
    if (data.length > 1) {
      sampled.add(data.last);
    }
    
    developer.log(
      'Chart data paginated: ${data.length} → ${sampled.length} points',
      name: 'Reports.Performance',
    );
    
    return sampled;
  }

  /// Get pagination info for UI display
  static Map<String, dynamic> getPaginationInfo(int originalCount, int paginatedCount) {
    return {
      'originalCount': originalCount,
      'displayedCount': paginatedCount,
      'isPaginated': originalCount != paginatedCount,
      'reductionPercentage': originalCount > 0 
          ? ((originalCount - paginatedCount) / originalCount * 100).round()
          : 0,
    };
  }
}
