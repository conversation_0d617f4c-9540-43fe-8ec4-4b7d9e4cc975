# Events Module Usage Examples

## Table of Contents

1. [Basic Event Management](#basic-event-management)
2. [Filtering and Search](#filtering-and-search)
3. [Analytics and Reporting](#analytics-and-reporting)
4. [Notifications](#notifications)
5. [Automation](#automation)
6. [Performance Optimization](#performance-optimization)
7. [Error Handling](#error-handling)

## Basic Event Management

### Creating a New Event

```dart
import 'package:cattle_manager/Dashboard/Events/models/event_isar.dart';
import 'package:cattle_manager/Dashboard/Events/models/event_enums.dart';
import 'package:cattle_manager/Dashboard/Events/controllers/events_controller.dart';

// Create a manual health event
final healthEvent = EventIsar()
  ..businessId = 'event-${DateTime.now().millisecondsSinceEpoch}'
  ..cattleTagId = 'C001'
  ..eventTypeId = 'vaccination'
  ..title = 'Annual FMD Vaccination'
  ..description = 'Foot and Mouth Disease vaccination for breeding cattle'
  ..scheduledDate = DateTime.now().add(const Duration(days: 7))
  ..status = EventStatus.scheduled
  ..priority = EventPriority.high
  ..category = EventCategory.health
  ..estimatedCost = 25.00
  ..location = 'Main Barn'
  ..notificationsEnabled = true
  ..reminderMinutes = [1440, 60]; // 1 day and 1 hour reminders

// Add the event using the controller
final controller = EventsController();
await controller.addEvent(healthEvent);
```

### Creating a Recurring Event

```dart
// Create a weekly feed check event
final feedCheckEvent = EventIsar()
  ..businessId = 'feed-check-weekly'
  ..cattleTagId = 'ALL' // For all cattle
  ..eventTypeId = 'feed-inspection'
  ..title = 'Weekly Feed Quality Check'
  ..description = 'Inspect feed quality and quantity'
  ..scheduledDate = DateTime.now().add(const Duration(days: 1))
  ..status = EventStatus.scheduled
  ..priority = EventPriority.medium
  ..category = EventCategory.feeding
  ..isRecurring = true
  ..recurrencePattern = RecurrencePattern.weekly
  ..recurrenceInterval = 1
  ..recurrenceEndDate = DateTime.now().add(const Duration(days: 365))
  ..notificationsEnabled = true
  ..reminderMinutes = [60]; // 1 hour reminder

await controller.addEvent(feedCheckEvent);
```

### Updating an Event

```dart
// Update an existing event
final existingEvent = controller.events
    .firstWhere((e) => e.businessId == 'event-123');

existingEvent
  ..notes = 'Updated with additional information'
  ..estimatedCost = 30.00
  ..location = 'North Pasture';

await controller.updateEvent(existingEvent);
```

### Completing an Event

```dart
// Mark an event as completed
await controller.completeEvent(
  'event-123',
  'Vaccination completed successfully. No adverse reactions observed.',
);

// Or complete with additional details
final event = controller.events
    .firstWhere((e) => e.businessId == 'event-123');

event
  ..status = EventStatus.completed
  ..completedDate = DateTime.now()
  ..completedBy = 'Dr. Smith'
  ..completionNotes = 'All cattle vaccinated. Next vaccination due in 6 months.'
  ..actualCost = 28.50;

await controller.updateEvent(event);
```

### Deleting an Event

```dart
// Delete an event by business ID
await controller.deleteEvent('event-123');

// Or delete with confirmation
final shouldDelete = await showDialog<bool>(
  context: context,
  builder: (context) => AlertDialog(
    title: const Text('Delete Event'),
    content: const Text('Are you sure you want to delete this event?'),
    actions: [
      TextButton(
        onPressed: () => Navigator.of(context).pop(false),
        child: const Text('Cancel'),
      ),
      TextButton(
        onPressed: () => Navigator.of(context).pop(true),
        child: const Text('Delete'),
      ),
    ],
  ),
);

if (shouldDelete == true) {
  await controller.deleteEvent('event-123');
}
```

## Filtering and Search

### Basic Filtering

```dart
// Filter by category
controller.applyCategoryFilter(EventCategory.health);

// Filter by status
controller.applyStatusFilter(EventStatus.scheduled);

// Filter by cattle
controller.applyCattleFilter('C001');

// Filter by priority
controller.updateFilters(priority: EventPriority.high);
```

### Advanced Filtering

```dart
// Apply multiple filters at once
controller.applyMultipleFilters(
  category: EventCategory.health,
  status: EventStatus.scheduled,
  priority: EventPriority.high,
  startDate: DateTime.now(),
  endDate: DateTime.now().add(const Duration(days: 30)),
);

// Create custom filter state
final customFilter = EventFilterState(
  searchQuery: 'vaccination',
  category: EventCategory.health,
  status: EventStatus.scheduled,
  startDate: DateTime.now().subtract(const Duration(days: 7)),
  endDate: DateTime.now().add(const Duration(days: 30)),
  priority: EventPriority.high,
  isAutoGenerated: false, // Only manual events
  location: 'Main Barn',
);

controller.applyFilters(customFilter);
```

### Search Functionality

```dart
// Simple text search
controller.applySearchFilter('vaccination');

// Search across multiple fields
controller.applySearchFilter('FMD'); // Searches title, description, notes

// Clear search
controller.applySearchFilter('');
```

### Date Range Filtering

```dart
// This week's events
final startOfWeek = DateTime.now().subtract(
  Duration(days: DateTime.now().weekday - 1),
);
final endOfWeek = startOfWeek.add(const Duration(days: 6));

controller.applyDateRangeFilter(startOfWeek, endOfWeek);

// Next 30 days
controller.applyDateRangeFilter(
  DateTime.now(),
  DateTime.now().add(const Duration(days: 30)),
);

// Overdue events (past events that aren't completed)
controller.applyDateRangeFilter(
  DateTime.now().subtract(const Duration(days: 365)),
  DateTime.now().subtract(const Duration(days: 1)),
);
controller.applyStatusFilter(EventStatus.scheduled);
```

### Clearing Filters

```dart
// Clear all filters
controller.clearFilters();

// Clear specific filters
controller.updateFilters(
  category: null,
  status: null,
  clearStartDate: true,
  clearEndDate: true,
);
```

## Analytics and Reporting

### Basic Analytics

```dart
// Get basic metrics
final analytics = controller.analytics;

print('=== Event Analytics ===');
print('Total events: ${analytics.totalEvents}');
print('Completed events: ${analytics.completedEvents}');
print('Scheduled events: ${analytics.scheduledEvents}');
print('Overdue events: ${analytics.overdueEvents}');
print('Completion rate: ${analytics.completionRate.toStringAsFixed(1)}%');
```

### Category Analysis

```dart
// Analyze events by category
final categoryDistribution = controller.eventsByCategory;

print('=== Events by Category ===');
categoryDistribution.forEach((category, count) {
  final percentage = (count / controller.totalEvents * 100).toStringAsFixed(1);
  print('$category: $count events ($percentage%)');
});

// Find most common category
final mostCommonCategory = categoryDistribution.entries
    .reduce((a, b) => a.value > b.value ? a : b);
print('Most common category: ${mostCommonCategory.key} (${mostCommonCategory.value} events)');
```

### Cost Analysis

```dart
// Analyze costs
print('=== Cost Analysis ===');
print('Total estimated costs: \$${controller.totalEstimatedCosts.toStringAsFixed(2)}');
print('Total actual costs: \$${controller.totalActualCosts.toStringAsFixed(2)}');
print('Average cost per event: \$${controller.averageEventCost.toStringAsFixed(2)}');

// Calculate cost variance
final costVariance = controller.totalActualCosts - controller.totalEstimatedCosts;
final variancePercentage = controller.totalEstimatedCosts > 0 
    ? (costVariance / controller.totalEstimatedCosts * 100).toStringAsFixed(1)
    : '0.0';

print('Cost variance: \$${costVariance.toStringAsFixed(2)} (${variancePercentage}%)');
```

### Time Analysis

```dart
// Analyze timing
print('=== Time Analysis ===');
print('Average completion time: ${controller.averageCompletionTime.toStringAsFixed(1)} hours');
print('Events this week: ${controller.eventsThisWeek}');
print('Events this month: ${controller.eventsThisMonth}');
print('Events next week: ${controller.eventsNextWeek}');
print('Events next month: ${controller.eventsNextMonth}');
```

### Automation Analysis

```dart
// Analyze automation effectiveness
print('=== Automation Analysis ===');
print('Auto-generated events: ${controller.autoGeneratedEvents}');
print('Manual events: ${controller.manualEvents}');
print('Automation rate: ${controller.automationRate.toStringAsFixed(1)}%');

// Events by source module
final sourceModules = controller.eventsBySourceModule;
print('Events by source module:');
sourceModules.forEach((module, count) {
  print('  $module: $count events');
});
```

### Cattle-Specific Analytics

```dart
// Analyze events per cattle
print('=== Cattle Analytics ===');
print('Cattle with events: ${controller.cattleWithEvents}');
print('Average events per cattle: ${controller.averageEventsPerCattle.toStringAsFixed(1)}');

// Top cattle by event count
final cattleEvents = controller.eventsByCattle;
final sortedCattle = cattleEvents.entries.toList()
  ..sort((a, b) => b.value.compareTo(a.value));

print('Top 5 cattle by event count:');
for (int i = 0; i < 5 && i < sortedCattle.length; i++) {
  final entry = sortedCattle[i];
  print('  ${entry.key}: ${entry.value} events');
}
```

## Notifications

### Setting Up Event Notifications

```dart
import 'package:cattle_manager/Dashboard/Events/services/event_notification_service.dart';

final notificationService = EventNotificationService();

// Initialize the service (call once during app startup)
notificationService.initialize();

// Create an event with notifications
final event = EventIsar()
  ..businessId = 'event-with-notifications'
  ..title = 'Important Vaccination'
  ..scheduledDate = DateTime.now().add(const Duration(days: 3))
  ..notificationsEnabled = true
  ..reminderMinutes = [4320, 1440, 60]; // 3 days, 1 day, 1 hour

// Schedule the reminders
await notificationService.scheduleEventReminders(event);
```

### Managing Notifications

```dart
// Cancel notifications for a completed event
await notificationService.cancelEventReminders('event-123');

// Send overdue notifications manually
final overdueCount = await notificationService.sendOverdueNotifications();
print('Sent $overdueCount overdue notifications');

// Send upcoming notifications manually
final upcomingCount = await notificationService.sendUpcomingEventNotifications();
print('Sent $upcomingCount upcoming notifications');
```

### Custom Notification Settings

```dart
// Get current notification settings
final settings = await notificationService.getNotificationSettings();

// Update notification preferences
if (settings != null) {
  settings
    ..eventsEnabled = true
    ..overdueEnabled = true
    ..upcomingEnabled = true
    ..reminderHours = [24, 1]; // 1 day and 1 hour default reminders
  
  await notificationService.updateNotificationSettings(settings);
}
```

## Automation

### Health Event Automation

```dart
import 'package:cattle_manager/Dashboard/Events/services/event_automation_service.dart';

// Automatically create health event when health record is saved
await EventAutomationService.createHealthEvent(
  cattleTagId: 'C001',
  healthRecordId: 'health-record-123',
  eventType: 'vaccination',
  date: DateTime.now(),
  notes: 'Annual FMD vaccination administered',
  diagnosis: 'Healthy',
  treatment: 'FMD Vaccine 5ml',
  cost: 25.00,
);
```

### Breeding Event Automation

```dart
// Automatically create breeding event and schedule pregnancy check
await EventAutomationService.createBreedingEvent(
  cattleTagId: 'C001',
  breedingRecordId: 'breeding-456',
  breedingDate: DateTime.now(),
  method: 'Artificial Insemination',
  bullIdOrType: 'Holstein Bull #123',
  schedulePregnancyCheck: true, // Will create pregnancy check event
  cost: 50.00,
);
```

### Transaction Event Automation

```dart
// Automatically create transaction event
await EventAutomationService.createTransactionEvent(
  cattleTagId: 'C001',
  transactionId: 'transaction-789',
  transactionType: 'purchase',
  date: DateTime.now(),
  amount: 1500.00,
  notes: 'Purchased from Smith Farm - excellent breeding stock',
);
```

### Weight Monitoring Automation

```dart
// Automatically create weight event and schedule next weighing
await EventAutomationService.createWeightEvent(
  cattleTagId: 'C001',
  weight: 450.5,
  date: DateTime.now(),
  scheduleNextWeighing: true, // Will schedule next weighing in 30 days
);
```

## Performance Optimization

### Pagination for Large Datasets

```dart
// Load events in pages for better performance
Future<void> loadEventsPage(int page) async {
  final result = await controller.getPaginatedEvents(
    page: page,
    pageSize: 20,
  );
  
  print('Page ${result.page + 1} of ${result.totalPages}');
  print('Showing ${result.items.length} of ${result.totalItems} events');
  
  // Process the events
  for (final event in result.items) {
    print('Event: ${event.title} - ${event.scheduledDate}');
  }
}

// Load first page
await loadEventsPage(0);
```

### Calendar Optimization

```dart
// Get optimized calendar events for efficient rendering
final calendarEvents = controller.getOptimizedCalendarEvents(
  DateTime.now(),
  DateTime.now().add(const Duration(days: 30)),
);

// Use in calendar widget
calendarEvents.forEach((date, events) {
  print('${date.day}/${date.month}: ${events.length} events');
  for (final event in events) {
    print('  - ${event.title}');
  }
});
```

### Lazy Loading Attachments

```dart
// Load attachments only when needed
Future<void> loadEventAttachments(String eventBusinessId) async {
  final attachments = await controller.getLazyAttachments(eventBusinessId);
  
  print('Event has ${attachments.length} attachments:');
  for (final attachment in attachments) {
    print('  - ${attachment.fileName} (${attachment.formattedFileSize})');
  }
}
```

### Efficient Filtering

```dart
// Use database-level filtering for better performance
controller.applyFilters(EventFilterState(
  category: EventCategory.health,
  status: EventStatus.scheduled,
  startDate: DateTime.now(),
  endDate: DateTime.now().add(const Duration(days: 30)),
));

// This creates an optimized database query instead of filtering in memory
```

## Error Handling

### Validation Errors

```dart
try {
  final invalidEvent = EventIsar()
    ..businessId = '' // Invalid: empty business ID
    ..cattleTagId = null // Invalid: missing cattle ID
    ..title = ''; // Invalid: empty title
  
  await controller.addEvent(invalidEvent);
} on ValidationException catch (e) {
  print('Validation error: ${e.message}');
  if (e.field != null) {
    print('Field with error: ${e.field}');
  }
  
  // Show user-friendly error message
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text('Please check the event details: ${e.message}'),
      backgroundColor: Colors.red,
    ),
  );
}
```

### Database Errors

```dart
try {
  await controller.addEvent(event);
} on DatabaseException catch (e) {
  print('Database error: ${e.message}');
  if (e.operation != null) {
    print('Failed operation: ${e.operation}');
  }
  
  // Show retry option
  final shouldRetry = await showDialog<bool>(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Database Error'),
      content: Text('Failed to save event: ${e.message}\n\nWould you like to try again?'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(true),
          child: const Text('Retry'),
        ),
      ],
    ),
  );
  
  if (shouldRetry == true) {
    await controller.addEvent(event);
  }
}
```

### Automation Errors

```dart
try {
  await EventAutomationService.createHealthEvent(
    cattleTagId: 'C001',
    healthRecordId: 'health-123',
    eventType: 'vaccination',
    date: DateTime.now(),
  );
} on AutomationException catch (e) {
  print('Automation error: ${e.message}');
  print('Source module: ${e.sourceModule}');
  print('Source record: ${e.sourceRecordId}');
  
  // Log for debugging but don't fail the main operation
  logger.warning('Event automation failed: ${e.message}');
}
```

### Network and Connectivity Errors

```dart
try {
  await controller.refresh();
} catch (e) {
  if (e.toString().contains('network') || e.toString().contains('connection')) {
    // Handle network errors
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Network error. Please check your connection and try again.'),
        backgroundColor: Colors.orange,
      ),
    );
  } else {
    // Handle other errors
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('An error occurred: ${e.toString()}'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
```

### Graceful Error Recovery

```dart
class EventsWidget extends StatefulWidget {
  @override
  _EventsWidgetState createState() => _EventsWidgetState();
}

class _EventsWidgetState extends State<EventsWidget> {
  late EventsController controller;
  bool hasError = false;
  String? errorMessage;
  
  @override
  void initState() {
    super.initState();
    controller = EventsController();
    _loadEvents();
  }
  
  Future<void> _loadEvents() async {
    try {
      setState(() {
        hasError = false;
        errorMessage = null;
      });
      
      await controller.refresh();
    } catch (e) {
      setState(() {
        hasError = true;
        errorMessage = e.toString();
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading events: $errorMessage'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadEvents,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
    
    return Consumer<EventsController>(
      builder: (context, controller, child) {
        if (controller.state == ControllerState.loading) {
          return const Center(child: CircularProgressIndicator());
        }
        
        return ListView.builder(
          itemCount: controller.events.length,
          itemBuilder: (context, index) {
            final event = controller.events[index];
            return EventCard(event: event);
          },
        );
      },
    );
  }
}
```

These examples demonstrate comprehensive usage of the Events module, covering all major functionality with practical, real-world scenarios.