import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/milk_controller.dart';
import '../../widgets/index.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../../shared/models/info_card_data.dart';



class MilkAnalyticsTab extends StatefulWidget {
  final MilkController controller;

  const MilkAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<MilkAnalyticsTab> createState() => _MilkAnalyticsTabState();
}

class _MilkAnalyticsTabState extends State<MilkAnalyticsTab> {
  int _selectedChartIndex = 0; // 0: Session Distribution, 1: Cattle Production

  // Use global constants instead of local ones
  static const _fallbackColor = AppColors.fallback;

  // Common chart configuration
  static const _chartRadius = 80.0;
  static const _chartCenterSpace = 50.0;
  static const _chartSectionsSpace = 2.0;
  static const _chartHeight = 240.0;

  // Common text styles
  static const _chartTitleStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white);

  @override
  void initState() {
    super.initState();
    // Data loading is now handled by the controller
  }

  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<Map<String, dynamic>> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header using universal component
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero),
        const SizedBox(height: kSpacingMedium),

        // 2. Use ResponsiveGrid.cards for consistent responsive behavior
        ResponsiveGrid.cards(
          children: cardData.map((data) {
            return UniversalInfoCard(
              title: data['title'] as String,
              value: data['value'] as String,
              subtitle: data['subtitle'] as String?,
              icon: data['icon'] as IconData,
              color: data['color'] as Color,
              badge: data['badge'] as String?,
              insight: data['insight'] as String?);
          }).toList()),
      ]);
  }

  // Universal pie chart builder - pure UI component without business logic
  Widget _buildUniversalPieChart(Map<String, dynamic> data, Map<String, Color> colors, {String? emptyMessage}) {
    if (data.isEmpty || widget.controller.totalMilkRecords == 0) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    // Ensure we have valid data before rendering
    final validEntries = data.entries.where((entry) => (entry.value as num) > 0).toList();
    if (validEntries.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    final total = validEntries.fold<double>(0, (sum, entry) => sum + (entry.value as num).toDouble());
    final sections = validEntries.map((entry) {
      final value = (entry.value as num).toDouble();
      final percentage = (value / total) * 100;
      final sectionColor = colors[entry.key] ?? _fallbackColor;

      return PieChartSectionData(
        color: sectionColor,
        value: value,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: _chartRadius,
        titleStyle: _chartTitleStyle);
    }).toList();

    // Add a small delay to ensure smooth rendering
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: _chartCenterSpace,
          sectionsSpace: _chartSectionsSpace,
          borderData: FlBorderData(show: false))));
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Handle different controller states
        switch (widget.controller.state) {
          case ControllerState.initial:
          case ControllerState.loading:
            return const Center(
              child: CircularProgressIndicator(),
            );
          case ControllerState.error:
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.controller.errorMessage ?? 'Failed to load milk data',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => widget.controller.loadData(),
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          case ControllerState.empty:
            return UniversalTabEmptyState.forTab(
              title: 'No Milk Data',
              message: 'Start by adding your first milk record to see analytics.',
              tabColor: AppColors.milkHeader,
              tabIndex: 0, // Analytics tab
              action: TabEmptyStateActions.addFirstRecord(
                onPressed: () {
                  // Navigate to records tab or show add dialog
                },
                tabColor: AppColors.milkHeader));
          case ControllerState.loaded:
      //           default:
 // Unreachable default case removed
            // Check if we have data to display
            if (widget.controller.totalMilkRecords == 0) {
              return UniversalTabEmptyState.forTab(
                title: 'No Milk Data',
                message: 'Add milk records to your herd to view comprehensive analytics and insights.',
                tabColor: AppColors.milkHeader,
                tabIndex: 0, // Analytics tab
                action: TabEmptyStateActions.addFirstRecord(
                  onPressed: () {
                    // Navigate to add milk record screen
                    Navigator.of(context).pushNamed('/milk/add');
                  },
                  tabColor: AppColors.milkHeader,
                ),
              );
            }

            // Define sections for clean, declarative layout
            final sections = [
              _buildEnhancedKPIDashboard(context),
              _buildMilkCompositionAnalytics(context),
              _buildProductionAnalytics(context),
              _buildSalesOverview(context),
            ];

            // Use RefreshIndicator with SingleChildScrollView for pull-to-refresh functionality
            return RefreshIndicator(
              onRefresh: () async {
                await widget.controller.refresh();
              },
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(kPaddingMedium), // Use global constant
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (int i = 0; i < sections.length; i++) ...[
                      sections[i],
                      if (i < sections.length - 1) const SizedBox(height: kSpacingLarge), // Use global constant
                    ],
                  ])));
        }
      });
  }

  Widget _buildEnhancedKPIDashboard(BuildContext context) {
    final kpiColors = _getKPIColors();
    final kpiCards = _buildKPICards(kpiColors);

    return _buildGridSection(
      title: 'Key Performance Indicators',
      subtitle: 'Essential metrics for your milk production management',
      icon: Icons.dashboard_outlined,
      headerColor: AppColors.milkHeader,
      cardData: kpiCards.map((card) => card.toMap()..['badge'] = null).toList());
  }

  /// Build KPI cards using InfoCardData for better type safety
  List<InfoCardData> _buildKPICards(List<Color> kpiColors) {
    return [
      InfoCardData(
        title: 'Total Records',
        value: widget.controller.totalMilkRecords.toString(),
        subtitle: 'milk records',
        icon: Icons.list_alt,
        color: kpiColors[0],
        insight: 'Total milk entries'),
      InfoCardData(
        title: 'Total Produced',
        value: '${widget.controller.totalMilkProduced.toStringAsFixed(1)}L',
        subtitle: 'liters produced',
        icon: Icons.water_drop,
        color: kpiColors[1],
        insight: 'Total milk production'),
      InfoCardData(
        title: 'Total Sales',
        value: widget.controller.totalMilkSales.toString(),
        subtitle: 'sales transactions',
        icon: Icons.point_of_sale,
        color: kpiColors[2],
        insight: 'Sales completed'),
      InfoCardData(
        title: 'Total Revenue',
        value: '\$${widget.controller.totalRevenue.toStringAsFixed(2)}',
        subtitle: 'revenue generated',
        icon: Icons.attach_money,
        color: kpiColors[3],
        insight: 'Total earnings'),
      InfoCardData(
        title: 'Average Daily',
        value: '${widget.controller.averageDailyProduction.toStringAsFixed(1)}L',
        subtitle: 'daily production',
        icon: Icons.today,
        color: kpiColors[4],
        insight: 'Daily average'),
      InfoCardData(
        title: 'Avg Price/Liter',
        value: '\$${widget.controller.averagePricePerLiter.toStringAsFixed(2)}',
        subtitle: 'per liter',
        icon: Icons.price_change,
        color: kpiColors[5],
        insight: 'Average selling price'),
      InfoCardData(
        title: 'Used for Calves',
        value: '${widget.controller.totalMilkUsedForCalves.toStringAsFixed(1)}L',
        subtitle: 'calf consumption',
        icon: Icons.child_care,
        color: kpiColors[6 % kpiColors.length],
        insight: 'Milk used for feeding calves'),
      InfoCardData(
        title: 'Used for Home',
        value: '${widget.controller.totalMilkUsedForHome.toStringAsFixed(1)}L',
        subtitle: 'home consumption',
        icon: Icons.home,
        color: kpiColors[7 % kpiColors.length],
        insight: 'Milk used for home consumption'),
      InfoCardData(
        title: 'Total Used',
        value: '${widget.controller.totalMilkUsed.toStringAsFixed(1)}L',
        subtitle: 'total consumption',
        icon: Icons.local_dining,
        color: kpiColors[8 % kpiColors.length],
        insight: 'Total milk used (calves + home)'),
    ];
  }

  Widget _buildMilkCompositionAnalytics(BuildContext context) {
    final sessionData = widget.controller.sessionDistribution;
    final cattleData = widget.controller.cattleProductionDistribution;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Milk Composition Analytics',
          icon: Icons.pie_chart_outline,
          color: AppColors.milkHeader,
          subtitle: 'Detailed breakdown of milk production patterns',
          filled: true,
          padding: EdgeInsets.zero),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Toggle buttons for chart selection
        _buildChartToggleButtons(context),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Single chart display based on selection
        _buildSelectedChart(context, sessionData, cattleData),
      ]);
  }

  Widget _buildChartToggleButtons(BuildContext context) {
    final chartOptions = [
      {'title': 'Sessions', 'icon': Icons.schedule},
      {'title': 'Cattle', 'icon': Icons.pets},
    ];

    // Use colors from AppColors instead of hardcoded values
    final toggleColors = [
      AppColors.milkKpiColors[0], // Teal
      AppColors.milkKpiColors[1], // Green
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chartOptions.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedChartIndex == index;

        return ChoiceChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected ? Colors.white : toggleColors[index],
              ),
              const SizedBox(width: 6),
              Text(
                option['title'] as String,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: isSelected ? Colors.white : toggleColors[index],
                ),
              ),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedChartIndex = index;
              });
            }
          },
          selectedColor: toggleColors[index],
          side: BorderSide(
            color: toggleColors[index],
            width: 2,
          ),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        );
      }).toList());
  }

  Widget _buildSelectedChart(
    BuildContext context,
    Map<String, double> sessionData,
    Map<String, double> cattleData) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildEnhancedChart(
          context,
          'Session Distribution',
          _buildUniversalPieChart(sessionData, _getSessionColors()),
          _buildEnhancedLegend(sessionData, _getSessionColors()),
          Icons.schedule);
      case 1:
        return _buildEnhancedChart(
          context,
          'Cattle Production Distribution',
          _buildUniversalPieChart(cattleData, _getCattleColors()),
          _buildEnhancedLegend(cattleData, _getCattleColors()),
          Icons.pets);
      default:
        return _buildEnhancedChart(
          context,
          'Session Distribution',
          _buildUniversalPieChart(sessionData, _getSessionColors()),
          _buildEnhancedLegend(sessionData, _getSessionColors()),
          Icons.schedule);
    }
  }

  Widget _buildEnhancedChart(
    BuildContext context,
    String title,
    Widget chart,
    Widget? legend,
    IconData icon) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(kBorderRadius * 2), // Use global constant with multiplier
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4)),
        ]),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingLarge), // Use global constant
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.milkHeader.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6)),
                  child: Icon(
                    icon,
                    color: AppColors.milkHeader,
                    size: 16)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold))),
              ]),
            const SizedBox(height: kSpacingLarge), // Use global constant
            SizedBox(height: _chartHeight, child: Center(child: chart)), // Chart height and centered
            if (legend != null) ...[
              const SizedBox(height: kSpacingMedium), // Use global constant
              legend,
            ],
          ])));
  }

  Widget _buildProductionAnalytics(BuildContext context) {
    final productionColors = _getProductionColors();
    final productionCards = _buildProductionCards(productionColors);

    return _buildGridSection(
      title: 'Production Analytics',
      subtitle: 'Detailed production metrics and performance indicators',
      icon: Icons.analytics,
      headerColor: AppColors.milkKpiColors[1], // Green
      cardData: productionCards.map((card) => card.toMap()).toList());
  }

  /// Build production analytics cards using InfoCardData for better type safety
  List<InfoCardData> _buildProductionCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Production Consistency',
        value: '${widget.controller.productionConsistency.toStringAsFixed(1)}%',
        subtitle: 'consistency score',
        icon: Icons.trending_up,
        color: colors[0],
        insight: 'Production reliability'),
      InfoCardData(
        title: 'Sales Efficiency',
        value: '${widget.controller.salesEfficiency.toStringAsFixed(1)}%',
        subtitle: 'sales efficiency',
        icon: Icons.sell,
        color: colors[1],
        insight: 'Sales performance'),
      InfoCardData(
        title: 'Top Producer',
        value: widget.controller.topProducingCattle.isNotEmpty
            ? widget.controller.topProducingCattle
            : 'N/A',
        subtitle: '${widget.controller.topProducingCattleAmount.toStringAsFixed(1)}L',
        icon: Icons.star,
        color: colors[2],
        insight: 'Best performing cattle'),
      InfoCardData(
        title: 'Production Days',
        value: widget.controller.daysWithProduction.toString(),
        subtitle: 'days with production',
        icon: Icons.calendar_today,
        color: colors[3],
        insight: 'Active production days'),
    ];
  }

  Widget _buildSalesOverview(BuildContext context) {
    final salesColors = _getSalesColors();
    final salesCards = _buildSalesCards(salesColors);

    return _buildGridSection(
      title: 'Sales Overview',
      subtitle: 'Revenue and sales performance metrics',
      icon: Icons.point_of_sale,
      headerColor: AppColors.milkKpiColors[2], // Blue
      cardData: salesCards.map((card) => card.toMap()).toList());
  }

  /// Build sales cards using InfoCardData for better type safety
  List<InfoCardData> _buildSalesCards(List<Color> salesColors) {
    return [
      InfoCardData(
        title: 'Total Milk Sold',
        value: '${widget.controller.totalMilkSold.toStringAsFixed(1)}L',
        subtitle: 'liters sold',
        icon: Icons.shopping_cart,
        color: salesColors[0],
        insight: 'Total sales volume'),
      InfoCardData(
        title: 'Best Buyer',
        value: widget.controller.bestBuyer.isNotEmpty
            ? widget.controller.bestBuyer
            : 'N/A',
        subtitle: '${widget.controller.bestBuyerVolume.toStringAsFixed(1)}L',
        icon: Icons.person,
        color: salesColors[1],
        insight: 'Top customer'),
      InfoCardData(
        title: 'Sales Days',
        value: widget.controller.daysWithSales.toString(),
        subtitle: 'days with sales',
        icon: Icons.event_available,
        color: salesColors[2],
        insight: 'Active sales days'),
      InfoCardData(
        title: 'Avg Per Cattle',
        value: '${widget.controller.averageProductionPerCattle.toStringAsFixed(1)}L',
        subtitle: 'per cattle',
        icon: Icons.pets,
        color: salesColors[3],
        insight: 'Production per animal'),
    ];
  }

  Widget _buildEnhancedLegend(Map<String, dynamic> data, Map<String, Color> colors) {
    if (data.isEmpty) return const SizedBox.shrink();

    final total = data.values.fold<double>(0, (sum, value) => sum + (value as num).toDouble());

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: data.entries.map((entry) {
        final value = (entry.value as num).toDouble();
        final percentage = total > 0 ? ((value / total) * 100).toStringAsFixed(1) : '0';
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: colors[entry.key]?.withValues(alpha: 0.1) ?? _fallbackColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6)),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: colors[entry.key] ?? _fallbackColor,
                  shape: BoxShape.circle)),
              const SizedBox(width: 6),
              Text(
                '$entry.key (${value.toStringAsFixed(1)}) $percentage%',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87)),
            ]));
      }).toList());
  }

  // Color Management - Different colors for each KPI card (multi-color rule)
  List<Color> _getKPIColors() => AppColors.milkKpiColors;
  List<Color> _getProductionColors() => AppColors.milkKpiColors;
  List<Color> _getSalesColors() => AppColors.milkKpiColors;

  Map<String, Color> _getSessionColors() => AppColors.milkSessionColors;

  Map<String, Color> _getCattleColors() {
    final cattleDistribution = widget.controller.cattleProductionDistribution;
    final cattleColors = <String, Color>{};

    // Use colors from AppColors instead of hardcoded values
    const availableColors = AppColors.milkKpiColors;

    int colorIndex = 0;
    for (final cattleName in cattleDistribution.keys) {
      cattleColors[cattleName] = availableColors[colorIndex % availableColors.length];
      colorIndex++;
    }

    return cattleColors;
  }
}
