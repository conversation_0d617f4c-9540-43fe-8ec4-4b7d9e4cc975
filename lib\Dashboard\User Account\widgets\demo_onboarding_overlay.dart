import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../screens/features_screen.dart';

/// Onboarding overlay that appears when users first enter demo mode
class DemoOnboardingOverlay extends StatefulWidget {
  final VoidCallback onComplete;

  const DemoOnboardingOverlay({
    super.key,
    required this.onComplete,
  });

  @override
  State<DemoOnboardingOverlay> createState() => _DemoOnboardingOverlayState();
}

class _DemoOnboardingOverlayState extends State<DemoOnboardingOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentStep = 0;
  final int _totalSteps = 4;

  final List<OnboardingStep> _steps = [
    const OnboardingStep(
      icon: Icons.preview,
      title: 'Welcome to Demo Mode!',
      description: 'You\'re now exploring Cattle Manager with realistic sample data. Everything you see represents what the app can do for your farm.',
      color: Colors.blue,
    ),
    const OnboardingStep(
      icon: Icons.visibility,
      title: 'Explore All Features',
      description: 'Browse through cattle records, health data, breeding information, and reports. All screens are fully functional for viewing.',
      color: Colors.green,
    ),
    const OnboardingStep(
      icon: Icons.lock_outline,
      title: 'Some Features Are Locked',
      description: 'Adding, editing, and deleting data is restricted in demo mode. You\'ll see helpful messages when you try these actions.',
      color: Colors.orange,
    ),
    const OnboardingStep(
      icon: Icons.account_circle,
      title: 'Ready to Get Started?',
      description: 'When you\'re ready to manage your real cattle data, tap "Exit Demo" in the banner above or "Sign Up" to create your account.',
      color: Colors.purple,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _animationController.reset();
      _animationController.forward();
    } else {
      _complete();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _complete() {
    widget.onComplete();
  }

  @override
  Widget build(BuildContext context) {
    final currentStep = _steps[_currentStep];
    
    return Material(
      color: Colors.black.withValues(alpha: 0.7),
      child: SafeArea(
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(24),
            constraints: const BoxConstraints(maxWidth: 400),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Progress indicator
                      Row(
                        children: List.generate(_totalSteps, (index) {
                          return Expanded(
                            child: Container(
                              height: 4,
                              margin: EdgeInsets.only(
                                right: index < _totalSteps - 1 ? 8 : 0,
                              ),
                              decoration: BoxDecoration(
                                color: index <= _currentStep
                                    ? currentStep.color
                                    : Colors.grey[300],
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          );
                        }),
                      ),
                      
                      const SizedBox(height: 32),
                      
                      // Icon
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: currentStep.color.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          currentStep.icon,
                          size: 48,
                          color: currentStep.color,
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Title
                      Text(
                        currentStep.title,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Description
                      Text(
                        currentStep.description,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 32),
                      
                      // Navigation buttons
                      Row(
                        children: [
                          if (_currentStep > 0)
                            Expanded(
                              child: OutlinedButton(
                                onPressed: _previousStep,
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text('Previous'),
                              ),
                            ),
                          
                          if (_currentStep > 0) const SizedBox(width: 16),
                          
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _nextStep,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: currentStep.color,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(
                                _currentStep < _totalSteps - 1 ? 'Next' : 'Start Exploring',
                                style: const TextStyle(fontWeight: FontWeight.w600),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Additional buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const FeaturesScreen(),
                                ),
                              );
                            },
                            child: Text(
                              'View All Features',
                              style: TextStyle(
                                color: Colors.orange[700],
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          TextButton(
                            onPressed: _complete,
                            child: Text(
                              'Skip Tutorial',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Data class for onboarding steps
class OnboardingStep {
  final IconData icon;
  final String title;
  final String description;
  final Color color;

  const OnboardingStep({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
  });
}
