import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import '../../../services/logging_service.dart';
import '../../../config/api_config.dart';
import 'package:get_it/get_it.dart';
import '../models/weight_record_isar.dart';
import 'weight_repository.dart';

/// Bidirectional sync service for Weight module
/// Handles synchronization between local Isar database and external API
class WeightSyncService {
  final WeightRepository _weightRepository = GetIt.instance<WeightRepository>();
  
  // Sync-related constants
  static const String _lastSyncKey = 'last_weight_sync';
  final LoggingService _logger = LoggingService();

  /// Get last sync time for incremental sync
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSync = prefs.getString(_lastSyncKey);
    return lastSync != null ? DateTime.parse(lastSync) : null;
  }

  /// Set last sync time
  Future<void> setLastSyncTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, time.toIso8601String());
  }

  /// Get all weight records for sync
  Future<List<WeightRecordIsar>> getAllWeightRecords() async {
    final isar = GetIt.instance<Isar>();
    return await isar.weightRecordIsars.where().findAll();
  }

  /// Get weight records modified since last sync
  Future<List<WeightRecordIsar>> getModifiedWeightRecordsSince(DateTime? lastSync) async {
    if (lastSync == null) {
      return await getAllWeightRecords();
    }
    
    final isar = GetIt.instance<Isar>();
    return await isar.weightRecordIsars
        .filter()
        .updatedAtGreaterThan(lastSync)
        .findAll();
  }

  /// Convert weight record to sync-friendly map
  Map<String, dynamic> _weightRecordToSyncMap(WeightRecordIsar record) {
    return {
      'id': record.businessId,
      'cattleId': record.cattle.value?.businessId,
      'weight': record.weight,
      'measurementDate': record.measurementDate?.toIso8601String(),
      'measurementMethod': record.measurementMethod.toString().split('.').last,
      'measurementQuality': record.measurementQuality.toString().split('.').last,
      'measurementLocation': record.measurementLocation,
      'isEstimate': record.isEstimate,
      'confidenceLevel': record.confidenceLevel,
      'notes': record.notes,
      'measuredBy': record.measuredBy,
      'weightUnit': record.weightUnit.toString().split('.').last,
      'createdAt': record.createdAt?.toIso8601String(),
      'updatedAt': record.updatedAt?.toIso8601String(),
    };
  }

  /// Convert sync map to weight record
  WeightRecordIsar _weightRecordFromSyncMap(Map<String, dynamic> map) {
    final record = WeightRecordIsar()
      ..businessId = map['id'] as String?
      ..weight = map['weight'] != null ? (map['weight'] as num).toDouble() : 0.0
      ..measurementLocation = map['measurementLocation'] as String?
      ..isEstimate = map['isEstimate'] as bool?
      ..confidenceLevel = map['confidenceLevel'] != null ? (map['confidenceLevel'] as num).toDouble() : null
      ..notes = map['notes'] as String?
      ..measuredBy = map['measuredBy'] as String?;

    // Handle dates
    if (map['measurementDate'] != null) {
      record.measurementDate = DateTime.parse(map['measurementDate']);
    }
    if (map['createdAt'] != null) {
      record.createdAt = DateTime.parse(map['createdAt']);
    }
    if (map['updatedAt'] != null) {
      record.updatedAt = DateTime.parse(map['updatedAt']);
    }

    // Handle enums
    if (map['measurementMethod'] != null) {
      record.measurementMethod = MeasurementMethod.values.firstWhere(
        (e) => e.toString().split('.').last == map['measurementMethod'],
        orElse: () => MeasurementMethod.scale,
      );
    }

    if (map['measurementQuality'] != null) {
      record.measurementQuality = MeasurementQuality.values.firstWhere(
        (e) => e.toString().split('.').last == map['measurementQuality'],
        orElse: () => MeasurementQuality.good,
      );
    }

    if (map['weightUnit'] != null) {
      record.weightUnit = WeightUnit.values.firstWhere(
        (e) => e.toString().split('.').last == map['weightUnit'],
        orElse: () => WeightUnit.kg,
      );
    }

    return record;
  }

  /// Bidirectional sync with external API
  Future<bool> syncData() async {
    try {
      // Check if API sync is enabled
      if (!ApiConfig.isApiSyncAvailable) {
        _logger.info('Weight sync skipped - API sync disabled (local-only mode)');
        return true; // Return success for local-only mode
      }

      final lastSync = await getLastSyncTime();
      final localRecords = await getModifiedWeightRecordsSince(lastSync);

      // Prepare data for API
      final syncData = {
        'lastSync': lastSync?.toIso8601String(),
        'records': localRecords.map((r) => _weightRecordToSyncMap(r)).toList(),
      };

      final response = await http.post(
        Uri.parse(ApiConfig.weightSync),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode(syncData),
      ).timeout(ApiConfig.syncTimeout);

      if (response.statusCode == 200) {
        if (!response.body.startsWith('{')) {
          _logger.error(
              'Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          return false;
        }

        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (!responseData.containsKey('records')) {
          _logger.error('Invalid response format: Missing records field');
          return false;
        }

        // Process server records and handle conflicts
        final List<dynamic> serverRecords = responseData['records'];
        await _processServerWeightRecords(serverRecords);

        await setLastSyncTime(DateTime.now());
        _logger.info('Weight records synchronized successfully');
        return true;
      } else {
        _logger.error('Failed to sync weight records: $response.statusCode');
        return false;
      }
    } catch (e) {
      _logger.error('Error syncing weight records: $e');
      return false;
    }
  }

  /// Process server records and handle conflicts
  Future<void> _processServerWeightRecords(List<dynamic> serverRecords) async {
    for (final recordData in serverRecords) {
      try {
        final serverRecord = _weightRecordFromSyncMap(recordData);
        final isar = GetIt.instance<Isar>();
        final existingRecord = await isar.weightRecordIsars
            .filter()
            .businessIdEqualTo(serverRecord.businessId)
            .findFirst();

        if (existingRecord == null) {
          // New record from server
          await _weightRepository.saveRecord(serverRecord);
        } else {
          // Handle conflict resolution - server wins for now
          final resolvedRecord = _resolveWeightConflict(existingRecord, serverRecord);
          await _weightRepository.saveRecord(resolvedRecord);
        }
      } catch (e) {
        _logger.error('Error processing server weight record: $e');
      }
    }
  }

  /// Simple conflict resolution - server wins
  /// In a more sophisticated implementation, this could use timestamps,
  /// user preferences, or merge strategies
  WeightRecordIsar _resolveWeightConflict(WeightRecordIsar local, WeightRecordIsar server) {
    // For now, server record wins in conflicts
    // Keep the local Isar ID but use server data
    server.id = local.id;
    return server;
  }
}
