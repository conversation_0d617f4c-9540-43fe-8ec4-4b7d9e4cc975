import 'dart:io';

import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../models/report_models.dart';

/// PDF Export Service
/// 
/// Generates professional PDF reports with modern layout, charts, and data tables.
/// Supports farm branding, consistent styling, and mobile-friendly generation.
class PdfExportService {
  static final PdfExportService _instance = PdfExportService._internal();
  factory PdfExportService() => _instance;
  PdfExportService._internal();

  /// Generate PDF report
  Future<File> generatePDF(ReportData reportData, ExportConfig config) async {
    final pdf = pw.Document();
    
    // Load fonts for better typography
    final regularFont = await _loadFont('assets/fonts/Roboto-Regular.ttf');
    final boldFont = await _loadFont('assets/fonts/Roboto-Bold.ttf');
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        theme: pw.ThemeData.withFont(
          base: regularFont,
          bold: boldFont,
        ),
        build: (context) => _buildPdfContent(reportData, config),
        header: (context) => _buildHeader(reportData, context),
        footer: (context) => _buildFooter(context),
      ),
    );
    
    return _savePdfFile(pdf, reportData.title);
  }

  /// Build PDF content sections
  List<pw.Widget> _buildPdfContent(ReportData reportData, ExportConfig config) {
    final content = <pw.Widget>[];
    
    // Executive Summary
    content.add(_buildExecutiveSummary(reportData));
    content.add(pw.SizedBox(height: 24));
    
    // Metrics Section
    if (config.includeMetrics && reportData.metrics.isNotEmpty) {
      content.add(_buildMetricsSection(reportData));
      content.add(pw.SizedBox(height: 24));
    }
    
    // Charts Section
    if (config.includeCharts && reportData.chartData.isNotEmpty) {
      content.add(_buildChartsSection(reportData));
      content.add(pw.SizedBox(height: 24));
    }
    
    // Data Tables Section
    if (config.includeTableData && reportData.tableData.isNotEmpty) {
      content.add(_buildDataTablesSection(reportData));
      content.add(pw.SizedBox(height: 24));
    }
    
    // Insights Section
    if (config.includeInsights && reportData.insights.isNotEmpty) {
      content.add(_buildInsightsSection(reportData));
    }
    
    return content;
  }

  /// Build modern header with farm branding
  pw.Widget _buildHeader(ReportData reportData, pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 16),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: PdfColors.grey300,
            width: 1,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'Cattle Manager Farm',
                style: pw.TextStyle(
                  fontSize: 20,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.Text(
                reportData.title,
                style: const pw.TextStyle(
                  fontSize: 16,
                  color: PdfColors.grey700,
                ),
              ),
              pw.Text(
                'Generated: ${_formatDateTime(reportData.generated)}',
                style: const pw.TextStyle(
                  fontSize: 10,
                  color: PdfColors.grey600,
                ),
              ),
            ],
          ),
          _buildFarmLogo(),
        ],
      ),
    );
  }

  /// Build footer with page numbers
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 16),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(
            color: PdfColors.grey300,
            width: 1,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'Cattle Manager App - Farm Reports',
            style: const pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.Text(
            'Page ${context.pageNumber} of ${context.pagesCount}',
            style: const pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build executive summary
  pw.Widget _buildExecutiveSummary(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Executive Summary'),
        pw.SizedBox(height: 12),
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            color: PdfColors.blue50,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.blue200),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                reportData.subtitle,
                style: const pw.TextStyle(
                  fontSize: 14,
                  color: PdfColors.grey700,
                ),
              ),
              if (reportData.startDate != null && reportData.endDate != null) ...[
                pw.SizedBox(height: 8),
                pw.Text(
                  'Report Period: ${reportData.dateRangeString}',
                  style: const pw.TextStyle(
                    fontSize: 12,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
              pw.SizedBox(height: 12),
              pw.Text(
                'Key Highlights:',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 4),
              ...reportData.insights.take(3).map((insight) => pw.Padding(
                padding: const pw.EdgeInsets.only(left: 8, bottom: 2),
                child: pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('• ', style: const pw.TextStyle(color: PdfColors.blue600)),
                    pw.Expanded(
                      child: pw.Text(
                        insight,
                        style: const pw.TextStyle(
                          fontSize: 11,
                          color: PdfColors.grey700,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ),
      ],
    );
  }

  /// Build metrics section
  pw.Widget _buildMetricsSection(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Key Metrics'),
        pw.SizedBox(height: 12),
        pw.Wrap(
          spacing: 16,
          runSpacing: 16,
          children: reportData.metrics.values.map((metric) => 
            _buildMetricCard(metric)
          ).toList(),
        ),
      ],
    );
  }

  /// Build individual metric card
  pw.Widget _buildMetricCard(ReportMetric metric) {
    return pw.Container(
      width: 180,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
        boxShadow: const [
          pw.BoxShadow(
            color: PdfColors.grey200,
            offset: PdfPoint(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            metric.title,
            style: const pw.TextStyle(
              fontSize: 12,
              color: PdfColors.grey600,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            metric.value,
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
          if (metric.subtitle.isNotEmpty) ...[
            pw.SizedBox(height: 4),
            pw.Text(
              metric.subtitle,
              style: const pw.TextStyle(
                fontSize: 10,
                color: PdfColors.grey500,
              ),
            ),
          ],
          if (metric.badge != null) ...[
            pw.SizedBox(height: 8),
            pw.Container(
              padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue100,
                borderRadius: pw.BorderRadius.circular(4),
              ),
              child: pw.Text(
                metric.badge!,
                style: pw.TextStyle(
                  fontSize: 9,
                  color: PdfColors.blue800,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build charts section
  pw.Widget _buildChartsSection(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Data Visualization'),
        pw.SizedBox(height: 12),
        _buildSimpleChart(reportData.chartData),
      ],
    );
  }

  /// Build simple chart for PDF (since complex charts are difficult in PDF)
  pw.Widget _buildSimpleChart(List<ChartPoint> data) {
    if (data.isEmpty) {
      return pw.Container(
        height: 200,
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Center(
          child: pw.Text(
            'No chart data available',
            style: const pw.TextStyle(color: PdfColors.grey500),
          ),
        ),
      );
    }

    final maxValue = data.map((d) => d.value).reduce((a, b) => a > b ? a : b);
    
    return pw.Container(
      height: 200,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'Data Trend',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 16),
          pw.Expanded(
            child: pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
              children: data.take(10).map((point) {
                final height = (point.value / maxValue) * 120;
                return pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Container(
                      width: 20,
                      height: height,
                      decoration: pw.BoxDecoration(
                        color: PdfColors.blue400,
                        borderRadius: pw.BorderRadius.circular(2),
                      ),
                    ),
                    pw.SizedBox(height: 4),
                    pw.Transform.rotate(
                      angle: -0.5,
                      child: pw.Text(
                        point.label.length > 6 
                          ? '${point.label.substring(0, 4)}..'
                          : point.label,
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Build data tables section
  pw.Widget _buildDataTablesSection(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Detailed Data'),
        pw.SizedBox(height: 12),
        _buildDataTable(reportData.tableData),
      ],
    );
  }

  /// Build data table
  pw.Widget _buildDataTable(List<Map<String, String>> tableData) {
    if (tableData.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(16),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Text(
          'No table data available',
          style: const pw.TextStyle(color: PdfColors.grey500),
        ),
      );
    }

    final headers = tableData.first.keys.toList();
    
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: headers.map((header) => pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              header,
              style: pw.TextStyle(
                fontWeight: pw.FontWeight.bold,
                fontSize: 10,
              ),
            ),
          )).toList(),
        ),
        // Data rows
        ...tableData.take(20).toList().asMap().entries.map((entry) {
          final index = entry.key;
          final row = entry.value;
          return pw.TableRow(
            decoration: pw.BoxDecoration(
              color: index % 2 == 0 ? PdfColors.white : PdfColors.grey50,
            ),
            children: headers.map((header) => pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                row[header] ?? '',
                style: const pw.TextStyle(fontSize: 9),
              ),
            )).toList(),
          );
        }),
      ],
    );
  }

  /// Build insights section
  pw.Widget _buildInsightsSection(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Insights & Recommendations'),
        pw.SizedBox(height: 12),
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            color: PdfColors.green50,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.green200),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: reportData.insights.map((insight) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 8),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('• ', style: const pw.TextStyle(color: PdfColors.green600)),
                  pw.Expanded(
                    child: pw.Text(
                      insight,
                      style: const pw.TextStyle(
                        fontSize: 11,
                        color: PdfColors.grey700,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  /// Build section title
  pw.Widget _buildSectionTitle(String title) {
    return pw.Text(
      title,
      style: pw.TextStyle(
        fontSize: 18,
        fontWeight: pw.FontWeight.bold,
        color: PdfColors.grey800,
      ),
    );
  }

  /// Build farm logo placeholder
  pw.Widget _buildFarmLogo() {
    return pw.Container(
      width: 60,
      height: 60,
      decoration: pw.BoxDecoration(
        color: PdfColors.blue600,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Center(
        child: pw.Text(
          'FARM',
          style: pw.TextStyle(
            color: PdfColors.white,
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
      ),
    );
  }

  // Helper methods

  Future<pw.Font> _loadFont(String path) async {
    try {
      final fontData = await rootBundle.load(path);
      return pw.Font.ttf(fontData);
    } catch (e) {
      // Fallback to default font if custom font fails to load
      return pw.Font.helvetica();
    }
  }

  Future<File> _savePdfFile(pw.Document pdf, String title) async {
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${_sanitizeFileName(title)}_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${directory.path}/$fileName');
    
    final bytes = await pdf.save();
    await file.writeAsBytes(bytes);
    
    return file;
  }

  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}