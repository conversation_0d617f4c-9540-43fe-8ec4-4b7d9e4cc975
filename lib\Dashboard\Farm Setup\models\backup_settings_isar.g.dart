// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backup_settings_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetBackupSettingsIsarCollection on Isar {
  IsarCollection<BackupSettingsIsar> get backupSettingsIsars =>
      this.collection();
}

const BackupSettingsIsarSchema = CollectionSchema(
  name: r'BackupSettingsIsar',
  id: -8422423144613465867,
  properties: {
    r'autoBackupEnabled': PropertySchema(
      id: 0,
      name: r'autoBackupEnabled',
      type: IsarType.bool,
    ),
    r'autoBackupFrequency': PropertySchema(
      id: 1,
      name: r'autoBackupFrequency',
      type: IsarType.long,
    ),
    r'backupFrequencyDays': PropertySchema(
      id: 2,
      name: r'backupFrequencyDays',
      type: IsarType.long,
    ),
    r'backupLocation': PropertySchema(
      id: 3,
      name: r'backupLocation',
      type: IsarType.string,
    ),
    r'businessId': PropertySchema(
      id: 4,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cloudBackupRetentionDays': PropertySchema(
      id: 5,
      name: r'cloudBackupRetentionDays',
      type: IsarType.long,
    ),
    r'createdAt': PropertySchema(
      id: 6,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'farmBusinessId': PropertySchema(
      id: 7,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'googleDriveAccountEmail': PropertySchema(
      id: 8,
      name: r'googleDriveAccountEmail',
      type: IsarType.string,
    ),
    r'googleDriveEnabled': PropertySchema(
      id: 9,
      name: r'googleDriveEnabled',
      type: IsarType.bool,
    ),
    r'googleDriveLastSync': PropertySchema(
      id: 10,
      name: r'googleDriveLastSync',
      type: IsarType.dateTime,
    ),
    r'isCloudStorageEnabled': PropertySchema(
      id: 11,
      name: r'isCloudStorageEnabled',
      type: IsarType.bool,
    ),
    r'isGoogleDriveSelected': PropertySchema(
      id: 12,
      name: r'isGoogleDriveSelected',
      type: IsarType.bool,
    ),
    r'lastBackupDate': PropertySchema(
      id: 13,
      name: r'lastBackupDate',
      type: IsarType.dateTime,
    ),
    r'lastCloudBackupDate': PropertySchema(
      id: 14,
      name: r'lastCloudBackupDate',
      type: IsarType.dateTime,
    ),
    r'maxCloudBackups': PropertySchema(
      id: 15,
      name: r'maxCloudBackups',
      type: IsarType.long,
    ),
    r'storageProvider': PropertySchema(
      id: 16,
      name: r'storageProvider',
      type: IsarType.string,
      enumMap: _BackupSettingsIsarstorageProviderEnumValueMap,
    ),
    r'storageProviderDisplayName': PropertySchema(
      id: 17,
      name: r'storageProviderDisplayName',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 18,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _backupSettingsIsarEstimateSize,
  serialize: _backupSettingsIsarSerialize,
  deserialize: _backupSettingsIsarDeserialize,
  deserializeProp: _backupSettingsIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _backupSettingsIsarGetId,
  getLinks: _backupSettingsIsarGetLinks,
  attach: _backupSettingsIsarAttach,
  version: '3.1.0+1',
);

int _backupSettingsIsarEstimateSize(
  BackupSettingsIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.backupLocation.length * 3;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.googleDriveAccountEmail;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.storageProvider.name.length * 3;
  bytesCount += 3 + object.storageProviderDisplayName.length * 3;
  return bytesCount;
}

void _backupSettingsIsarSerialize(
  BackupSettingsIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.autoBackupEnabled);
  writer.writeLong(offsets[1], object.autoBackupFrequency);
  writer.writeLong(offsets[2], object.backupFrequencyDays);
  writer.writeString(offsets[3], object.backupLocation);
  writer.writeString(offsets[4], object.businessId);
  writer.writeLong(offsets[5], object.cloudBackupRetentionDays);
  writer.writeDateTime(offsets[6], object.createdAt);
  writer.writeString(offsets[7], object.farmBusinessId);
  writer.writeString(offsets[8], object.googleDriveAccountEmail);
  writer.writeBool(offsets[9], object.googleDriveEnabled);
  writer.writeDateTime(offsets[10], object.googleDriveLastSync);
  writer.writeBool(offsets[11], object.isCloudStorageEnabled);
  writer.writeBool(offsets[12], object.isGoogleDriveSelected);
  writer.writeDateTime(offsets[13], object.lastBackupDate);
  writer.writeDateTime(offsets[14], object.lastCloudBackupDate);
  writer.writeLong(offsets[15], object.maxCloudBackups);
  writer.writeString(offsets[16], object.storageProvider.name);
  writer.writeString(offsets[17], object.storageProviderDisplayName);
  writer.writeDateTime(offsets[18], object.updatedAt);
}

BackupSettingsIsar _backupSettingsIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = BackupSettingsIsar();
  object.autoBackupEnabled = reader.readBool(offsets[0]);
  object.autoBackupFrequency = reader.readLong(offsets[1]);
  object.backupFrequencyDays = reader.readLongOrNull(offsets[2]);
  object.backupLocation = reader.readString(offsets[3]);
  object.businessId = reader.readStringOrNull(offsets[4]);
  object.cloudBackupRetentionDays = reader.readLong(offsets[5]);
  object.createdAt = reader.readDateTimeOrNull(offsets[6]);
  object.farmBusinessId = reader.readStringOrNull(offsets[7]);
  object.googleDriveAccountEmail = reader.readStringOrNull(offsets[8]);
  object.googleDriveEnabled = reader.readBool(offsets[9]);
  object.googleDriveLastSync = reader.readDateTimeOrNull(offsets[10]);
  object.id = id;
  object.lastBackupDate = reader.readDateTimeOrNull(offsets[13]);
  object.lastCloudBackupDate = reader.readDateTimeOrNull(offsets[14]);
  object.maxCloudBackups = reader.readLong(offsets[15]);
  object.storageProvider = _BackupSettingsIsarstorageProviderValueEnumMap[
          reader.readStringOrNull(offsets[16])] ??
      BackupStorageProvider.local;
  object.updatedAt = reader.readDateTimeOrNull(offsets[18]);
  return object;
}

P _backupSettingsIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBool(offset)) as P;
    case 1:
      return (reader.readLong(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readLong(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readBool(offset)) as P;
    case 10:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 11:
      return (reader.readBool(offset)) as P;
    case 12:
      return (reader.readBool(offset)) as P;
    case 13:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 14:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 15:
      return (reader.readLong(offset)) as P;
    case 16:
      return (_BackupSettingsIsarstorageProviderValueEnumMap[
              reader.readStringOrNull(offset)] ??
          BackupStorageProvider.local) as P;
    case 17:
      return (reader.readString(offset)) as P;
    case 18:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _BackupSettingsIsarstorageProviderEnumValueMap = {
  r'local': r'local',
  r'googleDrive': r'googleDrive',
};
const _BackupSettingsIsarstorageProviderValueEnumMap = {
  r'local': BackupStorageProvider.local,
  r'googleDrive': BackupStorageProvider.googleDrive,
};

Id _backupSettingsIsarGetId(BackupSettingsIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _backupSettingsIsarGetLinks(
    BackupSettingsIsar object) {
  return [];
}

void _backupSettingsIsarAttach(
    IsarCollection<dynamic> col, Id id, BackupSettingsIsar object) {
  object.id = id;
}

extension BackupSettingsIsarByIndex on IsarCollection<BackupSettingsIsar> {
  Future<BackupSettingsIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  BackupSettingsIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<BackupSettingsIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<BackupSettingsIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(BackupSettingsIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(BackupSettingsIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<BackupSettingsIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<BackupSettingsIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension BackupSettingsIsarQueryWhereSort
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QWhere> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension BackupSettingsIsarQueryWhere
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QWhereClause> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension BackupSettingsIsarQueryFilter
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QFilterCondition> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoBackupEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoBackupFrequency',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'autoBackupFrequency',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'autoBackupFrequency',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'autoBackupFrequency',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'backupFrequencyDays',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'backupFrequencyDays',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupFrequencyDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'backupFrequencyDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'backupFrequencyDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'backupFrequencyDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'backupLocation',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'backupLocation',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupLocation',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'backupLocation',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudBackupRetentionDaysEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudBackupRetentionDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudBackupRetentionDaysGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cloudBackupRetentionDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudBackupRetentionDaysLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cloudBackupRetentionDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudBackupRetentionDaysBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cloudBackupRetentionDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'googleDriveAccountEmail',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'googleDriveAccountEmail',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'googleDriveAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'googleDriveAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'googleDriveAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'googleDriveAccountEmail',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'googleDriveAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'googleDriveAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'googleDriveAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'googleDriveAccountEmail',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'googleDriveAccountEmail',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveAccountEmailIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'googleDriveAccountEmail',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'googleDriveEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveLastSyncIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'googleDriveLastSync',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveLastSyncIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'googleDriveLastSync',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveLastSyncEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'googleDriveLastSync',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveLastSyncGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'googleDriveLastSync',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveLastSyncLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'googleDriveLastSync',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      googleDriveLastSyncBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'googleDriveLastSync',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      isCloudStorageEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isCloudStorageEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      isGoogleDriveSelectedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isGoogleDriveSelected',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastBackupDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastCloudBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastCloudBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastCloudBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastCloudBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastCloudBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastCloudBackupDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'maxCloudBackups',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'maxCloudBackups',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'maxCloudBackups',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'maxCloudBackups',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderEqualTo(
    BackupStorageProvider value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'storageProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderGreaterThan(
    BackupStorageProvider value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'storageProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderLessThan(
    BackupStorageProvider value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'storageProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderBetween(
    BackupStorageProvider lower,
    BackupStorageProvider upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'storageProvider',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'storageProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'storageProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'storageProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'storageProvider',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'storageProvider',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'storageProvider',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'storageProviderDisplayName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'storageProviderDisplayName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'storageProviderDisplayName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'storageProviderDisplayName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'storageProviderDisplayName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'storageProviderDisplayName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'storageProviderDisplayName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'storageProviderDisplayName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'storageProviderDisplayName',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      storageProviderDisplayNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'storageProviderDisplayName',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension BackupSettingsIsarQueryObject
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QFilterCondition> {}

extension BackupSettingsIsarQueryLinks
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QFilterCondition> {}

extension BackupSettingsIsarQuerySortBy
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QSortBy> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupFrequencyDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupFrequencyDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudBackupRetentionDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupRetentionDays', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudBackupRetentionDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupRetentionDays', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByGoogleDriveAccountEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveAccountEmail', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByGoogleDriveAccountEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveAccountEmail', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByGoogleDriveEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByGoogleDriveEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByGoogleDriveLastSync() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveLastSync', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByGoogleDriveLastSyncDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveLastSync', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByIsCloudStorageEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudStorageEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByIsCloudStorageEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudStorageEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByIsGoogleDriveSelected() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isGoogleDriveSelected', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByIsGoogleDriveSelectedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isGoogleDriveSelected', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastCloudBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastCloudBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByMaxCloudBackups() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByMaxCloudBackupsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByStorageProvider() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProvider', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByStorageProviderDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProvider', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByStorageProviderDisplayName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProviderDisplayName', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByStorageProviderDisplayNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProviderDisplayName', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension BackupSettingsIsarQuerySortThenBy
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QSortThenBy> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupFrequencyDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupFrequencyDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudBackupRetentionDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupRetentionDays', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudBackupRetentionDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupRetentionDays', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByGoogleDriveAccountEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveAccountEmail', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByGoogleDriveAccountEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveAccountEmail', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByGoogleDriveEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByGoogleDriveEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByGoogleDriveLastSync() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveLastSync', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByGoogleDriveLastSyncDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'googleDriveLastSync', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByIsCloudStorageEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudStorageEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByIsCloudStorageEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudStorageEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByIsGoogleDriveSelected() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isGoogleDriveSelected', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByIsGoogleDriveSelectedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isGoogleDriveSelected', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastCloudBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastCloudBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByMaxCloudBackups() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByMaxCloudBackupsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByStorageProvider() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProvider', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByStorageProviderDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProvider', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByStorageProviderDisplayName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProviderDisplayName', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByStorageProviderDisplayNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storageProviderDisplayName', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension BackupSettingsIsarQueryWhereDistinct
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoBackupEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByAutoBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoBackupFrequency');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByBackupFrequencyDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'backupFrequencyDays');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByBackupLocation({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'backupLocation',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByCloudBackupRetentionDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cloudBackupRetentionDays');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByGoogleDriveAccountEmail({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'googleDriveAccountEmail',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByGoogleDriveEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'googleDriveEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByGoogleDriveLastSync() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'googleDriveLastSync');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByIsCloudStorageEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isCloudStorageEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByIsGoogleDriveSelected() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isGoogleDriveSelected');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByLastCloudBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastCloudBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByMaxCloudBackups() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'maxCloudBackups');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByStorageProvider({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'storageProvider',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByStorageProviderDisplayName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'storageProviderDisplayName',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension BackupSettingsIsarQueryProperty
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QQueryProperty> {
  QueryBuilder<BackupSettingsIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<BackupSettingsIsar, bool, QQueryOperations>
      autoBackupEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoBackupEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, int, QQueryOperations>
      autoBackupFrequencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoBackupFrequency');
    });
  }

  QueryBuilder<BackupSettingsIsar, int?, QQueryOperations>
      backupFrequencyDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'backupFrequencyDays');
    });
  }

  QueryBuilder<BackupSettingsIsar, String, QQueryOperations>
      backupLocationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'backupLocation');
    });
  }

  QueryBuilder<BackupSettingsIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<BackupSettingsIsar, int, QQueryOperations>
      cloudBackupRetentionDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cloudBackupRetentionDays');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<BackupSettingsIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<BackupSettingsIsar, String?, QQueryOperations>
      googleDriveAccountEmailProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'googleDriveAccountEmail');
    });
  }

  QueryBuilder<BackupSettingsIsar, bool, QQueryOperations>
      googleDriveEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'googleDriveEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      googleDriveLastSyncProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'googleDriveLastSync');
    });
  }

  QueryBuilder<BackupSettingsIsar, bool, QQueryOperations>
      isCloudStorageEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isCloudStorageEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, bool, QQueryOperations>
      isGoogleDriveSelectedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isGoogleDriveSelected');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      lastBackupDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      lastCloudBackupDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastCloudBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, int, QQueryOperations>
      maxCloudBackupsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'maxCloudBackups');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupStorageProvider, QQueryOperations>
      storageProviderProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'storageProvider');
    });
  }

  QueryBuilder<BackupSettingsIsar, String, QQueryOperations>
      storageProviderDisplayNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'storageProviderDisplayName');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
