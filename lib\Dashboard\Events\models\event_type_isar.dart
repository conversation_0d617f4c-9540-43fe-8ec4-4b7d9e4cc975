import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/material.dart';
import 'event_enums.dart';

part 'event_type_isar.g.dart';

/// Event Type model for Isar database
@collection
class EventTypeIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Name of the event type
  String? name;

  /// Description of the event type
  String? description;

  /// Category this event type belongs to
  @enumerated
  EventCategory category = EventCategory.other;

  /// Icon name for display
  String? iconName;

  /// Color hex code for display
  String? colorHex;

  /// Color property for backward compatibility
  @ignore
  Color get color {
    if (colorHex == null) return Colors.blue;
    try {
      return Color(int.parse(colorHex!.replaceFirst('#', '0xFF')));
    } catch (_) {
      return Colors.blue;
    }
  }
  set color(Color value) {
    colorHex = '#${value.toARGB32().toRadixString(16).padLeft(8, '0').substring(2)}';
  }

  /// Color value getter for compatibility
  @ignore
  Color? get colorValue {
    if (colorHex == null) return null;
    try {
      return Color(int.parse(colorHex!.replaceFirst('#', '0xFF')));
    } catch (_) {
      return null;
    }
  }

  /// Default settings for events of this type
  @enumerated
  EventPriority defaultPriority = EventPriority.medium;
  List<int>? defaultReminderMinutes;
  int? defaultDurationMinutes;

  /// Automation settings
  bool? canBeAutoGenerated;
  String? autoGenerationRules;

  /// Recurrence settings
  bool? supportsRecurrence;
  @enumerated
  RecurrencePattern defaultRecurrencePattern = RecurrencePattern.daily;

  /// Whether this event type is active
  bool? isActive;

  /// Metadata
  DateTime? createdAt;
  DateTime? updatedAt;

  /// Default constructor
  EventTypeIsar();

  /// Generate a business ID for event types
  static String generateBusinessId() {
    return const Uuid().v4();
  }

  /// Factory constructor for creating a new event type
  factory EventTypeIsar.create({
    required String name,
    required EventCategory category,
    String? description,
    String? iconName,
    String? colorHex,
    EventPriority? defaultPriority,
    List<int>? defaultReminderMinutes,
    int? defaultDurationMinutes,
    bool? canBeAutoGenerated,
    String? autoGenerationRules,
    bool? supportsRecurrence,
    RecurrencePattern? defaultRecurrencePattern,
    bool? isActive,
  }) {
    final eventType = EventTypeIsar()
      ..businessId = generateBusinessId()
      ..name = name
      ..description = description
      ..category = category
      ..iconName = iconName ?? category.iconName
      ..colorHex = colorHex ?? category.colorHex
      ..defaultPriority = defaultPriority ?? EventPriority.medium
      ..defaultReminderMinutes = defaultReminderMinutes ?? [1440, 60] // 1 day and 1 hour
      ..defaultDurationMinutes = defaultDurationMinutes ?? 60
      ..canBeAutoGenerated = canBeAutoGenerated ?? false
      ..autoGenerationRules = autoGenerationRules
      ..supportsRecurrence = supportsRecurrence ?? false
      ..defaultRecurrencePattern = defaultRecurrencePattern ?? RecurrencePattern.daily
      ..isActive = isActive ?? true
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    return eventType;
  }

  /// Update the event type's updated timestamp
  void touch() {
    updatedAt = DateTime.now();
  }

  /// Create default event types for the system
  static List<EventTypeIsar> createDefaultEventTypes() {
    return [
      // Health event types
      EventTypeIsar.create(
        name: 'Vaccination',
        category: EventCategory.health,
        description: 'Routine vaccination for disease prevention',
        canBeAutoGenerated: true,
        supportsRecurrence: true,
        defaultRecurrencePattern: RecurrencePattern.yearly,
      ),
      EventTypeIsar.create(
        name: 'Health Check',
        category: EventCategory.health,
        description: 'General health examination',
        canBeAutoGenerated: true,
        supportsRecurrence: true,
        defaultRecurrencePattern: RecurrencePattern.monthly,
      ),
      EventTypeIsar.create(
        name: 'Treatment',
        category: EventCategory.health,
        description: 'Medical treatment for illness or injury',
        canBeAutoGenerated: true,
        defaultPriority: EventPriority.high,
      ),
      EventTypeIsar.create(
        name: 'Deworming',
        category: EventCategory.health,
        description: 'Parasite prevention treatment',
        canBeAutoGenerated: true,
        supportsRecurrence: true,
        defaultRecurrencePattern: RecurrencePattern.monthly,
      ),

      // Breeding event types
      EventTypeIsar.create(
        name: 'Breeding',
        category: EventCategory.breeding,
        description: 'Artificial insemination or natural breeding',
        canBeAutoGenerated: true,
        defaultPriority: EventPriority.high,
      ),
      EventTypeIsar.create(
        name: 'Pregnancy Check',
        category: EventCategory.breeding,
        description: 'Pregnancy confirmation examination',
        canBeAutoGenerated: true,
        defaultPriority: EventPriority.high,
      ),
      EventTypeIsar.create(
        name: 'Calving',
        category: EventCategory.breeding,
        description: 'Expected or actual calving date',
        canBeAutoGenerated: true,
        defaultPriority: EventPriority.critical,
      ),
      EventTypeIsar.create(
        name: 'Weaning',
        category: EventCategory.breeding,
        description: 'Calf weaning from mother',
        canBeAutoGenerated: true,
      ),

      // Feeding event types
      EventTypeIsar.create(
        name: 'Feed Change',
        category: EventCategory.feeding,
        description: 'Change in feed type or quantity',
        canBeAutoGenerated: true,
      ),
      EventTypeIsar.create(
        name: 'Weight Check',
        category: EventCategory.feeding,
        description: 'Regular weight monitoring',
        canBeAutoGenerated: true,
        supportsRecurrence: true,
        defaultRecurrencePattern: RecurrencePattern.weekly,
      ),
      EventTypeIsar.create(
        name: 'Nutrition Assessment',
        category: EventCategory.feeding,
        description: 'Evaluation of nutritional needs',
        supportsRecurrence: true,
        defaultRecurrencePattern: RecurrencePattern.monthly,
      ),

      // Management event types
      EventTypeIsar.create(
        name: 'Tagging',
        category: EventCategory.management,
        description: 'Ear tag application or replacement',
        canBeAutoGenerated: true,
      ),
      EventTypeIsar.create(
        name: 'Movement',
        category: EventCategory.management,
        description: 'Moving cattle between pastures or facilities',
        canBeAutoGenerated: true,
      ),
      EventTypeIsar.create(
        name: 'Hoof Trimming',
        category: EventCategory.management,
        description: 'Routine hoof care and trimming',
        supportsRecurrence: true,
        defaultRecurrencePattern: RecurrencePattern.monthly,
      ),

      // Financial event types
      EventTypeIsar.create(
        name: 'Purchase',
        category: EventCategory.financial,
        description: 'Cattle purchase transaction',
        canBeAutoGenerated: true,
        defaultPriority: EventPriority.high,
      ),
      EventTypeIsar.create(
        name: 'Sale',
        category: EventCategory.financial,
        description: 'Cattle sale transaction',
        canBeAutoGenerated: true,
        defaultPriority: EventPriority.high,
      ),

      // Maintenance event types
      EventTypeIsar.create(
        name: 'Equipment Maintenance',
        category: EventCategory.maintenance,
        description: 'Routine equipment maintenance',
        supportsRecurrence: true,
        defaultRecurrencePattern: RecurrencePattern.monthly,
      ),
      EventTypeIsar.create(
        name: 'Facility Repair',
        category: EventCategory.maintenance,
        description: 'Repair of farm facilities',
      ),
    ];
  }
}