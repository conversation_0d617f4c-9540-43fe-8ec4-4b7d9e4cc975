import 'dart:async';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../Dashboard/Farm Setup/services/farm_setup_repository.dart';
import '../Dashboard/Farm Setup/models/backup_settings_isar.dart';

import 'package:flutter/foundation.dart';
/// Service that automatically triggers cloud backups after significant data changes
class AutoBackupService {
  static final AutoBackupService _instance = AutoBackupService._internal();
  final Logger _logger = Logger('AutoBackupService');
  
  late final FarmSetupRepository _farmSetupRepository;
  Timer? _backupTimer;
  bool _isInitialized = false;
  bool _hasPendingChanges = false;
  
  // Configurable delays
  static const Duration _immediateBackupDelay = Duration(minutes: 5); // Wait 5 minutes after changes
  static const Duration _periodicBackupInterval = Duration(hours: 6); // Check every 6 hours
  
  factory AutoBackupService() {
    return _instance;
  }
  
  AutoBackupService._internal();
  
  /// Initialize the auto backup service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
      _isInitialized = true;
      
      // Start periodic backup check
      _startPeriodicBackupCheck();
      
      _logger.info('AutoBackupService initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing AutoBackupService: $e');
      rethrow;
    }
  }
  
  /// Trigger a backup after significant data changes
  void triggerBackupAfterDataChange({
    required String changeType,
    String? entityId,
    Map<String, dynamic>? metadata,
  }) {
    if (!_isInitialized) {
      _logger.warning('AutoBackupService not initialized, ignoring backup trigger');
      return;
    }
    
    debugPrint('🔄 [AUTO_BACKUP] Data change detected: $changeType (Entity: ${entityId ?? 'N/A'})');
    
    _hasPendingChanges = true;
    
    // Cancel existing timer and start a new one
    _backupTimer?.cancel();
    _backupTimer = Timer(_immediateBackupDelay, () {
      _performAutoBackupIfEnabled(changeType: changeType);
    });
    
    _logger.info('Scheduled auto backup in $_immediateBackupDelay.inMinutes minutes due to: $changeType');
  }
  
  /// Start periodic backup checks
  void _startPeriodicBackupCheck() {
    Timer.periodic(_periodicBackupInterval, (timer) {
      _performPeriodicBackupCheck();
    });
  }
  
  /// Perform periodic backup check
  Future<void> _performPeriodicBackupCheck() async {
    try {
      final settings = await _farmSetupRepository.getBackupSettings();
      
      if (!settings.autoBackupEnabled || !settings.isCloudStorageEnabled) {
        return; // Auto backup or cloud storage not enabled
      }
      
      // Check if it's time for a scheduled backup
      final now = DateTime.now();
      final lastBackup = settings.lastCloudBackupDate ?? settings.lastBackupDate;
      
      if (lastBackup == null) {
        debugPrint('🔄 [AUTO_BACKUP] No previous backup found, triggering initial backup');
        await _performAutoBackupIfEnabled(changeType: 'initial_backup');
        return;
      }
      
      final daysSinceLastBackup = now.difference(lastBackup).inDays;
      
      if (daysSinceLastBackup >= settings.autoBackupFrequency) {
        debugPrint('🔄 [AUTO_BACKUP] Scheduled backup due ($daysSinceLastBackup days since last backup)');
        await _performAutoBackupIfEnabled(changeType: 'scheduled_backup');
      }
    } catch (e) {
      _logger.warning('Error during periodic backup check: $e');
    }
  }
  
  /// Perform auto backup if enabled and authenticated
  Future<void> _performAutoBackupIfEnabled({required String changeType}) async {
    try {
      debugPrint('🔄 [AUTO_BACKUP] Checking if auto backup should be performed...');
      
      final settings = await _farmSetupRepository.getBackupSettings();
      
      // Check if auto backup is enabled
      if (!settings.autoBackupEnabled) {
        debugPrint('⚠️ [AUTO_BACKUP] Auto backup disabled, skipping');
        return;
      }
      
      // Check if cloud storage is enabled
      if (!settings.isCloudStorageEnabled) {
        debugPrint('⚠️ [AUTO_BACKUP] Cloud storage disabled, skipping auto backup');
        return;
      }
      
      // Check if authenticated
      final isAuthenticated = await _farmSetupRepository.isCloudAuthenticated(settings.storageProvider);
      if (!isAuthenticated) {
        debugPrint('⚠️ [AUTO_BACKUP] Not authenticated with cloud provider, skipping');
        return;
      }
      
      debugPrint('✅ [AUTO_BACKUP] Conditions met, creating automatic backup...');
      
      // Perform the backup
      final result = await _farmSetupRepository.createBackup();
      
      if (result.success) {
        debugPrint('✅ [AUTO_BACKUP] Automatic backup successful: $result.message');
        _hasPendingChanges = false;
        
        _logger.info('Automatic backup completed successfully due to: $changeType');
      } else {
        debugPrint('❌ [AUTO_BACKUP] Automatic backup failed: $result.message');
        _logger.warning('Automatic backup failed: $result.message');
      }
    } catch (e) {
      debugPrint('❌ [AUTO_BACKUP] Error during automatic backup: $e');
      _logger.severe('Error during automatic backup: $e');
    }
  }
  
  /// Get backup status information
  Future<AutoBackupStatus> getBackupStatus() async {
    try {
      final settings = await _farmSetupRepository.getBackupSettings();
      final isAuthenticated = settings.isCloudStorageEnabled 
          ? await _farmSetupRepository.isCloudAuthenticated(settings.storageProvider)
          : false;
      
      return AutoBackupStatus(
        isEnabled: settings.autoBackupEnabled,
        isCloudStorageEnabled: settings.isCloudStorageEnabled,
        isAuthenticated: isAuthenticated,
        storageProvider: settings.storageProvider,
        lastBackupTime: settings.lastCloudBackupDate ?? settings.lastBackupDate,
        hasPendingChanges: _hasPendingChanges,
        nextScheduledBackup: _calculateNextScheduledBackup(settings),
      );
    } catch (e) {
      _logger.warning('Error getting backup status: $e');
      return const AutoBackupStatus.error();
    }
  }
  
  DateTime? _calculateNextScheduledBackup(BackupSettingsIsar settings) {
    final lastBackup = settings.lastCloudBackupDate ?? settings.lastBackupDate;
    if (lastBackup == null) return null;
    
    return lastBackup.add(Duration(days: settings.autoBackupFrequency));
  }
  
  /// Dispose resources
  void dispose() {
    _backupTimer?.cancel();
    _isInitialized = false;
    _logger.info('AutoBackupService disposed');
  }
}

/// Status information for auto backup service
class AutoBackupStatus {
  final bool isEnabled;
  final bool isCloudStorageEnabled;
  final bool isAuthenticated;
  final BackupStorageProvider storageProvider;
  final DateTime? lastBackupTime;
  final bool hasPendingChanges;
  final DateTime? nextScheduledBackup;
  final bool hasError;
  
  const AutoBackupStatus({
    required this.isEnabled,
    required this.isCloudStorageEnabled,
    required this.isAuthenticated,
    required this.storageProvider,
    this.lastBackupTime,
    this.hasPendingChanges = false,
    this.nextScheduledBackup,
    this.hasError = false,
  });
  
  const AutoBackupStatus.error()
      : isEnabled = false,
        isCloudStorageEnabled = false,
        isAuthenticated = false,
        storageProvider = BackupStorageProvider.local,
        lastBackupTime = null,
        hasPendingChanges = false,
        nextScheduledBackup = null,
        hasError = true;
  
  bool get isFullyConfigured => isEnabled && isCloudStorageEnabled && isAuthenticated;
  
  String get statusMessage {
    if (hasError) return 'Error checking backup status';
    if (!isEnabled) return 'Auto backup disabled';
    if (!isCloudStorageEnabled) return 'Cloud storage not enabled';
    if (!isAuthenticated) return 'Authentication required';
    if (hasPendingChanges) return 'Backup pending';
    return 'Auto backup active';
  }
}

/// Data change types that trigger automatic backups
class BackupTriggerType {
  static const String cattleAdded = 'cattle_added';
  static const String cattleUpdated = 'cattle_updated';
  static const String cattleDeleted = 'cattle_deleted';
  static const String healthRecordAdded = 'health_record_added';
  static const String breedingRecordAdded = 'breeding_record_added';
  static const String milkRecordAdded = 'milk_record_added';
  static const String weightRecordAdded = 'weight_record_added';
  static const String transactionAdded = 'transaction_added';
  static const String eventAdded = 'event_added';
  static const String farmSettingsUpdated = 'farm_settings_updated';
  static const String initialBackup = 'initial_backup';
  static const String scheduledBackup = 'scheduled_backup';
  static const String manualBackup = 'manual_backup';
}
