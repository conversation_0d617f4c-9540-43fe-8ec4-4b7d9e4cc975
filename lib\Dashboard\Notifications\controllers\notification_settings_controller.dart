import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import '../services/notification_service.dart';
import '../services/notification_settings_repository.dart';
import '../services/push_notification_service.dart';
import '../models/notification_settings_isar.dart';
import '../models/notification_priority.dart';

/// Controller for notification settings with Provider state management
class NotificationSettingsController extends ChangeNotifier {
  final NotificationService _service = GetIt.instance<NotificationService>();
  final NotificationSettingsRepository _repository = GetIt.instance<NotificationSettingsRepository>();
  final PushNotificationService _pushService = GetIt.instance<PushNotificationService>();
  
  /// Current settings
  NotificationSettingsIsar? _settings;
  
  /// Loading state
  bool _isLoading = false;
  
  /// Error message
  String? _errorMessage;
  
  /// Constructor
  NotificationSettingsController() {
    // Initial load
    loadSettings();
  }
  
  /// Get settings
  NotificationSettingsIsar? get settings => _settings;
  
  /// Get loading state
  bool get isLoading => _isLoading;
  
  /// Get error message
  String? get errorMessage => _errorMessage;
  
  /// Load settings
  Future<void> loadSettings() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      _settings = await _service.getSettings();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load settings: $e';
      notifyListeners();
    }
  }
  
  /// Update global notification settings
  Future<void> updateGlobalSettings({
    bool? notificationsEnabled,
    bool? pushNotificationsEnabled,
    bool? inAppNotificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? smsNotificationsEnabled,
  }) async {
    if (_settings == null) return;
    
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      final updatedSettings = <String, dynamic>{};
      
      if (notificationsEnabled != null) {
        updatedSettings['notificationsEnabled'] = notificationsEnabled;
      }
      
      if (pushNotificationsEnabled != null) {
        updatedSettings['pushNotificationsEnabled'] = pushNotificationsEnabled;
        
        // If enabling push notifications, request permission
        if (pushNotificationsEnabled) {
          await _pushService.initialize();
        }
      }
      
      if (inAppNotificationsEnabled != null) {
        updatedSettings['inAppNotificationsEnabled'] = inAppNotificationsEnabled;
      }
      
      if (emailNotificationsEnabled != null) {
        updatedSettings['emailNotificationsEnabled'] = emailNotificationsEnabled;
      }
      
      if (smsNotificationsEnabled != null) {
        updatedSettings['smsNotificationsEnabled'] = smsNotificationsEnabled;
      }
      
      await _service.updateSettings(updatedSettings);
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update settings: $e';
      notifyListeners();
    }
  }
  
  /// Update category settings
  Future<void> updateCategorySettings(String category, {bool? enabled, NotificationPriority? priority}) async {
    if (_settings == null) return;
    
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _repository.updateCategorySettings(
        category,
        enabled: enabled,
        priority: priority,
      );
      
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update category settings: $e';
      notifyListeners();
    }
  }
  
  /// Update quiet hours settings
  Future<void> updateQuietHours({bool? enabled, int? startHour, int? endHour}) async {
    if (_settings == null) return;
    
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _repository.updateQuietHours(
        enabled: enabled,
        startHour: startHour,
        endHour: endHour,
      );
      
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update quiet hours: $e';
      notifyListeners();
    }
  }
  
  /// Update emergency override settings
  Future<void> updateEmergencyOverride({bool? enabled, List<String>? keywords}) async {
    if (_settings == null) return;
    
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _repository.updateEmergencyOverride(
        enabled: enabled,
        keywords: keywords,
      );
      
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update emergency override: $e';
      notifyListeners();
    }
  }
  
  /// Update retention settings
  Future<void> updateRetentionSettings({
    int? maxNotificationsToKeep,
    int? autoDeleteAfterDays,
    bool? autoDeleteReadNotifications,
    int? autoDeleteReadAfterDays,
  }) async {
    if (_settings == null) return;
    
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _repository.updateRetentionSettings(
        maxNotificationsToKeep: maxNotificationsToKeep,
        autoDeleteAfterDays: autoDeleteAfterDays,
        autoDeleteReadNotifications: autoDeleteReadNotifications,
        autoDeleteReadAfterDays: autoDeleteReadAfterDays,
      );
      
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update retention settings: $e';
      notifyListeners();
    }
  }
  
  /// Update digest settings
  Future<void> updateDigestSettings({required String frequency, int? time}) async {
    if (_settings == null) return;
    
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _repository.updateDigestSettings(
        frequency: frequency,
        time: time,
      );
      
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update digest settings: $e';
      notifyListeners();
    }
  }
  
  /// Reset settings to defaults
  Future<void> resetToDefaults() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _repository.resetToDefaults();
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to reset settings: $e';
      notifyListeners();
    }
  }
  
  /// Subscribe to FCM topic
  Future<void> subscribeToTopic(String topic) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _pushService.subscribeToTopic(topic);
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to subscribe to topic: $e';
      notifyListeners();
    }
  }
  
  /// Unsubscribe from FCM topic
  Future<void> unsubscribeFromTopic(String topic) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      await _pushService.unsubscribeFromTopic(topic);
      await loadSettings();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to unsubscribe from topic: $e';
      notifyListeners();
    }
  }
  
  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Alias methods for backward compatibility

  /// Update global setting (alias for updateGlobalSettings)
  Future<void> updateGlobalSetting(String key, dynamic value) async {
    switch (key) {
      case 'notificationsEnabled':
        await updateGlobalSettings(notificationsEnabled: value as bool);
        break;
      case 'pushNotificationsEnabled':
        await updateGlobalSettings(pushNotificationsEnabled: value as bool);
        break;
      case 'inAppNotificationsEnabled':
        await updateGlobalSettings(inAppNotificationsEnabled: value as bool);
        break;
      case 'emailNotificationsEnabled':
        await updateGlobalSettings(emailNotificationsEnabled: value as bool);
        break;
      case 'smsNotificationsEnabled':
        await updateGlobalSettings(smsNotificationsEnabled: value as bool);
        break;
    }
  }

  /// Update category setting (alias for updateCategorySettings)
  Future<void> updateCategorySetting(String category, String key, dynamic value) async {
    switch (key) {
      case 'enabled':
        await updateCategorySettings(category, enabled: value as bool);
        break;
      case 'priority':
        await updateCategorySettings(category, priority: value as NotificationPriority);
        break;
    }
  }

  /// Update retention setting (alias for updateRetentionSettings)
  Future<void> updateRetentionSetting(String key, dynamic value) async {
    switch (key) {
      case 'maxNotificationsToKeep':
        await updateRetentionSettings(maxNotificationsToKeep: value as int);
        break;
      case 'autoDeleteAfterDays':
        await updateRetentionSettings(autoDeleteAfterDays: value as int);
        break;
      case 'autoDeleteReadNotifications':
        await updateRetentionSettings(autoDeleteReadNotifications: value as bool);
        break;
      case 'autoDeleteReadAfterDays':
        await updateRetentionSettings(autoDeleteReadAfterDays: value as int);
        break;
    }
  }
}