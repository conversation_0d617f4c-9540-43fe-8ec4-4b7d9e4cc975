import 'dart:io';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import '../models/report_models.dart';

/// Excel Export Service
/// 
/// Generates Excel spreadsheets with properly formatted data, calculations,
/// and multiple sheets for comprehensive farm reports.
class ExcelExportService {
  static final ExcelExportService _instance = ExcelExportService._internal();
  factory ExcelExportService() => _instance;
  ExcelExportService._internal();

  /// Generate Excel report
  Future<File> generateExcel(ReportData reportData, ExportConfig config) async {
    final excel = Excel.createExcel();
    
    // Remove default sheet
    excel.delete('Sheet1');
    
    // Create summary sheet
    _createSummarySheet(excel, reportData, config);
    
    // Create data sheet if table data exists
    if (config.includeTableData && reportData.tableData.isNotEmpty) {
      _createDataSheet(excel, reportData);
    }
    
    // Create metrics sheet if metrics exist
    if (config.includeMetrics && reportData.metrics.isNotEmpty) {
      _createMetricsSheet(excel, reportData);
    }
    
    // Create chart data sheet if chart data exists
    if (config.includeCharts && reportData.chartData.isNotEmpty) {
      _createChartDataSheet(excel, reportData);
    }
    
    return _saveExcelFile(excel, reportData.title);
  }

  /// Create summary sheet
  void _createSummarySheet(Excel excel, ReportData reportData, ExportConfig config) {
    final sheet = excel['Summary'];
    
    int currentRow = 0;
    
    // Header section
    _addHeader(sheet, reportData, currentRow);
    currentRow += 4;
    
    // Executive summary
    _addExecutiveSummary(sheet, reportData, currentRow);
    currentRow += reportData.insights.length + 3;
    
    // Key metrics summary
    if (config.includeMetrics && reportData.metrics.isNotEmpty) {
      _addMetricsSummary(sheet, reportData, currentRow);
      currentRow += reportData.metrics.length + 3;
    }
    
    // Quick stats
    _addQuickStats(sheet, reportData, currentRow);
    
    // Apply formatting
    _formatSummarySheet(sheet);
  }

  /// Create detailed data sheet
  void _createDataSheet(Excel excel, ReportData reportData) {
    final sheet = excel['Detailed Data'];
    
    if (reportData.tableData.isEmpty) return;
    
    // Add headers
    final headers = reportData.tableData.first.keys.toList();
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    }
    
    // Add data rows
    for (int rowIndex = 0; rowIndex < reportData.tableData.length; rowIndex++) {
      final row = reportData.tableData[rowIndex];
      for (int colIndex = 0; colIndex < headers.length; colIndex++) {
        final header = headers[colIndex];
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex, 
          rowIndex: rowIndex + 1,
        ));
        
        final value = row[header] ?? '';
        
        // Try to parse as number for better Excel formatting
        final numValue = double.tryParse(value.replaceAll(RegExp(r'[^\d.-]'), ''));
        if (numValue != null) {
          cell.value = DoubleCellValue(numValue);
        } else {
          cell.value = TextCellValue(value);
        }
        
        // Alternate row colors
        if (rowIndex % 2 == 1) {
          // Alternate row styling removed due to type issues
        }
      }
    }
    
    // Auto-fit columns
    _autoFitColumns(sheet, headers.length);
  }

  /// Create metrics sheet
  void _createMetricsSheet(Excel excel, ReportData reportData) {
    final sheet = excel['Metrics'];
    
    // Headers
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Metric');
    sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Value');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Subtitle');
    sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('Badge/Status');
    sheet.cell(CellIndex.indexByString('E1')).value = TextCellValue('Insight');
    
    // Style headers
    for (int col = 0; col < 5; col++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0));
      cell.cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    }
    
    // Add metrics data
    int row = 1;
    reportData.metrics.forEach((key, metric) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(metric.title);
      
      // Try to parse value as number
      final numValue = double.tryParse(metric.value.replaceAll(RegExp(r'[^\d.-]'), ''));
      if (numValue != null) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
            .value = DoubleCellValue(numValue);
      } else {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
            .value = TextCellValue(metric.value);
      }
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(metric.subtitle);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = TextCellValue(metric.badge ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = TextCellValue(metric.insight);
      
      row++;
    });
    
    _autoFitColumns(sheet, 5);
  }

  /// Create chart data sheet
  void _createChartDataSheet(Excel excel, ReportData reportData) {
    final sheet = excel['Chart Data'];
    
    // Headers
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Label');
    sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Value');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Date');
    
    // Style headers
    for (int col = 0; col < 3; col++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0));
      cell.cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    }
    
    // Add chart data
    for (int i = 0; i < reportData.chartData.length; i++) {
      final point = reportData.chartData[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(point.label);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = DoubleCellValue(point.value);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(point.date?.toString() ?? '');
    }
    
    // Add basic chart if data is suitable
    if (reportData.chartData.length <= 20) {
      _addSimpleChart(sheet, reportData.chartData);
    }
    
    _autoFitColumns(sheet, 3);
  }

  /// Add header information
  void _addHeader(Sheet sheet, ReportData reportData, int startRow) {
    // Farm name
    final farmCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow));
    farmCell.value = TextCellValue('Cattle Manager Farm');
    farmCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
    );
    
    // Report title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 1));
    titleCell.value = TextCellValue(reportData.title);
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
    );
    
    // Generation date
    final dateCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 2));
    dateCell.value = TextCellValue('Generated: ${_formatDateTime(reportData.generated)}');
    dateCell.cellStyle = CellStyle(fontSize: 10);
    
    // Date range if available
    if (reportData.startDate != null && reportData.endDate != null) {
      final rangeCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 3));
      rangeCell.value = TextCellValue('Period: ${reportData.dateRangeString}');
      rangeCell.cellStyle = CellStyle(fontSize: 10);
    }
  }

  /// Add executive summary
  void _addExecutiveSummary(Sheet sheet, ReportData reportData, int startRow) {
    // Section title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow));
    titleCell.value = TextCellValue('Executive Summary');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    
    // Subtitle
    final subtitleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 1));
    subtitleCell.value = TextCellValue(reportData.subtitle);
    
    // Insights
    for (int i = 0; i < reportData.insights.length; i++) {
      final insightCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 2 + i));
      insightCell.value = TextCellValue('• ${reportData.insights[i]}');
    }
  }

  /// Add metrics summary
  void _addMetricsSummary(Sheet sheet, ReportData reportData, int startRow) {
    // Section title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow));
    titleCell.value = TextCellValue('Key Metrics');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    
    // Metrics
    int row = startRow + 1;
    reportData.metrics.forEach((key, metric) {
      final metricCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row));
      metricCell.value = TextCellValue('${metric.title}: ${metric.value}');
      
      if (metric.subtitle.isNotEmpty) {
        final subtitleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row));
        subtitleCell.value = TextCellValue(metric.subtitle);
        // Subtitle styling removed due to type issues
      }
      
      row++;
    });
  }

  /// Add quick statistics
  void _addQuickStats(Sheet sheet, ReportData reportData, int startRow) {
    // Section title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow));
    titleCell.value = TextCellValue('Quick Statistics');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    
    // Stats
    final stats = [
      'Total Metrics: ${reportData.metrics.length}',
      'Chart Data Points: ${reportData.chartData.length}',
      'Table Rows: ${reportData.tableData.length}',
      'Insights: ${reportData.insights.length}',
    ];
    
    for (int i = 0; i < stats.length; i++) {
      final statCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 1 + i));
      statCell.value = TextCellValue(stats[i]);
    }
  }

  /// Add simple chart (basic Excel chart)
  void _addSimpleChart(Sheet sheet, List<ChartPoint> chartData) {
    // This is a placeholder for chart creation
    // Excel package has limited chart support, so we'll add a note
    final chartCell = sheet.cell(CellIndex.indexByString('E1'));
    chartCell.value = TextCellValue('Chart: Use Excel\'s Insert > Chart feature with this data');
    chartCell.cellStyle = CellStyle(
      bold: true,
    );
  }

  /// Format summary sheet
  void _formatSummarySheet(Sheet sheet) {
    // Set column widths
    sheet.setColumnWidth(0, 30);
    sheet.setColumnWidth(1, 20);
    sheet.setColumnWidth(2, 15);
  }

  /// Auto-fit columns
  void _autoFitColumns(Sheet sheet, int columnCount) {
    for (int i = 0; i < columnCount; i++) {
      sheet.setColumnAutoFit(i);
    }
  }

  /// Save Excel file
  Future<File> _saveExcelFile(Excel excel, String title) async {
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${_sanitizeFileName(title)}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');
    
    final bytes = excel.save();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }
    
    return file;
  }

  /// Helper methods
  
  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// Excel color constants for consistent styling
class ExcelColor {
  static const String blue100 = 'FFE3F2FD';
  static const String blue800 = 'FF1565C0';
  static const String green100 = 'FFE8F5E8';
  static const String orange100 = 'FFFFF3E0';
  static const String yellow100 = 'FFFFFDE7';
  static const String gray100 = 'FFF5F5F5';
  static const String gray600 = 'FF757575';
}