import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../controllers/health_controller.dart';
import '../models/health_record_isar.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../details/health_details_screen.dart';
import '../../widgets/universal_record_card.dart';
import '../../widgets/filters/filter_layout.dart';
import '../../widgets/filters/filters.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/message_utils.dart';
import '../../User Account/guards/demo_guard.dart';
class HealthRecordsTab extends StatefulWidget {
  const HealthRecordsTab({Key? key}) : super(key: key);

  @override
  State<HealthRecordsTab> createState() => _HealthRecordsTabState();
}

class _HealthRecordsTabState extends State<HealthRecordsTab> {
  late FilterController _filterController;

  @override
  void initState() {
    super.initState();
    debugPrint('🔧 HEALTH RECORDS TAB: Initializing...');
    _filterController = FilterController();
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  void _onFiltersChanged() {
    final healthController = context.read<HealthController>();
    final filterState = HealthFilterState(
      searchQuery: _filterController.searchQuery,
      startDate: _filterController.startDate,
      endDate: _filterController.endDate,
      cattleId: _filterController.globalFilters['cattle'],
      recordType: _filterController.globalFilters['recordType'],
      status: _filterController.globalFilters['status'],
    );
    healthController.applyFilters(filterState);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HealthController>(
      builder: (context, controller, child) {
        debugPrint('🔄 HEALTH RECORDS TAB: Building with $controller.healthRecords.length records');

        return Column(
          children: [
            // Filter Layout
            UniversalFilterLayout(
              controller: _filterController,
              theme: FilterTheme.health,
              moduleName: 'health',
              sortFields: const [...SortField.commonFields, ...SortField.healthFields],
              searchHint: 'Search health records...',
              totalCount: controller.unfilteredHealthRecords.length,
              filteredCount: controller.healthRecords.length,
              config: FilterLayoutConfig.standard,
            ),

            // Records List
            Expanded(
              child: controller.healthRecords.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.only(
                        left: 16,
                        right: 16,
                        top: 8,
                        bottom: 88, // Bottom padding for FAB
                      ),
                      itemCount: controller.healthRecords.length,
                      itemBuilder: (context, index) {
                        final record = controller.healthRecords[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildHealthRecordCard(record, controller),
                        );
                      },
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.healthHeader.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.medical_services_outlined,
              size: 64,
              color: AppColors.healthHeader,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Health Records',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Track your cattle\'s medical history, treatments, and health conditions',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              // Trigger the FAB action to add a health record
              // This will be handled by the parent screen's FAB
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Health Record'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.healthHeader,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthRecordCard(HealthRecordIsar record, HealthController controller) {
    // Get cattle information using the correct field
    final cattle = controller.getCattle(record.cattleTagId);
    debugPrint('🐄 HEALTH RECORD CARD: Looking for cattle with tagId: $record.cattleTagId');
    debugPrint('🐄 HEALTH RECORD CARD: Found cattle: ${cattle?.name ?? 'Unknown'} (${cattle?.tagId ?? 'No Tag'})');

    // Format row 1: Date + Name/TagID
    String dateText = _formatDate(record.date);
    String cattleInfo = cattle?.name ?? 'Unknown Cattle';
    if (cattle?.tagId != null && cattle!.tagId!.isNotEmpty) {
      cattleInfo = '${cattle.name ?? 'Unknown'} ($cattle.tagId)';
    }

    // Format row 2: Issue + Treatment
    String healthIssue = record.details ?? 'No issue specified';
    String treatment = record.treatment ?? 'No treatment';

    // Format row 3: Veterinarian + Cost (conditional)
    String? veterinarian = record.veterinarian?.isNotEmpty == true ? record.veterinarian : null;
    String? cost = record.cost != null && record.cost! > 0 ? '\$${record.cost!.toStringAsFixed(2)}' : null;

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: cattleInfo,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.pets,
      row2Left: healthIssue,
      row2Right: treatment,
      row2LeftIcon: Icons.medical_information,
      row2RightIcon: Icons.healing,
      // Row 3 - only show if we have veterinarian or cost data
      row3Left: veterinarian,
      row3Right: cost,
      row3LeftIcon: veterinarian != null ? Icons.person : null,
      row3RightIcon: cost != null ? Icons.receipt_long : null,
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.healthHeader,
      onTap: () => _navigateToHealthRecordDetail(record, controller),
      onEdit: () => _showEditHealthRecordDialog(record, controller),
      onDelete: () => _showDeleteConfirmation(record, controller),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'No date';
    return DateFormat('MMM dd, yyyy').format(date);
  }

  void _navigateToHealthRecordDetail(HealthRecordIsar record, HealthController controller) {
    debugPrint('🔍 HEALTH RECORD: Attempting navigation for record $record.id');
    debugPrint('🔍 HEALTH RECORD: Record cattleTagId: "$record.cattleTagId"');

    // Handle null or empty cattle tag ID
    final cattleTagId = record.cattleTagId;
    if (cattleTagId == null || cattleTagId.isEmpty) {
      debugPrint('❌ HEALTH RECORD: No cattle tag ID associated with this health record');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This health record is not associated with any cattle. Please edit the record to assign it to a cattle.'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 4),
        ),
      );
      return;
    }

    final cattle = controller.getCattle(cattleTagId);
    if (cattle == null) {
      debugPrint('❌ HEALTH RECORD: No cattle found with tagId: "$cattleTagId"');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Cattle with tag ID "$cattleTagId" not found. The cattle may have been deleted.'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    debugPrint('✅ HEALTH RECORD: Navigate to detail for cattle $cattle.name ($cattle.tagId)');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HealthDetailsScreen(
          cattle: cattle,
        ),
      ),
    );
  }

  void _showEditHealthRecordDialog(HealthRecordIsar record, HealthController controller) {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('edit_health_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'edit_health_record');
      return;
    }

    debugPrint('🔧 HEALTH RECORDS TAB: Opening edit dialog for record $record.id');
    showDialog(
      context: context,
      builder: (context) => HealthRecordFormDialog(
        healthRecord: record,
        cattle: controller.unfilteredCattle,
        onSave: (recordData) async {
          try {
            debugPrint('💾 HEALTH RECORDS TAB: Starting update for record $recordData.id');
            debugPrint('📊 HEALTH RECORDS TAB: Before update - Current records count: $controller.healthRecords.length');

            await controller.updateHealthRecord(recordData);

            debugPrint('✅ HEALTH RECORDS TAB: Update completed for record $recordData.id');
            debugPrint('📊 HEALTH RECORDS TAB: After update - Current records count: $controller.healthRecords.length');

            if (mounted) {
              MessageUtils.showSuccess(context, 'Health record updated successfully');
            }
          } catch (e) {
            debugPrint('❌ HEALTH RECORDS TAB: Update failed for record $recordData.id: $e');
            if (mounted) {
              MessageUtils.showError(context, 'Failed to update health record: $e');
            }
          }
        },
      ),
    );
  }

  void _showDeleteConfirmation(HealthRecordIsar record, HealthController controller) {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('delete_health_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'delete_health_record');
      return;
    }

    final cattle = controller.getCattle(record.cattleTagId);
    final recordDescription = 'Health record for ${cattle?.name ?? 'Unknown cattle'}';

    debugPrint('🗑️ HEALTH RECORDS TAB: Opening delete confirmation for record $record.id');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Health Record'),
        content: Text('Are you sure you want to delete this $recordDescription?'),
        actions: [
          TextButton(
            onPressed: () {
              debugPrint('❌ HEALTH RECORDS TAB: Delete cancelled for record $record.id');
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                debugPrint('🗑️ HEALTH RECORDS TAB: Starting delete for record $record.id');
                debugPrint('📊 HEALTH RECORDS TAB: Before delete - Current records count: $controller.healthRecords.length');

                await controller.deleteHealthRecord(record.id);

                debugPrint('✅ HEALTH RECORDS TAB: Delete completed for record $record.id');
                debugPrint('📊 HEALTH RECORDS TAB: After delete - Current records count: $controller.healthRecords.length');

                if (mounted) {
                  MessageUtils.showSuccess(context, 'Health record deleted successfully');
                }
              } catch (e) {
                debugPrint('❌ HEALTH RECORDS TAB: Delete failed for record $record.id: $e');
                if (mounted) {
                  MessageUtils.showError(context, 'Failed to delete health record: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }


}
