import 'package:isar/isar.dart';

import '../models/milk_record_isar.dart';
import '../models/milk_sale_isar.dart';
import '../../../services/database/isar_service.dart';

// Legacy alias for compatibility
typedef MilkHandler = MilkRepository;

/// Pure reactive repository for Milk Records module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class MilkRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  MilkRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE MILK RECORDS STREAMS ===//

  /// Watches all milk records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<MilkRecordIsar>> watchAllMilkRecords() {
    return _isar.milkRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all milk sales with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<MilkSaleIsar>> watchAllMilkSales() {
    return _isar.milkSaleIsars.where().watch(fireImmediately: true);
  }

  //=== MILK RECORDS CRUD ===//

  /// Save (add or update) a milk record using Isar's native upsert
  Future<void> saveMilkRecord(MilkRecordIsar record) async {
    await _isar.writeTxn(() async {
      await _isar.milkRecordIsars.put(record);
    });
  }

  /// Delete a milk record by its Isar ID
  Future<void> deleteMilkRecord(int id) async {
    await _isar.writeTxn(() async {
      await _isar.milkRecordIsars.delete(id);
    });
  }

  //=== MILK SALES CRUD ===//

  /// Save (add or update) a milk sale using Isar's native upsert
  Future<void> saveMilkSale(MilkSaleIsar sale) async {
    await _isar.writeTxn(() async {
      await _isar.milkSaleIsars.put(sale);
    });
  }

  /// Delete a milk sale by its Isar ID
  Future<void> deleteMilkSale(int id) async {
    await _isar.writeTxn(() async {
      await _isar.milkSaleIsars.delete(id);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get milk records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<MilkRecordIsar>> getMilkRecordsByCattleId(String cattleId) async {
    return await _isar.milkRecordIsars
        .filter()
        .cattleIdEqualTo(cattleId)
        .findAll();
  }

  /// Get all milk records (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<MilkRecordIsar>> getAllMilkRecords() async {
    return await _isar.milkRecordIsars.where().findAll();
  }

  /// Get all milk sales (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<MilkSaleIsar>> getAllMilkSales() async {
    return await _isar.milkSaleIsars.where().findAll();
  }
}