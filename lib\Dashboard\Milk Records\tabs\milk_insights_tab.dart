import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../controllers/milk_controller.dart';
import '../services/milk_insights_service.dart';

import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState

import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

/// Milk Insights Tab - Displays AI-generated insights and recommendations
///
/// Architectural Pattern: Pure Dependency Injection (Ultimate Form)
/// - ALL dependencies REQUIRED via constructor for complete architectural purity
/// - ZERO knowledge of dependency creation/location within the widget
/// - Widget is a pure function of its inputs - perfect for testing and reusability
/// - Parent widgets are responsible for dependency resolution
class MilkInsightsTab extends StatelessWidget {
  final MilkController controller; // Required - pure dependency injection
  final MilkInsightsService insightsService; // Required - pure dependency injection

  const MilkInsightsTab({
    Key? key,
    required this.controller,
    required this.insightsService,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Handle different controller states
    switch (controller.state) {
      case ControllerState.initial:
      case ControllerState.loading:
        return const Center(
          child: CircularProgressIndicator(),
        );
      case ControllerState.error:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                controller.errorMessage ?? 'Failed to generate insights',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => controller.loadData(),
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        );
      case ControllerState.empty:
        return UniversalTabEmptyState.forTab(
          title: 'No Data for Insights',
          message: 'Add milk records to get AI-powered insights and recommendations.',
          tabColor: AppColors.milkHeader,
          tabIndex: 2, // Insights tab
          action: TabEmptyStateActions.addFirstRecord(
            onPressed: () {
              // Navigate to records tab or show add dialog
            },
            tabColor: AppColors.milkHeader,
          ),
        );
      case ControllerState.loaded:
      //       default:
 // Unreachable default case removed
        return RefreshIndicator(
          onRefresh: () async {
            await controller.loadData();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(kPaddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(context),
                const SizedBox(height: kSpacingLarge),

                // Milk Production Insights
                _buildMilkInsights(context),
                const SizedBox(height: kSpacingLarge),

                // Management Recommendations
                _buildManagementRecommendations(context),
              ],
            ),
          ),
        );
    }
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.milkHeader,
            AppColors.milkHeader.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.milkHeader.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Milk Production Insights',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Smart recommendations for optimizing your milk production',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHeaderStat('Total Records', controller.totalMilkRecords.toString(), Icons.water_drop),
              const SizedBox(width: 24),
              _buildHeaderStat('Avg Daily', '${controller.averageDailyProduction.toStringAsFixed(1)}L', Icons.trending_up),
              const SizedBox(width: 24),
              _buildHeaderStat('Best Cow', controller.topProducingCattle.isNotEmpty ? controller.topProducingCattle : 'N/A', Icons.star),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.8), size: 16),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMilkInsights(BuildContext context) {
    // Pure dependency injection - use injected dependencies directly
    final insights = insightsService.generateInsights(controller.analytics);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Production Analysis',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (insights.isEmpty)
          _buildEmptyInsightsCard(context)
        else
          ...insights.map((insight) => _buildInsightCard(context, insight)),
      ],
    );
  }

  Widget _buildManagementRecommendations(BuildContext context) {
    final recommendations = GetIt.instance<MilkInsightsService>().generateManagementRecommendations();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Management Recommendations',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...recommendations.map((rec) => _buildRecommendationCard(context, rec)),
      ],
    );
  }

  Widget _buildInsightCard(BuildContext context, MilkInsight insight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: insight.color.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(insight.icon, color: insight.color, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: insight.priority.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: insight.priority.color.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    insight.priority.label,
                    style: TextStyle(
                      color: insight.priority.color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              insight.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (insight.recommendations.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Recommendations:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...insight.recommendations.map((rec) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(child: Text(rec)),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationCard(BuildContext context, ManagementRecommendation recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: recommendation.color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(recommendation.icon, color: recommendation.color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            recommendation.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInsightsCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(Icons.lightbulb_outline, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Specific Insights Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add more milk records to unlock personalized production insights',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }


}
