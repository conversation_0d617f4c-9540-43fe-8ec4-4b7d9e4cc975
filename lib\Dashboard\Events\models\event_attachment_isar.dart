import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'event_attachment_isar.g.dart';

/// Event Attachment model for Isar database
@collection
class EventAttachmentIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Reference to event business ID - indexed for filtering
  @Index()
  String? eventBusinessId;

  /// Original file name
  String? fileName;

  /// File path on device/storage
  String? filePath;

  /// File type (image, document, video)
  String? fileType;

  /// File size in bytes
  int? fileSize;

  /// MIME type of the file
  String? mimeType;

  /// Image-specific fields
  int? imageWidth;
  int? imageHeight;
  String? thumbnailPath;

  /// Document-specific fields
  int? pageCount;

  /// Video-specific fields
  int? durationSeconds;

  /// Metadata
  DateTime? createdAt;
  DateTime? updatedAt;

  /// Default constructor
  EventAttachmentIsar();

  /// Generate a business ID for event attachments
  static String generateBusinessId() {
    return const Uuid().v4();
  }

  /// Factory constructor for creating a new event attachment
  factory EventAttachmentIsar.create({
    required String eventBusinessId,
    required String fileName,
    required String filePath,
    required String fileType,
    required int fileSize,
    String? mimeType,
    int? imageWidth,
    int? imageHeight,
    String? thumbnailPath,
    int? pageCount,
    int? durationSeconds,
  }) {
    final attachment = EventAttachmentIsar()
      ..businessId = generateBusinessId()
      ..eventBusinessId = eventBusinessId
      ..fileName = fileName
      ..filePath = filePath
      ..fileType = fileType
      ..fileSize = fileSize
      ..mimeType = mimeType
      ..imageWidth = imageWidth
      ..imageHeight = imageHeight
      ..thumbnailPath = thumbnailPath
      ..pageCount = pageCount
      ..durationSeconds = durationSeconds
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    return attachment;
  }

  /// Check if this is an image attachment
  bool get isImage {
    return fileType?.toLowerCase() == 'image' ||
        mimeType?.startsWith('image/') == true;
  }

  /// Check if this is a document attachment
  bool get isDocument {
    return fileType?.toLowerCase() == 'document' ||
        mimeType?.startsWith('application/') == true ||
        mimeType?.contains('pdf') == true ||
        mimeType?.contains('doc') == true;
  }

  /// Check if this is a video attachment
  bool get isVideo {
    return fileType?.toLowerCase() == 'video' ||
        mimeType?.startsWith('video/') == true;
  }

  /// Get file extension from filename
  String? get fileExtension {
    if (fileName == null) return null;
    final lastDot = fileName!.lastIndexOf('.');
    if (lastDot == -1) return null;
    return fileName!.substring(lastDot + 1).toLowerCase();
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize == null) return 'Unknown size';
    
    final bytes = fileSize!;
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get display name for the attachment
  String get displayName {
    return fileName ?? 'Unnamed attachment';
  }

  /// Update the attachment's updated timestamp
  void touch() {
    updatedAt = DateTime.now();
  }

  /// Create attachment from image file
  factory EventAttachmentIsar.fromImage({
    required String eventBusinessId,
    required String fileName,
    required String filePath,
    required int fileSize,
    int? imageWidth,
    int? imageHeight,
    String? thumbnailPath,
  }) {
    return EventAttachmentIsar.create(
      eventBusinessId: eventBusinessId,
      fileName: fileName,
      filePath: filePath,
      fileType: 'image',
      fileSize: fileSize,
      mimeType: _getMimeTypeFromExtension(fileName),
      imageWidth: imageWidth,
      imageHeight: imageHeight,
      thumbnailPath: thumbnailPath,
    );
  }

  /// Create attachment from document file
  factory EventAttachmentIsar.fromDocument({
    required String eventBusinessId,
    required String fileName,
    required String filePath,
    required int fileSize,
    int? pageCount,
  }) {
    return EventAttachmentIsar.create(
      eventBusinessId: eventBusinessId,
      fileName: fileName,
      filePath: filePath,
      fileType: 'document',
      fileSize: fileSize,
      mimeType: _getMimeTypeFromExtension(fileName),
      pageCount: pageCount,
    );
  }

  /// Create attachment from video file
  factory EventAttachmentIsar.fromVideo({
    required String eventBusinessId,
    required String fileName,
    required String filePath,
    required int fileSize,
    int? durationSeconds,
    String? thumbnailPath,
  }) {
    return EventAttachmentIsar.create(
      eventBusinessId: eventBusinessId,
      fileName: fileName,
      filePath: filePath,
      fileType: 'video',
      fileSize: fileSize,
      mimeType: _getMimeTypeFromExtension(fileName),
      durationSeconds: durationSeconds,
      thumbnailPath: thumbnailPath,
    );
  }

  /// Get MIME type from file extension
  static String? _getMimeTypeFromExtension(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    switch (extension) {
      // Images
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'bmp':
        return 'image/bmp';
      
      // Documents
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'txt':
        return 'text/plain';
      case 'rtf':
        return 'application/rtf';
      
      // Videos
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/x-msvideo';
      case 'mov':
        return 'video/quicktime';
      case 'wmv':
        return 'video/x-ms-wmv';
      case 'flv':
        return 'video/x-flv';
      
      default:
        return null;
    }
  }
}