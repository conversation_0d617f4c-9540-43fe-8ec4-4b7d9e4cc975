import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'user_session_isar.dart';
import 'user_settings_isar.dart';
import '../../Farm Setup/models/farm_isar.dart';

part 'user_isar.g.dart';

@collection
class UserIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index(unique: true, caseSensitive: false)
  String? email;

  @Index(unique: true, caseSensitive: false)
  String? username;

  String? firstName;
  String? lastName;
  String? phoneNumber;
  String? profilePictureUrl;

  // Farm-related information
  String? farmName;
  String? farmLocation;
  String? farmDescription;

  // Password and security
  String? passwordHash;
  String? passwordSalt;

  // Google authentication
  bool isGoogleUser = false;
  String? googleId;
  String? profileImageUrl;
  
  // Email verification
  bool isEmailVerified = false;
  String? emailVerificationToken;
  DateTime? emailVerificationTokenExpiry;

  // Password reset
  String? passwordResetToken;
  DateTime? passwordResetTokenExpiry;

  // Account security
  bool isActive = true;
  bool isLocked = false;
  int failedLoginAttempts = 0;
  DateTime? lastFailedLoginAt;
  DateTime? lockedUntil;

  // Timestamps
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? lastLoginAt;

  // Relationships
  final sessions = IsarLinks<UserSessionIsar>();
  final settings = IsarLink<UserSettingsIsar>();
  
  // Link to associated farms (a user can manage multiple farms)
  final farms = IsarLinks<FarmIsar>();

  UserIsar();

  factory UserIsar.create({
    required String email,
    required String username,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
    String? farmName,
    String? farmLocation,
    String? farmDescription,
    bool isGoogleUser = false,
    String? googleId,
    String? profileImageUrl,
  }) {
    return UserIsar()
      ..businessId = const Uuid().v4()
      ..email = email.toLowerCase().trim()
      ..username = username.toLowerCase().trim()
      ..firstName = firstName.trim()
      ..lastName = lastName.trim()
      ..phoneNumber = phoneNumber?.trim()
      ..farmName = farmName?.trim()
      ..farmLocation = farmLocation?.trim()
      ..farmDescription = farmDescription?.trim()
      ..isGoogleUser = isGoogleUser
      ..googleId = googleId
      ..profileImageUrl = profileImageUrl
      ..isEmailVerified = isGoogleUser // Google users are pre-verified
      ..isActive = true
      ..isLocked = false
      ..failedLoginAttempts = 0
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  // Helper methods
  @ignore
  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();

  @ignore
  bool get canLogin => isActive && !isLocked && (lockedUntil == null || DateTime.now().isAfter(lockedUntil!));

  @ignore
  bool get needsPasswordReset => passwordResetToken != null &&
      passwordResetTokenExpiry != null &&
      DateTime.now().isBefore(passwordResetTokenExpiry!);

  @ignore
  bool get needsEmailVerification => !isEmailVerified &&
      emailVerificationToken != null &&
      emailVerificationTokenExpiry != null &&
      DateTime.now().isBefore(emailVerificationTokenExpiry!);

  void updateLastLogin() {
    lastLoginAt = DateTime.now();
    updatedAt = DateTime.now();
  }

  void incrementFailedLogin() {
    failedLoginAttempts++;
    lastFailedLoginAt = DateTime.now();
    updatedAt = DateTime.now();
    
    // Lock account after 5 failed attempts for 30 minutes
    if (failedLoginAttempts >= 5) {
      isLocked = true;
      lockedUntil = DateTime.now().add(const Duration(minutes: 30));
    }
  }

  void resetFailedLogins() {
    failedLoginAttempts = 0;
    lastFailedLoginAt = null;
    isLocked = false;
    lockedUntil = null;
    updatedAt = DateTime.now();
  }

  void setEmailVerificationToken(String token) {
    emailVerificationToken = token;
    emailVerificationTokenExpiry = DateTime.now().add(const Duration(hours: 24));
    updatedAt = DateTime.now();
  }

  void verifyEmail() {
    isEmailVerified = true;
    emailVerificationToken = null;
    emailVerificationTokenExpiry = null;
    updatedAt = DateTime.now();
  }

  void setPasswordResetToken(String token) {
    passwordResetToken = token;
    passwordResetTokenExpiry = DateTime.now().add(const Duration(hours: 1));
    updatedAt = DateTime.now();
  }

  void clearPasswordResetToken() {
    passwordResetToken = null;
    passwordResetTokenExpiry = null;
    updatedAt = DateTime.now();
  }

  void updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? farmName,
    String? farmLocation,
    String? farmDescription,
    String? profilePictureUrl,
  }) {
    if (firstName != null) this.firstName = firstName.trim();
    if (lastName != null) this.lastName = lastName.trim();
    if (phoneNumber != null) this.phoneNumber = phoneNumber.trim();
    if (farmName != null) this.farmName = farmName.trim();
    if (farmLocation != null) this.farmLocation = farmLocation.trim();
    if (farmDescription != null) this.farmDescription = farmDescription.trim();
    if (profilePictureUrl != null) this.profilePictureUrl = profilePictureUrl;
    updatedAt = DateTime.now();
  }
}
