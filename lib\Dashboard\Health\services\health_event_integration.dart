import '../models/health_record_isar.dart';

class HealthEventIntegration {
  // EventService integration temporarily disabled
  // final EventService _eventService;

  HealthEventIntegration();

  /// Create health-related events
  Future<void> createHealthEvents(HealthRecordIsar healthRecord) async {
    // EventService integration temporarily disabled
    /*
    try {
      // Create vaccination event
      if (healthRecord.date != null && healthRecord.recordType == 'vaccination') {
        await _createVaccinationEvent(healthRecord);
      }

      // Create deworming event
      if (healthRecord.date != null && healthRecord.recordType == 'deworming') {
        await _createDewormingEvent(healthRecord);
      }

      // Create treatment event
      if (healthRecord.date != null && healthRecord.recordType == 'treatment') {
        await _createTreatmentEvent(healthRecord);
      }

      // Create checkup event
      if (healthRecord.date != null && healthRecord.recordType == 'checkup') {
        await _createCheckupEvent(healthRecord);
      }

      // Create follow-up event
      if (healthRecord.followUpDate != null) {
        await _createFollowUpEvent(healthRecord);
      }
    } catch (e) {
      debugPrint('Error creating health events: $e');
    }
    */
  }

  /*
  /// Create vaccination event
  Future<void> _createVaccinationEvent(HealthRecordIsar healthRecord) async {
    final event = EventIsar()
      ..title = 'Vaccination - ${healthRecord.cattleTagId}'
      ..description = 'Vaccination for cattle ${healthRecord.cattleTagId}: ${healthRecord.details ?? 'Routine vaccination'}'
      // .eventType = EventType.health
      // ..priority = EventPriority.high
      // ..status = EventStatus.pending
      // .startDateTime = healthRecord.date!
      // .endDateTime = healthRecord.date!.add(const Duration(hours: 1))
      // .cattleIds = [healthRecord.cattleTagId!]
      ..businessId = healthRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create deworming event
  Future<void> _createDewormingEvent(HealthRecordIsar healthRecord) async {
    final event = EventIsar()
      ..title = 'Deworming - ${healthRecord.cattleTagId}'
      ..description = 'Deworming for cattle ${healthRecord.cattleTagId}'
      // .eventType = EventType.health
      // ..priority = EventPriority.medium
      // ..status = EventStatus.pending
      // .startDateTime = healthRecord.date!
      // .endDateTime = healthRecord.date!.add(const Duration(hours: 1))
      // .cattleIds = [healthRecord.cattleTagId!]
      ..businessId = healthRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create treatment event
  Future<void> _createTreatmentEvent(HealthRecordIsar healthRecord) async {
    final event = EventIsar()
      ..title = 'Treatment - ${healthRecord.cattleTagId}'
      ..description = 'Treatment for cattle ${healthRecord.cattleTagId}: ${healthRecord.diagnosis ?? 'Medical treatment'}'
      // .eventType = EventType.health
      // ..priority = EventPriority.high
      // ..status = EventStatus.pending
      // .startDateTime = healthRecord.date!
      // .endDateTime = healthRecord.date!.add(const Duration(hours: 2))
      // .cattleIds = [healthRecord.cattleTagId!]
      ..businessId = healthRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create checkup event
  Future<void> _createCheckupEvent(HealthRecordIsar healthRecord) async {
    final event = EventIsar()
      ..title = 'Health Checkup - ${healthRecord.cattleTagId}'
      ..description = 'Routine health checkup for cattle ${healthRecord.cattleTagId}'
      // .eventType = EventType.health
      // ..priority = EventPriority.low
      // ..status = EventStatus.pending
      // .startDateTime = healthRecord.date!
      // .endDateTime = healthRecord.date!.add(const Duration(hours: 1))
      // .cattleIds = [healthRecord.cattleTagId!]
      ..businessId = healthRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create follow-up event
  Future<void> _createFollowUpEvent(HealthRecordIsar healthRecord) async {
    final event = EventIsar()
      ..title = 'Follow-up - ${healthRecord.cattleTagId}'
      ..description = 'Follow-up check for cattle ${healthRecord.cattleTagId}'
      // .eventType = EventType.health
      // ..priority = EventPriority.medium
      // ..status = EventStatus.pending
      // .startDateTime = healthRecord.followUpDate!
      // .endDateTime = healthRecord.followUpDate!.add(const Duration(hours: 1))
      // .cattleIds = [healthRecord.cattleTagId!]
      ..businessId = healthRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }
  */

  /*
  /// Update health events
  Future<void> updateHealthEvents(HealthRecordIsar healthRecord) async {
    try {
      // Update existing events based on health record changes
      final allEvents = await _eventService.getEventsByBusinessId(healthRecord.businessId!);
      final events = allEvents.where((event) =>
        event.cattleIds?.contains(healthRecord.cattleTagId) == true
      ).toList();

      for (final event in events) {
        if (event.eventType == EventType.health) {
          await _updateEventFromHealthRecord(event, healthRecord);
        }
      }
    } catch (e) {
      debugPrint('Error updating health events: $e');
    }
  }

  /// Update event from health record
  Future<void> _updateEventFromHealthRecord(
    EventIsar event,
    HealthRecordIsar healthRecord,
  ) async {
    if (event.title?.contains('Vaccination') == true && healthRecord.recordType == 'vaccination') {
      event.startDateTime = healthRecord.date!;
      event.endDateTime = healthRecord.date!.add(const Duration(hours: 1));
      event.description = 'Vaccination for cattle ${healthRecord.cattleTagId}: ${healthRecord.details ?? 'Routine vaccination'}';
    } else if (event.title?.contains('Deworming') == true && healthRecord.recordType == 'deworming') {
      event.startDateTime = healthRecord.date!;
      event.endDateTime = healthRecord.date!.add(const Duration(hours: 1));
    } else if (event.title?.contains('Treatment') == true && healthRecord.recordType == 'treatment') {
      event.startDateTime = healthRecord.date!;
      event.endDateTime = healthRecord.date!.add(const Duration(hours: 2));
      event.description = 'Treatment for cattle ${healthRecord.cattleTagId}: ${healthRecord.diagnosis ?? 'Medical treatment'}';
    } else if (event.title?.contains('Health Checkup') == true && healthRecord.recordType == 'checkup') {
      event.startDateTime = healthRecord.date!;
      event.endDateTime = healthRecord.date!.add(const Duration(hours: 1));
    } else if (event.title?.contains('Follow-up') == true && healthRecord.followUpDate != null) {
      event.startDateTime = healthRecord.followUpDate!;
      event.endDateTime = healthRecord.followUpDate!.add(const Duration(hours: 1));
    }

    event.updatedAt = DateTime.now();
    await _eventService.updateEvent(event);
  }

  /// Delete health events
  Future<void> deleteHealthEvents(String cattleId, String businessId) async {
    try {
      final allEvents = await _eventService.getEventsByBusinessId(businessId);
      final events = allEvents.where((event) =>
        event.cattleIds?.contains(cattleId) == true
      ).toList();

      final healthEvents = events.where((event) =>
        event.eventType == EventType.health
      ).toList();

      for (final event in healthEvents) {
        await _eventService.deleteEvent(event.id);
      }
    } catch (e) {
      debugPrint('Error deleting health events: $e');
    }
  }

  /// Create recurring health events
  Future<void> createRecurringHealthEvents({
    required String cattleId,
    required String businessId,
    required DateTime startDate,
    required int intervalDays,
    required int totalEvents,
    required String eventType,
    required String description,
  }) async {
    try {
      for (int i = 0; i < totalEvents; i++) {
        final eventDate = startDate.add(Duration(days: i * intervalDays));

        final event = EventIsar()
          ..title = '$eventType - $cattleId'
          ..description = description
          // .eventType = EventType.health
          // ..priority = EventPriority.medium
          // ..status = EventStatus.pending
          // .startDateTime = eventDate
          // .endDateTime = eventDate.add(const Duration(hours: 1))
          // .cattleIds = [cattleId]
          ..businessId = businessId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        await _eventService.createEvent(event);
      }
    } catch (e) {
      debugPrint('Error creating recurring health events: $e');
    }
  }
  */
}