import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../routes/app_routes.dart';

/// Banner widget to indicate demo mode and provide upgrade options
class DemoModeBanner extends StatelessWidget {
  final bool showUpgradeButton;
  final VoidCallback? onUpgradePressed;

  const DemoModeBanner({
    super.key,
    this.showUpgradeButton = true,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange[100]!,
            Colors.orange[50]!,
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.orange[200]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.orange[200],
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.preview,
              color: Colors.orange[800],
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Demo Mode',
                  style: TextStyle(
                    color: Colors.orange[800],
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'You\'re viewing sample data. Sign up to manage your own cattle records.',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontSize: 12,
                    height: 1.2,
                  ),
                ),
              ],
            ),
          ),
          
          if (showUpgradeButton) ...[
            const SizedBox(width: 12),
            _buildUpgradeButton(context),
          ],
        ],
      ),
    );
  }

  Widget _buildUpgradeButton(BuildContext context) {
    return ElevatedButton(
      onPressed: onUpgradePressed ?? () => _showUpgradeOptions(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        minimumSize: const Size(0, 32),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 1,
      ),
      child: const Text(
        'Sign Up',
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showUpgradeOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const DemoUpgradeBottomSheet(),
    );
  }
}

/// Bottom sheet with upgrade options and feature comparison
class DemoUpgradeBottomSheet extends StatelessWidget {
  const DemoUpgradeBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Title
          const Text(
            'Unlock Full Features',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Create your account to access all cattle management features',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 32),
          
          // Feature comparison
          _buildFeatureComparison(),
          
          const SizedBox(height: 32),
          
          // Action buttons
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.pushNamed(context, AppRoutes.register);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Create Account',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(height: 12),
              
              OutlinedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.pushNamed(context, AppRoutes.login);
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.primary),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Sign In',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(height: 12),
              
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Continue Demo'),
              ),
            ],
          ),
          
          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildFeatureComparison() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          _buildFeatureRow('View sample data', true, true),
          _buildFeatureRow('Add/edit cattle records', false, true),
          _buildFeatureRow('Health & breeding tracking', false, true),
          _buildFeatureRow('Generate reports', false, true),
          _buildFeatureRow('Cloud backup & sync', false, true),
          _buildFeatureRow('Export data', false, true),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(String feature, bool demoAvailable, bool fullAvailable) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              feature,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          
          // Demo column
          SizedBox(
            width: 60,
            child: Column(
              children: [
                if (feature == 'View sample data') // Header
                  const Text(
                    'Demo',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange,
                    ),
                  )
                else
                  Icon(
                    demoAvailable ? Icons.check : Icons.close,
                    color: demoAvailable ? Colors.green : Colors.red,
                    size: 18,
                  ),
              ],
            ),
          ),
          
          // Full version column
          SizedBox(
            width: 60,
            child: Column(
              children: [
                if (feature == 'View sample data') // Header
                  const Text(
                    'Full',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  )
                else
                  Icon(
                    fullAvailable ? Icons.check : Icons.close,
                    color: fullAvailable ? Colors.green : Colors.red,
                    size: 18,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
