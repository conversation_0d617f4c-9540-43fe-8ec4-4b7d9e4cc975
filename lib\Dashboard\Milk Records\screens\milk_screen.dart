import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import '../controllers/milk_controller.dart';
import '../dialogs/milk_form_dialog.dart';
import '../dialogs/milk_sale_entry_dialog.dart';
import '../tabs/milk_records_tab.dart';
import '../tabs/milk_sales_tab.dart';
import '../tabs/milk_analytics_tab.dart';
import '../tabs/milk_insights_tab.dart';
import '../services/milk_insights_service.dart';
import '../../../routes/app_routes.dart';
import '../../User Account/guards/demo_guard.dart';

 // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
 // Import Universal Colors

/// Milk screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class MilkScreen extends StatelessWidget {
  const MilkScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => MilkController(),
      child: const _MilkScreenContent());
  }
}

/// Internal content widget that accesses the controller via Provider
class _MilkScreenContent extends StatefulWidget {
  const _MilkScreenContent();

  @override
  State<_MilkScreenContent> createState() => _MilkScreenContentState();
}

class _MilkScreenContentState extends State<_MilkScreenContent>
    with TickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Milk Management',
      body: Consumer<MilkController>(
        builder: (context, milkController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.fourTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => MilkAnalyticsTab(controller: milkController)),
              Builder(
                builder: (context) => const MilkRecordsTab(), // Uses Provider pattern
              ),
              Builder(
                builder: (context) => const MilkSalesTab(), // Uses Provider pattern
              ),
              Builder(
                builder: (context) => MilkInsightsTab(
                  // Ultimate Pure Dependency Injection: ALL dependencies provided by parent
                  // Widget has ZERO knowledge of dependency creation - perfect architectural purity
                  controller: milkController,
                  insightsService: GetIt.instance<MilkInsightsService>())),
            ],
            labels: const ['Analytics', 'Records', 'Sales', 'Insights'],
            icons: const [Icons.analytics, Icons.list, Icons.point_of_sale, Icons.lightbulb],
            showFABs: const [false, true, true, false], // FAB on Records and Sales tabs
          );

          // Simple state management without UniversalStateBuilder
          if (milkController.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Error Loading Data',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    milkController.errorMessage ?? 'Failed to load milk data',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {}, // No manual retry needed - reactive streams auto-recover
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return _tabManager!; // Tab manager is guaranteed to be initialized above
        }),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.milkReport),
          tooltip: 'View Milk Reports'),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: () => _getDialogForCurrentTab()(),
            tooltip: _getTooltipForCurrentTab()) ?? const SizedBox.shrink(); // Handle null case
        }), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  void _showAddMilkDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_milk_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_milk_record');
      return;
    }

    final milkController = context.read<MilkController>();

    showDialog(
      context: context,
      builder: (context) => MilkFormDialog(
        cattle: milkController.cattle,
        onSave: (record) async {
          await milkController.addMilkRecord(record);
        },
        // No onRecordAdded callback needed - reactive streams handle updates!
      ));
  }

  void _showMilkSaleDialog() async {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_milk_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_milk_record');
      return;
    }

    final milkController = context.read<MilkController>();

    // Calculate available milk for sale
    final double availableMilk = await _calculateAvailableMilk();

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => MilkSaleEntryDialog(
        selectedDate: DateTime.now(),
        availableMilk: availableMilk,
        onSave: (sale) async {
          await milkController.addMilkSale(sale);
        },
      ),
    );
  }

  Future<double> _calculateAvailableMilk() async {
    final milkController = context.read<MilkController>();

    // Calculate total milk produced today
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final todayRecords = milkController.milkRecords.where((record) {
      final recordDate = record.date ?? DateTime.now();
      return recordDate.isAfter(startOfDay) && recordDate.isBefore(endOfDay);
    });

    final totalProduced = todayRecords.fold<double>(
      0.0, (sum, record) {
        // Calculate total milk quantity from all sessions
        final morning = record.morning ?? 0.0;
        final afternoon = record.afternoon ?? 0.0;
        final evening = record.evening ?? 0.0;
        return sum + morning + afternoon + evening;
      });

    // Calculate total sold today
    final todaySales = milkController.unfilteredMilkSales.where((sale) {
      final saleDate = sale.date ?? DateTime.now();
      return saleDate.isAfter(startOfDay) && saleDate.isBefore(endOfDay);
    });

    final totalSold = todaySales.fold<double>(
      0.0, (sum, sale) => sum + (sale.quantity ?? 0.0));

    // Calculate total usage (home + calf) from existing sales
    final totalHomeUsage = todaySales.fold<double>(
      0.0, (sum, sale) => sum + (sale.homeUsage ?? 0.0));
    final totalCalfUsage = todaySales.fold<double>(
      0.0, (sum, sale) => sum + (sale.calfUsage ?? 0.0));

    final availableMilk = totalProduced - totalSold - totalHomeUsage - totalCalfUsage;

    // Debug information
    debugPrint('🥛 Available Milk Calculation:');
    debugPrint('  Total Produced: ${totalProduced.toStringAsFixed(1)}L');
    debugPrint('  Total Sold: ${totalSold.toStringAsFixed(1)}L');
    debugPrint('  Total Home Usage: ${totalHomeUsage.toStringAsFixed(1)}L');
    debugPrint('  Total Calf Usage: ${totalCalfUsage.toStringAsFixed(1)}L');
    debugPrint('  Available: ${availableMilk.toStringAsFixed(1)}L');

    return availableMilk > 0 ? availableMilk : 0.0;
  }

  void Function() _getDialogForCurrentTab() {
    switch (_tabController.index) {
      case 1: // Records tab
        return _showAddMilkDialog;
      case 2: // Sales tab
        return _showMilkSaleDialog;
      default:
        return _showAddMilkDialog;
    }
  }

  String _getTooltipForCurrentTab() {
    switch (_tabController.index) {
      case 1: // Records tab
        return 'Add Milk Record';
      case 2: // Sales tab
        return 'Add Milk Sale';
      default:
        return 'Add Record';
    }
  }

  // State mapping is now handled by ScreenStateMapper mixin
}