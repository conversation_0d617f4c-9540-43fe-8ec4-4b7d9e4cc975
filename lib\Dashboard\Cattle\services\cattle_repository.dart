import 'package:isar/isar.dart';
import '../models/cattle_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Cattle module database operations
/// Following the weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
/// Sync functionality moved to dedicated CattleSyncService for clean separation
class CattleRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  CattleRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE CATTLE STREAMS ===//

  /// Watches all cattle with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<CattleIsar>> watchAllCattle() {
    return _isar.cattleIsars.where().watch(fireImmediately: true);
  }

  //=== CATTLE CRUD ===//

  /// Save (add or update) a cattle record using <PERSON><PERSON>'s native upsert
  Future<void> saveCattle(CattleIsar cattle) async {
    await _isar.writeTxn(() async {
      await _isar.cattleIsars.put(cattle);
    });
  }

  /// Delete a cattle record by its Isar ID
  Future<void> deleteCattle(int cattleId) async {
    await _isar.writeTxn(() async {
      await _isar.cattleIsars.delete(cattleId);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS AND VALIDATION ===//

  /// Get all cattle (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<CattleIsar>> getAllCattle() async {
    return await _isar.cattleIsars.where().findAll();
  }

  /// Get cattle by tag ID (for validation and navigation)
  /// Returns a Future<CattleIsar?> for one-time data fetching
  Future<CattleIsar?> getCattleByTagId(String tagId) async {
    return await _isar.cattleIsars
        .filter()
        .tagIdEqualTo(tagId, caseSensitive: false)
        .findFirst();
  }

  /// Get cattle by business ID (for validation and navigation)
  /// Returns a Future<CattleIsar?> for one-time data fetching
  Future<CattleIsar?> getCattleByBusinessId(String businessId) async {
    return await _isar.cattleIsars.getByBusinessId(businessId);
  }

  /// Update cattle - alias for saveCattle for backward compatibility
  Future<void> updateCattle(CattleIsar cattle) async {
    await saveCattle(cattle);
  }
}