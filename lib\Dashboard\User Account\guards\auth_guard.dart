import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../services/auth_service.dart';
import '../screens/welcome_screen.dart';

/// Authentication guard to protect routes that require authentication
class AuthGuard {
  static final AuthService _authService = GetIt.instance<AuthService>();

  /// Check if user is authenticated and redirect to welcome screen if not
  static Widget guard(Widget protectedWidget) {
    return FutureBuilder<bool>(
      future: _checkAuthentication(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Show loading screen while checking authentication
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasData && snapshot.data == true) {
          // User is authenticated, show the protected widget
          return protectedWidget;
        } else {
          // User is not authenticated, show welcome screen
          return const WelcomeScreen();
        }
      },
    );
  }

  /// Check if user is currently authenticated
  static Future<bool> _checkAuthentication() async {
    try {
      // Initialize auth service if not already initialized
      await _authService.initialize();
      return _authService.isAuthenticated;
    } catch (e) {
      return false;
    }
  }

  /// Check authentication status synchronously (for use in route guards)
  static bool isAuthenticated() {
    return _authService.isAuthenticated;
  }

  /// Navigate to welcome screen if not authenticated
  static void redirectIfNotAuthenticated(BuildContext context) {
    if (!isAuthenticated()) {
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/welcome',
        (route) => false,
      );
    }
  }
}
