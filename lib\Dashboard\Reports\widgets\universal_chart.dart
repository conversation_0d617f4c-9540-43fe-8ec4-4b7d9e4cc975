import 'package:flutter/material.dart';
import '../models/report_models.dart';
import '../services/chart_service.dart';

/// Universal Chart Widget
/// 
/// A reusable chart component that provides consistent chart display
/// across all reports with standardized styling and responsive behavior.
class UniversalChart extends StatelessWidget {
  final ChartType type;
  final List<ChartPoint> data;
  final String title;
  final Color? primaryColor;
  final bool showGrid;
  final bool showLegend;
  final double? height;
  final EdgeInsets? padding;
  final VoidCallback? onTap;
  final bool isLoading;
  final String? errorMessage;

  const UniversalChart({
    Key? key,
    required this.type,
    required this.data,
    required this.title,
    this.primaryColor,
    this.showGrid = true,
    this.showLegend = true,
    this.height,
    this.padding,
    this.onTap,
    this.isLoading = false,
    this.errorMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = MediaQuery.of(context).size.width > 600;
    final chartHeight = height ?? (isLargeScreen ? 250.0 : 200.0);
    final chartPadding = padding ?? EdgeInsets.all(isLargeScreen ? 16.0 : 12.0);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: chartPadding,
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: _buildContent(context, chartHeight),
      ),
    );
  }

  Widget _buildContent(BuildContext context, double chartHeight) {
    if (isLoading) {
      return _buildLoadingState(chartHeight);
    }

    if (errorMessage != null) {
      return _buildErrorState(context, chartHeight);
    }

    if (data.isEmpty) {
      return _buildEmptyState(chartHeight);
    }

    return _buildChart(context, chartHeight);
  }

  Widget _buildChart(BuildContext context, double chartHeight) {
    final chartService = ChartService();
    
    return chartService.buildChart(
      type: type,
      data: data,
      title: title,
      primaryColor: primaryColor ?? Theme.of(context).primaryColor,
      showGrid: showGrid,
      showLegend: showLegend,
      height: chartHeight,
    );
  }

  Widget _buildLoadingState(double chartHeight) {
    return SizedBox(
      height: chartHeight + 60,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading chart data...',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, double chartHeight) {
    return SizedBox(
      height: chartHeight + 60,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.3),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red.withValues(alpha: 0.7),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading chart',
                      style: TextStyle(
                        color: Colors.red.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      errorMessage ?? 'Unknown error occurred',
                      style: TextStyle(
                        color: Colors.red.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(double chartHeight) {
    return SizedBox(
      height: chartHeight + 60,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _getEmptyStateIcon(),
                      size: 48,
                      color: Colors.grey.withValues(alpha: 0.7),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No data available',
                      style: TextStyle(
                        color: Colors.grey.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Try adjusting your filters or date range',
                      style: TextStyle(
                        color: Colors.grey.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getEmptyStateIcon() {
    switch (type) {
      case ChartType.line:
        return Icons.show_chart;
      case ChartType.bar:
        return Icons.bar_chart;
      case ChartType.pie:
        return Icons.pie_chart;
      case ChartType.area:
        return Icons.area_chart;
    }
  }

  /// Factory constructors for common chart types

  /// Line chart for trends over time
  factory UniversalChart.line({
    required List<ChartPoint> data,
    required String title,
    Color? primaryColor,
    bool showGrid = true,
    double? height,
    VoidCallback? onTap,
    bool isLoading = false,
    String? errorMessage,
  }) {
    return UniversalChart(
      type: ChartType.line,
      data: data,
      title: title,
      primaryColor: primaryColor,
      showGrid: showGrid,
      showLegend: false, // Line charts typically don't need legends
      height: height,
      onTap: onTap,
      isLoading: isLoading,
      errorMessage: errorMessage,
    );
  }

  /// Bar chart for comparisons
  factory UniversalChart.bar({
    required List<ChartPoint> data,
    required String title,
    Color? primaryColor,
    bool showGrid = true,
    double? height,
    VoidCallback? onTap,
    bool isLoading = false,
    String? errorMessage,
  }) {
    return UniversalChart(
      type: ChartType.bar,
      data: data,
      title: title,
      primaryColor: primaryColor,
      showGrid: showGrid,
      showLegend: false, // Bar charts typically don't need legends
      height: height,
      onTap: onTap,
      isLoading: isLoading,
      errorMessage: errorMessage,
    );
  }

  /// Pie chart for distributions
  factory UniversalChart.pie({
    required List<ChartPoint> data,
    required String title,
    bool showLegend = true,
    double? height,
    VoidCallback? onTap,
    bool isLoading = false,
    String? errorMessage,
  }) {
    return UniversalChart(
      type: ChartType.pie,
      data: data,
      title: title,
      showGrid: false, // Pie charts don't use grids
      showLegend: showLegend,
      height: height,
      onTap: onTap,
      isLoading: isLoading,
      errorMessage: errorMessage,
    );
  }

  /// Area chart for cumulative data
  factory UniversalChart.area({
    required List<ChartPoint> data,
    required String title,
    Color? primaryColor,
    bool showGrid = true,
    double? height,
    VoidCallback? onTap,
    bool isLoading = false,
    String? errorMessage,
  }) {
    return UniversalChart(
      type: ChartType.area,
      data: data,
      title: title,
      primaryColor: primaryColor,
      showGrid: showGrid,
      showLegend: false, // Area charts typically don't need legends
      height: height,
      onTap: onTap,
      isLoading: isLoading,
      errorMessage: errorMessage,
    );
  }
}

/// Chart container for responsive layout
class ChartContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? height;

  const ChartContainer({
    Key? key,
    required this.child,
    this.padding,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = MediaQuery.of(context).size.width > 600;
    final containerPadding = padding ?? EdgeInsets.all(isLargeScreen ? 16.0 : 8.0);

    return Container(
      height: height,
      padding: containerPadding,
      child: child,
    );
  }
}

/// Chart grid for displaying multiple charts
class ChartGrid extends StatelessWidget {
  final List<Widget> charts;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const ChartGrid({
    Key? key,
    required this.charts,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.2,
    this.crossAxisSpacing = 16.0,
    this.mainAxisSpacing = 16.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = MediaQuery.of(context).size.width > 600;
    final actualCrossAxisCount = isLargeScreen ? crossAxisCount : 1;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: actualCrossAxisCount,
      childAspectRatio: childAspectRatio,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      children: charts,
    );
  }
}