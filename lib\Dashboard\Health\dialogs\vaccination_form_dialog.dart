import 'package:flutter/material.dart';

import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../constants/app_colors.dart';
import '../../Transactions/models/category_isar.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import 'package:get_it/get_it.dart';

// --- Constants ---
class _AppStrings {
  static const String addVaccinationTitle = 'Add Vaccination';
  static const String editVaccinationTitle = 'Edit Vaccination';
  static const String cattleLabel = 'Cattle';
  static const String vaccineNameLabel = 'Vaccine Name';
  static const String batchNumberLabel = 'Batch Number';
  static const String manufacturerLabel = 'Manufacturer';
  static const String administeredDateLabel = 'Administered Date';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';

  // Validation messages
  static const String vaccineNameRequired = 'Vaccine name is required';
  static const String dateRequired = 'Administered date is required';
}

class VaccinationFormDialog extends StatefulWidget {
  final String cattleId;
  final List<CattleIsar>? cattle;
  final VaccinationIsar? vaccination;
  final Future<bool> Function(VaccinationIsar)? onSave;

  const VaccinationFormDialog({
    Key? key,
    required this.cattleId,
    this.cattle,
    this.vaccination,
    this.onSave,
  }) : super(key: key);

  @override
  State<VaccinationFormDialog> createState() => _VaccinationFormDialogState();
}

class _VaccinationFormDialogState extends State<VaccinationFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();

  // Categories
  List<CategoryIsar> _vaccineCategories = [];
  bool _isLoadingCategories = true;

  // Controllers and selections
  String? _selectedVaccineName;
  final _batchNumberController = TextEditingController();
  final _manufacturerController = TextEditingController();
  final _notesController = TextEditingController();
  final _costController = TextEditingController();

  // State variables
  late String _cattleId;
  DateTime _administeredDate = DateTime.now();
  bool _isSaving = false;
  bool _showOptionalFields = false;

  @override
  void initState() {
    super.initState();
    _cattleId = widget.cattleId;

    if (widget.vaccination != null) {
      _loadVaccinationData();
    }

    // Load vaccine categories
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final vaccineCategories = await _farmSetupRepository.getVaccineCategories();
      setState(() {
        _vaccineCategories = vaccineCategories;
        _isLoadingCategories = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCategories = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load vaccine categories: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _batchNumberController.dispose();
    _manufacturerController.dispose();
    _notesController.dispose();
    _costController.dispose();
    super.dispose();
  }

  void _loadVaccinationData() {
    final vaccination = widget.vaccination!;
    _selectedVaccineName = vaccination.vaccineName;
    _batchNumberController.text = vaccination.batchNumber ?? '';
    _manufacturerController.text = vaccination.manufacturer ?? '';
    _administeredDate = vaccination.date ?? DateTime.now();
    _notesController.text = vaccination.notes ?? '';
    _costController.text = vaccination.cost?.toString() ?? '';
  }

  // Helper method to get selected cattle
  CattleIsar? _getSelectedCattle() {
    if (widget.cattle == null || widget.cattle!.isEmpty) {
      return null;
    }

    return widget.cattle!.firstWhere(
      (cattle) => cattle.businessId == _cattleId,
      orElse: () => widget.cattle!.first,
    );
  }

  // Format cattle for display
  String _getCattleDisplayName() {
    final selectedCattle = _getSelectedCattle();
    if (selectedCattle == null) {
      return 'Cattle ID: $_cattleId';
    }

    return '${selectedCattle.name ?? 'Unnamed'} (${selectedCattle.tagId ?? 'No Tag'})';
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      // Validate required fields
      if (_selectedVaccineName?.trim().isEmpty ?? true) {
        throw Exception(_AppStrings.vaccineNameRequired);
      }

      // Create or update VaccinationIsar object
      final VaccinationIsar vaccinationRecord;

      if (widget.vaccination != null) {
        // Update existing vaccination using copyWith to preserve ID and businessId
        vaccinationRecord = widget.vaccination!.copyWith(
          cattleTagId: _cattleId,
          vaccineName: _selectedVaccineName ?? '',
          batchNumber: _batchNumberController.text.trim(),
          manufacturer: _manufacturerController.text.trim(),
          date: _administeredDate,
          notes: _notesController.text.trim(),
          cost: double.tryParse(_costController.text) ?? 0.0,
        );
      } else {
        // Create new vaccination record
        vaccinationRecord = VaccinationIsar()
          ..cattleTagId = _cattleId
          ..vaccineName = _selectedVaccineName ?? ''
          ..batchNumber = _batchNumberController.text.trim()
          ..manufacturer = _manufacturerController.text.trim()
          ..date = _administeredDate
          ..notes = _notesController.text.trim()
          ..cost = double.tryParse(_costController.text) ?? 0.0
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
      }

      // Call the save callback if provided
      if (widget.onSave != null) {
        await widget.onSave!(vaccinationRecord);
      }

      // Show success message and close dialog
      if (mounted) {
        HealthMessageUtils.showSuccess(context,
          widget.vaccination == null
            ? 'Vaccination record created successfully'
            : 'Vaccination record updated successfully');
        Navigator.of(context).pop(vaccinationRecord);
      }
    } catch (e) {
      debugPrint('ERROR: Failed to save vaccination: $e');
      if (mounted) {
        setState(() => _isSaving = false);
        HealthMessageUtils.showError(context,
            'Error: ${e.toString().replaceAll('Exception: ', '')}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return UniversalFormDialog(
      title: widget.vaccination == null ? _AppStrings.addVaccinationTitle : _AppStrings.editVaccinationTitle,
      headerIcon: Icons.vaccines,
      formContent: _buildFormContent(),
      actionButtons: UniversalDialogButtons.cancelSaveRow(
        onCancel: () => Navigator.of(context).pop(),
        onSave: _handleSave,
        isSaving: _isSaving,
      ),
    );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Cattle Selection/Display
          if (widget.cattle != null && widget.cattle!.length > 1)
            UniversalFormField.dropdownField<String>(
              label: _AppStrings.cattleLabel,
              value: _cattleId,
              items: widget.cattle!.map<DropdownMenuItem<String>>((cattle) {
                final displayName = '${cattle.name ?? 'Unnamed'} (${cattle.tagId ?? 'No Tag'})';
                return DropdownMenuItem(
                  value: cattle.businessId,
                  child: Text(displayName, overflow: TextOverflow.ellipsis),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _cattleId = value!;
                });
              },
              prefixIcon: Icons.pets,
              prefixIconColor: Colors.green, // Changed from brown (forbidden)
              validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
            )
          else
            UniversalFormField.textField(
              label: _AppStrings.cattleLabel,
              initialValue: _getCattleDisplayName(),
              readOnly: true,
              prefixIcon: Icons.pets,
              prefixIconColor: Colors.green, // Changed from brown (forbidden)
            ),
          UniversalFormField.spacing,

          // Administered Date Field (moved to come after cattle selection)
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.administeredDateLabel,
            value: _administeredDate,
            onChanged: (date) {
              setState(() {
                _administeredDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            validator: (date) {
              if (date == null) {
                return _AppStrings.dateRequired;
              }
              return null;
            },
            lastDate: DateTime.now(),
          ),
          UniversalFormField.spacing,

          // Vaccine Name Dropdown
          _isLoadingCategories
              ? const Center(child: CircularProgressIndicator())
              : UniversalFormField.dropdownField<String>(
                  label: _AppStrings.vaccineNameLabel,
                  value: _selectedVaccineName,
                  items: _vaccineCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.name,
                      child: Text(category.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedVaccineName = value;
                    });
                  },
                  prefixIcon: Icons.vaccines,
                  prefixIconColor: Colors.blue,
                  validator: (value) => UniversalFormField.dropdownValidator(value, 'vaccine name'),
          ),
          UniversalFormField.sectionSpacing,

          // Optional Fields Toggle
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showOptionalFields = !_showOptionalFields;
                });
              },
              icon: Icon(
                _showOptionalFields
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: AppColors.healthHeader,
              ),
              label: Text(
                _showOptionalFields
                    ? 'Hide Optional Information'
                    : 'Show Optional Information',
                style: const TextStyle(
                  color: AppColors.healthHeader,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: AppColors.healthHeader,
                  width: 1.5,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Optional Fields (shown when toggle is enabled)
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Batch Number Field
            UniversalFormField.textField(
              label: _AppStrings.batchNumberLabel,
              controller: _batchNumberController,
              prefixIcon: Icons.numbers,
              prefixIconColor: Colors.indigo,
            ),
            UniversalFormField.spacing,

            // Manufacturer Field
            UniversalFormField.textField(
              label: _AppStrings.manufacturerLabel,
              controller: _manufacturerController,
              prefixIcon: Icons.factory,
              prefixIconColor: Colors.purple,
            ),
            UniversalFormField.spacing,

            // Cost Field
            UniversalFormField.numberField(
              label: _AppStrings.costLabel,
              controller: _costController,
              allowDecimals: true,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.green,
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.notes,
              prefixIconColor: Colors.teal,
            ),
          ],
        ],
      ),
    );
  }
}
