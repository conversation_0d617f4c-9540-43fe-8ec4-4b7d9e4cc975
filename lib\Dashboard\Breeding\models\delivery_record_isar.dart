import 'dart:convert';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'delivery_record_isar.g.dart';

/// Represents a delivery/calving record in the Isar database
@collection
class DeliveryRecordIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Reference to the cattle that gave birth
  @Index(type: IndexType.value)
  String? cattleId;

  /// Reference to the pregnancy record if applicable
  @Index(type: IndexType.value)
  String? pregnancyId;

  /// Date of delivery
  @Index()
  DateTime? deliveryDate;

  /// Type of birth (Normal, Assisted, C-Section, etc.)
  String? deliveryType;

  /// Number of calves born
  int calfCount = 1;

  /// Alias for calfCount for backward compatibility
  int get numberOfCalves => calfCount;
  set numberOfCalves(int value) => calfCount = value;

  /// Whether there were complications
  bool hadComplications = false;

  /// Description of any complications
  String? complicationDetails;

  /// Veterinarian who assisted (if any)
  String? veterinarian;

  /// Cost of delivery assistance/veterinary care
  double? cost;

  /// Notes about the delivery
  String? notes;

  /// Status of the delivery record
  String? status;

  /// Date when the record was created
  DateTime? createdAt;

  /// Date when the record was last updated
  DateTime? updatedAt;

  /// CalfIDs (comma-separated list of tag IDs of calves born)
  String? calfIds;

  /// Calf details (JSON string containing full calf information)
  String? calfDetailsJson;

  /// Default constructor
  DeliveryRecordIsar();

  /// Generate a formatted ID for delivery records in the format 'cattleId-Delivery-sequenceNumber'
  static String generateFormattedId(String cattleId, int sequenceNumber) {
    return '$cattleId-Delivery-$sequenceNumber';
  }

  /// Factory constructor for creating a new delivery record
  factory DeliveryRecordIsar.create({
    required String cattleId,
    required DateTime deliveryDate,
    required String deliveryType,
    String? pregnancyId,
    int calfCount = 1,
    bool hadComplications = false,
    String? complicationDetails,
    String? veterinarian,
    double? cost,
    String? notes,
    String? calfIds,
    List<Map<String, dynamic>>? calfDetails,
    String? businessId,
  }) {
    return DeliveryRecordIsar()
      ..businessId = businessId ?? const Uuid().v4()
      ..cattleId = cattleId
      ..pregnancyId = pregnancyId
      ..deliveryDate = deliveryDate
      ..deliveryType = deliveryType
      ..calfCount = calfCount
      ..hadComplications = hadComplications
      ..complicationDetails = complicationDetails
      ..veterinarian = veterinarian
      ..cost = cost
      ..notes = notes
      ..status = 'Completed'
      ..calfIds = calfIds
      ..calfDetailsJson = calfDetails != null ? jsonEncode(calfDetails) : null
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  /// Convert to a Map for serialization
  Map<String, dynamic> toMap() {
    // Parse calf details from JSON if available
    List<dynamic>? calfDetails;
    if (calfDetailsJson != null && calfDetailsJson!.isNotEmpty) {
      try {
        calfDetails = jsonDecode(calfDetailsJson!) as List<dynamic>;
      } catch (e) {
        // If parsing fails, calfDetails will remain null
        calfDetails = null;
      }
    }

    return {
      'id': businessId,
      'cattleId': cattleId,
      'pregnancyId': pregnancyId,
      'deliveryDate': deliveryDate?.toIso8601String(),
      'deliveryType': deliveryType,
      'calfCount': calfCount,
      'hadComplications': hadComplications,
      'complicationDetails': complicationDetails,
      'veterinarian': veterinarian,
      'notes': notes,
      'status': status,
      'calfIds': calfIds,
      'calfDetails': calfDetails, // Include parsed calf details
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Create from a Map representation
  factory DeliveryRecordIsar.fromMap(Map<String, dynamic> map) {
    // Handle calf details - convert from list to JSON string if needed
    String? calfDetailsJson;
    if (map['calfDetails'] != null) {
      if (map['calfDetails'] is String) {
        calfDetailsJson = map['calfDetails'] as String;
      } else if (map['calfDetails'] is List) {
        calfDetailsJson = jsonEncode(map['calfDetails']);
      }
    }

    return DeliveryRecordIsar()
      ..businessId = map['id'] as String?
      ..cattleId = map['cattleId'] as String?
      ..pregnancyId = map['pregnancyId'] as String?
      ..deliveryDate = map['deliveryDate'] != null
          ? DateTime.parse(map['deliveryDate'] as String)
          : null
      ..deliveryType = map['deliveryType'] as String?
      ..calfCount = map['calfCount'] as int? ?? 1
      ..hadComplications = map['hadComplications'] as bool? ?? false
      ..complicationDetails = map['complicationDetails'] as String?
      ..veterinarian = map['veterinarian'] as String?
      ..notes = map['notes'] as String?
      ..status = map['status'] as String? ?? 'Completed'
      ..calfIds = map['calfIds'] as String?
      ..calfDetailsJson = calfDetailsJson
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'] as String)
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'] as String)
          : DateTime.now();
  }
}