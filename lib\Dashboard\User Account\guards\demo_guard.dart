import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get_it/get_it.dart';

import '../screens/login_screen.dart';
import '../widgets/demo_onboarding_overlay.dart';
import '../services/auth_service.dart';


/// A guard that wraps the app to provide demo mode functionality
/// Displays a banner when in demo mode and handles authentication state
class DemoGuard extends StatelessWidget {
  final Widget child;
  final AuthState authState;

  const DemoGuard({
    super.key,
    required this.child,
    required this.authState,
  });

  // Static flag to track onboarding state
  static bool _hasSeenOnboarding = false;

  @override
  Widget build(BuildContext context) {
    switch (authState) {
      case AuthState.authenticated:
        return child;
      case AuthState.demo:
        return _DemoModeWrapper(child: child);
      case AuthState.unauthenticated:
        return const LoginScreen();
    }
  }

  /// Show the features showcase dialog
  static void showFeaturesShowcase(BuildContext context) {
    Navigator.of(context).pushNamed('/features');
  }

  /// Show onboarding overlay
  static void _showOnboarding(BuildContext context) {
    // Find the _DemoModeWrapperState and trigger onboarding
    final state = context.findAncestorStateOfType<_DemoModeWrapperState>();
    state?._showOnboardingOverlay();
  }

  /// Exit demo mode and return to login
  static void _exitDemoMode(BuildContext context) {
    // Clear demo mode flag
    _demoModeFlag = false;
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
      (route) => false,
    );
  }

  /// Exit demo mode programmatically
  static void exitDemoMode() {
    _demoModeFlag = false;
  }

  /// Show upgrade dialog
  static void _showUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upgrade to Full Version'),
        content: const Text('Sign up to unlock all features and save your data permanently.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushNamed('/register');
            },
            child: const Text('Sign Up'),
          ),
        ],
      ),
    );
  }

  /// Reset onboarding state (for testing)
  static void resetOnboarding() {
    _hasSeenOnboarding = false;
  }

  /// Check if user has seen onboarding
  static bool get hasSeenOnboarding => _hasSeenOnboarding;

  /// Initialize demo mode state from preferences
  static Future<void> initializeDemoState() async {
    final prefs = await SharedPreferences.getInstance();
    _hasSeenOnboarding = prefs.getBool('demo_onboarding_seen') ?? false;
  }

  /// Save demo mode state to preferences
  static Future<void> _saveDemoState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('demo_onboarding_seen', _hasSeenOnboarding);
  }

  /// Guard method to wrap widgets with demo functionality
  static Widget guard(Widget child) {
    return Builder(
      builder: (context) {
        // Check actual authentication state
        final authState = _determineAuthState();
        return DemoGuard(
          authState: authState,
          child: child,
        );
      },
    );
  }

  /// Determine the current authentication state
  static AuthState _determineAuthState() {
    try {
      final authService = GetIt.instance<AuthService>();

      // Check if user is authenticated
      if (authService.isAuthenticated) {
        return AuthState.authenticated;
      }

      // Check if we're in demo mode
      if (_isDemoModeActive()) {
        return AuthState.demo;
      }

      // Default to unauthenticated
      return AuthState.unauthenticated;
    } catch (e) {
      // If AuthService is not available or there's an error, assume unauthenticated
      return AuthState.unauthenticated;
    }
  }

  /// Check if demo mode is currently active
  static bool _isDemoModeActive() {
    try {
      // Check if demo mode was started (could be stored in SharedPreferences or a flag)
      // For now, we'll use a simple approach - demo mode is active if explicitly started
      debugPrint('🔍 [DEMO_GUARD] Checking demo mode flag: $_demoModeFlag');
      return _demoModeFlag;
    } catch (e) {
      debugPrint('❌ [DEMO_GUARD] Error checking demo mode status: $e');
      return false;
    }
  }

  /// Public method to check if demo mode is active
  static bool isDemoModeActive() {
    final isActive = _isDemoModeActive();
    debugPrint('🔍 [DEMO_GUARD] isDemoModeActive() returning: $isActive');
    return isActive;
  }

  // Flag to track if demo mode was explicitly started
  static bool _demoModeFlag = false;

  /// Start demo mode
  static Future<void> startDemoMode() async {
    debugPrint('🚀 [DEMO_GUARD] Starting demo mode...');

    // Set demo mode flag
    _demoModeFlag = true;
    debugPrint('✅ [DEMO_GUARD] Demo mode flag set to true');

    // Initialize demo state and navigate to dashboard
    debugPrint('🔧 [DEMO_GUARD] Initializing demo state...');
    await initializeDemoState();
    debugPrint('✅ [DEMO_GUARD] Demo state initialization completed');

    // Note: Welcome screen preference handling removed as SettingsRepository was deleted
    debugPrint('ℹ️ [DEMO_GUARD] Skipping welcome screen preference update (SettingsRepository removed)');

    debugPrint('✅ [DEMO_GUARD] Demo mode startup completed');
  }

  /// Check if a feature is restricted in demo mode
  static bool isFeatureRestricted(String feature) {
    // In demo mode, certain features might be restricted
    // For now, return false to allow all features in demo
    return false;
  }

  /// Show demo restriction message
  static void showDemoRestrictionMessage(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('This feature is limited in demo mode. Sign up to unlock full access.'),
        action: SnackBarAction(
          label: 'Sign Up',
          onPressed: () => _showUpgradeDialog(context),
        ),
      ),
    );
  }
}

/// StatefulWidget wrapper for demo mode that properly manages onboarding state
class _DemoModeWrapper extends StatefulWidget {
  final Widget child;

  const _DemoModeWrapper({required this.child});

  @override
  State<_DemoModeWrapper> createState() => _DemoModeWrapperState();
}

class _DemoModeWrapperState extends State<_DemoModeWrapper> {
  bool _showOnboarding = false;

  @override
  void initState() {
    super.initState();
    // Show onboarding if this is the first time in demo mode
    if (!DemoGuard._hasSeenOnboarding) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _showOnboarding = true;
        });
      });
    }
  }

  void _completeOnboarding() {
    setState(() {
      _showOnboarding = false;
    });
    DemoGuard._hasSeenOnboarding = true;
    DemoGuard._saveDemoState();
  }

  void _showOnboardingOverlay() {
    setState(() {
      _showOnboarding = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main app with demo banner
        Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                // Enhanced demo mode banner - 2-row grid system
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.orange[100]!,
                        Colors.orange[50]!,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withValues(alpha: 0.2),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Row 1: Demo Mode title + Features + Help buttons
                      Row(
                        children: [
                          // Demo Mode title section - flex 3 with fixed width for alignment
                          Expanded(
                            flex: 3,
                            child: SizedBox(
                              height: 32, // Match button height for consistent row height
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: Colors.orange[200],
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.preview,
                                      color: Colors.orange[800],
                                      size: 18,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        'Demo Mode',
                                        style: TextStyle(
                                          color: Colors.orange[800],
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          
                          // Features button - flex 2
                          Expanded(
                            flex: 2,
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () => DemoGuard.showFeaturesShowcase(context),
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  constraints: const BoxConstraints(minHeight: 32),
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.featured_play_list,
                                        size: 16,
                                        color: Colors.orange[800],
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Features',
                                        style: TextStyle(
                                          color: Colors.orange[800],
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          
                          // Help button - flex 2
                          Expanded(
                            flex: 2,
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () => DemoGuard._showOnboarding(context),
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  constraints: const BoxConstraints(minHeight: 32),
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.help_outline,
                                        size: 16,
                                        color: Colors.orange[800],
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Help',
                                        style: TextStyle(
                                          color: Colors.orange[800],
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      // Vertical spacing between rows
                      const SizedBox(height: 8),
                      
                      // Row 2: Demo Mode subtitle + Exit + Sign Up buttons
                      Row(
                        children: [
                          // Demo Mode subtitle section - flex 3 with consistent structure for alignment
                          Expanded(
                            flex: 3,
                            child: SizedBox(
                              height: 32, // Match button height for consistent row height
                              child: Row(
                                children: [
                                  // Invisible spacer to match icon container + spacing width from Row 1
                                  const SizedBox(width: 42), // Icon container (30px) + spacing (12px) = 42px
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        'Exploring with\nsample data',
                                        style: TextStyle(
                                          color: Colors.orange[700],
                                          fontSize: 12,
                                          height: 1.2,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          
                          // Exit button - flex 2
                          Expanded(
                            flex: 2,
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () => DemoGuard._exitDemoMode(context),
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  constraints: const BoxConstraints(minHeight: 32),
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.exit_to_app,
                                        size: 16,
                                        color: Colors.orange[800],
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Exit',
                                        style: TextStyle(
                                          color: Colors.orange[800],
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          
                          // Sign Up button - flex 2
                          Expanded(
                            flex: 2,
                            child: Material(
                              color: Colors.orange[600],
                              borderRadius: BorderRadius.circular(16),
                              child: InkWell(
                                onTap: () => DemoGuard._showUpgradeDialog(context),
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  constraints: const BoxConstraints(minHeight: 32),
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                  child: const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.person_add,
                                        size: 16,
                                        color: Colors.white,
                                      ),
                                      SizedBox(width: 6),
                                      Text(
                                        'Sign Up',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Main content
                Expanded(child: widget.child),
              ],
            ),
          ),
        ),

        // Onboarding overlay (shown on first demo mode entry)
        if (_showOnboarding)
          DemoOnboardingOverlay(
            onComplete: _completeOnboarding,
          ),
      ],
    );
  }
}

enum AuthState {
  authenticated,
  demo,
  unauthenticated,
}
