import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../controllers/weight_controller.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';
import '../dialogs/weight_form_dialog.dart';
import '../models/weight_record_isar.dart';
import '../details/weight_details_screen.dart';
import '../../../utils/message_utils.dart';

class WeightRecordsTab extends StatefulWidget {
  final WeightController? controller; // Made optional to support Provider pattern

  const WeightRecordsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<WeightRecordsTab> createState() => _WeightRecordsTabState();
}

class _WeightRecordsTabState extends State<WeightRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  WeightController get _controller => widget.controller ?? context.read<WeightController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // Apply filters at database level for ultimate scalability
    _controller.applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    // Check if we have data to display
    if (_controller.totalWeightRecords == 0) {
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    // Get weight records data - now pre-filtered at database level
    final records = _controller.weightRecords;
    final allRecordsCount = _controller.totalWeightRecords; // This represents total before filtering

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.weight,
          moduleName: 'weight',
          sortFields: const [...SortField.commonFields, ...SortField.weightFields],
          searchHint: 'Search weight records by cattle, date, or method...',
          totalCount: allRecordsCount,
          filteredCount: records.length,
        ),

        // Weight Records List - data is already filtered at database level
        Expanded(
          child: records.isEmpty
              ? _buildEmptyState(allRecordsCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    // UX Decision: Pull-to-refresh acts as a complete reset, clearing filters
                    // to provide users with a "fresh start" experience. This is intuitive
                    // behavior when users want to see all data without current filter constraints.
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: records.length,
                    itemBuilder: (context, index) {
                      final record = records[index];
                      return _buildWeightRecordCard(record);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  /// Consolidated empty state builder - single source of truth for all empty states
  ///
  /// [isCompletelyEmpty] - true when no weight records exist at all, false when records exist but filters hide them
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    // Get the tab color for Records tab (index 1) from weight module
    const tabColor = AppColors.weightHeader;

    if (isCompletelyEmpty) {
      // No weight records exist - show call-to-action to add first record
      return UniversalTabEmptyState.forTab(
        title: 'No Weight Records',
        message: 'Add your first weight record to start tracking your herd\'s growth.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddWeightRecordDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Weight records exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Weight Records',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildWeightRecordCard(WeightRecordIsar record) {
    final cattle = record.cattle.value;

    // Format row 1: Date + Weight
    String dateText = _formatDate(record.measurementDate);
    String weightText = record.formattedWeight;

    // Format row 2: Cattle name and measurement method
    String cattleName = cattle?.name ?? 'Unknown Cattle';
    String methodText = _formatMeasurementMethod(record.measurementMethod);

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: weightText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.monitor_weight,
      row2Left: cattleName,
      row2Right: methodText,
      row2LeftIcon: Icons.pets,
      row2RightIcon: Icons.scale,
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.weightHeader,
      onTap: () => _navigateToWeightRecordDetail(record),
      onEdit: () => _showEditWeightRecordDialog(record),
      onDelete: () => _showDeleteConfirmation(record),
    );
  }

  void _navigateToWeightRecordDetail(WeightRecordIsar record) {
    // Navigate to weight details screen for the associated cattle
    final cattle = record.cattle.value;
    if (cattle != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => WeightDetailsScreen(
            cattle: cattle,
            onCattleUpdated: (updatedCattle) {
              // Handle cattle updates if needed
              // The weight controller streams will handle automatic updates
            },
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unable to navigate: Cattle information not found'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Consolidated dialog helper for both add and edit operations
  ///
  /// [record] - null for add operation, existing record for edit operation
  void _showWeightRecordFormDialog([WeightRecordIsar? record]) {

    showDialog(
      context: context,
      builder: (context) => WeightFormDialog(
        cattle: _controller.allCattle,
        existingRecord: record, // null for add, existing record for edit
      ),
    );
  }

  /// Show dialog to add new weight record
  void _showAddWeightRecordDialog() => _showWeightRecordFormDialog();

  /// Show dialog to edit existing weight record
  void _showEditWeightRecordDialog(WeightRecordIsar record) => _showWeightRecordFormDialog(record);

  void _showDeleteConfirmation(WeightRecordIsar record) async {
    final cattle = record.cattle.value;
    final confirmed = await WeightMessageUtils.showWeightRecordDeleteConfirmation(
      context,
      cattleName: cattle?.name ?? 'Unknown cattle',
      recordId: record.businessId,
      weight: record.formattedWeight,
      date: record.measurementDate != null
          ? DateFormat('MMM dd, yyyy').format(record.measurementDate!)
          : 'Unknown date',
    );

    if (confirmed == true) {
      try {
        await _controller.deleteWeightRecord(record.id);
        if (mounted) {
          MessageUtils.showSuccess(context, 'Weight record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Error deleting record: $e');
        }
      }
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }

  String _formatMeasurementMethod(MeasurementMethod? method) {
    switch (method) {
      case MeasurementMethod.scale:
        return 'Scale';
      case MeasurementMethod.tape:
        return 'Tape';
      case MeasurementMethod.visualEstimate:
        return 'Visual';
      case null:
        return 'Unknown';
    }
  }
}
