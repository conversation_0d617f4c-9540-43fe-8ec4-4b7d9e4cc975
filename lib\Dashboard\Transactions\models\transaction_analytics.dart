// Unused import removed

/// Data class to hold all transaction analytics results
class TransactionAnalyticsResult {
  // KPI Metrics
  final int totalTransactions;
  final int incomeTransactions;
  final int expenseTransactions;
  final double totalIncome;
  final double totalExpenses;
  final double netBalance;
  final double averageIncome;
  final double averageExpense;
  final double averageTransactionAmount;

  // Distributions
  final Map<String, double> categoryBreakdown;
  final Map<String, double> paymentMethodBreakdown;
  final Map<String, int> categoryFrequency;
  final Map<String, int> paymentMethodFrequency;

  // Time-based metrics
  final DateTime? firstTransactionDate;
  final DateTime? lastTransactionDate;
  final Map<String, double> monthlyIncome;
  final Map<String, double> monthlyExpenses;
  final Map<String, double> monthlyNetBalance;

  // Performance insights
  final String topIncomeCategory;
  final double topIncomeCategoryAmount;
  final String topExpenseCategory;
  final double topExpenseCategoryAmount;
  final String mostUsedPaymentMethod;
  final int mostUsedPaymentMethodCount;

  // Financial health indicators
  final double incomeToExpenseRatio;
  final double savingsRate; // (Income - Expenses) / Income * 100
  final double expenseVariability; // Coefficient of variation for expenses
  final double incomeVariability; // Coefficient of variation for income

  // Trends
  final double incomeTrend; // Positive/negative trend
  final double expenseTrend; // Positive/negative trend
  final int daysWithTransactions;

  const TransactionAnalyticsResult({
    required this.totalTransactions,
    required this.incomeTransactions,
    required this.expenseTransactions,
    required this.totalIncome,
    required this.totalExpenses,
    required this.netBalance,
    required this.averageIncome,
    required this.averageExpense,
    required this.averageTransactionAmount,
    required this.categoryBreakdown,
    required this.paymentMethodBreakdown,
    required this.categoryFrequency,
    required this.paymentMethodFrequency,
    required this.firstTransactionDate,
    required this.lastTransactionDate,
    required this.monthlyIncome,
    required this.monthlyExpenses,
    required this.monthlyNetBalance,
    required this.topIncomeCategory,
    required this.topIncomeCategoryAmount,
    required this.topExpenseCategory,
    required this.topExpenseCategoryAmount,
    required this.mostUsedPaymentMethod,
    required this.mostUsedPaymentMethodCount,
    required this.incomeToExpenseRatio,
    required this.savingsRate,
    required this.expenseVariability,
    required this.incomeVariability,
    required this.incomeTrend,
    required this.expenseTrend,
    required this.daysWithTransactions,
  });

  /// Empty result for when there's no data
  static const empty = TransactionAnalyticsResult(
    totalTransactions: 0,
    incomeTransactions: 0,
    expenseTransactions: 0,
    totalIncome: 0.0,
    totalExpenses: 0.0,
    netBalance: 0.0,
    averageIncome: 0.0,
    averageExpense: 0.0,
    averageTransactionAmount: 0.0,
    categoryBreakdown: {},
    paymentMethodBreakdown: {},
    categoryFrequency: {},
    paymentMethodFrequency: {},
    firstTransactionDate: null,
    lastTransactionDate: null,
    monthlyIncome: {},
    monthlyExpenses: {},
    monthlyNetBalance: {},
    topIncomeCategory: '',
    topIncomeCategoryAmount: 0.0,
    topExpenseCategory: '',
    topExpenseCategoryAmount: 0.0,
    mostUsedPaymentMethod: '',
    mostUsedPaymentMethodCount: 0,
    incomeToExpenseRatio: 0.0,
    savingsRate: 0.0,
    expenseVariability: 0.0,
    incomeVariability: 0.0,
    incomeTrend: 0.0,
    expenseTrend: 0.0,
    daysWithTransactions: 0,
  );
}

class TransactionInsightsData {
  final String topIncomeCategory;
  final String topExpenseCategory;
  final String mostUsedPaymentMethod;
  final List<String> recommendations;
  final Map<String, Map<String, double>> monthlyTrends;
  final double monthlyAverageIncome;
  final double monthlyAverageExpense;

  TransactionInsightsData({
    this.topIncomeCategory = 'N/A',
    this.topExpenseCategory = 'N/A',
    this.mostUsedPaymentMethod = 'N/A',
    this.recommendations = const [],
    this.monthlyTrends = const {},
    this.monthlyAverageIncome = 0.0,
    this.monthlyAverageExpense = 0.0,
  });
}
