import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/notification_center_controller.dart';
import '../widgets/notification_card.dart';
import '../widgets/notification_filter_bar.dart';
import '../models/notification_status.dart';
import '../../../constants/app_colors.dart';

/// Screen for displaying and managing notifications
class NotificationCenterScreen extends StatefulWidget {
  const NotificationCenterScreen({Key? key}) : super(key: key);

  @override
  State<NotificationCenterScreen> createState() => _NotificationCenterScreenState();
}

class _NotificationCenterScreenState extends State<NotificationCenterScreen> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    
    // Initialize controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationCenterController>().initialize();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      context.read<NotificationCenterController>().loadMoreNotifications();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          Consumer<NotificationCenterController>(
            builder: (context, controller, child) {
              if (controller.isSelectionMode) {
                return Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.select_all),
                      onPressed: controller.selectAll,
                      tooltip: 'Select All',
                    ),
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: controller.clearSelection,
                      tooltip: 'Clear Selection',
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) => _handleBulkAction(context, value),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'mark_read',
                          child: Text('Mark as Read'),
                        ),
                        const PopupMenuItem(
                          value: 'archive',
                          child: Text('Archive'),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('Delete'),
                        ),
                      ],
                    ),
                  ],
                );
              }
              
              return Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.checklist),
                    onPressed: controller.toggleSelectionMode,
                    tooltip: 'Select Mode',
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: controller.refreshNotifications,
                    tooltip: 'Refresh',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<NotificationCenterController>(
        builder: (context, controller, child) {
          if (controller.isLoading && controller.notifications.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: controller.refreshNotifications,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Filter bar
              NotificationFilterBar(
                selectedCategory: controller.selectedCategory,
                selectedStatus: controller.selectedStatus,
                selectedPriority: controller.selectedPriority,
                searchQuery: controller.searchQuery,
                onCategoryChanged: controller.filterByCategory,
                onStatusChanged: controller.filterByStatus,
                onPriorityChanged: controller.filterByPriority,
                onSearchChanged: controller.search,
                onClearFilters: controller.clearAllFilters,
              ),
              
              // Notifications list
              Expanded(
                child: controller.notifications.isEmpty
                    ? _buildEmptyState()
                    : _buildNotificationsList(controller),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: AppColors.notificationStatusColors['read']!.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.notificationStatusColors['read']!.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'re all caught up!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.notificationStatusColors['read']!.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(NotificationCenterController controller) {
    return RefreshIndicator(
      onRefresh: controller.refreshNotifications,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(8),
        itemCount: controller.notifications.length + (controller.hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= controller.notifications.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final notification = controller.notifications[index];
          return NotificationCard(
            notification: notification,
            isSelected: controller.selectedNotificationIds.contains(notification.businessId),
            isSelectionMode: controller.isSelectionMode,
            onTap: () => _handleNotificationTap(context, notification),
            onSelect: () => controller.selectNotification(notification.businessId!),
            onMarkAsRead: () => _markAsRead(context, notification.businessId!),
            onArchive: () => _archiveNotification(context, notification.businessId!),
            onDelete: () => _deleteNotification(context, notification.businessId!),
          );
        },
      ),
    );
  }

  void _handleNotificationTap(BuildContext context, notification) {
    final controller = context.read<NotificationCenterController>();
    
    if (controller.isSelectionMode) {
      controller.selectNotification(notification.businessId!);
    } else {
      // Navigate to related screen or mark as read
      if (notification.status == NotificationStatus.unread) {
        _markAsRead(context, notification.businessId!);
      }
      
      // Handle deep linking based on actionUrl or related record
      if (notification.actionUrl != null) {
        // Navigate to specific screen
        // Navigator.pushNamed(context, notification.actionUrl!);
      }
    }
  }

  void _markAsRead(BuildContext context, String notificationId) {
    context.read<NotificationCenterController>().markSelectedAsRead();
  }

  void _archiveNotification(BuildContext context, String notificationId) {
    context.read<NotificationCenterController>().archiveSelected();
  }

  void _deleteNotification(BuildContext context, String notificationId) {
    _showDeleteConfirmation(context, [notificationId]);
  }

  void _handleBulkAction(BuildContext context, String action) {
    final controller = context.read<NotificationCenterController>();
    
    switch (action) {
      case 'mark_read':
        controller.markSelectedAsRead();
        break;
      case 'archive':
        controller.archiveSelected();
        break;
      case 'delete':
        _showDeleteConfirmation(context, controller.selectedNotificationIds.toList());
        break;
    }
  }

  void _showDeleteConfirmation(BuildContext context, List<String> notificationIds) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notifications'),
        content: Text(
          notificationIds.length == 1
              ? 'Are you sure you want to delete this notification?'
              : 'Are you sure you want to delete ${notificationIds.length} notifications?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (notificationIds.length == 1) {
                // Delete single notification
                // Implementation would go here
              } else {
                context.read<NotificationCenterController>().deleteSelected();
              }
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}