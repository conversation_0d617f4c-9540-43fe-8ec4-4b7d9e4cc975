import 'package:flutter/foundation.dart';

/// API configuration for the Cattle Manager App
/// This file contains API endpoints and sync settings
class ApiConfig {
  // API Configuration
  static const String appName = 'Cattle Manager App';
  static const String apiVersion = 'v1';
  
  // Environment-based configuration
  static const bool isProduction = false; // Set to true for production builds
  static const bool enableApiSync = false; // DISABLED - Set to true when API endpoints are available and working
  static const bool enableDemoApiMode = true; // Enable demo/mock API for testing

  // API Base URLs
  static const String productionApiUrl = 'https://api.cattlemanager.com/v1';
  static const String developmentApiUrl = 'https://dev-api.cattlemanager.com/v1';
  static const String localApiUrl = 'http://localhost:3000/api/v1';
  static const String demoApiUrl = 'https://demo-api.cattlemanager.com/v1'; // Mock API for demonstration
  
  // Get the appropriate API base URL based on environment
  static String get apiBaseUrl {
    if (!enableApiSync) {
      return ''; // Return empty string when sync is disabled
    }

    if (enableDemoApiMode) {
      return demoApiUrl; // Use demo API for testing
    } else if (isProduction) {
      return productionApiUrl;
    } else {
      return developmentApiUrl; // Use development URL for testing
    }
  }
  
  // API Endpoints
  static String get cattleSync => '$apiBaseUrl/cattle/sync';
  static String get healthSync => '$apiBaseUrl/health/sync';
  static String get breedingSync => '$apiBaseUrl/breeding/sync';
  static String get weightSync => '$apiBaseUrl/weight/sync';
  static String get eventsSync => '$apiBaseUrl/events/sync';
  static String get transactionsSync => '$apiBaseUrl/transactions/sync';
  static String get milkSync => '$apiBaseUrl/milk/sync';
  
  // Sync Configuration
  static const Duration syncTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // Local-only mode settings
  static const bool enableLocalOnlyMode = true; // Always allow local-only operation
  static const String localOnlyMessage = 'Operating in local-only mode. Data sync is disabled.';
  
  // API Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': '$appName/$apiVersion',
  };
  
  // Check if API sync is available and enabled
  static bool get isApiSyncAvailable {
    final result = enableApiSync && apiBaseUrl.isNotEmpty;
    debugPrint('🔍 [API_CONFIG] isApiSyncAvailable check:');
    debugPrint('🔍 [API_CONFIG] enableApiSync: $enableApiSync');
    debugPrint('🔍 [API_CONFIG] apiBaseUrl: "$apiBaseUrl"');
    debugPrint('🔍 [API_CONFIG] result: $result');
    return result;
  }
  
  // Get sync status message
  static String get syncStatusMessage {
    if (!enableApiSync) {
      return localOnlyMessage;
    } else if (enableDemoApiMode) {
      return 'Connected to demo API (mock data)';
    } else if (isProduction) {
      return 'Connected to production API';
    } else {
      return 'Connected to development API';
    }
  }

  // Get detailed API configuration info
  static Map<String, dynamic> get apiConfigInfo {
    return {
      'enableApiSync': enableApiSync,
      'enableDemoApiMode': enableDemoApiMode,
      'isProduction': isProduction,
      'currentApiUrl': apiBaseUrl,
      'isApiSyncAvailable': isApiSyncAvailable,
      'syncStatusMessage': syncStatusMessage,
      'environment': _getCurrentEnvironment(),
    };
  }

  static String _getCurrentEnvironment() {
    if (!enableApiSync) return 'local-only';
    if (enableDemoApiMode) return 'demo';
    if (isProduction) return 'production';
    return 'development';
  }
  
  // Validate API configuration
  static bool validateConfig() {
    if (!enableApiSync) {
      return true; // Local-only mode is always valid
    }
    
    return apiBaseUrl.isNotEmpty && 
           apiBaseUrl.startsWith('http') &&
           syncTimeout.inSeconds > 0 &&
           maxRetryAttempts > 0;
  }
}
