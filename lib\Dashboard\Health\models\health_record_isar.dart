import 'package:isar/isar.dart';

part 'health_record_isar.g.dart';

@collection
class HealthRecordIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? cattleTagId;

  String? recordType;

  String? details;

  String? diagnosis;

  String? treatment;

  String? medicine;

  double? dose;

  String? dosageUnit;

  @Index()
  DateTime? date;

  bool? followUpRequired;

  DateTime? followUpDate;

  bool? isFollowUp;

  bool? isResolved;

  String? previousRecordId;

  String? condition;

  String? veterinarian;

  double? cost;

  String? notes;

  // Additional properties for compatibility
  String? status;

  String? description;

  String? session;

  DateTime? createdAt;

  DateTime? updatedAt;

  // Backward compatibility getters and setters for Cattle model
  String? get recordId => businessId;
  set recordId(String? value) => businessId = value;

  String? get cattleId => cattleTagId;
  set cattleId(String? value) => cattleTagId = value;

  String? get cattleBusinessId => cattleTagId;
  set cattleBusinessId(String? value) => cattleTagId = value;

  HealthRecordIsar();

  /// Generate a formatted ID for health records in the format 'cattleTagId-Health-sequenceNumber'
  static String generateFormattedId(String cattleTagId, int sequenceNumber) {
    return '$cattleTagId-Health-$sequenceNumber';
  }

  /// Generate a deterministic record ID for health records to ensure consistency
  /// across app reinstallations. Based on cattle tag ID, date, and record type.
  static String generateRecordId(
      String cattleTagId, DateTime date, String recordType) {
    // Format the date in a consistent way (YYYYMMDD)
    final dateStr =
        "$date.year${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}";

    // Normalize record type (lowercase, remove spaces)
    final normalizedType = recordType.toLowerCase().replaceAll(' ', '_');

    // Create a unique ID combining cattle tag ID, date and record type
    final uniqueKey = "$cattleTagId-$dateStr-$normalizedType";

    // Return with a prefix to distinguish health records
    return "health_$uniqueKey";
  }

  factory HealthRecordIsar.create({
    String? businessId,
    required String cattleTagId,
    String? recordType = 'general',
    String? details,
    String? diagnosis,
    required String treatment,
    String? medicine,
    double? dose,
    String? dosageUnit,
    required DateTime date,
    bool followUpRequired = false,
    DateTime? followUpDate,
    bool isFollowUp = false,
    bool isResolved = false,
    String? previousRecordId,
    String? condition,
    String? veterinarian,
    double? cost,
    String? notes,
    String? status,
    String? description,
    String? session,
  }) {
    return HealthRecordIsar()
      ..businessId =
          businessId ?? generateRecordId(cattleTagId, date, recordType ?? 'general')
      ..cattleTagId = cattleTagId
      ..recordType = recordType
      ..details = details
      ..diagnosis = diagnosis
      ..treatment = treatment
      ..medicine = medicine
      ..dose = dose
      ..dosageUnit = dosageUnit
      ..date = date
      ..followUpRequired = followUpRequired
      ..followUpDate = followUpDate
      ..isFollowUp = isFollowUp
      ..isResolved = isResolved
      ..previousRecordId = previousRecordId
      ..condition = condition
      ..veterinarian = veterinarian
      ..cost = cost
      ..notes = notes
      ..status = status ?? (isResolved ? 'resolved' : 'active')
      ..description = description ?? details
      ..session = session
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  // Factory method for backward compatibility with Cattle model
  factory HealthRecordIsar.fromCattleModel({
    required String cattleTagId,
    required DateTime date,
    required String condition,
    required String treatment,
    String notes = '',
    String veterinarian = '',
    double cost = 0.0,
  }) {
    return HealthRecordIsar()
      ..businessId = generateRecordId(cattleTagId, date, 'general')
      ..cattleTagId = cattleTagId
      ..date = date
      ..condition = condition
      ..treatment = treatment
      ..notes = notes
      ..veterinarian = veterinarian
      ..cost = cost
      ..recordType = 'general'
      ..isResolved = false
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleTagId': cattleTagId,
      'recordType': recordType,
      'details': details,
      'diagnosis': diagnosis,
      'treatment': treatment,
      'medicine': medicine,
      'dose': dose,
      'dosageUnit': dosageUnit,
      'date': date?.toIso8601String(),
      'followUpRequired': followUpRequired,
      'followUpDate': followUpDate?.toIso8601String(),
      'isFollowUp': isFollowUp,
      'isResolved': isResolved,
      'previousRecordId': previousRecordId,
      'condition': condition,
      'veterinarian': veterinarian,
      'cost': cost,
      'notes': notes,
      'status': status,
      'description': description,
      'session': session,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // For backward compatibility with Cattle model
  Map<String, dynamic> toCattleMap() {
    return {
      'id': businessId,
      'cattleTagId': cattleTagId,
      'date': date?.toIso8601String(),
      'condition': condition,
      'treatment': treatment,
      'notes': notes,
      'veterinarian': veterinarian,
      'cost': cost,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory HealthRecordIsar.fromMap(Map<String, dynamic> map) {
    return HealthRecordIsar()
      ..businessId = map['id'] as String? ?? map['businessId'] as String? ?? map['recordId'] as String?
      ..cattleTagId =
          map['cattleTagId'] as String? ?? map['cattleId'] as String? ?? map['cattleBusinessId'] as String?
      ..recordType = map['recordType'] as String?
      ..details = map['details'] as String?
      ..diagnosis = map['diagnosis'] as String?
      ..treatment = map['treatment'] as String?
      ..medicine = map['medicine'] as String?
      ..dose = map['dose'] != null ? double.parse(map['dose'].toString()) : null
      ..dosageUnit = map['dosageUnit'] as String?
      ..date =
          map['date'] != null ? DateTime.parse(map['date'].toString()) : null
      ..followUpRequired = map['followUpRequired'] as bool?
      ..followUpDate = map['followUpDate'] != null
          ? DateTime.parse(map['followUpDate'].toString())
          : null
      ..isFollowUp = map['isFollowUp'] as bool?
      ..isResolved = map['isResolved'] as bool?
      ..previousRecordId = map['previousRecordId'] as String?
      ..condition = map['condition'] as String?
      ..veterinarian = map['veterinarian'] as String?
      ..cost = map['cost'] != null ? double.parse(map['cost'].toString()) : null
      ..notes = map['notes'] as String?
      ..status = map['status'] as String?
      ..description = map['description'] as String?
      ..session = map['session'] as String?
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'].toString())
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'].toString())
          : DateTime.now();
  }

  factory HealthRecordIsar.fromJson(Map<String, dynamic> json) =>
      HealthRecordIsar.fromMap(json);

  HealthRecordIsar copyWith({
    String? businessId,
    String? cattleTagId,
    String? recordType,
    String? details,
    String? diagnosis,
    String? treatment,
    String? medicine,
    double? dose,
    String? dosageUnit,
    DateTime? date,
    bool? followUpRequired,
    DateTime? followUpDate,
    bool? isFollowUp,
    bool? isResolved,
    String? previousRecordId,
    String? condition,
    String? veterinarian,
    double? cost,
    String? notes,
    String? status,
    String? description,
    String? session,
  }) {
    return HealthRecordIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..cattleTagId = cattleTagId ?? this.cattleTagId
      ..recordType = recordType ?? this.recordType
      ..details = details ?? this.details
      ..diagnosis = diagnosis ?? this.diagnosis
      ..treatment = treatment ?? this.treatment
      ..medicine = medicine ?? this.medicine
      ..dose = dose ?? this.dose
      ..dosageUnit = dosageUnit ?? this.dosageUnit
      ..date = date ?? this.date
      ..followUpRequired = followUpRequired ?? this.followUpRequired
      ..followUpDate = followUpDate ?? this.followUpDate
      ..isFollowUp = isFollowUp ?? this.isFollowUp
      ..isResolved = isResolved ?? this.isResolved
      ..previousRecordId = previousRecordId ?? this.previousRecordId
      ..condition = condition ?? this.condition
      ..veterinarian = veterinarian ?? this.veterinarian
      ..cost = cost ?? this.cost
      ..notes = notes ?? this.notes
      ..status = status ?? this.status
      ..description = description ?? this.description
      ..session = session ?? this.session
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }
}
