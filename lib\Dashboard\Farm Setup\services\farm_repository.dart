import 'package:isar/isar.dart';
import '../models/farm_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Farm database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class FarmRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  FarmRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE FARM STREAMS ===//

  /// Watches all farms with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<FarmIsar>> watchAllFarms() {
    return _isar.farmIsars.where().watch(fireImmediately: true);
  }

  //=== FARM CRUD ===//

  /// Save (add or update) a farm using Isar's native upsert
  Future<void> saveFarm(FarmIsar farm) async {
    await _isar.writeTxn(() async {
      await _isar.farmIsars.put(farm);
    });
  }

  /// Delete a farm by its Isar ID
  Future<void> deleteFarm(int id) async {
    await _isar.writeTxn(() async {
      await _isar.farmIsars.delete(id);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all farms (for analytics and report generation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<FarmIsar>> getAllFarms() async {
    return await _isar.farmIsars.where().findAll();
  }

  /// Get farm by business ID (for validation and navigation)
  /// Returns a Future<FarmIsar?> for one-time data fetching
  Future<FarmIsar?> getFarmByBusinessId(String businessId) async {
    return await _isar.farmIsars.getByFarmBusinessId(businessId);
  }

  /// Get the current farm (single farm per user)
  Future<FarmIsar?> getCurrentFarm() async {
    return await _isar.farmIsars.where().findFirst();
  }

  //=== BACKWARD COMPATIBILITY METHODS ===//

  /// Add farm - alias for saveFarm for backward compatibility
  Future<FarmIsar> addFarm(FarmIsar farm) async {
    await saveFarm(farm);
    return farm;
  }

  /// Update farm - alias for saveFarm for backward compatibility
  Future<void> updateFarm(FarmIsar farm) async {
    await saveFarm(farm);
  }
}
