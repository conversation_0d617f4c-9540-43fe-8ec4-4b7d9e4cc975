import 'package:isar/isar.dart';
import '../models/breed_category_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Breed Category database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class BreedCategoryRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  BreedCategoryRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE BREED CATEGORY STREAMS ===//

  /// Watches all breed categories with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<BreedCategoryIsar>> watchAllBreedCategories() {
    return _isar.breedCategoryIsars.where().watch(fireImmediately: true);
  }

  //=== BREED CATEGORY CRUD ===//

  /// Save (add or update) a breed category using <PERSON><PERSON>'s native upsert
  Future<void> saveBreedCategory(BreedCategoryIsar category) async {
    await _isar.writeTxn(() async {
      await _isar.breedCategoryIsars.put(category);
    });
  }

  /// Delete a breed category by its Isar ID
  Future<void> deleteBreedCategory(int id) async {
    await _isar.writeTxn(() async {
      await _isar.breedCategoryIsars.delete(id);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all breed categories (for analytics and report generation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<BreedCategoryIsar>> getAllBreedCategories() async {
    return await _isar.breedCategoryIsars.where().findAll();
  }

  /// Get breed category by business ID (for validation and navigation)
  /// Returns a Future<BreedCategoryIsar?> for one-time data fetching
  Future<BreedCategoryIsar?> getBreedCategoryByBusinessId(String businessId) async {
    return await _isar.breedCategoryIsars.getByBusinessId(businessId);
  }

  /// Get breed categories by farm ID (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<BreedCategoryIsar>> getBreedCategoriesByFarmId(String farmId) async {
    return await _isar.breedCategoryIsars
        .filter()
        .farmBusinessIdEqualTo(farmId)
        .findAll();
  }

  //=== BACKWARD COMPATIBILITY METHODS ===//

  /// Add breed category - alias for saveBreedCategory for backward compatibility
  Future<void> addBreedCategory(BreedCategoryIsar category) async {
    await saveBreedCategory(category);
  }

  /// Update breed category - alias for saveBreedCategory for backward compatibility
  Future<void> updateBreedCategory(BreedCategoryIsar category) async {
    await saveBreedCategory(category);
  }
}
