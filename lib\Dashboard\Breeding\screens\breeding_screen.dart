import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import '../controllers/breeding_controller.dart';
import '../dialogs/breeding_form_dialog.dart';
import '../dialogs/pregnancy_form_dialog.dart';
import '../dialogs/delivery_form_dialog.dart';
import '../tabs/breeding_analytics_tab.dart';
import '../tabs/breeding_records_tab.dart';
import '../tabs/pregnancy_records_tab.dart';
import '../tabs/delivery_records_tab.dart';
import '../tabs/breeding_insights_tab.dart';
import '../services/breeding_insights_service.dart';
import '../../../routes/app_routes.dart';
import '../../../utils/message_utils.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum
import '../../User Account/guards/demo_guard.dart';

 // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart';

/// Breeding screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class BreedingScreen extends StatelessWidget {
  const BreedingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BreedingController(),
      child: const _BreedingScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _BreedingScreenContent extends StatefulWidget {
  const _BreedingScreenContent();

  @override
  State<_BreedingScreenContent> createState() => _BreedingScreenContentState();
}

class _BreedingScreenContentState extends State<_BreedingScreenContent>
    with TickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 5,
      vsync: this,
      initialIndex: 0, // Explicitly start at leftmost tab (Analytics)
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Real-time loading with automatic dialog opening when ready
  void _showLoadingAndWaitForData(BreedingController controller, String dialogType) {
    // Show immediate loading feedback
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('Loading cattle data...'),
          ],
        ),
        duration: Duration(milliseconds: 500), // Very short - real-time feel
      ),
    );

    // Listen for state changes and auto-open dialog when ready
    void listener() {
      if (!mounted) return;

      if (controller.state == ControllerState.loaded) {
        controller.removeListener(listener);

        // Data is ready - check if we have cattle and open dialog
        if (controller.femaleCattleList.isNotEmpty) {
          switch (dialogType) {
            case 'breeding':
              _openBreedingDialog(controller);
              break;
            case 'pregnancy':
              _openPregnancyDialog(controller);
              break;
            case 'delivery':
              _openDeliveryDialog(controller);
              break;
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No active female cattle available for breeding records.'),
              duration: Duration(milliseconds: 1000),
            ),
          );
        }
      }
    }

    controller.addListener(listener);

    // Safety timeout - remove listener after 5 seconds if still loading
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        controller.removeListener(listener);
      }
    });
  }

  void _showAddBreedingDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_breeding_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_breeding_record');
      return;
    }

    final breedingController = context.read<BreedingController>();

    // Real-time check - immediate response
    if (breedingController.state == ControllerState.loading) {
      _showLoadingAndWaitForData(breedingController, 'breeding');
      return;
    }

    if (breedingController.femaleCattleList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No active female cattle available for breeding records.'),
          duration: Duration(milliseconds: 1000), // Quick feedback
        ),
      );
      return;
    }

    _openBreedingDialog(breedingController);
  }

  void _openBreedingDialog(BreedingController breedingController) {
    showDialog(
      context: context,
      builder: (context) => BreedingFormDialog(
        preloadedCattle: breedingController.femaleCattleList, // Fixed: Use femaleCattleList instead of cattle
        preloadedAnimalTypes: breedingController.animalTypesMap, // Fixed: Add missing animal types
        onSave: (record) async {
          try {
            debugPrint('🏠 BREEDING SCREEN: Received breeding record from form dialog');
            debugPrint('   Record ID: $record.businessId');
            debugPrint('   Cattle ID: $record.cattleId');

            await breedingController.addBreedingRecord(record);
            debugPrint('✅ BREEDING SCREEN: Breeding record added to controller');

            if (!mounted) return;

            // Use standardized success message
            if (context.mounted) {
              BreedingMessageUtils.showSuccess(context,
                  BreedingMessageUtils.breedingRecordCreated());
            }

          } catch (e) {
            debugPrint('Error adding breeding record: $e');

            if (!mounted) return;

            // Use standardized error message
            if (context.mounted) {
              BreedingMessageUtils.showError(context, 'Error adding breeding record');
            }
          }
        },
      ),
    );
  }

  void _showAddPregnancyDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_breeding_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_breeding_record');
      return;
    }

    final breedingController = context.read<BreedingController>();

    // Real-time check - immediate response
    if (breedingController.state == ControllerState.loading) {
      _showLoadingAndWaitForData(breedingController, 'pregnancy');
      return;
    }

    if (breedingController.femaleCattleList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No active female cattle available for pregnancy records.'),
          duration: Duration(milliseconds: 1000), // Quick feedback
        ),
      );
      return;
    }

    _openPregnancyDialog(breedingController);
  }

  void _openPregnancyDialog(BreedingController breedingController) {
    showDialog(
      context: context,
      builder: (context) => PregnancyFormDialog(
        preloadedCattle: breedingController.femaleCattleList, // Fixed: Use femaleCattleList instead of cattle
        preloadedAnimalTypes: breedingController.animalTypesMap, // Fixed: Add missing animal types
        onSave: (record) async {
          try {
            await breedingController.addPregnancyRecord(record);

            if (!mounted) return;

            // Use standardized success message
            if (context.mounted) {
              BreedingMessageUtils.showSuccess(context,
                  BreedingMessageUtils.pregnancyRecordCreated());
            }

          } catch (e) {
            debugPrint('Error adding pregnancy record: $e');

            if (!mounted) return;

            // Use standardized error message
            if (context.mounted) {
              BreedingMessageUtils.showError(context, 'Error adding pregnancy record');
            }
          }
        },
      ),
    );
  }

  void _showAddDeliveryDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_breeding_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_breeding_record');
      return;
    }

    final breedingController = context.read<BreedingController>();

    // Real-time check - immediate response
    if (breedingController.state == ControllerState.loading) {
      _showLoadingAndWaitForData(breedingController, 'delivery');
      return;
    }

    if (breedingController.femaleCattleList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No active female cattle available for delivery records.'),
          duration: Duration(milliseconds: 1000), // Quick feedback
        ),
      );
      return;
    }

    _openDeliveryDialog(breedingController);
  }

  void _openDeliveryDialog(BreedingController breedingController) {
    showDialog(
      context: context,
      builder: (context) => DeliveryFormDialog(
        existingCattle: breedingController.femaleCattleList, // Fixed: Use femaleCattleList instead of cattle
        onSave: (record) async {
          try {
            await breedingController.addDeliveryRecord(record);

            if (!mounted) return;

            // Use standardized success message
            if (context.mounted) {
              BreedingMessageUtils.showSuccess(context,
                  BreedingMessageUtils.deliveryRecordCreated());
            }

          } catch (e) {
            debugPrint('Error adding delivery record: $e');

            if (!mounted) return;

            // Use standardized error message
            if (context.mounted) {
              BreedingMessageUtils.showError(context, 'Error adding delivery record');
            }
          }
        },
      ),
    );
  }

  void _getCurrentTabAction() {
    switch (_tabController.index) {
      case 0:
        // Analytics tab - no FAB action
        break;
      case 1:
        _showAddBreedingDialog();
        break;
      case 2:
        _showAddPregnancyDialog();
        break;
      case 3:
        _showAddDeliveryDialog();
        break;
      case 4:
        // Insights tab - no FAB action
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Breeding Management',
      body: Consumer<BreedingController>(
        builder: (context, breedingController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.fiveTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => BreedingAnalyticsTab(controller: breedingController), // Analytics tab
              ),
              Builder(
                builder: (context) => const BreedingRecordsTab(), // Uses Provider pattern
              ),
              Builder(
                builder: (context) => PregnancyRecordsTab(controller: breedingController), // Pregnancy Records tab
              ),
              Builder(
                builder: (context) => DeliveryRecordsTab(controller: breedingController), // Delivery Records tab
              ),
              Builder(
                builder: (context) => BreedingInsightsTab(
                  controller: breedingController,
                  insightsService: GetIt.instance<BreedingInsightsService>(),
                ), // Insights tab
              ),
            ],
            labels: const ['Analytics', 'Breeding', 'Pregnancy', 'Delivery', 'Insights'],
            icons: const [Icons.analytics, Icons.favorite, Icons.pregnant_woman, Icons.child_care, Icons.lightbulb],
            showFABs: const [false, true, true, true, false], // FAB disabled for Analytics and Insights
          );

          // Handle different states
          if (breedingController.state == ControllerState.loading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (breedingController.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    breedingController.errorMessage ?? 'Failed to load breeding data',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {}, // No manual retry needed - reactive streams auto-recover
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return _tabManager!;
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.breedingReport,
          ),
          tooltip: 'View Breeding Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _getCurrentTabAction,
            tooltip: 'Add Record',
            backgroundColor: AppColors.breedingHeader,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}
