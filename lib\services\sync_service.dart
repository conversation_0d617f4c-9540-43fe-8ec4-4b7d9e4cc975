import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:uuid/uuid.dart';
import '../config/api_config.dart';
import '../Dashboard/Milk Records/services/milk_service.dart';
import '../Dashboard/Farm Setup/services/cloud_backup_service.dart';
import '../Dashboard/Farm Setup/services/farm_setup_repository.dart';
import '../Dashboard/Farm Setup/models/farm_isar.dart';
import '../Dashboard/Cattle/services/cattle_sync_service.dart';
import '../Dashboard/Health/services/health_sync_service.dart';
import '../Dashboard/Breeding/services/breeding_sync_service.dart';
import '../Dashboard/Weight/services/weight_sync_service.dart';

import '../Dashboard/Transactions/services/transactions_sync_service.dart';

/// Comprehensive sync service for all farm data modules
/// 
/// This service handles bidirectional synchronization across all major modules:
/// - Milk Records (external API sync)
/// - Cattle Records (bidirectional API sync)
/// - Health Records (bidirectional API sync)
/// - Breeding Records (bidirectional API sync)
/// - Weight Records (bidirectional API sync)
/// - Events (bidirectional API sync)
/// - Transactions (bidirectional API sync)
/// - Cloud Backup (Google Drive integration)
/// 
/// Features:
/// - Network connectivity checking
/// - Bidirectional data synchronization
/// - Conflict resolution
/// - Incremental sync using timestamps
/// - Graceful error handling
/// - Progress tracking
/// - Comprehensive result reporting
class SyncService {
  final MilkService _milkService = MilkService();
  final CloudBackupService _cloudBackupService = GetIt.instance<CloudBackupService>();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  
  // Dedicated bidirectional sync services for consistent architecture
  final CattleSyncService _cattleSyncService = CattleSyncService();
  final HealthSyncService _healthSyncService = HealthSyncService();
  final BreedingSyncService _breedingSyncService = BreedingSyncService();
  final WeightSyncService _weightSyncService = WeightSyncService();

  final TransactionsSyncService _transactionsSyncService = TransactionsSyncService();

  /// Check network connectivity
  Future<bool> checkConnectivity() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      if (connectivityResults.contains(ConnectivityResult.none) || connectivityResults.isEmpty) {
        return false;
      }
      
      // Additional check by trying to reach a reliable server
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      debugPrint('Connectivity check failed: $e');
      return false;
    }
  }

  /// Comprehensive bidirectional sync functionality for all modules
  Future<SyncResult> syncAllData(FarmIsar? currentFarm) async {
    debugPrint('🔄 [SYNC_SERVICE_DEBUG] syncAllData() called with farm: ${currentFarm?.name ?? 'null'} (ID: ${currentFarm?.farmBusinessId ?? 'null'})');

    final List<String> syncResults = [];
    bool hasErrors = false;

    try {
      // STEP 1: Ensure farm exists before any sync operations
      debugPrint('🔄 [SYNC_SERVICE_DEBUG] Step 1: Checking farm existence...');
      FarmIsar? workingFarm = currentFarm;

      if (workingFarm == null) {
        debugPrint('⚠️ [SYNC_SERVICE_DEBUG] No farm provided, attempting to get current farm...');
        try {
          workingFarm = await _farmSetupRepository.getCurrentFarm();
          if (workingFarm != null) {
            debugPrint('✅ [SYNC_SERVICE_DEBUG] Found existing farm: $workingFarm.name (ID: $workingFarm.farmBusinessId)');
          }
        } catch (e) {
          debugPrint('⚠️ [SYNC_SERVICE_DEBUG] No current farm found: $e');
        }
      }

      // If still no farm, create a default one
      if (workingFarm == null) {
        debugPrint('🏠 [SYNC_SERVICE_DEBUG] No farm exists, creating default farm for sync operations...');
        try {
          final defaultFarm = FarmIsar.create(
            id: const Uuid().v4(),
            name: 'My Farm',
            ownerName: 'Farm Owner',
            ownerContact: '+1234567890',
            ownerEmail: '<EMAIL>',
            farmType: FarmType.dairy,
            cattleCount: 0,
            capacity: 100,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          workingFarm = await _farmSetupRepository.addFarm(defaultFarm);
          debugPrint('✅ [SYNC_SERVICE_DEBUG] Created default farm: $workingFarm.name (ID: $workingFarm.farmBusinessId)');
          syncResults.add('✓ Created default farm for sync operations');
        } catch (e) {
          debugPrint('❌ [SYNC_SERVICE_DEBUG] Failed to create default farm: $e');
          syncResults.add('✗ Failed to create farm: ${e.toString()}');
          hasErrors = true;
          return SyncResult(
            success: false,
            results: syncResults,
            hasWarnings: false,
          );
        }
      }

      debugPrint('✅ [SYNC_SERVICE_DEBUG] Using farm for sync: $workingFarm.name (ID: $workingFarm.farmBusinessId)');

      // Check if API sync is enabled
      debugPrint('🔍 [SYNC_SERVICE] API Sync Debug:');
      debugPrint('🔍 [SYNC_SERVICE] enableApiSync: $ApiConfig.enableApiSync');
      debugPrint('🔍 [SYNC_SERVICE] apiBaseUrl: "$ApiConfig.apiBaseUrl"');
      debugPrint('🔍 [SYNC_SERVICE] isApiSyncAvailable: $ApiConfig.isApiSyncAvailable');

      if (!ApiConfig.isApiSyncAvailable) {
        debugPrint('✅ [SYNC_SERVICE] API sync disabled - returning early');
        syncResults.add('ℹ $ApiConfig.localOnlyMessage');
        syncResults.add('✓ Local data is up to date');

        // Still perform cloud backup if enabled
        try {
          final backupSettings = await _farmSetupRepository.getBackupSettings();
          if (backupSettings.isCloudStorageEnabled) {
            final isAuthenticated = await _cloudBackupService.isAuthenticated(backupSettings.storageProvider);

            if (isAuthenticated) {
              final backupResult = await _cloudBackupService.createCloudBackup(
                workingFarm.farmBusinessId ?? 'default',
                backupSettings.storageProvider,
              );

              if (backupResult.success) {
                syncResults.add('✓ Cloud backup created successfully');
              } else {
                syncResults.add('✗ Cloud backup failed: $backupResult.message');
                hasErrors = true;
              }
            } else {
              syncResults.add('⚠ Cloud backup skipped (not authenticated or no farm)');
            }
          } else {
            syncResults.add('⚠ Cloud backup disabled in settings');
          }
        } catch (e) {
          syncResults.add('✗ Cloud backup error: ${e.toString()}');
          hasErrors = true;
        }

        return SyncResult(
          success: !hasErrors,
          results: syncResults,
          hasWarnings: syncResults.any((result) => result.startsWith('⚠')),
        );
      }

      debugPrint('⚠️ [SYNC_SERVICE] API sync is enabled - proceeding with sync operations');
      // 1. Sync Milk Records (bidirectional API sync)
      try {
        final milkSyncSuccess = await _milkService.syncData();
        if (milkSyncSuccess) {
          syncResults.add('✓ Milk Records synced successfully');
        } else {
          syncResults.add('✗ Milk Records sync failed');
          hasErrors = true;
        }
      } catch (e) {
        syncResults.add('✗ Milk Records sync error: ${e.toString()}');
        hasErrors = true;
      }

      // 2. Sync Cattle Records (bidirectional API sync)
      try {
        final cattleSyncSuccess = await _cattleSyncService.syncData();
        if (cattleSyncSuccess) {
          syncResults.add('✓ Cattle Records synced successfully');
        } else {
          syncResults.add('✗ Cattle Records sync failed');
          hasErrors = true;
        }
      } catch (e) {
        syncResults.add('✗ Cattle Records sync error: ${e.toString()}');
        hasErrors = true;
      }

      // 3. Sync Health Records (bidirectional API sync)
      try {
        final healthSyncSuccess = await _healthSyncService.syncData();
        if (healthSyncSuccess) {
          syncResults.add('✓ Health Records synced successfully');
        } else {
          syncResults.add('✗ Health Records sync failed');
          hasErrors = true;
        }
      } catch (e) {
        syncResults.add('✗ Health Records sync error: ${e.toString()}');
        hasErrors = true;
      }

      // 4. Sync Breeding Records (bidirectional API sync)
      try {
        final breedingSyncSuccess = await _breedingSyncService.syncData();
        if (breedingSyncSuccess) {
          syncResults.add('✓ Breeding Records synced successfully');
        } else {
          syncResults.add('✗ Breeding Records sync failed');
          hasErrors = true;
        }
      } catch (e) {
        syncResults.add('✗ Breeding Records sync error: ${e.toString()}');
        hasErrors = true;
      }

      // 5. Sync Weight Records (bidirectional API sync)
      try {
        final weightSyncSuccess = await _weightSyncService.syncData();
        if (weightSyncSuccess) {
          syncResults.add('✓ Weight Records synced successfully');
        } else {
          syncResults.add('✗ Weight Records sync failed');
          hasErrors = true;
        }
      } catch (e) {
        syncResults.add('✗ Weight Records sync error: ${e.toString()}');
        hasErrors = true;
      }



      // 6. Sync Transactions (bidirectional API sync)
      try {
        final transactionsSyncSuccess = await _transactionsSyncService.syncData();
        if (transactionsSyncSuccess) {
          syncResults.add('✓ Transactions synced successfully');
        } else {
          syncResults.add('✗ Transactions sync failed');
          hasErrors = true;
        }
      } catch (e) {
        syncResults.add('✗ Transactions sync error: ${e.toString()}');
        hasErrors = true;
      }

      // 8. Cloud Backup Sync (if enabled and authenticated)
      try {
        final backupSettings = await _farmSetupRepository.getBackupSettings();
        if (backupSettings.isCloudStorageEnabled) {
          final isAuthenticated = await _cloudBackupService.isAuthenticated(backupSettings.storageProvider);
          
          if (isAuthenticated) {
            final backupResult = await _cloudBackupService.createCloudBackup(
              workingFarm.farmBusinessId ?? 'default',
              backupSettings.storageProvider,
            );
            
            if (backupResult.success) {
              syncResults.add('✓ Cloud backup created successfully');
            } else {
              syncResults.add('✗ Cloud backup failed: $backupResult.message');
              hasErrors = true;
            }
          } else {
            syncResults.add('⚠ Cloud backup skipped (not authenticated or no farm)');
          }
        } else {
          syncResults.add('⚠ Cloud backup disabled in settings');
        }
      } catch (e) {
        syncResults.add('✗ Cloud backup error: ${e.toString()}');
        hasErrors = true;
      }

      return SyncResult(
        success: !hasErrors,
        results: syncResults,
        hasWarnings: syncResults.any((result) => result.startsWith('⚠')),
      );

    } catch (e) {
      debugPrint('Sync error: $e');
      return SyncResult(
        success: false,
        results: ['✗ Sync failed: ${e.toString()}'],
        hasWarnings: false,
      );
    }
  }
}

/// Result of a sync operation
class SyncResult {
  final bool success;
  final List<String> results;
  final bool hasWarnings;

  SyncResult({
    required this.success,
    required this.results,
    required this.hasWarnings,
  });

  /// Get a summary message for the sync result
  String get summaryMessage {
    if (success && !hasWarnings) {
      return 'All data synchronized successfully!';
    } else if (success && hasWarnings) {
      return 'Sync completed with some warnings. Check individual module status.';
    } else {
      return 'Sync completed with errors. Check individual module status.';
    }
  }

  /// Get detailed results as a formatted string
  String get detailedResults {
    return results.join('\n');
  }
}
