import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../../../constants/app_colors.dart';

/// Specialized event card widget for consistent event display
/// This widget provides a more event-specific layout compared to UniversalRecordCard
class EventCard extends StatelessWidget {
  final EventIsar event;
  final String? cattleName;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onComplete;
  final bool compact;

  const EventCard({
    Key? key,
    required this.event,
    this.cattleName,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onComplete,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: compact
          ? const EdgeInsets.symmetric(horizontal: 4, vertical: 4)
          : const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      elevation: 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getBorderColor(),
          width: 1.5,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Container(
          padding: compact
              ? const EdgeInsets.all(8)
              : const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with status indicator and actions
              _buildHeaderRow(),
              const SizedBox(height: 8),
              // Title and category
              _buildTitleRow(),
              const SizedBox(height: 8),
              // Date and cattle info
              _buildInfoRow(),
              // Priority and location (if available)
              if (_shouldShowExtraInfo()) ...[
                const SizedBox(height: 8),
                _buildExtraInfoRow(),
              ],
              // Description/notes (if available)
              if (_hasDescription()) ...[
                const SizedBox(height: 8),
                _buildDescriptionRow(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build header row with status indicator and action menu
  Widget _buildHeaderRow() {
    return Row(
      children: [
        // Status indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getStatusColor(),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getStatusIcon(),
                size: 14,
                color: _getStatusColor(),
              ),
              const SizedBox(width: 4),
              Text(
                event.status.displayName,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: _getStatusColor(),
                ),
              ),
            ],
          ),
        ),
        const Spacer(),
        // Priority indicator (if not medium)
        if (event.priority != EventPriority.medium) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getPriorityColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.priority_high,
                  size: 12,
                  color: _getPriorityColor(),
                ),
                const SizedBox(width: 2),
                Text(
                  event.priority.displayName,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: _getPriorityColor(),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
        ],
        // Action menu
        if (onEdit != null || onDelete != null || onComplete != null)
          _buildActionMenu(),
      ],
    );
  }

  /// Build title row with event title and category
  Widget _buildTitleRow() {
    return Row(
      children: [
        // Category icon
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: _getCategoryColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getCategoryIcon(),
            size: 18,
            color: _getCategoryColor(),
          ),
        ),
        const SizedBox(width: 12),
        // Event title
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                event.title ?? 'Untitled Event',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF303F9F),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                event.category.displayName,
                style: TextStyle(
                  fontSize: 12,
                  color: _getCategoryColor(),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build info row with date and cattle information
  Widget _buildInfoRow() {
    return Row(
      children: [
        // Date info
        Expanded(
          child: Row(
            children: [
              Icon(
                Icons.schedule,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatDate(event.scheduledDate),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[800],
                      ),
                    ),
                    if (event.isOverdue)
                      Text(
                        'Overdue',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.red[600],
                          fontWeight: FontWeight.w500,
                        ),
                      )
                    else if (event.isUpcoming)
                      Text(
                        'Upcoming',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Cattle info
        if (cattleName != null) ...[
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: [
                Icon(
                  Icons.pets,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    cattleName!,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[800],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build extra info row with location and other details
  Widget _buildExtraInfoRow() {
    return Row(
      children: [
        // Location (if available)
        if (event.location?.isNotEmpty == true) ...[
          Expanded(
            child: Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    event.location!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[700],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
        // Cost info (if available)
        if (event.estimatedCost != null && event.estimatedCost! > 0) ...[
          const SizedBox(width: 16),
          Row(
            children: [
              Icon(
                Icons.attach_money,
                size: 16,
                color: Colors.grey[600],
              ),
              Text(
                '\$${event.estimatedCost!.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Build description row
  Widget _buildDescriptionRow() {
    final description = event.description?.isNotEmpty == true 
        ? event.description 
        : event.notes;
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.notes,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              description!,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
                fontStyle: FontStyle.italic,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build action menu
  Widget _buildActionMenu() {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Colors.grey[600],
        size: 20,
      ),
      onSelected: (String choice) {
        switch (choice) {
          case 'Complete':
            onComplete?.call();
            break;
          case 'Edit':
            onEdit?.call();
            break;
          case 'Delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (BuildContext context) => [
        if (event.status != EventStatus.completed && onComplete != null)
          const PopupMenuItem(
            value: 'Complete',
            child: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green),
                SizedBox(width: 8),
                Text('Mark Complete'),
              ],
            ),
          ),
        if (onEdit != null)
          const PopupMenuItem(
            value: 'Edit',
            child: Row(
              children: [
                Icon(Icons.edit_outlined),
                SizedBox(width: 8),
                Text('Edit'),
              ],
            ),
          ),
        if (onDelete != null)
          const PopupMenuItem(
            value: 'Delete',
            child: Row(
              children: [
                Icon(Icons.delete_outline, color: Colors.red),
                SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
      ],
    );
  }

  /// Check if extra info should be shown
  bool _shouldShowExtraInfo() {
    return (event.location?.isNotEmpty == true) ||
           (event.estimatedCost != null && event.estimatedCost! > 0);
  }

  /// Check if description should be shown
  bool _hasDescription() {
    return (event.description?.isNotEmpty == true) ||
           (event.notes?.isNotEmpty == true);
  }

  /// Get border color based on event status
  Color _getBorderColor() {
    if (event.isOverdue) return Colors.red;
    if (event.isUpcoming) return Colors.orange;
    return _getStatusColor().withValues(alpha: 0.3);
  }

  /// Get status color
  Color _getStatusColor() {
    switch (event.status) {
      case EventStatus.scheduled:
        return Colors.blue;
      case EventStatus.inProgress:
        return Colors.orange;
      case EventStatus.completed:
        return Colors.green;
      case EventStatus.cancelled:
        return Colors.grey;
      case EventStatus.overdue:
        return Colors.red;
      case EventStatus.missed:
        return Colors.red.shade800;
    }
  }

  /// Get status icon
  IconData _getStatusIcon() {
    switch (event.status) {
      case EventStatus.scheduled:
        return Icons.schedule;
      case EventStatus.inProgress:
        return Icons.play_circle;
      case EventStatus.completed:
        return Icons.check_circle;
      case EventStatus.cancelled:
        return Icons.cancel;
      case EventStatus.overdue:
        return Icons.warning;
      case EventStatus.missed:
        return Icons.error;
    }
  }

  /// Get priority color
  Color _getPriorityColor() {
    switch (event.priority) {
      case EventPriority.critical:
        return Colors.red;
      case EventPriority.high:
        return Colors.orange;
      case EventPriority.medium:
        return AppColors.eventsHeader;
      case EventPriority.low:
        return Colors.green;
    }
  }

  /// Get category color
  Color _getCategoryColor() {
    switch (event.category) {
      case EventCategory.health:
        return AppColors.healthHeader;
      case EventCategory.breeding:
        return AppColors.breedingHeader;
      case EventCategory.feeding:
        return Colors.green;
      case EventCategory.management:
        return Colors.blue;
      case EventCategory.maintenance:
        return Colors.orange;
      case EventCategory.financial:
        return AppColors.transactionHeader;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return AppColors.eventsHeader;
    }
  }

  /// Get category icon
  IconData _getCategoryIcon() {
    switch (event.category) {
      case EventCategory.health:
        return Icons.medical_services;
      case EventCategory.breeding:
        return Icons.favorite;
      case EventCategory.feeding:
        return Icons.restaurant;
      case EventCategory.management:
        return Icons.business;
      case EventCategory.maintenance:
        return Icons.build;
      case EventCategory.financial:
        return Icons.attach_money;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return Icons.event;
    }
  }

  /// Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'No Date';
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate = DateTime(date.year, date.month, date.day);
    
    if (eventDate == today) {
      return 'Today ${DateFormat('HH:mm').format(date)}';
    } else if (eventDate == today.add(const Duration(days: 1))) {
      return 'Tomorrow ${DateFormat('HH:mm').format(date)}';
    } else if (eventDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday ${DateFormat('HH:mm').format(date)}';
    } else {
      return DateFormat('MMM dd, yyyy HH:mm').format(date);
    }
  }
}