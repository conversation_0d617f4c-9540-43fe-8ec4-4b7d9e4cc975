// This file contains template code for creating new repositories
// Copy and modify this template when creating new module repositories

/*
import 'package:isar/isar.dart';
import '../models/{module}_isar.dart';
import '../../core/base/base_repository.dart';
import '../../services/database/isar_service.dart';

/// Pure reactive repository for {Module} module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class {Module}Repository extends BaseRepository<{Model}Isar> {
  
  /// Public constructor with explicit dependency injection
  {Module}Repository(IsarService isarService) : super(isarService);

  //=== REACTIVE STREAMS ===//

  /// Watches all {module} records with reactive updates
  /// The controller is responsible for all filtering and sorting
  @override
  Stream<List<{Model}Isar>> watchAll() {
    return isar.{model}Isars.where().watch(fireImmediately: true);
  }

  /// Optional: Watch a single {module} record by business ID
  @override
  Stream<{Model}Isar?> watchById(String businessId) {
    return isar.{model}Isars
        .filter()
        .businessIdEqualTo(businessId)
        .watch(fireImmediately: true)
        .map((list) => list.isNotEmpty ? list.first : null);
  }

  //=== CRUD OPERATIONS ===//

  /// Save (add or update) a {module} record using Isar's native upsert
  @override
  Future<void> save({Model}Isar record) async {
    await isar.writeTxn(() async {
      await isar.{model}Isars.put(record);
      // If the model has IsarLinks, save them here:
      // await record.relatedData.save();
    });
  }

  /// Delete a {module} record by its Isar ID
  @override
  Future<void> delete(int id) async {
    await isar.writeTxn(() async {
      await isar.{model}Isars.delete(id);
    });
  }

  /// Delete a {module} record by its business ID
  @override
  Future<void> deleteByBusinessId(String businessId) async {
    await isar.writeTxn(() async {
      await isar.{model}Isars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all {module} records (for analytics and report generation)
  /// Returns a Future<List> for one-time data fetching
  @override
  Future<List<{Model}Isar>> getAll() async {
    return await isar.{model}Isars.where().findAll();
  }

  /// Get {module} records by date range for filtered operations
  @override
  Future<List<{Model}Isar>> getByDateRange(DateTime startDate, DateTime endDate) async {
    return await isar.{model}Isars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }

  /// Get {module} records by specific business IDs for filtered operations
  @override
  Future<List<{Model}Isar>> getByIds(List<String> businessIds) async {
    if (businessIds.isEmpty) return await getAll();
    
    return await isar.{model}Isars
        .filter()
        .anyOf(businessIds, (q, businessId) => q.businessIdEqualTo(businessId))
        .findAll();
  }

  //=== MODULE-SPECIFIC QUERY METHODS ===//

  // Add any module-specific query methods here
  // For example:
  
  /// Get {module} records by status
  Future<List<{Model}Isar>> getByStatus(String status) async {
    return await isar.{model}Isars
        .filter()
        .statusEqualTo(status)
        .findAll();
  }
}

// If your module needs cattle-related filtering, extend CattleRelatedRepository instead:
/*
class {Module}Repository extends CattleRelatedRepository<{Model}Isar> {
  
  {Module}Repository(IsarService isarService) : super(isarService);

  // ... implement all the same methods as above ...

  //=== CATTLE-RELATED METHODS ===//

  /// Get {module} records for specific cattle (for analytics and filtering)
  @override
  Future<List<{Model}Isar>> getByCattleIds(List<String> cattleIds) async {
    if (cattleIds.isEmpty) return await getAll();
    
    return await isar.{model}Isars
        .filter()
        .anyOf(cattleIds, (q, cattleId) => q.cattleBusinessIdEqualTo(cattleId))
        .findAll();
  }
}
*/

// If your module needs farm-related filtering, extend FarmRelatedRepository instead:
/*
class {Module}Repository extends FarmRelatedRepository<{Model}Isar> {
  
  {Module}Repository(IsarService isarService) : super(isarService);

  // ... implement all the same methods as above ...

  //=== FARM-RELATED METHODS ===//

  /// Get {module} records for specific farm (for analytics and filtering)
  @override
  Future<List<{Model}Isar>> getByFarmId(String farmId) async {
    return await isar.{model}Isars
        .filter()
        .farmIdEqualTo(farmId)
        .findAll();
  }
}
*/

// If your module needs both cattle and farm filtering, extend CattleFarmRepository instead:
/*
class {Module}Repository extends CattleFarmRepository<{Model}Isar> {
  
  {Module}Repository(IsarService isarService) : super(isarService);

  // ... implement all the same methods as above ...

  //=== CATTLE-RELATED METHODS ===//

  @override
  Future<List<{Model}Isar>> getByCattleIds(List<String> cattleIds) async {
    if (cattleIds.isEmpty) return await getAll();
    
    return await isar.{model}Isars
        .filter()
        .anyOf(cattleIds, (q, cattleId) => q.cattleBusinessIdEqualTo(cattleId))
        .findAll();
  }

  //=== FARM-RELATED METHODS ===//

  @override
  Future<List<{Model}Isar>> getByFarmId(String farmId) async {
    return await isar.{model}Isars
        .filter()
        .farmIdEqualTo(farmId)
        .findAll();
  }
}
*/
*/
