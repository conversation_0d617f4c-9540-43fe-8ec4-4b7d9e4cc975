/// Email configuration for the Cattle Manager App
/// This file contains email service settings and templates
class EmailConfig {
  // Email service configuration
  static const String appName = 'Cattle Manager App';
  static const String supportEmail = '<EMAIL>';
  static const String noReplyEmail = '<EMAIL>';

  // Base URLs for email verification and password reset
  static const String baseUrl = 'https://cattlemanager.com'; // Replace with your actual domain
  static const String verificationPath = '/verify-email';
  static const String passwordResetPath = '/reset-password';
  
  // Email service providers configuration
  // Option 1: SMTP Configuration (Gmail, Outlook, etc.)
  static const String smtpHost = 'smtp.gmail.com';
  static const int smtpPort = 587;
  static const String smtpUsername = '<EMAIL>'; // Replace with your email
  static const String smtpPassword = 'your-app-password'; // Replace with your app password
  
  // Option 2: EmailJS Configuration (for client-side email sending)
  static const String emailJsServiceId = 'your_service_id'; // Replace with your EmailJS service ID
  static const String emailJsTemplateId = 'your_template_id'; // Replace with your EmailJS template ID
  static const String emailJsUserId = 'your_user_id'; // Replace with your EmailJS user ID
  
  // Option 3: SendGrid Configuration
  static const String sendGridApiKey = 'your_sendgrid_api_key'; // Replace with your SendGrid API key
  static const String sendGridFromEmail = '<EMAIL>';
  static const String sendGridFromName = 'Cattle Manager Team';
  
  // Email templates
  static String getVerificationEmailSubject() => 'Verify Your Email - $appName';
  
  static String getVerificationEmailBody({
    required String userName,
    required String verificationLink,
  }) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2E7D32; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background-color: #2E7D32; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>$appName</h1>
        </div>
        <div class="content">
            <h2>Welcome, $userName!</h2>
            <p>Thank you for registering with $appName. To complete your registration, please verify your email address by clicking the button below:</p>
            
            <div style="text-align: center;">
                <a href="$verificationLink" class="button">Verify Email Address</a>
            </div>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #2E7D32;">$verificationLink</p>
            
            <p><strong>Important:</strong> This verification link will expire in 24 hours for security reasons.</p>
            
            <p>If you didn't create an account with us, please ignore this email.</p>
            
            <p>Best regards,<br>The $appName Team</p>
        </div>
        <div class="footer">
            <p>© 2024 $appName. All rights reserved.</p>
            <p>If you have any questions, contact us at $supportEmail</p>
        </div>
    </div>
</body>
</html>
''';
  }
  
  static String getWelcomeEmailSubject() => 'Welcome to $appName!';
  
  static String getWelcomeEmailBody({required String userName}) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2E7D32; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .feature { margin: 15px 0; padding: 10px; background-color: white; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to $appName!</h1>
        </div>
        <div class="content">
            <h2>Hello $userName,</h2>
            <p>Welcome to $appName! We're excited to have you join our community of farmers and agricultural professionals.</p>
            
            <h3>What you can do with $appName:</h3>
            <div class="feature">📊 <strong>Manage Cattle Records:</strong> Keep detailed records of all your cattle</div>
            <div class="feature">🏥 <strong>Health Tracking:</strong> Monitor health records and vaccinations</div>
            <div class="feature">🐄 <strong>Breeding Management:</strong> Track breeding cycles and genealogy</div>
            <div class="feature">🥛 <strong>Milk Production:</strong> Record and analyze milk production data</div>
            <div class="feature">📈 <strong>Reports & Analytics:</strong> Generate comprehensive reports</div>
            <div class="feature">⚡ <strong>Smart Alerts:</strong> Get notified about important events</div>
            
            <p>To get started, simply log into your account and begin setting up your farm profile.</p>
            
            <p>If you have any questions or need assistance, don't hesitate to reach out to our support team at $supportEmail.</p>
            
            <p>Happy farming!<br>The $appName Team</p>
        </div>
        <div class="footer">
            <p>© 2024 $appName. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
''';
  }
  
  static String getPasswordResetEmailSubject() => 'Reset Your Password - $appName';
  
  static String getPasswordResetEmailBody({
    required String userName,
    required String resetLink,
  }) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2E7D32; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background-color: #2E7D32; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Reset Request</h1>
        </div>
        <div class="content">
            <h2>Hello $userName,</h2>
            <p>We received a request to reset your password for your $appName account.</p>
            
            <div style="text-align: center;">
                <a href="$resetLink" class="button">Reset Password</a>
            </div>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #2E7D32;">$resetLink</p>
            
            <div class="warning">
                <strong>⚠️ Important Security Information:</strong>
                <ul>
                    <li>This password reset link will expire in 1 hour</li>
                    <li>If you didn't request this reset, please ignore this email</li>
                    <li>Your password will remain unchanged if you don't click the link</li>
                </ul>
            </div>
            
            <p>For security reasons, if you continue to receive these emails without requesting them, please contact our support team immediately.</p>
            
            <p>Best regards,<br>The $appName Team</p>
        </div>
        <div class="footer">
            <p>© 2024 $appName. All rights reserved.</p>
            <p>If you have any questions, contact us at $supportEmail</p>
        </div>
    </div>
</body>
</html>
''';
  }
}
