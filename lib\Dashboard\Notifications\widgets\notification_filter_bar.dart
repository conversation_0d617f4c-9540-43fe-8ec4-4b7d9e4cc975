import 'package:flutter/material.dart';

import '../models/notification_status.dart';
import '../models/notification_priority.dart';
import '../../../constants/app_colors.dart';

/// Widget for filtering notifications
class NotificationFilterBar extends StatelessWidget {
  final String? selectedCategory;
  final NotificationStatus? selectedStatus;
  final NotificationPriority? selectedPriority;
  final String? searchQuery;
  final Function(String?) onCategoryChanged;
  final Function(NotificationStatus?) onStatusChanged;
  final Function(NotificationPriority?) onPriorityChanged;
  final Function(String?) onSearchChanged;
  final VoidCallback onClearFilters;

  const NotificationFilterBar({
    Key? key,
    this.selectedCategory,
    this.selectedStatus,
    this.selectedPriority,
    this.searchQuery,
    required this.onCategoryChanged,
    required this.onStatusChanged,
    required this.onPriorityChanged,
    required this.onSearchChanged,
    required this.onClearFilters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search notifications...',
              prefixIcon: Icon(
                Icons.search,
                color: AppColors.notificationActionColors['filter'],
              ),
              border: const OutlineInputBorder(),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: onSearchChanged,
          ),
          
          const SizedBox(height: 12),
          
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Category filter
                _buildFilterChip(
                  label: selectedCategory ?? 'All Categories',
                  isSelected: selectedCategory != null,
                  onTap: () => _showCategoryFilter(context),
                ),
                
                const SizedBox(width: 8),
                
                // Status filter
                _buildFilterChip(
                  label: selectedStatus?.name ?? 'All Status',
                  isSelected: selectedStatus != null,
                  onTap: () => _showStatusFilter(context),
                ),
                
                const SizedBox(width: 8),
                
                // Priority filter
                _buildFilterChip(
                  label: selectedPriority?.name ?? 'All Priorities',
                  isSelected: selectedPriority != null,
                  onTap: () => _showPriorityFilter(context),
                ),
                
                const SizedBox(width: 8),
                
                // Clear filters
                if (_hasActiveFilters())
                  _buildFilterChip(
                    label: 'Clear All',
                    isSelected: false,
                    onTap: onClearFilters,
                    icon: Icons.clear,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    IconData? icon,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.notificationActionColors['filter']
              : AppColors.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? AppColors.notificationActionColors['filter']!
                : AppColors.notificationActionColors['filter']!.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 16,
                color: isSelected
                    ? Colors.white
                    : AppColors.notificationActionColors['filter'],
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                color: isSelected
                    ? Colors.white
                    : AppColors.notificationActionColors['filter'],
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _hasActiveFilters() {
    return selectedCategory != null ||
           selectedStatus != null ||
           selectedPriority != null ||
           (searchQuery != null && searchQuery!.isNotEmpty);
  }

  void _showCategoryFilter(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _CategoryFilterSheet(
        selectedCategory: selectedCategory,
        onCategorySelected: onCategoryChanged,
      ),
    );
  }

  void _showStatusFilter(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _StatusFilterSheet(
        selectedStatus: selectedStatus,
        onStatusSelected: onStatusChanged,
      ),
    );
  }

  void _showPriorityFilter(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _PriorityFilterSheet(
        selectedPriority: selectedPriority,
        onPrioritySelected: onPriorityChanged,
      ),
    );
  }
}

class _CategoryFilterSheet extends StatelessWidget {
  final String? selectedCategory;
  final Function(String?) onCategorySelected;

  const _CategoryFilterSheet({
    this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final categories = ['health', 'breeding', 'milk', 'weight', 'events', 'system'];
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Filter by Category',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...categories.map((category) => ListTile(
            title: Text(category.toUpperCase()),
            trailing: selectedCategory == category ? const Icon(Icons.check) : null,
            onTap: () {
              onCategorySelected(selectedCategory == category ? null : category);
              Navigator.pop(context);
            },
          )),
          ListTile(
            title: const Text('All Categories'),
            trailing: selectedCategory == null ? const Icon(Icons.check) : null,
            onTap: () {
              onCategorySelected(null);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}

class _StatusFilterSheet extends StatelessWidget {
  final NotificationStatus? selectedStatus;
  final Function(NotificationStatus?) onStatusSelected;

  const _StatusFilterSheet({
    this.selectedStatus,
    required this.onStatusSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Filter by Status',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...NotificationStatus.values.map((status) => ListTile(
            title: Text(status.name.toUpperCase()),
            trailing: selectedStatus == status ? const Icon(Icons.check) : null,
            onTap: () {
              onStatusSelected(selectedStatus == status ? null : status);
              Navigator.pop(context);
            },
          )),
          ListTile(
            title: const Text('All Status'),
            trailing: selectedStatus == null ? const Icon(Icons.check) : null,
            onTap: () {
              onStatusSelected(null);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}

class _PriorityFilterSheet extends StatelessWidget {
  final NotificationPriority? selectedPriority;
  final Function(NotificationPriority?) onPrioritySelected;

  const _PriorityFilterSheet({
    this.selectedPriority,
    required this.onPrioritySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Filter by Priority',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...NotificationPriority.values.map((priority) => ListTile(
            title: Text(priority.name.toUpperCase()),
            trailing: selectedPriority == priority ? const Icon(Icons.check) : null,
            onTap: () {
              onPrioritySelected(selectedPriority == priority ? null : priority);
              Navigator.pop(context);
            },
          )),
          ListTile(
            title: const Text('All Priorities'),
            trailing: selectedPriority == null ? const Icon(Icons.check) : null,
            onTap: () {
              onPrioritySelected(null);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}