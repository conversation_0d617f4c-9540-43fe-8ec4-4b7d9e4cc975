import '../models/notification_priority.dart';

/// Request model for scheduling notifications
class ScheduledNotificationRequest {
  final String title;
  final String message;
  final DateTime scheduledTime;
  final String? category;
  final String? cattleId;
  final NotificationPriority priority;
  final Map<String, dynamic>? additionalData;
  final List<String>? deliveryChannels;
  final bool persistent;
  final Duration? expiresAfter;
  final String? recurrenceRule; // For recurring notifications
  final DateTime? recurrenceEndDate;
  final int? maxOccurrences;
  
  const ScheduledNotificationRequest({
    required this.title,
    required this.message,
    required this.scheduledTime,
    this.category,
    this.cattleId,
    this.priority = NotificationPriority.normal,
    this.additionalData,
    this.deliveryChannels,
    this.persistent = false,
    this.expiresAfter,
    this.recurrenceRule,
    this.recurrenceEndDate,
    this.maxOccurrences,
  });
  
  /// Create a vaccination reminder scheduled notification
  factory ScheduledNotificationRequest.vaccinationReminder({
    required String cattleName,
    required String vaccinationType,
    required DateTime dueDate,
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    // Schedule notification 7 days before due date
    final scheduledTime = dueDate.subtract(const Duration(days: 7));
    
    return ScheduledNotificationRequest(
      title: 'Vaccination Reminder',
      message: '$cattleName is due for $vaccinationType vaccination in 7 days.',
      scheduledTime: scheduledTime,
      category: 'vaccination_reminder',
      cattleId: cattleId,
      priority: NotificationPriority.normal,
      additionalData: {
        'cattleName': cattleName,
        'vaccinationType': vaccinationType,
        'dueDate': dueDate.toIso8601String(),
        'reminderType': 'vaccination',
        ...?additionalData,
      },
      deliveryChannels: ['push'],
    );
  }
  
  /// Create a breeding cycle scheduled notification
  factory ScheduledNotificationRequest.breedingCycle({
    required String cattleName,
    required DateTime expectedDate,
    required String cycleType, // 'heat', 'breeding', 'pregnancy_check'
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    String title;
    String message;
    NotificationPriority priority = NotificationPriority.normal;
    
    switch (cycleType.toLowerCase()) {
      case 'heat':
        title = 'Heat Cycle Expected';
        message = '$cattleName is expected to be in heat.';
        priority = NotificationPriority.high;
        break;
      case 'breeding':
        title = 'Breeding Window';
        message = '$cattleName is in optimal breeding window.';
        priority = NotificationPriority.high;
        break;
      case 'pregnancy_check':
        title = 'Pregnancy Check Due';
        message = '$cattleName is due for pregnancy check.';
        break;
      default:
        title = 'Breeding Reminder';
        message = '$cattleName has a breeding-related event.';
    }
    
    return ScheduledNotificationRequest(
      title: title,
      message: message,
      scheduledTime: expectedDate,
      category: 'breeding_cycle',
      cattleId: cattleId,
      priority: priority,
      additionalData: {
        'cattleName': cattleName,
        'cycleType': cycleType,
        'expectedDate': expectedDate.toIso8601String(),
        ...?additionalData,
      },
      deliveryChannels: priority == NotificationPriority.high ? ['push', 'email'] : ['push'],
    );
  }
  
  /// Create a recurring health check scheduled notification
  factory ScheduledNotificationRequest.recurringHealthCheck({
    required String cattleName,
    required DateTime firstCheckDate,
    required Duration interval, // e.g., Duration(days: 30) for monthly
    String? cattleId,
    DateTime? endDate,
    int? maxOccurrences,
    Map<String, dynamic>? additionalData,
  }) {
    return ScheduledNotificationRequest(
      title: 'Health Check Reminder',
      message: '$cattleName is due for routine health check.',
      scheduledTime: firstCheckDate,
      category: 'health_check',
      cattleId: cattleId,
      priority: NotificationPriority.normal,
      additionalData: {
        'cattleName': cattleName,
        'checkType': 'routine',
        'interval': interval.inDays,
        ...?additionalData,
      },
      deliveryChannels: ['push'],
      recurrenceRule: _createRecurrenceRule(interval),
      recurrenceEndDate: endDate,
      maxOccurrences: maxOccurrences,
    );
  }
  
  /// Create a feed schedule notification
  factory ScheduledNotificationRequest.feedingSchedule({
    required String cattleName,
    required DateTime feedingTime,
    required String feedType,
    String? cattleId,
    bool recurring = true,
    Map<String, dynamic>? additionalData,
  }) {
    return ScheduledNotificationRequest(
      title: 'Feeding Time',
      message: 'Time to feed $cattleName with $feedType.',
      scheduledTime: feedingTime,
      category: 'feeding_schedule',
      cattleId: cattleId,
      priority: NotificationPriority.low,
      additionalData: {
        'cattleName': cattleName,
        'feedType': feedType,
        'feedingTime': feedingTime.toIso8601String(),
        ...?additionalData,
      },
      deliveryChannels: ['push'],
      recurrenceRule: recurring ? 'FREQ=DAILY' : null,
    );
  }
  
  /// Create a milking schedule notification
  factory ScheduledNotificationRequest.milkingSchedule({
    required String cattleName,
    required DateTime milkingTime,
    String? cattleId,
    bool recurring = true,
    Map<String, dynamic>? additionalData,
  }) {
    return ScheduledNotificationRequest(
      title: 'Milking Time',
      message: 'Time to milk $cattleName.',
      scheduledTime: milkingTime,
      category: 'milking_schedule',
      cattleId: cattleId,
      priority: NotificationPriority.normal,
      additionalData: {
        'cattleName': cattleName,
        'milkingTime': milkingTime.toIso8601String(),
        'sessionType': 'scheduled',
        ...?additionalData,
      },
      deliveryChannels: ['push'],
      recurrenceRule: recurring ? 'FREQ=DAILY' : null,
    );
  }
  
  /// Create a medication reminder scheduled notification
  factory ScheduledNotificationRequest.medicationReminder({
    required String cattleName,
    required String medicationName,
    required DateTime administrationTime,
    required Duration treatmentDuration,
    required Duration dosageInterval,
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    final endDate = administrationTime.add(treatmentDuration);
    
    return ScheduledNotificationRequest(
      title: 'Medication Reminder',
      message: 'Time to administer $medicationName to $cattleName.',
      scheduledTime: administrationTime,
      category: 'medication_reminder',
      cattleId: cattleId,
      priority: NotificationPriority.high,
      additionalData: {
        'cattleName': cattleName,
        'medicationName': medicationName,
        'dosageInterval': dosageInterval.inHours,
        'treatmentDuration': treatmentDuration.inDays,
        ...?additionalData,
      },
      deliveryChannels: ['push', 'email'],
      persistent: true,
      recurrenceRule: _createRecurrenceRule(dosageInterval),
      recurrenceEndDate: endDate,
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'message': message,
      'scheduledTime': scheduledTime.toIso8601String(),
      'category': category,
      'cattleId': cattleId,
      'priority': priority.name,
      'additionalData': additionalData,
      'deliveryChannels': deliveryChannels,
      'persistent': persistent,
      'expiresAfter': expiresAfter?.inMilliseconds,
      'recurrenceRule': recurrenceRule,
      'recurrenceEndDate': recurrenceEndDate?.toIso8601String(),
      'maxOccurrences': maxOccurrences,
    };
  }
  
  /// Create from JSON
  factory ScheduledNotificationRequest.fromJson(Map<String, dynamic> json) {
    return ScheduledNotificationRequest(
      title: json['title'],
      message: json['message'],
      scheduledTime: DateTime.parse(json['scheduledTime']),
      category: json['category'],
      cattleId: json['cattleId'],
      priority: NotificationPriority.values.firstWhere(
        (p) => p.name == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      additionalData: json['additionalData']?.cast<String, dynamic>(),
      deliveryChannels: json['deliveryChannels']?.cast<String>(),
      persistent: json['persistent'] ?? false,
      expiresAfter: json['expiresAfter'] != null 
          ? Duration(milliseconds: json['expiresAfter']) 
          : null,
      recurrenceRule: json['recurrenceRule'],
      recurrenceEndDate: json['recurrenceEndDate'] != null 
          ? DateTime.parse(json['recurrenceEndDate']) 
          : null,
      maxOccurrences: json['maxOccurrences'],
    );
  }
  
  /// Check if this is a recurring notification
  bool get isRecurring => recurrenceRule != null;
  
  /// Check if the notification is still valid (not expired)
  bool get isValid {
    final now = DateTime.now();
    
    // Check if scheduled time has passed
    if (scheduledTime.isBefore(now)) {
      // If it's recurring, check if it's still within the recurrence period
      if (isRecurring) {
        if (recurrenceEndDate != null && now.isAfter(recurrenceEndDate!)) {
          return false;
        }
        return true;
      }
      // Non-recurring notifications expire after their scheduled time
      return false;
    }
    
    return true;
  }
  
  /// Helper method to create recurrence rule from Duration
  static String _createRecurrenceRule(Duration interval) {
    if (interval.inDays >= 1) {
      if (interval.inDays == 1) {
        return 'FREQ=DAILY';
      } else if (interval.inDays == 7) {
        return 'FREQ=WEEKLY';
      } else if (interval.inDays >= 28 && interval.inDays <= 31) {
        return 'FREQ=MONTHLY';
      } else {
        return 'FREQ=DAILY;INTERVAL=${interval.inDays}';
      }
    } else if (interval.inHours >= 1) {
      return 'FREQ=HOURLY;INTERVAL=${interval.inHours}';
    } else {
      return 'FREQ=MINUTELY;INTERVAL=${interval.inMinutes}';
    }
  }
}
