import '../models/cattle_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Breeding/models/breeding_record_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';

/// Farm pricing settings for financial calculations
class FarmPricingSettings {
  final double milkPricePerLiter;
  final double dailyMaintenanceCost;
  final double averageCalfValue;
  final double vaccinationCost;
  final double antibioticTreatmentCost;
  final double surgicalProcedureCost;
  final double generalTreatmentCost;
  final double monthlyAppreciationRate;

  const FarmPricingSettings({
    this.milkPricePerLiter = 0.50,
    this.dailyMaintenanceCost = 5.0,
    this.averageCalfValue = 500.0,
    this.vaccinationCost = 25.0,
    this.antibioticTreatmentCost = 75.0,
    this.surgicalProcedureCost = 500.0,
    this.generalTreatmentCost = 50.0,
    this.monthlyAppreciationRate = 0.005, // 0.5% per month
  });

  /// Create default pricing settings
  factory FarmPricingSettings.defaults() => const FarmPricingSettings();

  /// Create pricing settings from user preferences/settings
  factory FarmPricingSettings.fromSettings(Map<String, dynamic> settings) {
    return FarmPricingSettings(
      milkPricePerLiter: settings['milkPricePerLiter']?.toDouble() ?? 0.50,
      dailyMaintenanceCost: settings['dailyMaintenanceCost']?.toDouble() ?? 5.0,
      averageCalfValue: settings['averageCalfValue']?.toDouble() ?? 500.0,
      vaccinationCost: settings['vaccinationCost']?.toDouble() ?? 25.0,
      antibioticTreatmentCost: settings['antibioticTreatmentCost']?.toDouble() ?? 75.0,
      surgicalProcedureCost: settings['surgicalProcedureCost']?.toDouble() ?? 500.0,
      generalTreatmentCost: settings['generalTreatmentCost']?.toDouble() ?? 50.0,
      monthlyAppreciationRate: settings['monthlyAppreciationRate']?.toDouble() ?? 0.005,
    );
  }
}

/// Result class for individual cattle analytics
class IndividualCattleAnalyticsResult {
  final ProductionMetrics production;
  final BreedingMetrics breeding;
  final HealthMetrics health;
  final FinancialMetrics financial;

  const IndividualCattleAnalyticsResult({
    required this.production,
    required this.breeding,
    required this.health,
    required this.financial,
  });

  /// Empty state for initialization
  static const IndividualCattleAnalyticsResult empty = IndividualCattleAnalyticsResult(
    production: ProductionMetrics.empty,
    breeding: BreedingMetrics.empty,
    health: HealthMetrics.empty,
    financial: FinancialMetrics.empty,
  );
}

/// Production metrics for individual cattle
class ProductionMetrics {
  final double lifetimeMilkYield;
  final double averageDailyYield;
  final double peakDailyYield;
  final int totalMilkingDays;
  final double currentLactationYield;
  final int daysInMilk;
  final double yieldTrend; // Positive = increasing, negative = decreasing

  const ProductionMetrics({
    required this.lifetimeMilkYield,
    required this.averageDailyYield,
    required this.peakDailyYield,
    required this.totalMilkingDays,
    required this.currentLactationYield,
    required this.daysInMilk,
    required this.yieldTrend,
  });

  /// Empty state for initialization
  static const ProductionMetrics empty = ProductionMetrics(
    lifetimeMilkYield: 0.0,
    averageDailyYield: 0.0,
    peakDailyYield: 0.0,
    totalMilkingDays: 0,
    currentLactationYield: 0.0,
    daysInMilk: 0,
    yieldTrend: 0.0,
  );
}

/// Breeding metrics for individual cattle
class BreedingMetrics {
  final int totalCalves;
  final double averageCalvingInterval; // Days between calvings
  final int daysOpen; // Days since last calving without conception
  final double conceptionRate; // Percentage of successful breedings
  final DateTime? lastBreedingDate;
  final DateTime? expectedCalvingDate;
  final int totalBreedingAttempts;
  final bool isCurrentlyPregnant;

  const BreedingMetrics({
    required this.totalCalves,
    required this.averageCalvingInterval,
    required this.daysOpen,
    required this.conceptionRate,
    this.lastBreedingDate,
    this.expectedCalvingDate,
    required this.totalBreedingAttempts,
    required this.isCurrentlyPregnant,
  });

  /// Empty state for initialization
  static const BreedingMetrics empty = BreedingMetrics(
    totalCalves: 0,
    averageCalvingInterval: 0.0,
    daysOpen: 0,
    conceptionRate: 0.0,
    totalBreedingAttempts: 0,
    isCurrentlyPregnant: false,
  );
}

/// Health metrics for individual cattle
class HealthMetrics {
  final int healthScore; // 0-100 composite health score
  final int totalTreatments;
  final double treatmentFrequency; // Treatments per year
  final int daysSinceLastTreatment;
  final int totalVaccinations;
  final DateTime? lastVaccinationDate;
  final DateTime? nextRecommendedCheckup;
  final List<String> chronicConditions;
  final double recoveryRate; // Percentage of successful treatments

  const HealthMetrics({
    required this.healthScore,
    required this.totalTreatments,
    required this.treatmentFrequency,
    required this.daysSinceLastTreatment,
    required this.totalVaccinations,
    this.lastVaccinationDate,
    this.nextRecommendedCheckup,
    required this.chronicConditions,
    required this.recoveryRate,
  });

  /// Empty state for initialization
  static const HealthMetrics empty = HealthMetrics(
    healthScore: 0,
    totalTreatments: 0,
    treatmentFrequency: 0.0,
    daysSinceLastTreatment: 0,
    totalVaccinations: 0,
    chronicConditions: [],
    recoveryRate: 0.0,
  );
}

/// Financial metrics for individual cattle
class FinancialMetrics {
  final double totalInvestment;
  final double currentEstimatedValue;
  final double totalRevenue; // From milk sales, calf sales, etc.
  final double totalCosts; // Feed, medical, housing
  final double netProfit; // Revenue - costs
  final double roi; // Return on investment percentage
  final double monthlyMaintenanceCost;
  final double projectedAnnualRevenue;

  const FinancialMetrics({
    required this.totalInvestment,
    required this.currentEstimatedValue,
    required this.totalRevenue,
    required this.totalCosts,
    required this.netProfit,
    required this.roi,
    required this.monthlyMaintenanceCost,
    required this.projectedAnnualRevenue,
  });

  /// Empty state for initialization
  static const FinancialMetrics empty = FinancialMetrics(
    totalInvestment: 0.0,
    currentEstimatedValue: 0.0,
    totalRevenue: 0.0,
    totalCosts: 0.0,
    netProfit: 0.0,
    roi: 0.0,
    monthlyMaintenanceCost: 0.0,
    projectedAnnualRevenue: 0.0,
  );
}

/// Service for calculating individual cattle analytics
class IndividualCattleAnalyticsService {
  // Health scoring constants - business rules made explicit
  static const int _pointsPerTreatment = 2;
  static const int _pointsPerChronicCondition = 10;
  static const int _pointsForRecentTreatment = 15;
  static const int _pointsForSomewhatRecentTreatment = 5;
  static const int _bonusForGoodRecovery = 5;
  static const int _bonusForVaccinations = 5;
  static const int _maxTreatmentPenalty = 30;
  static const int _maxChronicConditionPenalty = 20;
  static const int _recentTreatmentDays = 30;
  static const int _somewhatRecentTreatmentDays = 90;
  static const double _goodRecoveryThreshold = 90.0;
  static const int _minVaccinationsForBonus = 2;
  static const int _chronicConditionThreshold = 3; // 3+ treatments for same condition

  /// Calculate comprehensive analytics for an individual cattle
  static IndividualCattleAnalyticsResult calculate(
    CattleIsar cattle,
    List<MilkRecordIsar> milkRecords,
    List<HealthRecordIsar> healthRecords,
    List<BreedingRecordIsar> breedingRecords,
    AnimalTypeIsar animalType,
    FarmPricingSettings pricingSettings,
  ) {
    return IndividualCattleAnalyticsResult(
      production: _calculateProductionMetrics(cattle, milkRecords),
      breeding: _calculateBreedingMetrics(cattle, breedingRecords, animalType),
      health: _calculateHealthMetrics(cattle, healthRecords),
      financial: _calculateFinancialMetrics(cattle, milkRecords, healthRecords, pricingSettings),
    );
  }

  /// Calculate production metrics from milk records
  static ProductionMetrics _calculateProductionMetrics(
    CattleIsar cattle,
    List<MilkRecordIsar> milkRecords,
  ) {
    if (milkRecords.isEmpty) {
      return ProductionMetrics.empty;
    }

    // Calculate lifetime milk yield
    final lifetimeMilkYield = milkRecords
        .map((record) => record.quantity ?? 0.0)
        .fold(0.0, (sum, quantity) => sum + quantity);

    // Calculate average daily yield
    final totalMilkingDays = milkRecords.length;
    final averageDailyYield = totalMilkingDays > 0 ? lifetimeMilkYield / totalMilkingDays : 0.0;

    // Calculate peak daily yield
    final peakDailyYield = milkRecords
        .map((record) => record.quantity ?? 0.0)
        .fold(0.0, (max, quantity) => quantity > max ? quantity : max);

    // Calculate current lactation metrics (last 305 days)
    final now = DateTime.now();
    final lactationStart = now.subtract(const Duration(days: 305));
    final currentLactationRecords = milkRecords
        .where((record) => record.date != null && record.date!.isAfter(lactationStart))
        .toList();

    final currentLactationYield = currentLactationRecords
        .map((record) => record.quantity ?? 0.0)
        .fold(0.0, (sum, quantity) => sum + quantity);

    final daysInMilk = currentLactationRecords.length;

    // Calculate yield trend (simple: last 30 days vs previous 30 days)
    final last30Days = now.subtract(const Duration(days: 30));
    final previous30Days = now.subtract(const Duration(days: 60));

    final recentRecords = milkRecords
        .where((record) => record.date != null && record.date!.isAfter(last30Days))
        .toList();
    final previousRecords = milkRecords
        .where((record) => 
            record.date != null && 
            record.date!.isAfter(previous30Days) && 
            record.date!.isBefore(last30Days))
        .toList();

    final recentAverage = recentRecords.isNotEmpty
        ? recentRecords.map((r) => r.quantity ?? 0.0).fold(0.0, (sum, q) => sum + q) / recentRecords.length
        : 0.0;
    final previousAverage = previousRecords.isNotEmpty
        ? previousRecords.map((r) => r.quantity ?? 0.0).fold(0.0, (sum, q) => sum + q) / previousRecords.length
        : 0.0;

    final yieldTrend = previousAverage > 0 ? ((recentAverage - previousAverage) / previousAverage) * 100 : 0.0;

    return ProductionMetrics(
      lifetimeMilkYield: lifetimeMilkYield,
      averageDailyYield: averageDailyYield,
      peakDailyYield: peakDailyYield,
      totalMilkingDays: totalMilkingDays,
      currentLactationYield: currentLactationYield,
      daysInMilk: daysInMilk,
      yieldTrend: yieldTrend,
    );
  }

  /// Calculate breeding metrics with comprehensive reproductive analysis
  static BreedingMetrics _calculateBreedingMetrics(
    CattleIsar cattle,
    List<BreedingRecordIsar> breedingRecords,
    AnimalTypeIsar animalType,
  ) {
    if (breedingRecords.isEmpty) {
      return BreedingMetrics.empty;
    }

    // Filter records for this specific cattle
    final cattleBreedingRecords = breedingRecords
        .where((record) => record.cattleId == cattle.businessId)
        .toList();

    if (cattleBreedingRecords.isEmpty) {
      return BreedingMetrics.empty;
    }

    // Sort by date for chronological analysis
    cattleBreedingRecords.sort((a, b) => a.date!.compareTo(b.date!));

    // Calculate metrics
    final totalAttempts = cattleBreedingRecords.length;
    final successfulConceptions = cattleBreedingRecords
        .where((record) => record.status?.toLowerCase() == 'completed' || record.status?.toLowerCase() == 'confirmed')
        .length;

    final conceptionRate = totalAttempts > 0
        ? (successfulConceptions / totalAttempts) * 100
        : 0.0;

    // Calculate calving intervals (time between successful pregnancies)
    final successfulBreedings = cattleBreedingRecords
        .where((record) => record.status?.toLowerCase() == 'completed' || record.status?.toLowerCase() == 'confirmed')
        .toList();

    double averageCalvingInterval = 0.0;
    if (successfulBreedings.length > 1) {
      int totalDays = 0;
      for (int i = 1; i < successfulBreedings.length; i++) {
        totalDays += successfulBreedings[i].date!
            .difference(successfulBreedings[i - 1].date!)
            .inDays;
      }
      averageCalvingInterval = totalDays / (successfulBreedings.length - 1);
    }

    // Calculate days open (since last successful breeding)
    int daysOpen = 0;
    if (successfulBreedings.isNotEmpty) {
      final lastSuccessfulBreeding = successfulBreedings.last;
      daysOpen = DateTime.now().difference(lastSuccessfulBreeding.date!).inDays;
    }

    // Check current pregnancy status (assume pregnant if last breeding was successful and recent)
    bool isCurrentlyPregnant = false;
    if (cattleBreedingRecords.isNotEmpty) {
      final lastBreeding = cattleBreedingRecords.last;
      final daysSinceLastBreeding = DateTime.now().difference(lastBreeding.date!).inDays;
      // Use animal type specific gestation period instead of hardcoded 280 days
      final gestationPeriod = animalType.defaultGestationDays ?? 280; // Fallback to cattle default
      final isLastBreedingSuccessful = lastBreeding.status?.toLowerCase() == 'completed' || lastBreeding.status?.toLowerCase() == 'confirmed';
      isCurrentlyPregnant = isLastBreedingSuccessful && daysSinceLastBreeding < gestationPeriod;
    }

    return BreedingMetrics(
      totalCalves: successfulConceptions, // Each successful conception typically results in a calf
      averageCalvingInterval: averageCalvingInterval,
      daysOpen: daysOpen,
      conceptionRate: conceptionRate,
      totalBreedingAttempts: totalAttempts,
      isCurrentlyPregnant: isCurrentlyPregnant,
    );
  }

  /// Calculate health metrics with comprehensive medical analysis
  static HealthMetrics _calculateHealthMetrics(
    CattleIsar cattle,
    List<HealthRecordIsar> healthRecords,
  ) {
    // Filter records for this specific cattle
    final cattleHealthRecords = healthRecords
        .where((record) => record.cattleId == cattle.businessId)
        .toList();

    if (cattleHealthRecords.isEmpty) {
      // Return baseline health metrics for cattle with no health records
      return const HealthMetrics(
        healthScore: 85, // Assume good health if no issues recorded
        totalTreatments: 0,
        treatmentFrequency: 0.0,
        daysSinceLastTreatment: 999, // Large number indicating no recent treatments
        totalVaccinations: 0,
        chronicConditions: [],
        recoveryRate: 100.0, // Perfect recovery rate with no treatments
      );
    }

    // Sort by date for chronological analysis
    cattleHealthRecords.sort((a, b) => a.date!.compareTo(b.date!));

    // Basic counts
    final totalTreatments = cattleHealthRecords.length;
    final vaccinations = cattleHealthRecords
        .where((record) => record.treatment?.toLowerCase().contains('vaccin') == true ||
                          record.recordType?.toLowerCase().contains('vaccin') == true)
        .length;

    // Calculate treatment frequency (treatments per year)
    double treatmentFrequency = 0.0;
    if (cattleHealthRecords.isNotEmpty) {
      final firstTreatment = cattleHealthRecords.first.date!;
      final daysSinceFirstTreatment = DateTime.now().difference(firstTreatment).inDays;
      final yearsSinceFirstTreatment = daysSinceFirstTreatment / 365.0;
      if (yearsSinceFirstTreatment > 0) {
        treatmentFrequency = totalTreatments / yearsSinceFirstTreatment;
      }
    }

    // Days since last treatment
    final daysSinceLastTreatment = cattleHealthRecords.isNotEmpty
        ? DateTime.now().difference(cattleHealthRecords.last.date!).inDays
        : 999;

    // Calculate recovery rate (successful treatments / total treatments)
    final successfulTreatments = cattleHealthRecords
        .where((record) => record.isResolved == true ||
                          record.notes?.toLowerCase().contains('recovered') == true ||
                          record.notes?.toLowerCase().contains('cured') == true ||
                          record.notes?.toLowerCase().contains('successful') == true)
        .length;

    final recoveryRate = totalTreatments > 0
        ? (successfulTreatments / totalTreatments) * 100
        : 100.0;

    // Identify chronic conditions (recurring treatment types)
    final treatmentTypeCounts = <String, int>{};
    for (final record in cattleHealthRecords) {
      final type = (record.treatment ?? record.diagnosis ?? record.condition ?? 'unknown').toLowerCase();
      treatmentTypeCounts[type] = (treatmentTypeCounts[type] ?? 0) + 1;
    }

    final chronicConditions = treatmentTypeCounts.entries
        .where((entry) => entry.value >= _chronicConditionThreshold)
        .map((entry) => entry.key)
        .toList();

    // Calculate composite health score (0-100)
    int healthScore = 100;

    // Deduct points for treatments (more treatments = lower score)
    healthScore -= (totalTreatments * _pointsPerTreatment).clamp(0, _maxTreatmentPenalty);

    // Deduct points for chronic conditions
    healthScore -= (chronicConditions.length * _pointsPerChronicCondition).clamp(0, _maxChronicConditionPenalty);

    // Deduct points for recent treatments (indicates current health issues)
    if (daysSinceLastTreatment < _recentTreatmentDays) {
      healthScore -= _pointsForRecentTreatment;
    } else if (daysSinceLastTreatment < _somewhatRecentTreatmentDays) {
      healthScore -= _pointsForSomewhatRecentTreatment;
    }

    // Bonus points for good recovery rate
    if (recoveryRate >= _goodRecoveryThreshold) {
      healthScore += _bonusForGoodRecovery;
    }

    // Bonus points for vaccinations (preventive care)
    if (vaccinations >= _minVaccinationsForBonus) {
      healthScore += _bonusForVaccinations;
    }

    // Ensure score stays within bounds
    healthScore = healthScore.clamp(0, 100);

    return HealthMetrics(
      healthScore: healthScore,
      totalTreatments: totalTreatments,
      treatmentFrequency: treatmentFrequency,
      daysSinceLastTreatment: daysSinceLastTreatment,
      totalVaccinations: vaccinations,
      chronicConditions: chronicConditions,
      recoveryRate: recoveryRate,
    );
  }

  /// Calculate financial metrics with comprehensive profitability analysis
  static FinancialMetrics _calculateFinancialMetrics(
    CattleIsar cattle,
    List<MilkRecordIsar> milkRecords,
    List<HealthRecordIsar> healthRecords,
    FarmPricingSettings pricingSettings,
  ) {
    // Initial investment (purchase price)
    final purchasePrice = cattle.purchasePrice ?? 0.0;

    // Filter records for this specific cattle
    final cattleMilkRecords = milkRecords
        .where((record) => record.cattleBusinessId == cattle.businessId)
        .toList();

    final cattleHealthRecords = healthRecords
        .where((record) => record.cattleId == cattle.businessId)
        .toList();

    // Calculate revenue from milk production
    double milkRevenue = 0.0;
    for (final record in cattleMilkRecords) {
      // Calculate revenue using dynamic milk price from settings
      milkRevenue += (record.quantity ?? 0.0) * pricingSettings.milkPricePerLiter;
    }

    // Calculate health costs (veterinary expenses)
    double healthCosts = 0.0;
    for (final record in cattleHealthRecords) {
      // Use actual cost if available, otherwise estimate based on treatment type
      if (record.cost != null && record.cost! > 0) {
        healthCosts += record.cost!;
      } else {
        // Estimate treatment costs based on treatment type using dynamic pricing
        final treatmentType = (record.treatment ?? record.diagnosis ?? record.condition ?? '').toLowerCase();
        if (treatmentType.contains('vaccin')) {
          healthCosts += pricingSettings.vaccinationCost;
        } else if (treatmentType.contains('antibiotic')) {
          healthCosts += pricingSettings.antibioticTreatmentCost;
        } else if (treatmentType.contains('surgery')) {
          healthCosts += pricingSettings.surgicalProcedureCost;
        } else {
          healthCosts += pricingSettings.generalTreatmentCost;
        }
      }
    }

    // Calculate ongoing maintenance costs
    final daysOwned = cattle.purchaseDate != null
        ? DateTime.now().difference(cattle.purchaseDate!).inDays
        : (cattle.dateOfBirth != null
            ? DateTime.now().difference(cattle.dateOfBirth!).inDays
            : 365); // Default to 1 year if no dates available

    // Use dynamic daily maintenance cost from settings
    final dailyMaintenanceCost = pricingSettings.dailyMaintenanceCost;
    final totalMaintenanceCosts = daysOwned * dailyMaintenanceCost;

    // Monthly maintenance cost
    final monthlyMaintenanceCost = dailyMaintenanceCost * 30;

    // Total costs
    final totalCosts = purchasePrice + healthCosts + totalMaintenanceCosts;

    // Total investment (purchase + ongoing costs)
    final totalInvestment = totalCosts;

    // Estimate current market value (appreciation over time)
    final monthsOwned = daysOwned / 30.0;
    final monthlyAppreciationRate = pricingSettings.monthlyAppreciationRate;
    final currentEstimatedValue = purchasePrice * (1 + (monthsOwned * monthlyAppreciationRate));

    // Total revenue (milk + estimated calf value)
    // Use dynamic calf value from settings
    final estimatedCalfRevenue = (daysOwned / 365.0) * pricingSettings.averageCalfValue;
    final totalRevenue = milkRevenue + estimatedCalfRevenue;

    // Net profit
    final netProfit = totalRevenue - totalCosts;

    // ROI calculation
    final roi = totalInvestment > 0 ? (netProfit / totalInvestment) * 100 : 0.0;

    // Projected annual revenue (based on current performance)
    final dailyMilkProduction = cattleMilkRecords.isNotEmpty
        ? cattleMilkRecords.map((r) => r.quantity ?? 0.0).reduce((a, b) => a + b) / cattleMilkRecords.length
        : 0.0;
    final projectedAnnualMilkRevenue = dailyMilkProduction * 365 * pricingSettings.milkPricePerLiter;
    final projectedAnnualCalfRevenue = pricingSettings.averageCalfValue; // One calf per year
    final projectedAnnualRevenue = projectedAnnualMilkRevenue + projectedAnnualCalfRevenue;

    return FinancialMetrics(
      totalInvestment: totalInvestment,
      currentEstimatedValue: currentEstimatedValue,
      totalRevenue: totalRevenue,
      totalCosts: totalCosts,
      netProfit: netProfit,
      roi: roi,
      monthlyMaintenanceCost: monthlyMaintenanceCost,
      projectedAnnualRevenue: projectedAnnualRevenue,
    );
  }
}

/// Extension methods for ProductionMetrics display and insights
extension ProductionMetricsDisplay on ProductionMetrics {
  /// Get production insight based on lifetime yield
  String get productionInsight {
    if (lifetimeMilkYield > 0) return 'Productive animal';
    return 'No production recorded';
  }

  /// Get daily yield insight
  String get dailyYieldInsight {
    if (averageDailyYield > 20) return 'High producer';
    if (averageDailyYield > 10) return 'Average producer';
    return 'Low producer';
  }

  /// Get milking days insight
  String get milkingDaysInsight {
    if (totalMilkingDays > 300) return 'Long production history';
    return 'Limited history';
  }

  /// Get current lactation insight
  String get currentLactationInsight => '$daysInMilk days in milk';

  /// Get yield trend insight
  String get yieldTrendInsight {
    if (yieldTrend >= 0) return 'Improving performance';
    return 'Declining performance';
  }
}

/// Extension methods for BreedingMetrics display and insights
extension BreedingMetricsDisplay on BreedingMetrics {
  /// Get calves insight
  String get calvesInsight {
    if (totalCalves > 3) return 'Highly productive';
    if (totalCalves > 0) return 'Productive';
    return 'No offspring yet';
  }

  /// Get calving interval insight
  String get calvingIntervalInsight {
    if (averageCalvingInterval <= 0) return 'No calving history';
    if (averageCalvingInterval <= 365) return 'Excellent interval';
    return 'Extended interval';
  }

  /// Get days open insight
  String get daysOpenInsight {
    if (daysOpen < 85) return 'Optimal breeding window';
    return 'Extended open period';
  }

  /// Get conception rate insight
  String get conceptionRateInsight {
    if (conceptionRate > 80) return 'Excellent fertility';
    if (conceptionRate > 60) return 'Good fertility';
    return 'Fertility concerns';
  }

  /// Get breeding attempts insight
  String get breedingAttemptsInsight => 'Breeding history';

  /// Get pregnancy status insight
  String get pregnancyStatusInsight {
    if (isCurrentlyPregnant) return 'Expecting calf';
    return 'Available for breeding';
  }
}

/// Extension methods for HealthMetrics display and insights
extension HealthMetricsDisplay on HealthMetrics {
  /// Get health score color
  Color get healthScoreColor {
    if (healthScore >= 80) return AppColors.success;
    if (healthScore >= 60) return AppColors.warning;
    return AppColors.error;
  }

  /// Get health score insight
  String get healthScoreInsight {
    if (healthScore >= 80) return 'Excellent health';
    if (healthScore >= 60) return 'Good health';
    return 'Health concerns';
  }

  /// Get treatments insight
  String get treatmentsInsight {
    if (totalTreatments == 0) return 'No treatments needed';
    if (totalTreatments < 5) return 'Minimal treatments';
    return 'Frequent treatments';
  }

  /// Get treatment frequency insight
  String get treatmentFrequencyInsight {
    if (treatmentFrequency < 2) return 'Low frequency';
    return 'High frequency';
  }

  /// Get days since treatment insight
  String get daysSinceTreatmentInsight {
    if (daysSinceLastTreatment > 180) return 'Long time healthy';
    return 'Recent treatment';
  }

  /// Get vaccinations insight
  String get vaccinationsInsight => 'Immunization history';

  /// Get recovery rate insight
  String get recoveryRateInsight {
    if (recoveryRate > 90) return 'Excellent recovery';
    if (recoveryRate > 70) return 'Good recovery';
    return 'Recovery concerns';
  }
}

/// Extension methods for FinancialMetrics display and insights
extension FinancialMetricsDisplay on FinancialMetrics {
  /// Format currency values with configurable symbol - single source of truth
  String formatCurrency(double amount, {String symbol = '\$'}) {
    if (amount == 0) return '${symbol}0';
    return NumberFormat.currency(locale: 'en_US', symbol: symbol).format(amount);
  }

  /// Format total investment with default symbol
  String get formattedTotalInvestment => formatCurrency(totalInvestment);

  /// Format current value with default symbol
  String get formattedCurrentValue => formatCurrency(currentEstimatedValue);

  /// Format total revenue with default symbol
  String get formattedTotalRevenue => formatCurrency(totalRevenue);

  /// Format net profit with default symbol
  String get formattedNetProfit => formatCurrency(netProfit);

  /// Format monthly cost with default symbol
  String get formattedMonthlyCost => formatCurrency(monthlyMaintenanceCost);

  /// Format total costs with default symbol
  String get formattedTotalCosts => formatCurrency(totalCosts);

  /// Format projected annual revenue with default symbol
  String get formattedProjectedAnnualRevenue => formatCurrency(projectedAnnualRevenue);

  /// Get investment insight
  String get investmentInsight => 'Total money invested';

  /// Get current value insight
  String get currentValueInsight {
    if (currentEstimatedValue > totalInvestment) return 'Value appreciation';
    return 'Value depreciation';
  }

  /// Get revenue insight
  String get revenueInsight {
    if (totalRevenue > 0) return 'Revenue generating';
    return 'No revenue yet';
  }

  /// Get profit insight
  String get profitInsight {
    if (netProfit >= 0) return 'Profitable investment';
    return 'Operating at loss';
  }

  /// Get ROI insight
  String get roiInsight {
    if (roi > 15) return 'Excellent ROI';
    if (roi > 5) return 'Good ROI';
    return 'Poor ROI';
  }

  /// Get monthly cost insight
  String get monthlyCostInsight => 'Ongoing maintenance cost';
}
