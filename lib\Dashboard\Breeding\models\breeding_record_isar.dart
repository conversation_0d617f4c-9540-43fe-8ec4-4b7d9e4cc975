import 'package:isar/isar.dart';

part 'breeding_record_isar.g.dart';

/// Represents a breeding record in the Isar database
@collection
class BreedingRecordIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the breeding record - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// ID of the cattle that was bred
  @Index(type: IndexType.value)
  String? cattleId;

  /// Date when the breeding occurred
  DateTime? date;

  /// ID or type of the bull used for breeding
  String? bullIdOrType;

  /// Backward compatibility getter for bullId
  String? get bullId => bullIdOrType;

  /// Backward compatibility getter for cattleBusinessId
  String? get cattleBusinessId => cattleId;

  /// Method used for breeding (Artificial Insemination, Natural Breeding, etc.)
  @Index()
  String? method;

  /// Current status of the breeding (Pending, Confirmed, Completed, Failed)
  @Index()
  String? status;

  /// Expected calving date if breeding is successful
  DateTime? expectedDate;

  /// Cost of the breeding service
  double? cost;

  /// Additional notes about the breeding
  String? notes;

  /// Creation timestamp
  DateTime? createdAt;

  /// Last update timestamp
  DateTime? updatedAt;

  /// Default constructor for Isar
  BreedingRecordIsar();

  /// Generate a deterministic business ID for breeding records to ensure consistency
  /// across app reinstallations. Based on cattle ID and breeding date.
  static String generateBusinessId(String cattleId, DateTime date) {
    // Format the date in a consistent way (YYYYMMDD)
    final dateStr =
        "$date.year${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}";

    // Create a unique ID combining cattle ID and date
    final uniqueKey = "$cattleId-$dateStr";

    // Return with a prefix to distinguish breeding records
    return "breeding_$uniqueKey";
  }

  /// Generate a formatted ID for breeding records in the format 'cattleId-Breeding-sequenceNumber'
  static String generateFormattedId(String cattleId, int sequenceNumber) {
    return '$cattleId-Breeding-$sequenceNumber';
  }

  /// Factory constructor for creating a new breeding record
  factory BreedingRecordIsar.create({
    required String cattleId,
    required DateTime date,
    required String bullIdOrType,
    required String method,
    required String status,
    DateTime? expectedDate,
    double? cost = 0.0,
    String? notes,
    String? businessId,
  }) {
    return BreedingRecordIsar()
      ..businessId = businessId ?? generateBusinessId(cattleId, date) // Use date-based unique ID as default
      ..cattleId = cattleId
      ..date = date
      ..bullIdOrType = bullIdOrType
      ..method = method
      ..status = status
      ..expectedDate = expectedDate
      ..cost = cost
      ..notes = notes
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  /// Convert to a map for serialization
  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleId': cattleId,
      'date': date?.toIso8601String(),
      'bullIdOrType': bullIdOrType,
      'method': method,
      'status': status,
      'expectedDate': expectedDate?.toIso8601String(),
      'cost': cost,
      'notes': notes,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Alias for toMap
  Map<String, dynamic> toJson() => toMap();

  /// Create from a map
  factory BreedingRecordIsar.fromMap(Map<String, dynamic> map) {
    return BreedingRecordIsar()
      ..businessId = map['id'] as String?
      ..cattleId = map['cattleId'] as String?
      ..date = map['date'] != null
          ? map['date'] is String
              ? DateTime.parse(map['date'])
              : map['date'] as DateTime
          : null
      ..bullIdOrType = map['bullIdOrType'] as String?
      ..method = map['method'] as String?
      ..status = map['status'] as String?
      ..expectedDate = map['expectedDate'] != null
          ? map['expectedDate'] is String
              ? DateTime.parse(map['expectedDate'])
              : map['expectedDate'] as DateTime
          : null
      ..cost = map['cost'] != null ? (map['cost'] as num).toDouble() : 0.0
      ..notes = map['notes'] as String?
      ..createdAt = map['createdAt'] != null
          ? map['createdAt'] is String
              ? DateTime.parse(map['createdAt'])
              : map['createdAt'] as DateTime
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? map['updatedAt'] is String
              ? DateTime.parse(map['updatedAt'])
              : map['updatedAt'] as DateTime
          : DateTime.now();
  }

  /// Create from JSON
  factory BreedingRecordIsar.fromJson(Map<String, dynamic> json) =>
      BreedingRecordIsar.fromMap(json);

  /// Create a copy with updated fields
  BreedingRecordIsar copyWith({
    String? businessId,
    String? cattleId,
    DateTime? date,
    String? bullIdOrType,
    String? method,
    String? status,
    DateTime? expectedDate,
    double? cost,
    String? notes,
  }) {
    return BreedingRecordIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..cattleId = cattleId ?? this.cattleId
      ..date = date ?? this.date
      ..bullIdOrType = bullIdOrType ?? this.bullIdOrType
      ..method = method ?? this.method
      ..status = status ?? this.status
      ..expectedDate = expectedDate ?? this.expectedDate
      ..cost = cost ?? this.cost
      ..notes = notes ?? this.notes
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }

  /// Operator to access properties using map-like syntax
  dynamic operator [](String key) {
    switch (key) {
      case 'id':
        return businessId;
      case 'cattleId':
        return cattleId;
      case 'date':
        return date;
      case 'bullIdOrType':
        return bullIdOrType;
      case 'method':
        return method;
      case 'status':
        return status;
      case 'expectedDate':
        return expectedDate;
      case 'cost':
        return cost;
      case 'notes':
        return notes;
      case 'createdAt':
        return createdAt;
      case 'updatedAt':
        return updatedAt;
      default:
        return null;
    }
  }
}
