import 'dart:math' as math;
import '../models/milk_record_isar.dart';
import '../models/milk_sale_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Data class to hold all milk analytics results
class MilkAnalyticsResult {
  // KPI Metrics
  final int totalMilkRecords;
  final int totalMilkSales;
  final double totalMilkProduced;
  final double totalMilkSold;
  final double totalMilkUsedForCalves;
  final double totalMilkUsedForHome;
  final double totalMilkUsed; // calfUsage + homeUsage
  final double averageDailyProduction;
  final double averageProductionPerCattle;
  final double totalRevenue;
  final double averagePricePerLiter;

  // Production distributions
  final Map<String, double> sessionDistribution; // Morning, Afternoon, Evening
  final Map<String, double> cattleProductionDistribution;
  final Map<String, int> productionFrequencyDistribution;

  // Sales distributions
  final Map<String, double> buyerDistribution;
  final Map<String, int> paymentMethodDistribution;
  final Map<String, int> paymentStatusDistribution;

  // Performance metrics
  final double productionConsistency; // Coefficient of variation
  final double salesEfficiency; // Sold/Produced ratio
  final String topProducingCattle;
  final double topProducingCattleAmount;
  final String bestBuyer;
  final double bestBuyerVolume;

  // Trends and insights
  final double productionTrend; // Positive/negative trend
  final double priceTrend; // Price trend over time
  final int daysWithProduction;
  final int daysWithSales;

  const MilkAnalyticsResult({
    required this.totalMilkRecords,
    required this.totalMilkSales,
    required this.totalMilkProduced,
    required this.totalMilkSold,
    required this.totalMilkUsedForCalves,
    required this.totalMilkUsedForHome,
    required this.totalMilkUsed,
    required this.averageDailyProduction,
    required this.averageProductionPerCattle,
    required this.totalRevenue,
    required this.averagePricePerLiter,
    required this.sessionDistribution,
    required this.cattleProductionDistribution,
    required this.productionFrequencyDistribution,
    required this.buyerDistribution,
    required this.paymentMethodDistribution,
    required this.paymentStatusDistribution,
    required this.productionConsistency,
    required this.salesEfficiency,
    required this.topProducingCattle,
    required this.topProducingCattleAmount,
    required this.bestBuyer,
    required this.bestBuyerVolume,
    required this.productionTrend,
    required this.priceTrend,
    required this.daysWithProduction,
    required this.daysWithSales,
  });

  /// Empty result for when there's no data
  static const empty = MilkAnalyticsResult(
    totalMilkRecords: 0,
    totalMilkSales: 0,
    totalMilkProduced: 0.0,
    totalMilkSold: 0.0,
    totalMilkUsedForCalves: 0.0,
    totalMilkUsedForHome: 0.0,
    totalMilkUsed: 0.0,
    averageDailyProduction: 0.0,
    averageProductionPerCattle: 0.0,
    totalRevenue: 0.0,
    averagePricePerLiter: 0.0,
    sessionDistribution: {},
    cattleProductionDistribution: {},
    productionFrequencyDistribution: {},
    buyerDistribution: {},
    paymentMethodDistribution: {},
    paymentStatusDistribution: {},
    productionConsistency: 0.0,
    salesEfficiency: 0.0,
    topProducingCattle: '',
    topProducingCattleAmount: 0.0,
    bestBuyer: '',
    bestBuyerVolume: 0.0,
    productionTrend: 0.0,
    priceTrend: 0.0,
    daysWithProduction: 0,
    daysWithSales: 0,
  );
}

/// Pure analytics service for milk calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class MilkAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static MilkAnalyticsResult calculate(
    List<MilkRecordIsar> milkRecords,
    List<MilkSaleIsar> milkSales,
    List<CattleIsar> cattle,
  ) {
    if (milkRecords.isEmpty && milkSales.isEmpty) {
      return MilkAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _MilkAnalyticsAccumulator();

    // Process milk records
    for (final record in milkRecords) {
      accumulator.processMilkRecord(record);
    }

    // Process milk sales
    for (final sale in milkSales) {
      accumulator.processMilkSale(sale);
    }

    // Calculate derived metrics
    accumulator.calculateDerivedMetrics(cattle);

    return accumulator.toResult();
  }

  /// Calculate production metrics for a specific cattle
  static Map<String, double> calculateCattleProductionMetrics(
    String cattleId,
    List<MilkRecordIsar> milkRecords,
  ) {
    final cattleRecords = milkRecords.where((r) => r.cattleBusinessId == cattleId).toList();
    
    if (cattleRecords.isEmpty) {
      return {
        'totalProduction': 0.0,
        'averageDaily': 0.0,
        'morningAverage': 0.0,
        'afternoonAverage': 0.0,
        'eveningAverage': 0.0,
        'consistency': 0.0,
      };
    }

    final totalProduction = cattleRecords.fold(0.0, (sum, r) => sum + r.totalYield);
    final averageDaily = totalProduction / cattleRecords.length;
    
    final morningTotal = cattleRecords.fold(0.0, (sum, r) => sum + (r.morningAmount ?? 0.0));
    final afternoonTotal = cattleRecords.fold(0.0, (sum, r) => sum + (r.afternoonAmount ?? 0.0));
    final eveningTotal = cattleRecords.fold(0.0, (sum, r) => sum + (r.eveningAmount ?? 0.0));

    final morningAverage = morningTotal / cattleRecords.length;
    final afternoonAverage = afternoonTotal / cattleRecords.length;
    final eveningAverage = eveningTotal / cattleRecords.length;

    // Calculate consistency (coefficient of variation)
    final dailyYields = cattleRecords.map((r) => r.totalYield).toList();
    final consistency = _calculateConsistency(dailyYields, averageDaily);

    return {
      'totalProduction': totalProduction,
      'averageDaily': averageDaily,
      'morningAverage': morningAverage,
      'afternoonAverage': afternoonAverage,
      'eveningAverage': eveningAverage,
      'consistency': consistency,
    };
  }

  /// Calculate production trend over time
  static double calculateProductionTrend(List<MilkRecordIsar> milkRecords) {
    if (milkRecords.length < 2) return 0.0;

    // Sort by date
    final sortedRecords = List<MilkRecordIsar>.from(milkRecords)
      ..sort((a, b) => (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now()));

    // Calculate trend using simple linear regression
    final n = sortedRecords.length;
    double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;

    for (int i = 0; i < n; i++) {
      final x = i.toDouble(); // Time index
      final y = sortedRecords[i].totalYield; // Production value
      
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumX2 += x * x;
    }

    // Calculate slope (trend)
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope;
  }

  /// Calculate coefficient of variation for consistency
  static double _calculateConsistency(List<double> values, double mean) {
    if (values.isEmpty || mean == 0) return 0.0;

    final variance = values.fold(0.0, (sum, value) => sum + ((value - mean) * (value - mean))) / values.length;
    final standardDeviation = variance > 0 ? math.sqrt(variance) : 0.0;
    
    return mean > 0 ? (standardDeviation / mean) * 100 : 0.0;
  }
}

/// Efficient single-pass accumulator for all milk analytics calculations
class _MilkAnalyticsAccumulator {
  // Basic counts
  int totalMilkRecords = 0;
  int totalMilkSales = 0;

  // Production metrics
  double totalMilkProduced = 0.0;
  double totalMorningProduction = 0.0;
  double totalAfternoonProduction = 0.0;
  double totalEveningProduction = 0.0;

  // Sales metrics
  double totalMilkSold = 0.0;
  double totalMilkUsedForCalves = 0.0;
  double totalMilkUsedForHome = 0.0;
  double totalRevenue = 0.0;
  double totalPriceSum = 0.0;
  int salesWithPrice = 0;

  // Distributions
  final Map<String, double> cattleProductionMap = {};
  final Map<String, double> buyerVolumeMap = {};
  final Map<String, int> paymentMethodMap = {};
  final Map<String, int> paymentStatusMap = {};

  // Date tracking
  final Set<DateTime> productionDates = {};
  final Set<DateTime> salesDates = {};

  // Production tracking for consistency
  final List<double> dailyProductions = [];

  /// Process a single milk record
  void processMilkRecord(MilkRecordIsar record) {
    totalMilkRecords++;

    final totalYield = record.totalYield;
    totalMilkProduced += totalYield;
    
    totalMorningProduction += record.morningAmount ?? 0.0;
    totalAfternoonProduction += record.afternoonAmount ?? 0.0;
    totalEveningProduction += record.eveningAmount ?? 0.0;

    // Track cattle production
    final cattleId = record.cattleBusinessId ?? 'Unknown';
    cattleProductionMap[cattleId] = (cattleProductionMap[cattleId] ?? 0.0) + totalYield;

    // Track production dates
    if (record.date != null) {
      final dateOnly = DateTime(record.date!.year, record.date!.month, record.date!.day);
      productionDates.add(dateOnly);
    }

    // Track daily productions for consistency calculation
    dailyProductions.add(totalYield);
  }

  /// Process a single milk sale
  void processMilkSale(MilkSaleIsar sale) {
    totalMilkSales++;

    totalMilkSold += sale.quantity ?? 0.0;
    totalMilkUsedForCalves += sale.calfUsage ?? 0.0;
    totalMilkUsedForHome += sale.homeUsage ?? 0.0;
    totalRevenue += sale.total ?? 0.0;

    // Track price data
    if ((sale.price ?? 0.0) > 0) {
      totalPriceSum += sale.price ?? 0.0;
      salesWithPrice++;
    }

    // Track buyer volumes
    final buyer = (sale.buyer?.isNotEmpty ?? false) ? sale.buyer! : 'Unknown';
    buyerVolumeMap[buyer] = (buyerVolumeMap[buyer] ?? 0.0) + (sale.quantity ?? 0.0);

    // Track payment methods
    final paymentMethod = sale.paymentMethod ?? 'Unknown';
    paymentMethodMap[paymentMethod] = (paymentMethodMap[paymentMethod] ?? 0) + 1;

    // Track payment status
    final paymentStatus = sale.paymentStatus ?? 'Unknown';
    paymentStatusMap[paymentStatus] = (paymentStatusMap[paymentStatus] ?? 0) + 1;

    // Track sales dates
    if (sale.date != null) {
      final dateOnly = DateTime(sale.date!.year, sale.date!.month, sale.date!.day);
      salesDates.add(dateOnly);
    }
  }

  /// Calculate derived metrics
  void calculateDerivedMetrics(List<CattleIsar> cattle) {
    // Additional calculations can be added here if needed
  }

  /// Convert accumulated data to immutable result
  MilkAnalyticsResult toResult() {
    final averageDailyProduction = productionDates.isNotEmpty 
        ? totalMilkProduced / productionDates.length 
        : 0.0;

    final averageProductionPerCattle = cattleProductionMap.isNotEmpty 
        ? totalMilkProduced / cattleProductionMap.length 
        : 0.0;

    final averagePricePerLiter = salesWithPrice > 0 
        ? totalPriceSum / salesWithPrice 
        : 0.0;

    final salesEfficiency = totalMilkProduced > 0 
        ? (totalMilkSold / totalMilkProduced * 100).clamp(0.0, 100.0)
        : 0.0;

    // Calculate session distribution
    final totalSessionProduction = totalMorningProduction + totalAfternoonProduction + totalEveningProduction;
    final sessionDistribution = totalSessionProduction > 0 ? {
      'Morning': (totalMorningProduction / totalSessionProduction * 100),
      'Afternoon': (totalAfternoonProduction / totalSessionProduction * 100),
      'Evening': (totalEveningProduction / totalSessionProduction * 100),
    } : <String, double>{};

    // Find top producing cattle
    String topProducingCattle = '';
    double topProducingCattleAmount = 0.0;
    if (cattleProductionMap.isNotEmpty) {
      final topEntry = cattleProductionMap.entries.reduce((a, b) => a.value > b.value ? a : b);
      topProducingCattle = topEntry.key;
      topProducingCattleAmount = topEntry.value;
    }

    // Find best buyer
    String bestBuyer = '';
    double bestBuyerVolume = 0.0;
    if (buyerVolumeMap.isNotEmpty) {
      final topBuyer = buyerVolumeMap.entries.reduce((a, b) => a.value > b.value ? a : b);
      bestBuyer = topBuyer.key;
      bestBuyerVolume = topBuyer.value;
    }

    // Calculate production consistency
    final productionConsistency = MilkAnalyticsService._calculateConsistency(
      dailyProductions, 
      averageDailyProduction
    );

    // Create frequency distribution for production ranges
    final productionFrequencyDistribution = _createProductionFrequencyDistribution();

    return MilkAnalyticsResult(
      totalMilkRecords: totalMilkRecords,
      totalMilkSales: totalMilkSales,
      totalMilkProduced: totalMilkProduced,
      totalMilkSold: totalMilkSold,
      totalMilkUsedForCalves: totalMilkUsedForCalves,
      totalMilkUsedForHome: totalMilkUsedForHome,
      totalMilkUsed: totalMilkUsedForCalves + totalMilkUsedForHome,
      averageDailyProduction: averageDailyProduction,
      averageProductionPerCattle: averageProductionPerCattle,
      totalRevenue: totalRevenue,
      averagePricePerLiter: averagePricePerLiter,
      sessionDistribution: Map.unmodifiable(sessionDistribution),
      cattleProductionDistribution: Map.unmodifiable(cattleProductionMap),
      productionFrequencyDistribution: Map.unmodifiable(productionFrequencyDistribution),
      buyerDistribution: Map.unmodifiable(buyerVolumeMap),
      paymentMethodDistribution: Map.unmodifiable(paymentMethodMap),
      paymentStatusDistribution: Map.unmodifiable(paymentStatusMap),
      productionConsistency: productionConsistency,
      salesEfficiency: salesEfficiency,
      topProducingCattle: topProducingCattle,
      topProducingCattleAmount: topProducingCattleAmount,
      bestBuyer: bestBuyer,
      bestBuyerVolume: bestBuyerVolume,
      productionTrend: 0.0, // Would need time-series analysis
      priceTrend: 0.0, // Would need time-series analysis
      daysWithProduction: productionDates.length,
      daysWithSales: salesDates.length,
    );
  }

  /// Create production frequency distribution (low, medium, high production days)
  Map<String, int> _createProductionFrequencyDistribution() {
    if (dailyProductions.isEmpty) return {};

    final sortedProductions = List<double>.from(dailyProductions)..sort();
    final length = sortedProductions.length;
    
    final lowThreshold = sortedProductions[(length * 0.33).floor()];
    final highThreshold = sortedProductions[(length * 0.67).floor()];

    int lowCount = 0, mediumCount = 0, highCount = 0;

    for (final production in dailyProductions) {
      if (production <= lowThreshold) {
        lowCount++;
      } else if (production <= highThreshold) {
        mediumCount++;
      } else {
        highCount++;
      }
    }

    return {
      'Low (0-${lowThreshold.toStringAsFixed(1)}L)': lowCount,
      'Medium (${lowThreshold.toStringAsFixed(1)}-${highThreshold.toStringAsFixed(1)}L)': mediumCount,
      'High (${highThreshold.toStringAsFixed(1)}L+)': highCount,
    };
  }
}
