import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/breeding_controller.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';
import 'package:intl/intl.dart';

class DeliveryRecordsTab extends StatefulWidget {
  final BreedingController? controller; // Made optional to support Provider pattern

  const DeliveryRecordsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<DeliveryRecordsTab> createState() => _DeliveryRecordsTabState();
}

class _DeliveryRecordsTabState extends State<DeliveryRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  BreedingController get _controller => widget.controller ?? context.read<BreedingController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // TODO: Implement delivery-specific filtering when needed
    // For now, delivery records don't have complex filtering requirements
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    // For now, we'll show completed pregnancies as delivery records
    // TODO: Replace with actual delivery records when available
    final deliveryRecords = _controller.unfilteredPregnancyRecords
        .where((record) => record.status?.toLowerCase() == 'completed')
        .toList();

    // Check if we have data to display
    if (deliveryRecords.isEmpty) {
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.breeding,
          moduleName: 'delivery',
          sortFields: const [...SortField.commonFields],
          searchHint: 'Search delivery records by cattle...',
          totalCount: deliveryRecords.length,
          filteredCount: deliveryRecords.length,
        ),

        // Delivery Records List
        Expanded(
          child: deliveryRecords.isEmpty
              ? _buildEmptyState(true)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: deliveryRecords.length,
                    itemBuilder: (context, index) {
                      final record = deliveryRecords[index];
                      return _buildDeliveryCard(record);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(bool isCompletelyEmpty) {
    const tabColor = AppColors.breedingHeader;

    if (isCompletelyEmpty) {
      // No delivery records exist - show call-to-action to add first record
      return UniversalTabEmptyState.forTab(
        title: 'No Delivery Records',
        message: 'Delivery records will appear here when pregnancies are completed.',
        tabColor: tabColor,
        tabIndex: 3, // Delivery tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddDeliveryDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Records exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Delivery Records',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 3, // Delivery tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildDeliveryCard(dynamic record) {
    // For now, using pregnancy records as delivery records
    // TODO: Replace with actual delivery record structure
    final cattle = _controller.cattle.firstWhere(
      (c) => c.tagId == record.cattleId, // Use tagId for consistency
      orElse: () => throw StateError('Cattle not found'),
    );

    // Format dates
    String deliveryDateText = record.actualCalvingDate != null
        ? DateFormat('MMM dd, yyyy').format(record.actualCalvingDate!)
        : record.expectedCalvingDate != null
            ? 'Expected: ${DateFormat('MMM dd, yyyy').format(record.expectedCalvingDate!)}'
            : 'Unknown date';

    String statusText = 'Delivered';

    // Format row 1: Delivery date + status
    // Format row 2: Cattle name + breeding date
    String cattleName = cattle.name ?? 'Unknown Cattle';
    if (cattle.tagId != null && cattle.tagId!.isNotEmpty) {
      cattleName += ' (${cattle.tagId!.toUpperCase()})';
    }

    String breedingDateText = record.startDate != null
        ? 'Bred: ${DateFormat('MMM dd, yyyy').format(record.startDate!)}'
        : 'Breeding date unknown';

    return UniversalRecordCard(
      row1Left: deliveryDateText,
      row1Right: statusText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.check_circle,
      row2Left: cattleName,
      row2Right: breedingDateText,
      row2LeftIcon: Icons.pets,
      row2RightIcon: Icons.favorite,
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.breedingHeader,
      onTap: () => _navigateToDeliveryDetail(record),
      onEdit: () => _showEditDeliveryDialog(record),
      onDelete: () => _showDeleteConfirmation(record),
    );
  }

  void _navigateToDeliveryDetail(dynamic record) {
    // TODO: Implement delivery detail navigation when delivery details screen is available
    // For now, show a placeholder message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Delivery details screen coming soon')),
    );
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  void _showAddDeliveryDialog() {
    // This will be called from the parent screen's FAB
    // Implementation handled in breeding_screen.dart
  }

  void _showEditDeliveryDialog(dynamic record) {
    // TODO: Implement edit delivery dialog when delivery records are properly implemented
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit delivery functionality coming soon')),
    );
  }

  void _showDeleteConfirmation(dynamic record) {
    // TODO: Implement delete delivery confirmation when delivery records are properly implemented
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Delete delivery functionality coming soon')),
    );
  }
}
