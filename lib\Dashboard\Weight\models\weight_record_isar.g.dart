// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'weight_record_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWeightRecordIsarCollection on Isar {
  IsarCollection<WeightRecordIsar> get weightRecordIsars => this.collection();
}

const WeightRecordIsarSchema = CollectionSchema(
  name: r'WeightRecordIsar',
  id: 2738907642127391313,
  properties: {
    r'bodyConditionNotes': PropertySchema(
      id: 0,
      name: r'bodyConditionNotes',
      type: IsarType.string,
    ),
    r'bodyConditionScore': PropertySchema(
      id: 1,
      name: r'bodyConditionScore',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cattleBusinessId': PropertySchema(
      id: 3,
      name: r'cattleBusinessId',
      type: IsarType.string,
    ),
    r'confidenceLevel': PropertySchema(
      id: 4,
      name: r'confidenceLevel',
      type: IsarType.double,
    ),
    r'createdAt': PropertySchema(
      id: 5,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'createdBy': PropertySchema(
      id: 6,
      name: r'createdBy',
      type: IsarType.string,
    ),
    r'dailyGain': PropertySchema(
      id: 7,
      name: r'dailyGain',
      type: IsarType.double,
    ),
    r'date': PropertySchema(
      id: 8,
      name: r'date',
      type: IsarType.dateTime,
    ),
    r'daysSinceLastMeasurement': PropertySchema(
      id: 9,
      name: r'daysSinceLastMeasurement',
      type: IsarType.long,
    ),
    r'farmBusinessId': PropertySchema(
      id: 10,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'feedQuality': PropertySchema(
      id: 11,
      name: r'feedQuality',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarfeedQualityEnumValueMap,
    ),
    r'feedingStatus': PropertySchema(
      id: 12,
      name: r'feedingStatus',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarfeedingStatusEnumValueMap,
    ),
    r'healthStatus': PropertySchema(
      id: 13,
      name: r'healthStatus',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarhealthStatusEnumValueMap,
    ),
    r'isEstimate': PropertySchema(
      id: 14,
      name: r'isEstimate',
      type: IsarType.bool,
    ),
    r'isPregnant': PropertySchema(
      id: 15,
      name: r'isPregnant',
      type: IsarType.bool,
    ),
    r'isValidated': PropertySchema(
      id: 16,
      name: r'isValidated',
      type: IsarType.bool,
    ),
    r'measuredBy': PropertySchema(
      id: 17,
      name: r'measuredBy',
      type: IsarType.string,
    ),
    r'measurementDate': PropertySchema(
      id: 18,
      name: r'measurementDate',
      type: IsarType.dateTime,
    ),
    r'measurementLocation': PropertySchema(
      id: 19,
      name: r'measurementLocation',
      type: IsarType.string,
    ),
    r'measurementMethod': PropertySchema(
      id: 20,
      name: r'measurementMethod',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarmeasurementMethodEnumValueMap,
    ),
    r'measurementQuality': PropertySchema(
      id: 21,
      name: r'measurementQuality',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarmeasurementQualityEnumValueMap,
    ),
    r'notes': PropertySchema(
      id: 22,
      name: r'notes',
      type: IsarType.string,
    ),
    r'pregnancyStage': PropertySchema(
      id: 23,
      name: r'pregnancyStage',
      type: IsarType.long,
    ),
    r'previousWeight': PropertySchema(
      id: 24,
      name: r'previousWeight',
      type: IsarType.double,
    ),
    r'season': PropertySchema(
      id: 25,
      name: r'season',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarseasonEnumValueMap,
    ),
    r'targetDate': PropertySchema(
      id: 26,
      name: r'targetDate',
      type: IsarType.dateTime,
    ),
    r'targetWeight': PropertySchema(
      id: 27,
      name: r'targetWeight',
      type: IsarType.double,
    ),
    r'updatedAt': PropertySchema(
      id: 28,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'updatedBy': PropertySchema(
      id: 29,
      name: r'updatedBy',
      type: IsarType.string,
    ),
    r'validatedAt': PropertySchema(
      id: 30,
      name: r'validatedAt',
      type: IsarType.dateTime,
    ),
    r'validatedBy': PropertySchema(
      id: 31,
      name: r'validatedBy',
      type: IsarType.string,
    ),
    r'weatherConditions': PropertySchema(
      id: 32,
      name: r'weatherConditions',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarweatherConditionsEnumValueMap,
    ),
    r'weight': PropertySchema(
      id: 33,
      name: r'weight',
      type: IsarType.double,
    ),
    r'weightGain': PropertySchema(
      id: 34,
      name: r'weightGain',
      type: IsarType.double,
    ),
    r'weightGoal': PropertySchema(
      id: 35,
      name: r'weightGoal',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarweightGoalEnumValueMap,
    ),
    r'weightUnit': PropertySchema(
      id: 36,
      name: r'weightUnit',
      type: IsarType.byte,
      enumMap: _WeightRecordIsarweightUnitEnumValueMap,
    )
  },
  estimateSize: _weightRecordIsarEstimateSize,
  serialize: _weightRecordIsarSerialize,
  deserialize: _weightRecordIsarDeserialize,
  deserializeProp: _weightRecordIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {
    r'cattle': LinkSchema(
      id: -2658938024014869795,
      name: r'cattle',
      target: r'CattleIsar',
      single: true,
    )
  },
  embeddedSchemas: {},
  getId: _weightRecordIsarGetId,
  getLinks: _weightRecordIsarGetLinks,
  attach: _weightRecordIsarAttach,
  version: '3.1.0+1',
);

int _weightRecordIsarEstimateSize(
  WeightRecordIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.bodyConditionNotes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.createdBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.measuredBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.measurementLocation;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.updatedBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.validatedBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _weightRecordIsarSerialize(
  WeightRecordIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.bodyConditionNotes);
  writer.writeDouble(offsets[1], object.bodyConditionScore);
  writer.writeString(offsets[2], object.businessId);
  writer.writeString(offsets[3], object.cattleBusinessId);
  writer.writeDouble(offsets[4], object.confidenceLevel);
  writer.writeDateTime(offsets[5], object.createdAt);
  writer.writeString(offsets[6], object.createdBy);
  writer.writeDouble(offsets[7], object.dailyGain);
  writer.writeDateTime(offsets[8], object.date);
  writer.writeLong(offsets[9], object.daysSinceLastMeasurement);
  writer.writeString(offsets[10], object.farmBusinessId);
  writer.writeByte(offsets[11], object.feedQuality.index);
  writer.writeByte(offsets[12], object.feedingStatus.index);
  writer.writeByte(offsets[13], object.healthStatus.index);
  writer.writeBool(offsets[14], object.isEstimate);
  writer.writeBool(offsets[15], object.isPregnant);
  writer.writeBool(offsets[16], object.isValidated);
  writer.writeString(offsets[17], object.measuredBy);
  writer.writeDateTime(offsets[18], object.measurementDate);
  writer.writeString(offsets[19], object.measurementLocation);
  writer.writeByte(offsets[20], object.measurementMethod.index);
  writer.writeByte(offsets[21], object.measurementQuality.index);
  writer.writeString(offsets[22], object.notes);
  writer.writeLong(offsets[23], object.pregnancyStage);
  writer.writeDouble(offsets[24], object.previousWeight);
  writer.writeByte(offsets[25], object.season.index);
  writer.writeDateTime(offsets[26], object.targetDate);
  writer.writeDouble(offsets[27], object.targetWeight);
  writer.writeDateTime(offsets[28], object.updatedAt);
  writer.writeString(offsets[29], object.updatedBy);
  writer.writeDateTime(offsets[30], object.validatedAt);
  writer.writeString(offsets[31], object.validatedBy);
  writer.writeByte(offsets[32], object.weatherConditions.index);
  writer.writeDouble(offsets[33], object.weight);
  writer.writeDouble(offsets[34], object.weightGain);
  writer.writeByte(offsets[35], object.weightGoal.index);
  writer.writeByte(offsets[36], object.weightUnit.index);
}

WeightRecordIsar _weightRecordIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WeightRecordIsar();
  object.bodyConditionNotes = reader.readStringOrNull(offsets[0]);
  object.bodyConditionScore = reader.readDoubleOrNull(offsets[1]);
  object.businessId = reader.readStringOrNull(offsets[2]);
  object.confidenceLevel = reader.readDoubleOrNull(offsets[4]);
  object.createdAt = reader.readDateTimeOrNull(offsets[5]);
  object.createdBy = reader.readStringOrNull(offsets[6]);
  object.dailyGain = reader.readDoubleOrNull(offsets[7]);
  object.date = reader.readDateTimeOrNull(offsets[8]);
  object.daysSinceLastMeasurement = reader.readLongOrNull(offsets[9]);
  object.farmBusinessId = reader.readStringOrNull(offsets[10]);
  object.feedQuality = _WeightRecordIsarfeedQualityValueEnumMap[
          reader.readByteOrNull(offsets[11])] ??
      FeedQuality.excellent;
  object.feedingStatus = _WeightRecordIsarfeedingStatusValueEnumMap[
          reader.readByteOrNull(offsets[12])] ??
      FeedingStatus.normal;
  object.healthStatus = _WeightRecordIsarhealthStatusValueEnumMap[
          reader.readByteOrNull(offsets[13])] ??
      HealthStatus.healthy;
  object.id = id;
  object.isEstimate = reader.readBoolOrNull(offsets[14]);
  object.isPregnant = reader.readBoolOrNull(offsets[15]);
  object.isValidated = reader.readBoolOrNull(offsets[16]);
  object.measuredBy = reader.readStringOrNull(offsets[17]);
  object.measurementDate = reader.readDateTimeOrNull(offsets[18]);
  object.measurementLocation = reader.readStringOrNull(offsets[19]);
  object.measurementMethod = _WeightRecordIsarmeasurementMethodValueEnumMap[
          reader.readByteOrNull(offsets[20])] ??
      MeasurementMethod.scale;
  object.measurementQuality = _WeightRecordIsarmeasurementQualityValueEnumMap[
          reader.readByteOrNull(offsets[21])] ??
      MeasurementQuality.excellent;
  object.notes = reader.readStringOrNull(offsets[22]);
  object.pregnancyStage = reader.readLongOrNull(offsets[23]);
  object.previousWeight = reader.readDoubleOrNull(offsets[24]);
  object.season =
      _WeightRecordIsarseasonValueEnumMap[reader.readByteOrNull(offsets[25])] ??
          Season.spring;
  object.targetDate = reader.readDateTimeOrNull(offsets[26]);
  object.targetWeight = reader.readDoubleOrNull(offsets[27]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[28]);
  object.updatedBy = reader.readStringOrNull(offsets[29]);
  object.validatedAt = reader.readDateTimeOrNull(offsets[30]);
  object.validatedBy = reader.readStringOrNull(offsets[31]);
  object.weatherConditions = _WeightRecordIsarweatherConditionsValueEnumMap[
          reader.readByteOrNull(offsets[32])] ??
      WeatherConditions.sunny;
  object.weight = reader.readDouble(offsets[33]);
  object.weightGain = reader.readDoubleOrNull(offsets[34]);
  object.weightGoal = _WeightRecordIsarweightGoalValueEnumMap[
          reader.readByteOrNull(offsets[35])] ??
      WeightGoal.gain;
  object.weightUnit = _WeightRecordIsarweightUnitValueEnumMap[
          reader.readByteOrNull(offsets[36])] ??
      WeightUnit.kg;
  return object;
}

P _weightRecordIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readDoubleOrNull(offset)) as P;
    case 5:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readDoubleOrNull(offset)) as P;
    case 8:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 9:
      return (reader.readLongOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (_WeightRecordIsarfeedQualityValueEnumMap[
              reader.readByteOrNull(offset)] ??
          FeedQuality.excellent) as P;
    case 12:
      return (_WeightRecordIsarfeedingStatusValueEnumMap[
              reader.readByteOrNull(offset)] ??
          FeedingStatus.normal) as P;
    case 13:
      return (_WeightRecordIsarhealthStatusValueEnumMap[
              reader.readByteOrNull(offset)] ??
          HealthStatus.healthy) as P;
    case 14:
      return (reader.readBoolOrNull(offset)) as P;
    case 15:
      return (reader.readBoolOrNull(offset)) as P;
    case 16:
      return (reader.readBoolOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 19:
      return (reader.readStringOrNull(offset)) as P;
    case 20:
      return (_WeightRecordIsarmeasurementMethodValueEnumMap[
              reader.readByteOrNull(offset)] ??
          MeasurementMethod.scale) as P;
    case 21:
      return (_WeightRecordIsarmeasurementQualityValueEnumMap[
              reader.readByteOrNull(offset)] ??
          MeasurementQuality.excellent) as P;
    case 22:
      return (reader.readStringOrNull(offset)) as P;
    case 23:
      return (reader.readLongOrNull(offset)) as P;
    case 24:
      return (reader.readDoubleOrNull(offset)) as P;
    case 25:
      return (_WeightRecordIsarseasonValueEnumMap[
              reader.readByteOrNull(offset)] ??
          Season.spring) as P;
    case 26:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 27:
      return (reader.readDoubleOrNull(offset)) as P;
    case 28:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 29:
      return (reader.readStringOrNull(offset)) as P;
    case 30:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 31:
      return (reader.readStringOrNull(offset)) as P;
    case 32:
      return (_WeightRecordIsarweatherConditionsValueEnumMap[
              reader.readByteOrNull(offset)] ??
          WeatherConditions.sunny) as P;
    case 33:
      return (reader.readDouble(offset)) as P;
    case 34:
      return (reader.readDoubleOrNull(offset)) as P;
    case 35:
      return (_WeightRecordIsarweightGoalValueEnumMap[
              reader.readByteOrNull(offset)] ??
          WeightGoal.gain) as P;
    case 36:
      return (_WeightRecordIsarweightUnitValueEnumMap[
              reader.readByteOrNull(offset)] ??
          WeightUnit.kg) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WeightRecordIsarfeedQualityEnumValueMap = {
  'excellent': 0,
  'good': 1,
  'fair': 2,
  'poor': 3,
};
const _WeightRecordIsarfeedQualityValueEnumMap = {
  0: FeedQuality.excellent,
  1: FeedQuality.good,
  2: FeedQuality.fair,
  3: FeedQuality.poor,
};
const _WeightRecordIsarfeedingStatusEnumValueMap = {
  'normal': 0,
  'increased': 1,
  'decreased': 2,
  'fasting': 3,
};
const _WeightRecordIsarfeedingStatusValueEnumMap = {
  0: FeedingStatus.normal,
  1: FeedingStatus.increased,
  2: FeedingStatus.decreased,
  3: FeedingStatus.fasting,
};
const _WeightRecordIsarhealthStatusEnumValueMap = {
  'healthy': 0,
  'sick': 1,
  'recovering': 2,
};
const _WeightRecordIsarhealthStatusValueEnumMap = {
  0: HealthStatus.healthy,
  1: HealthStatus.sick,
  2: HealthStatus.recovering,
};
const _WeightRecordIsarmeasurementMethodEnumValueMap = {
  'scale': 0,
  'tape': 1,
  'visualEstimate': 2,
};
const _WeightRecordIsarmeasurementMethodValueEnumMap = {
  0: MeasurementMethod.scale,
  1: MeasurementMethod.tape,
  2: MeasurementMethod.visualEstimate,
};
const _WeightRecordIsarmeasurementQualityEnumValueMap = {
  'excellent': 0,
  'good': 1,
  'fair': 2,
  'poor': 3,
};
const _WeightRecordIsarmeasurementQualityValueEnumMap = {
  0: MeasurementQuality.excellent,
  1: MeasurementQuality.good,
  2: MeasurementQuality.fair,
  3: MeasurementQuality.poor,
};
const _WeightRecordIsarseasonEnumValueMap = {
  'spring': 0,
  'summer': 1,
  'autumn': 2,
  'winter': 3,
};
const _WeightRecordIsarseasonValueEnumMap = {
  0: Season.spring,
  1: Season.summer,
  2: Season.autumn,
  3: Season.winter,
};
const _WeightRecordIsarweatherConditionsEnumValueMap = {
  'sunny': 0,
  'cloudy': 1,
  'rainy': 2,
  'stormy': 3,
  'hot': 4,
  'cold': 5,
};
const _WeightRecordIsarweatherConditionsValueEnumMap = {
  0: WeatherConditions.sunny,
  1: WeatherConditions.cloudy,
  2: WeatherConditions.rainy,
  3: WeatherConditions.stormy,
  4: WeatherConditions.hot,
  5: WeatherConditions.cold,
};
const _WeightRecordIsarweightGoalEnumValueMap = {
  'gain': 0,
  'maintain': 1,
  'lose': 2,
};
const _WeightRecordIsarweightGoalValueEnumMap = {
  0: WeightGoal.gain,
  1: WeightGoal.maintain,
  2: WeightGoal.lose,
};
const _WeightRecordIsarweightUnitEnumValueMap = {
  'kg': 0,
  'lbs': 1,
};
const _WeightRecordIsarweightUnitValueEnumMap = {
  0: WeightUnit.kg,
  1: WeightUnit.lbs,
};

Id _weightRecordIsarGetId(WeightRecordIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _weightRecordIsarGetLinks(WeightRecordIsar object) {
  return [object.cattle];
}

void _weightRecordIsarAttach(
    IsarCollection<dynamic> col, Id id, WeightRecordIsar object) {
  object.id = id;
  object.cattle.attach(col, col.isar.collection<CattleIsar>(), r'cattle', id);
}

extension WeightRecordIsarByIndex on IsarCollection<WeightRecordIsar> {
  Future<WeightRecordIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  WeightRecordIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<WeightRecordIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<WeightRecordIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(WeightRecordIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(WeightRecordIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<WeightRecordIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<WeightRecordIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension WeightRecordIsarQueryWhereSort
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QWhere> {
  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WeightRecordIsarQueryWhere
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QWhereClause> {
  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension WeightRecordIsarQueryFilter
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QFilterCondition> {
  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bodyConditionNotes',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bodyConditionNotes',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bodyConditionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bodyConditionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bodyConditionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bodyConditionNotes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'bodyConditionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'bodyConditionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'bodyConditionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'bodyConditionNotes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bodyConditionNotes',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionNotesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'bodyConditionNotes',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionScoreIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bodyConditionScore',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionScoreIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bodyConditionScore',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionScoreEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bodyConditionScore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionScoreGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bodyConditionScore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionScoreLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bodyConditionScore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      bodyConditionScoreBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bodyConditionScore',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      confidenceLevelIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'confidenceLevel',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      confidenceLevelIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'confidenceLevel',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      confidenceLevelEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'confidenceLevel',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      confidenceLevelGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'confidenceLevel',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      confidenceLevelLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'confidenceLevel',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      confidenceLevelBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'confidenceLevel',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'createdBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      createdByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dailyGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dailyGain',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dailyGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dailyGain',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dailyGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dailyGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dailyGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dailyGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dailyGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dailyGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dailyGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dailyGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      dateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      daysSinceLastMeasurementIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'daysSinceLastMeasurement',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      daysSinceLastMeasurementIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'daysSinceLastMeasurement',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      daysSinceLastMeasurementEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'daysSinceLastMeasurement',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      daysSinceLastMeasurementGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'daysSinceLastMeasurement',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      daysSinceLastMeasurementLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'daysSinceLastMeasurement',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      daysSinceLastMeasurementBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'daysSinceLastMeasurement',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedQualityEqualTo(FeedQuality value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'feedQuality',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedQualityGreaterThan(
    FeedQuality value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'feedQuality',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedQualityLessThan(
    FeedQuality value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'feedQuality',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedQualityBetween(
    FeedQuality lower,
    FeedQuality upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'feedQuality',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedingStatusEqualTo(FeedingStatus value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'feedingStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedingStatusGreaterThan(
    FeedingStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'feedingStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedingStatusLessThan(
    FeedingStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'feedingStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      feedingStatusBetween(
    FeedingStatus lower,
    FeedingStatus upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'feedingStatus',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      healthStatusEqualTo(HealthStatus value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      healthStatusGreaterThan(
    HealthStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'healthStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      healthStatusLessThan(
    HealthStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'healthStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      healthStatusBetween(
    HealthStatus lower,
    HealthStatus upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'healthStatus',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isEstimateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isEstimate',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isEstimateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isEstimate',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isEstimateEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isEstimate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isPregnantIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isPregnant',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isPregnantIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isPregnant',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isPregnantEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isPregnant',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isValidatedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isValidated',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isValidatedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isValidated',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      isValidatedEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isValidated',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'measuredBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'measuredBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'measuredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'measuredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'measuredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'measuredBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'measuredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'measuredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'measuredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'measuredBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'measuredBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measuredByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'measuredBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'measurementDate',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'measurementDate',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'measurementDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'measurementDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'measurementDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'measurementDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'measurementLocation',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'measurementLocation',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'measurementLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'measurementLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'measurementLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'measurementLocation',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'measurementLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'measurementLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'measurementLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'measurementLocation',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'measurementLocation',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementLocationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'measurementLocation',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementMethodEqualTo(MeasurementMethod value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'measurementMethod',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementMethodGreaterThan(
    MeasurementMethod value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'measurementMethod',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementMethodLessThan(
    MeasurementMethod value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'measurementMethod',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementMethodBetween(
    MeasurementMethod lower,
    MeasurementMethod upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'measurementMethod',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementQualityEqualTo(MeasurementQuality value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'measurementQuality',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementQualityGreaterThan(
    MeasurementQuality value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'measurementQuality',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementQualityLessThan(
    MeasurementQuality value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'measurementQuality',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      measurementQualityBetween(
    MeasurementQuality lower,
    MeasurementQuality upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'measurementQuality',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      pregnancyStageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'pregnancyStage',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      pregnancyStageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'pregnancyStage',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      pregnancyStageEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pregnancyStage',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      pregnancyStageGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'pregnancyStage',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      pregnancyStageLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'pregnancyStage',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      pregnancyStageBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'pregnancyStage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      previousWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'previousWeight',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      previousWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'previousWeight',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      previousWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'previousWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      previousWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'previousWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      previousWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'previousWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      previousWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'previousWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      seasonEqualTo(Season value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'season',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      seasonGreaterThan(
    Season value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'season',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      seasonLessThan(
    Season value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'season',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      seasonBetween(
    Season lower,
    Season upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'season',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'targetDate',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'targetDate',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'targetWeight',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'targetWeight',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      targetWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'updatedBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      updatedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'updatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'validatedAt',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'validatedAt',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'validatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'validatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'validatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'validatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'validatedBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'validatedBy',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'validatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'validatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'validatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'validatedBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'validatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'validatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'validatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'validatedBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'validatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      validatedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'validatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weatherConditionsEqualTo(WeatherConditions value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weatherConditions',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weatherConditionsGreaterThan(
    WeatherConditions value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weatherConditions',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weatherConditionsLessThan(
    WeatherConditions value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weatherConditions',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weatherConditionsBetween(
    WeatherConditions lower,
    WeatherConditions upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weatherConditions',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightGain',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightGain',
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGoalEqualTo(WeightGoal value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightGoal',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGoalGreaterThan(
    WeightGoal value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightGoal',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGoalLessThan(
    WeightGoal value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightGoal',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightGoalBetween(
    WeightGoal lower,
    WeightGoal upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightGoal',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightUnitEqualTo(WeightUnit value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightUnit',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightUnitGreaterThan(
    WeightUnit value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightUnit',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightUnitLessThan(
    WeightUnit value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightUnit',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      weightUnitBetween(
    WeightUnit lower,
    WeightUnit upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightUnit',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WeightRecordIsarQueryObject
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QFilterCondition> {}

extension WeightRecordIsarQueryLinks
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QFilterCondition> {
  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattle(FilterQuery<CattleIsar> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'cattle');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterFilterCondition>
      cattleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'cattle', 0, true, 0, true);
    });
  }
}

extension WeightRecordIsarQuerySortBy
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QSortBy> {
  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByBodyConditionNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionNotes', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByBodyConditionNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionNotes', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByBodyConditionScore() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByBodyConditionScoreDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByConfidenceLevel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'confidenceLevel', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByConfidenceLevelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'confidenceLevel', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByDailyGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyGain', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByDailyGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyGain', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy> sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByDaysSinceLastMeasurement() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysSinceLastMeasurement', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByDaysSinceLastMeasurementDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysSinceLastMeasurement', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByFeedQuality() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedQuality', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByFeedQualityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedQuality', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByFeedingStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedingStatus', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByFeedingStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedingStatus', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByHealthStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByHealthStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByIsEstimate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEstimate', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByIsEstimateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEstimate', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByIsPregnant() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPregnant', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByIsPregnantDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPregnant', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByIsValidated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isValidated', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByIsValidatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isValidated', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasuredBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measuredBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasuredByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measuredBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementDate', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementDate', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementLocation', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementLocation', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementMethod() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementMethod', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementMethodDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementMethod', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementQuality() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementQuality', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByMeasurementQualityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementQuality', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy> sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByPregnancyStage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyStage', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByPregnancyStageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyStage', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByPreviousWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByPreviousWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortBySeason() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'season', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortBySeasonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'season', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByTargetDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByTargetDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByTargetWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByTargetWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByValidatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByValidatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByValidatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByValidatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeatherConditions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeatherConditionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGain', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeightGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGain', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeightGoal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGoal', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeightGoalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGoal', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeightUnit() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightUnit', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      sortByWeightUnitDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightUnit', Sort.desc);
    });
  }
}

extension WeightRecordIsarQuerySortThenBy
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QSortThenBy> {
  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByBodyConditionNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionNotes', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByBodyConditionNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionNotes', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByBodyConditionScore() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByBodyConditionScoreDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByConfidenceLevel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'confidenceLevel', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByConfidenceLevelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'confidenceLevel', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByDailyGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyGain', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByDailyGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyGain', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy> thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByDaysSinceLastMeasurement() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysSinceLastMeasurement', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByDaysSinceLastMeasurementDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysSinceLastMeasurement', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByFeedQuality() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedQuality', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByFeedQualityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedQuality', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByFeedingStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedingStatus', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByFeedingStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'feedingStatus', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByHealthStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByHealthStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByIsEstimate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEstimate', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByIsEstimateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEstimate', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByIsPregnant() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPregnant', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByIsPregnantDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPregnant', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByIsValidated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isValidated', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByIsValidatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isValidated', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasuredBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measuredBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasuredByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measuredBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementDate', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementDate', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementLocation', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementLocation', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementMethod() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementMethod', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementMethodDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementMethod', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementQuality() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementQuality', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByMeasurementQualityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'measurementQuality', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy> thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByPregnancyStage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyStage', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByPregnancyStageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyStage', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByPreviousWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByPreviousWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'previousWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenBySeason() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'season', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenBySeasonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'season', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByTargetDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByTargetDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByTargetWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByTargetWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByValidatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByValidatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByValidatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedBy', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByValidatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'validatedBy', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeatherConditions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeatherConditionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGain', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeightGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGain', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeightGoal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGoal', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeightGoalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightGoal', Sort.desc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeightUnit() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightUnit', Sort.asc);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QAfterSortBy>
      thenByWeightUnitDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightUnit', Sort.desc);
    });
  }
}

extension WeightRecordIsarQueryWhereDistinct
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct> {
  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByBodyConditionNotes({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bodyConditionNotes',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByBodyConditionScore() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bodyConditionScore');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByCattleBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByConfidenceLevel() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'confidenceLevel');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByCreatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByDailyGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dailyGain');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct> distinctByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByDaysSinceLastMeasurement() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'daysSinceLastMeasurement');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByFeedQuality() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'feedQuality');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByFeedingStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'feedingStatus');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByHealthStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'healthStatus');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByIsEstimate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isEstimate');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByIsPregnant() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isPregnant');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByIsValidated() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isValidated');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByMeasuredBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'measuredBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByMeasurementDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'measurementDate');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByMeasurementLocation({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'measurementLocation',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByMeasurementMethod() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'measurementMethod');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByMeasurementQuality() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'measurementQuality');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct> distinctByNotes(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByPregnancyStage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pregnancyStage');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByPreviousWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'previousWeight');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctBySeason() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'season');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByTargetDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetDate');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByTargetWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetWeight');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByUpdatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByValidatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'validatedAt');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByValidatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'validatedBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByWeatherConditions() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weatherConditions');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weight');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightGain');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByWeightGoal() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightGoal');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightRecordIsar, QDistinct>
      distinctByWeightUnit() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightUnit');
    });
  }
}

extension WeightRecordIsarQueryProperty
    on QueryBuilder<WeightRecordIsar, WeightRecordIsar, QQueryProperty> {
  QueryBuilder<WeightRecordIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      bodyConditionNotesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bodyConditionNotes');
    });
  }

  QueryBuilder<WeightRecordIsar, double?, QQueryOperations>
      bodyConditionScoreProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bodyConditionScore');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      cattleBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleBusinessId');
    });
  }

  QueryBuilder<WeightRecordIsar, double?, QQueryOperations>
      confidenceLevelProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'confidenceLevel');
    });
  }

  QueryBuilder<WeightRecordIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      createdByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdBy');
    });
  }

  QueryBuilder<WeightRecordIsar, double?, QQueryOperations>
      dailyGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dailyGain');
    });
  }

  QueryBuilder<WeightRecordIsar, DateTime?, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<WeightRecordIsar, int?, QQueryOperations>
      daysSinceLastMeasurementProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'daysSinceLastMeasurement');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<WeightRecordIsar, FeedQuality, QQueryOperations>
      feedQualityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'feedQuality');
    });
  }

  QueryBuilder<WeightRecordIsar, FeedingStatus, QQueryOperations>
      feedingStatusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'feedingStatus');
    });
  }

  QueryBuilder<WeightRecordIsar, HealthStatus, QQueryOperations>
      healthStatusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'healthStatus');
    });
  }

  QueryBuilder<WeightRecordIsar, bool?, QQueryOperations> isEstimateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isEstimate');
    });
  }

  QueryBuilder<WeightRecordIsar, bool?, QQueryOperations> isPregnantProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isPregnant');
    });
  }

  QueryBuilder<WeightRecordIsar, bool?, QQueryOperations>
      isValidatedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isValidated');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      measuredByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'measuredBy');
    });
  }

  QueryBuilder<WeightRecordIsar, DateTime?, QQueryOperations>
      measurementDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'measurementDate');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      measurementLocationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'measurementLocation');
    });
  }

  QueryBuilder<WeightRecordIsar, MeasurementMethod, QQueryOperations>
      measurementMethodProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'measurementMethod');
    });
  }

  QueryBuilder<WeightRecordIsar, MeasurementQuality, QQueryOperations>
      measurementQualityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'measurementQuality');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<WeightRecordIsar, int?, QQueryOperations>
      pregnancyStageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pregnancyStage');
    });
  }

  QueryBuilder<WeightRecordIsar, double?, QQueryOperations>
      previousWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'previousWeight');
    });
  }

  QueryBuilder<WeightRecordIsar, Season, QQueryOperations> seasonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'season');
    });
  }

  QueryBuilder<WeightRecordIsar, DateTime?, QQueryOperations>
      targetDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetDate');
    });
  }

  QueryBuilder<WeightRecordIsar, double?, QQueryOperations>
      targetWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetWeight');
    });
  }

  QueryBuilder<WeightRecordIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      updatedByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedBy');
    });
  }

  QueryBuilder<WeightRecordIsar, DateTime?, QQueryOperations>
      validatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'validatedAt');
    });
  }

  QueryBuilder<WeightRecordIsar, String?, QQueryOperations>
      validatedByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'validatedBy');
    });
  }

  QueryBuilder<WeightRecordIsar, WeatherConditions, QQueryOperations>
      weatherConditionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weatherConditions');
    });
  }

  QueryBuilder<WeightRecordIsar, double, QQueryOperations> weightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weight');
    });
  }

  QueryBuilder<WeightRecordIsar, double?, QQueryOperations>
      weightGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightGain');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightGoal, QQueryOperations>
      weightGoalProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightGoal');
    });
  }

  QueryBuilder<WeightRecordIsar, WeightUnit, QQueryOperations>
      weightUnitProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightUnit');
    });
  }
}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWeightGoalIsarCollection on Isar {
  IsarCollection<WeightGoalIsar> get weightGoalIsars => this.collection();
}

const WeightGoalIsarSchema = CollectionSchema(
  name: r'WeightGoalIsar',
  id: -7173709677750095144,
  properties: {
    r'achievedDate': PropertySchema(
      id: 0,
      name: r'achievedDate',
      type: IsarType.dateTime,
    ),
    r'businessId': PropertySchema(
      id: 1,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 2,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'currentWeight': PropertySchema(
      id: 3,
      name: r'currentWeight',
      type: IsarType.double,
    ),
    r'dailyTargetGain': PropertySchema(
      id: 4,
      name: r'dailyTargetGain',
      type: IsarType.double,
    ),
    r'farmBusinessId': PropertySchema(
      id: 5,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'goalType': PropertySchema(
      id: 6,
      name: r'goalType',
      type: IsarType.byte,
      enumMap: _WeightGoalIsargoalTypeEnumValueMap,
    ),
    r'isAchieved': PropertySchema(
      id: 7,
      name: r'isAchieved',
      type: IsarType.bool,
    ),
    r'notes': PropertySchema(
      id: 8,
      name: r'notes',
      type: IsarType.string,
    ),
    r'progressPercentage': PropertySchema(
      id: 9,
      name: r'progressPercentage',
      type: IsarType.double,
    ),
    r'startDate': PropertySchema(
      id: 10,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'startingWeight': PropertySchema(
      id: 11,
      name: r'startingWeight',
      type: IsarType.double,
    ),
    r'status': PropertySchema(
      id: 12,
      name: r'status',
      type: IsarType.byte,
      enumMap: _WeightGoalIsarstatusEnumValueMap,
    ),
    r'targetDate': PropertySchema(
      id: 13,
      name: r'targetDate',
      type: IsarType.dateTime,
    ),
    r'targetWeight': PropertySchema(
      id: 14,
      name: r'targetWeight',
      type: IsarType.double,
    ),
    r'updatedAt': PropertySchema(
      id: 15,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'weeklyTargetGain': PropertySchema(
      id: 16,
      name: r'weeklyTargetGain',
      type: IsarType.double,
    ),
    r'weightToGain': PropertySchema(
      id: 17,
      name: r'weightToGain',
      type: IsarType.double,
    ),
    r'weightToLose': PropertySchema(
      id: 18,
      name: r'weightToLose',
      type: IsarType.double,
    )
  },
  estimateSize: _weightGoalIsarEstimateSize,
  serialize: _weightGoalIsarSerialize,
  deserialize: _weightGoalIsarDeserialize,
  deserializeProp: _weightGoalIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {
    r'cattle': LinkSchema(
      id: -7026412553967111676,
      name: r'cattle',
      target: r'CattleIsar',
      single: true,
    )
  },
  embeddedSchemas: {},
  getId: _weightGoalIsarGetId,
  getLinks: _weightGoalIsarGetLinks,
  attach: _weightGoalIsarAttach,
  version: '3.1.0+1',
);

int _weightGoalIsarEstimateSize(
  WeightGoalIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _weightGoalIsarSerialize(
  WeightGoalIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDateTime(offsets[0], object.achievedDate);
  writer.writeString(offsets[1], object.businessId);
  writer.writeDateTime(offsets[2], object.createdAt);
  writer.writeDouble(offsets[3], object.currentWeight);
  writer.writeDouble(offsets[4], object.dailyTargetGain);
  writer.writeString(offsets[5], object.farmBusinessId);
  writer.writeByte(offsets[6], object.goalType.index);
  writer.writeBool(offsets[7], object.isAchieved);
  writer.writeString(offsets[8], object.notes);
  writer.writeDouble(offsets[9], object.progressPercentage);
  writer.writeDateTime(offsets[10], object.startDate);
  writer.writeDouble(offsets[11], object.startingWeight);
  writer.writeByte(offsets[12], object.status.index);
  writer.writeDateTime(offsets[13], object.targetDate);
  writer.writeDouble(offsets[14], object.targetWeight);
  writer.writeDateTime(offsets[15], object.updatedAt);
  writer.writeDouble(offsets[16], object.weeklyTargetGain);
  writer.writeDouble(offsets[17], object.weightToGain);
  writer.writeDouble(offsets[18], object.weightToLose);
}

WeightGoalIsar _weightGoalIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WeightGoalIsar();
  object.achievedDate = reader.readDateTimeOrNull(offsets[0]);
  object.businessId = reader.readStringOrNull(offsets[1]);
  object.createdAt = reader.readDateTimeOrNull(offsets[2]);
  object.currentWeight = reader.readDoubleOrNull(offsets[3]);
  object.dailyTargetGain = reader.readDoubleOrNull(offsets[4]);
  object.farmBusinessId = reader.readStringOrNull(offsets[5]);
  object.goalType =
      _WeightGoalIsargoalTypeValueEnumMap[reader.readByteOrNull(offsets[6])] ??
          GoalType.weightGain;
  object.id = id;
  object.isAchieved = reader.readBoolOrNull(offsets[7]);
  object.notes = reader.readStringOrNull(offsets[8]);
  object.progressPercentage = reader.readDoubleOrNull(offsets[9]);
  object.startDate = reader.readDateTimeOrNull(offsets[10]);
  object.startingWeight = reader.readDoubleOrNull(offsets[11]);
  object.status =
      _WeightGoalIsarstatusValueEnumMap[reader.readByteOrNull(offsets[12])] ??
          GoalStatus.active;
  object.targetDate = reader.readDateTimeOrNull(offsets[13]);
  object.targetWeight = reader.readDoubleOrNull(offsets[14]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[15]);
  object.weeklyTargetGain = reader.readDoubleOrNull(offsets[16]);
  object.weightToGain = reader.readDoubleOrNull(offsets[17]);
  object.weightToLose = reader.readDoubleOrNull(offsets[18]);
  return object;
}

P _weightGoalIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readDoubleOrNull(offset)) as P;
    case 4:
      return (reader.readDoubleOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (_WeightGoalIsargoalTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GoalType.weightGain) as P;
    case 7:
      return (reader.readBoolOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readDoubleOrNull(offset)) as P;
    case 10:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 11:
      return (reader.readDoubleOrNull(offset)) as P;
    case 12:
      return (_WeightGoalIsarstatusValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GoalStatus.active) as P;
    case 13:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 14:
      return (reader.readDoubleOrNull(offset)) as P;
    case 15:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 16:
      return (reader.readDoubleOrNull(offset)) as P;
    case 17:
      return (reader.readDoubleOrNull(offset)) as P;
    case 18:
      return (reader.readDoubleOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WeightGoalIsargoalTypeEnumValueMap = {
  'weightGain': 0,
  'weightLoss': 1,
  'weightMaintenance': 2,
};
const _WeightGoalIsargoalTypeValueEnumMap = {
  0: GoalType.weightGain,
  1: GoalType.weightLoss,
  2: GoalType.weightMaintenance,
};
const _WeightGoalIsarstatusEnumValueMap = {
  'active': 0,
  'achieved': 1,
  'paused': 2,
  'cancelled': 3,
};
const _WeightGoalIsarstatusValueEnumMap = {
  0: GoalStatus.active,
  1: GoalStatus.achieved,
  2: GoalStatus.paused,
  3: GoalStatus.cancelled,
};

Id _weightGoalIsarGetId(WeightGoalIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _weightGoalIsarGetLinks(WeightGoalIsar object) {
  return [object.cattle];
}

void _weightGoalIsarAttach(
    IsarCollection<dynamic> col, Id id, WeightGoalIsar object) {
  object.id = id;
  object.cattle.attach(col, col.isar.collection<CattleIsar>(), r'cattle', id);
}

extension WeightGoalIsarByIndex on IsarCollection<WeightGoalIsar> {
  Future<WeightGoalIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  WeightGoalIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<WeightGoalIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<WeightGoalIsar?> getAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(WeightGoalIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(WeightGoalIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<WeightGoalIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<WeightGoalIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension WeightGoalIsarQueryWhereSort
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QWhere> {
  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WeightGoalIsarQueryWhere
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QWhereClause> {
  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause> idGreaterThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension WeightGoalIsarQueryFilter
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QFilterCondition> {
  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      achievedDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'achievedDate',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      achievedDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'achievedDate',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      achievedDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'achievedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      achievedDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'achievedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      achievedDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'achievedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      achievedDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'achievedDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      currentWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'currentWeight',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      currentWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'currentWeight',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      currentWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'currentWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      currentWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'currentWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      currentWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'currentWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      currentWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'currentWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      dailyTargetGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dailyTargetGain',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      dailyTargetGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dailyTargetGain',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      dailyTargetGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dailyTargetGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      dailyTargetGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dailyTargetGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      dailyTargetGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dailyTargetGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      dailyTargetGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dailyTargetGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      goalTypeEqualTo(GoalType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'goalType',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      goalTypeGreaterThan(
    GoalType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'goalType',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      goalTypeLessThan(
    GoalType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'goalType',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      goalTypeBetween(
    GoalType lower,
    GoalType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'goalType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      isAchievedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isAchieved',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      isAchievedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isAchieved',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      isAchievedEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isAchieved',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      progressPercentageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'progressPercentage',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      progressPercentageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'progressPercentage',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      progressPercentageEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'progressPercentage',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      progressPercentageGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'progressPercentage',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      progressPercentageLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'progressPercentage',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      progressPercentageBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'progressPercentage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startingWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startingWeight',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startingWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startingWeight',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startingWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startingWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startingWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startingWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startingWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startingWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      startingWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startingWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      statusEqualTo(GoalStatus value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      statusGreaterThan(
    GoalStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      statusLessThan(
    GoalStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      statusBetween(
    GoalStatus lower,
    GoalStatus upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'targetDate',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'targetDate',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'targetWeight',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'targetWeight',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      targetWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weeklyTargetGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weeklyTargetGain',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weeklyTargetGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weeklyTargetGain',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weeklyTargetGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weeklyTargetGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weeklyTargetGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weeklyTargetGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weeklyTargetGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weeklyTargetGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weeklyTargetGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weeklyTargetGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightToGain',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightToGain',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightToGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightToGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightToGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightToGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToLoseIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightToLose',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToLoseIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightToLose',
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToLoseEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightToLose',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToLoseGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightToLose',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToLoseLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightToLose',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      weightToLoseBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightToLose',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }
}

extension WeightGoalIsarQueryObject
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QFilterCondition> {}

extension WeightGoalIsarQueryLinks
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QFilterCondition> {
  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition> cattle(
      FilterQuery<CattleIsar> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'cattle');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterFilterCondition>
      cattleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'cattle', 0, true, 0, true);
    });
  }
}

extension WeightGoalIsarQuerySortBy
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QSortBy> {
  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByAchievedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'achievedDate', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByAchievedDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'achievedDate', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByCurrentWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByCurrentWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByDailyTargetGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyTargetGain', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByDailyTargetGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyTargetGain', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> sortByGoalType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goalType', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByGoalTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goalType', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByIsAchieved() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAchieved', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByIsAchievedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAchieved', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByProgressPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByProgressPercentageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByStartingWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startingWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByStartingWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startingWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByTargetDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByTargetDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByTargetWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByTargetWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByWeeklyTargetGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weeklyTargetGain', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByWeeklyTargetGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weeklyTargetGain', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByWeightToGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToGain', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByWeightToGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToGain', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByWeightToLose() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToLose', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      sortByWeightToLoseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToLose', Sort.desc);
    });
  }
}

extension WeightGoalIsarQuerySortThenBy
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QSortThenBy> {
  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByAchievedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'achievedDate', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByAchievedDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'achievedDate', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByCurrentWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByCurrentWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByDailyTargetGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyTargetGain', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByDailyTargetGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dailyTargetGain', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByGoalType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goalType', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByGoalTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goalType', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByIsAchieved() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAchieved', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByIsAchievedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAchieved', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByProgressPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByProgressPercentageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByStartingWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startingWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByStartingWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startingWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByTargetDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByTargetDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetDate', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByTargetWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByTargetWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy> thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByWeeklyTargetGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weeklyTargetGain', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByWeeklyTargetGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weeklyTargetGain', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByWeightToGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToGain', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByWeightToGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToGain', Sort.desc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByWeightToLose() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToLose', Sort.asc);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QAfterSortBy>
      thenByWeightToLoseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightToLose', Sort.desc);
    });
  }
}

extension WeightGoalIsarQueryWhereDistinct
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct> {
  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByAchievedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'achievedDate');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct> distinctByBusinessId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByCurrentWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'currentWeight');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByDailyTargetGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dailyTargetGain');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct> distinctByGoalType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'goalType');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByIsAchieved() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isAchieved');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct> distinctByNotes(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByProgressPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'progressPercentage');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByStartingWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startingWeight');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct> distinctByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByTargetDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetDate');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByTargetWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetWeight');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByWeeklyTargetGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weeklyTargetGain');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByWeightToGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightToGain');
    });
  }

  QueryBuilder<WeightGoalIsar, WeightGoalIsar, QDistinct>
      distinctByWeightToLose() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightToLose');
    });
  }
}

extension WeightGoalIsarQueryProperty
    on QueryBuilder<WeightGoalIsar, WeightGoalIsar, QQueryProperty> {
  QueryBuilder<WeightGoalIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WeightGoalIsar, DateTime?, QQueryOperations>
      achievedDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'achievedDate');
    });
  }

  QueryBuilder<WeightGoalIsar, String?, QQueryOperations> businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<WeightGoalIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      currentWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'currentWeight');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      dailyTargetGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dailyTargetGain');
    });
  }

  QueryBuilder<WeightGoalIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<WeightGoalIsar, GoalType, QQueryOperations> goalTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'goalType');
    });
  }

  QueryBuilder<WeightGoalIsar, bool?, QQueryOperations> isAchievedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isAchieved');
    });
  }

  QueryBuilder<WeightGoalIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      progressPercentageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'progressPercentage');
    });
  }

  QueryBuilder<WeightGoalIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      startingWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startingWeight');
    });
  }

  QueryBuilder<WeightGoalIsar, GoalStatus, QQueryOperations> statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<WeightGoalIsar, DateTime?, QQueryOperations>
      targetDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetDate');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      targetWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetWeight');
    });
  }

  QueryBuilder<WeightGoalIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      weeklyTargetGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weeklyTargetGain');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      weightToGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightToGain');
    });
  }

  QueryBuilder<WeightGoalIsar, double?, QQueryOperations>
      weightToLoseProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightToLose');
    });
  }
}
