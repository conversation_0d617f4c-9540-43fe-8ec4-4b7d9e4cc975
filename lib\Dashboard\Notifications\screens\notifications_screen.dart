import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../../../constants/app_colors.dart';

class NotificationItem {
  final String title;
  final String message;
  final DateTime time;
  final bool isRead;
  final NotificationType type;

  NotificationItem({
    required this.title,
    required this.message,
    required this.time,
    this.isRead = false,
    required this.type,
  });
}

enum NotificationType {
  health,
  breeding,
  general,
  alert
}

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<NotificationItem> _notifications = [
    NotificationItem(
      title: 'Health Check Due',
      message: 'Cattle #1234 is due for vaccination',
      time: DateTime.now().subtract(const Duration(hours: 2)),
      type: NotificationType.health,
    ),
    NotificationItem(
      title: 'Breeding Alert',
      message: 'Cow #5678 is in heat',
      time: DateTime.now().subtract(const Duration(hours: 5)),
      type: NotificationType.breeding,
    ),
    NotificationItem(
      title: 'Feed Stock Low',
      message: 'Feed inventory is below 20%',
      time: DateTime.now().subtract(const Duration(days: 1)),
      type: NotificationType.alert,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.health:
        return AppColors.notificationCategoryColors['health']!;
      case NotificationType.breeding:
        return AppColors.notificationCategoryColors['breeding']!;
      case NotificationType.general:
        return AppColors.notificationCategoryColors['system']!;
      case NotificationType.alert:
        return AppColors.notificationPriorityColors['critical']!;
    }
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.health:
        return Icons.medical_services;
      case NotificationType.breeding:
        return Icons.pets;
      case NotificationType.general:
        return Icons.notifications;
      case NotificationType.alert:
        return Icons.warning;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        backgroundColor: AppColors.notificationHeader,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'center',
                child: ListTile(
                  leading: Icon(Icons.notifications_active),
                  title: Text('Notification Center'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Notification Settings'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'retention',
                child: ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Retention Settings'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'cattle_settings',
                child: ListTile(
                  leading: Icon(Icons.pets),
                  title: Text('Cattle Notifications'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Health'),
            Tab(text: 'Breeding'),
            Tab(text: 'Alerts'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationList(_notifications),
          _buildNotificationList(_notifications.where((n) => n.type == NotificationType.health).toList()),
          _buildNotificationList(_notifications.where((n) => n.type == NotificationType.breeding).toList()),
          _buildNotificationList(_notifications.where((n) => n.type == NotificationType.alert).toList()),
        ],
      ),
    );
  }

  Widget _buildNotificationList(List<NotificationItem> notifications) {
    if (notifications.isEmpty) {
      return const Center(
        child: Text('No notifications'),
      );
    }

    return ListView.builder(
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getTypeColor(notification.type).withAlpha((0.2 * 255).round()),
              child: Icon(
                _getTypeIcon(notification.type),
                color: _getTypeColor(notification.type),
              ),
            ),
            title: Text(
              notification.title,
              style: TextStyle(
                fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.message),
                const SizedBox(height: 4),
                Text(
                  _formatTime(notification.time),
                  style: TextStyle(
                    color: AppColors.notificationStatusColors['read']!.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            onTap: () {
              // Handle notification tap
              setState(() {
                notifications[index] = NotificationItem(
                  title: notification.title,
                  message: notification.message,
                  time: notification.time,
                  isRead: true,
                  type: notification.type,
                );
              });
            },
          ),
        );
      },
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '$difference.inDaysd ago';
    } else if (difference.inHours > 0) {
      return '$difference.inHoursh ago';
    } else if (difference.inMinutes > 0) {
      return '$difference.inMinutesm ago';
    } else {
      return 'Just now';
    }
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'center':
        Navigator.pushNamed(context, AppRoutes.notificationCenter);
        break;
      case 'settings':
        Navigator.pushNamed(context, AppRoutes.notificationSettings);
        break;
      case 'retention':
        Navigator.pushNamed(context, AppRoutes.notificationRetentionSettings);
        break;
      case 'cattle_settings':
        Navigator.pushNamed(context, AppRoutes.cattleNotificationSettings);
        break;
    }
  }
}
