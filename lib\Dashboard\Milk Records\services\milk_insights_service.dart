import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../services/milk_analytics_service.dart';

/// Service for generating milk production insights and recommendations
/// Extracted from UI layer for better testability and reusability
/// Uses dependency injection pattern for architectural consistency
class MilkInsightsService {
  // Business Rule Thresholds - Centralized for easy maintenance and clarity
  // These constants define the business logic for milk production insights

  /// Daily production below this threshold is considered "low" requiring attention
  static const double _lowDailyProductionThreshold = 10.0; // liters

  /// Daily production above this threshold is considered "excellent"
  static const double _excellentDailyProductionThreshold = 25.0; // liters

  /// Number of milk records below this threshold is considered "insufficient data"
  static const int _minMilkRecordsThreshold = 5;

  /// Production consistency below this threshold is considered "inconsistent"
  static const double _lowConsistencyThreshold = 0.7; // 70%

  /// Sales efficiency below this threshold is considered "poor"
  static const double _lowSalesEfficiencyThreshold = 0.8; // 80%

  /// Generate comprehensive insights for milk production management
  /// This is the main entry point for the insights tab
  List<MilkInsight> generateInsights(MilkAnalyticsResult analytics) {
    final insights = <MilkInsight>[];

    // Production Analysis
    final productionInsight = _analyzeProduction(analytics);
    if (productionInsight != null) {
      insights.add(productionInsight);
    }

    // Production Consistency Analysis
    final consistencyInsight = _analyzeConsistency(analytics);
    if (consistencyInsight != null) {
      insights.add(consistencyInsight);
    }

    // Sales Analysis
    final salesInsight = _analyzeSales(analytics);
    if (salesInsight != null) {
      insights.add(salesInsight);
    }

    // Revenue Analysis
    final revenueInsight = _analyzeRevenue(analytics);
    if (revenueInsight != null) {
      insights.add(revenueInsight);
    }

    // General Best Practices (always shown)
    insights.add(_getMilkBestPractices());

    return insights;
  }

  /// Analyze production levels and provide insights
  MilkInsight? _analyzeProduction(MilkAnalyticsResult analytics) {
    if (analytics.totalMilkRecords < _minMilkRecordsThreshold) {
      return MilkInsight(
        title: 'Insufficient Production Data',
        description: 'You need more milk production records to analyze production patterns effectively.',
        icon: Icons.info_outline,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Record daily milk production consistently',
          'Track production by milking session',
          'Monitor individual cow production',
          'Note environmental factors affecting production',
        ],
      );
    }

    final avgDaily = analytics.averageDailyProduction;

    if (avgDaily < _lowDailyProductionThreshold) {
      return MilkInsight(
        title: 'Low Daily Production',
        description: 'Your average daily production of ${avgDaily.toStringAsFixed(1)}L is below optimal levels.',
        icon: Icons.trending_down,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Review cow nutrition and feeding schedule',
          'Check water availability and quality',
          'Evaluate milking technique and equipment',
          'Monitor cow health and stress levels',
          'Consider breed-specific production expectations',
          'Assess environmental conditions (temperature, humidity)',
        ],
      );
    } else if (avgDaily > _excellentDailyProductionThreshold) {
      return MilkInsight(
        title: 'Excellent Production Levels',
        description: 'Your average daily production of ${avgDaily.toStringAsFixed(1)}L is excellent!',
        icon: Icons.star,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current feeding and management practices',
          'Share successful techniques with other farmers',
          'Monitor for any production decline',
          'Consider expanding production capacity',
        ],
      );
    }

    return null; // Average production - no specific insight needed
  }

  /// Analyze production consistency and provide insights
  MilkInsight? _analyzeConsistency(MilkAnalyticsResult analytics) {
    final consistency = analytics.productionConsistency;

    if (consistency < _lowConsistencyThreshold) {
      return MilkInsight(
        title: 'Inconsistent Production',
        description: 'Your production consistency of ${(consistency * 100).toStringAsFixed(1)}% indicates irregular patterns.',
        icon: Icons.trending_flat,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Establish consistent milking schedules',
          'Maintain regular feeding times',
          'Monitor factors causing production variations',
          'Improve environmental consistency',
          'Review cow health and stress factors',
        ],
      );
    } else if (consistency > 0.9) {
      return MilkInsight(
        title: 'Highly Consistent Production',
        description: 'Your production consistency of ${(consistency * 100).toStringAsFixed(1)}% is excellent!',
        icon: Icons.check_circle,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current management practices',
          'Maintain consistent schedules',
          'Monitor for any changes in consistency',
        ],
      );
    }

    return null;
  }

  /// Analyze sales performance and provide insights
  MilkInsight? _analyzeSales(MilkAnalyticsResult analytics) {
    if (analytics.totalMilkRecords > 0 && analytics.totalMilkSales == 0) {
      return MilkInsight(
        title: 'No Sales Records',
        description: 'You have milk production records but no sales records. Track sales to monitor revenue.',
        icon: Icons.point_of_sale,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Record all milk sales transactions',
          'Track prices and market trends',
          'Monitor customer relationships',
          'Calculate profit margins',
          'Identify best-selling periods',
        ],
      );
    }

    final salesEfficiency = analytics.salesEfficiency;
    if (salesEfficiency < _lowSalesEfficiencyThreshold && analytics.totalMilkSales > 0) {
      return MilkInsight(
        title: 'Low Sales Efficiency',
        description: 'Your sales efficiency of ${(salesEfficiency * 100).toStringAsFixed(1)}% could be improved.',
        icon: Icons.trending_down,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Improve milk storage and preservation',
          'Find additional sales channels',
          'Negotiate better prices with buyers',
          'Reduce milk wastage',
          'Optimize production timing for sales',
        ],
      );
    }

    return null;
  }

  /// Analyze revenue patterns and provide insights
  MilkInsight? _analyzeRevenue(MilkAnalyticsResult analytics) {
    if (analytics.totalRevenue == 0 && analytics.totalMilkSales > 0) {
      return MilkInsight(
        title: 'Missing Revenue Data',
        description: 'You have sales records but no revenue data. Track pricing for better financial insights.',
        icon: Icons.attach_money,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Record sale prices for all transactions',
          'Track market price trends',
          'Calculate profit margins',
          'Monitor seasonal price variations',
        ],
      );
    }

    final avgPrice = analytics.averagePricePerLiter;
    if (avgPrice > 0) {
      // Note: These thresholds would need to be adjusted based on local market conditions
      if (avgPrice < 0.5) {
        return MilkInsight(
          title: 'Low Milk Prices',
          description: 'Your average price of \$${avgPrice.toStringAsFixed(2)}/L may be below market rates.',
          icon: Icons.trending_down,
          color: Colors.orange,
          priority: InsightPriority.medium,
          recommendations: [
            'Research local market prices',
            'Negotiate better rates with buyers',
            'Consider direct-to-consumer sales',
            'Improve milk quality for premium pricing',
            'Explore value-added products',
          ],
        );
      } else if (avgPrice > 1.5) {
        return MilkInsight(
          title: 'Excellent Milk Prices',
          description: 'Your average price of \$${avgPrice.toStringAsFixed(2)}/L is excellent!',
          icon: Icons.trending_up,
          color: Colors.green,
          priority: InsightPriority.low,
          recommendations: [
            'Maintain current quality standards',
            'Continue relationships with premium buyers',
            'Consider expanding production',
            'Share pricing strategies with other farmers',
          ],
        );
      }
    }

    return null;
  }

  /// Generate management recommendations for milk production
  List<ManagementRecommendation> generateManagementRecommendations() {
    return [
      ManagementRecommendation(
        title: 'Milk Production Best Practices',
        description: 'Maintain consistent milking schedules and proper cow nutrition to optimize milk production.',
        icon: Icons.water_drop,
        color: Colors.blue,
      ),
      ManagementRecommendation(
        title: 'Quality Control',
        description: 'Implement proper milk storage, cooling, and equipment cleaning to ensure milk quality.',
        icon: Icons.verified,
        color: Colors.green,
      ),
      ManagementRecommendation(
        title: 'Record Keeping',
        description: 'Keep detailed production records to track performance and identify improvement opportunities.',
        icon: Icons.assignment,
        color: AppColors.milkHeader,
      ),
      ManagementRecommendation(
        title: 'Health Monitoring',
        description: 'Monitor udder health regularly and track feed quality to maintain production levels.',
        icon: Icons.health_and_safety,
        color: Colors.orange,
      ),
    ];
  }

  /// Get general milk production best practices
  MilkInsight _getMilkBestPractices() {
    return MilkInsight(
      title: 'Milk Production Best Practices',
      description: 'Follow these best practices to optimize milk production and ensure quality dairy operations.',
      icon: Icons.star,
      color: Colors.green,
      priority: InsightPriority.medium,
      recommendations: [
        'Maintain consistent milking schedules',
        'Ensure proper cow nutrition and hydration',
        'Monitor udder health regularly',
        'Keep detailed production records',
        'Implement proper milk storage and cooling',
        'Regular equipment cleaning and maintenance',
        'Track feed quality and quantity',
        'Monitor environmental conditions',
      ],
    );
  }
}

/// Data classes for milk insights
class MilkInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  MilkInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

class ManagementRecommendation {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  ManagementRecommendation({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
