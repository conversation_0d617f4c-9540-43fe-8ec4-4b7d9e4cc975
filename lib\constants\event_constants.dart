
// import '../Dashboard/Events/models/event_recurrence_isar.dart'; // File removed

/// Event module constants - DISABLED
/// This class is disabled because EventType and related enums have been removed
class EventConstants {
  /*
  // All constants below are commented out due to missing EventType and related enums

  /// Default event colors
  static const Map<EventType, String> defaultEventColors = {
    EventType.veterinary: '#FF6B6B',
    EventType.feeding: '#4ECDC4',
    EventType.breeding: '#45B7D1',
    EventType.milking: '#96CEB4',
    EventType.maintenance: '#FECA57',
    EventType.inspection: '#FF9FF3',
    EventType.training: '#54A0FF',
    EventType.general: '#A29BFE',
    EventType.emergency: '#FD79A8',
  };

  /// Default event icons
  static const Map<EventType, String> defaultEventIcons = {
    EventType.veterinary: '🏥',
    EventType.feeding: '🌾',
    EventType.breeding: '❤️',
    EventType.milking: '🥛',
    EventType.maintenance: '🔧',
    EventType.inspection: '🔍',
    EventType.training: '📚',
    EventType.general: '📅',
    EventType.emergency: '🚨',
  };

  /// Priority colors
  static const Map<EventPriority, String> priorityColors = {
    EventPriority.low: '#4CAF50',
    EventPriority.medium: '#FF9800',
    EventPriority.high: '#F44336',
    EventPriority.critical: '#9C27B0',
  };

  /// Status colors
  static const Map<EventStatus, String> statusColors = {
    EventStatus.scheduled: '#2196F3',
    EventStatus.inProgress: '#FF9800',
    EventStatus.completed: '#4CAF50',
    EventStatus.cancelled: '#F44336',
    EventStatus.postponed: '#9C27B0',
  };

  /// Default reminder times in minutes
  static const List<int> defaultReminderTimes = [
    0,      // At event time
    15,     // 15 minutes before
    30,     // 30 minutes before
    60,     // 1 hour before
    120,    // 2 hours before
    1440,   // 1 day before
    2880,   // 2 days before
    10080,  // 1 week before
  ];

  /// Default recurrence intervals
  static const Map<String, int> recurrenceIntervals = {
    'daily': 1,
    'weekly': 7,
    'monthly': 30,
    'yearly': 365,
  };

  /// Event type display names
  static const Map<EventType, String> eventTypeNames = {
    EventType.veterinary: 'Veterinary',
    EventType.feeding: 'Feeding',
    EventType.breeding: 'Breeding',
    EventType.milking: 'Milking',
    EventType.maintenance: 'Maintenance',
    EventType.inspection: 'Inspection',
    EventType.training: 'Training',
    EventType.general: 'General',
    EventType.emergency: 'Emergency',
  };

  /// Priority display names
  static const Map<EventPriority, String> priorityNames = {
    EventPriority.low: 'Low',
    EventPriority.medium: 'Medium',
    EventPriority.high: 'High',
    EventPriority.critical: 'Critical',
  };

  /// Status display names
  static const Map<EventStatus, String> statusNames = {
    EventStatus.scheduled: 'Scheduled',
    EventStatus.inProgress: 'In Progress',
    EventStatus.completed: 'Completed',
    EventStatus.cancelled: 'Cancelled',
    EventStatus.postponed: 'Postponed',
  };

  /// Default event duration in minutes
  static const int defaultEventDuration = 60;

  /// Maximum event duration in minutes (24 hours)
  static const int maxEventDuration = 1440;

  /// Minimum event duration in minutes (15 minutes)
  static const int minEventDuration = 15;

  /// Default recurrence settings
  static const Map<String, dynamic> defaultRecurrenceSettings = {
    'frequency': RecurrenceFrequency.daily,
    'interval': 1,
    'maxOccurrences': 10,
  };

  /// Calendar view types
  static const List<String> calendarViewTypes = [
    'month',
    'week',
    'day',
    'agenda',
  ];

  /// Default calendar view
  static const String defaultCalendarView = 'month';

  /// Date format strings
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String fullDateTimeFormat = 'EEEE, dd MMMM yyyy HH:mm';

  /// Notification channels
  static const String eventReminderChannel = 'event_reminders';
  static const String eventUpdatesChannel = 'event_updates';
  static const String eventAlertsChannel = 'event_alerts';

  /// Notification IDs
  static const int eventReminderNotificationId = 1000;
  static const int eventUpdateNotificationId = 2000;
  static const int eventAlertNotificationId = 3000;

  /// Analytics keys
  static const String analyticsEventCreated = 'event_created';
  static const String analyticsEventUpdated = 'event_updated';
  static const String analyticsEventDeleted = 'event_deleted';
  static const String analyticsEventCompleted = 'event_completed';
  static const String analyticsEventCancelled = 'event_cancelled';
  static const String analyticsEventPostponed = 'event_postponed';

  /// Error messages
  static const String errorEventNotFound = 'Event not found';
  static const String errorInvalidDate = 'Invalid date or time';
  static const String errorInvalidDuration = 'Invalid event duration';
  static const String errorDatabaseConnection = 'Database connection error';
  static const String errorPermissionDenied = 'Permission denied';

  /// Success messages
  static const String successEventCreated = 'Event created successfully';
  static const String successEventUpdated = 'Event updated successfully';
  static const String successEventDeleted = 'Event deleted successfully';
  static const String successEventCompleted = 'Event marked as completed';
  static const String successEventCancelled = 'Event cancelled successfully';
  static const String successEventPostponed = 'Event postponed successfully';

  /// Validation rules
  static const int minEventTitleLength = 3;
  static const int maxEventTitleLength = 100;
  static const int minEventDescriptionLength = 0;
  static const int maxEventDescriptionLength = 1000;
  static const int minLocationLength = 0;
  static const int maxLocationLength = 200;
  static const int minNotesLength = 0;
  static const int maxNotesLength = 2000;
  */
}
