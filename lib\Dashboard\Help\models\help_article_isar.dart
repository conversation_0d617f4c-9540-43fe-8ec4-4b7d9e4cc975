import 'package:isar/isar.dart';

part 'help_article_isar.g.dart';

/// Help article model for storing help content in local database
@collection
class HelpArticleIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  // Article content
  String? title;
  String? content;
  String? category;
  String? tags;
  
  // Metadata
  int priority = 0;
  bool isActive = true;
  DateTime? createdAt;
  DateTime? updatedAt;

  // Search and navigation
  String? searchKeywords;
  String? relatedArticles;

  /// Create a new help article
  static HelpArticleIsar create({
    required String businessId,
    required String title,
    required String content,
    String? category,
    String? tags,
    int priority = 0,
    String? searchKeywords,
  }) {
    final article = HelpArticleIsar()
      ..businessId = businessId
      ..title = title
      ..content = content
      ..category = category
      ..tags = tags
      ..priority = priority
      ..searchKeywords = searchKeywords
      ..isActive = true
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    return article;
  }

  /// Update article content
  void updateContent({
    String? title,
    String? content,
    String? category,
    String? tags,
    int? priority,
    String? searchKeywords,
  }) {
    if (title != null) this.title = title;
    if (content != null) this.content = content;
    if (category != null) this.category = category;
    if (tags != null) this.tags = tags;
    if (priority != null) this.priority = priority;
    if (searchKeywords != null) this.searchKeywords = searchKeywords;
    updatedAt = DateTime.now();
  }

  /// Mark article as inactive
  void deactivate() {
    isActive = false;
    updatedAt = DateTime.now();
  }

  /// Mark article as active
  void activate() {
    isActive = true;
    updatedAt = DateTime.now();
  }
}
