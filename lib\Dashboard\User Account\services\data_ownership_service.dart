import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../models/user_isar.dart';
import '../services/auth_service.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Service for managing data ownership and access control
class DataOwnershipService {
  final Logger _logger = Logger('DataOwnershipService');

  // Get AuthService from dependency injection instead of creating own instance
  AuthService get _authService => GetIt.instance<AuthService>();

  /// Check if current user can access cattle record
  bool canAccessCattle(CattleIsar cattle) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    // Super admin can access all records (if implemented)
    // if (currentUser.role == UserRole.superAdmin) return true;

    // Owner can access their own cattle
    if (cattle.ownerUserId == currentUser.businessId) return true;

    // Farm members can access cattle in their farm (if farm sharing is implemented)
    if (cattle.farmId != null && _canAccessFarm(cattle.farmId!)) return true;

    return false;
  }

  /// Check if current user can modify cattle record
  bool canModifyCattle(CattleIsar cattle) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    // Only owner can modify cattle records
    return cattle.ownerUserId == currentUser.businessId;
  }

  /// Check if current user can delete cattle record
  bool canDeleteCattle(CattleIsar cattle) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return false;

    // Only owner can delete cattle records
    return cattle.ownerUserId == currentUser.businessId;
  }



  /// Filter cattle list to only include accessible records
  List<CattleIsar> filterAccessibleCattle(List<CattleIsar> cattleList) {
    return cattleList.where((cattle) => canAccessCattle(cattle)).toList();
  }



  /// Set ownership for new cattle record
  CattleIsar setOwnership(CattleIsar cattle, {String? farmId}) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    cattle.ownerUserId = currentUser.businessId;
    cattle.farmId = farmId;
    
    _logger.info('Ownership set for cattle $cattle.tagId to user $currentUser.email');
    return cattle;
  }

  /// Transfer ownership of cattle to another user
  Future<bool> transferCattleOwnership({
    required CattleIsar cattle,
    required String newOwnerUserId,
    String? newFarmId,
  }) async {
    try {
      if (!canModifyCattle(cattle)) {
        _logger.warning('User ${_authService.currentUser?.email} attempted to transfer cattle $cattle.tagId without permission');
        return false;
      }

      final oldOwner = cattle.ownerUserId;
      cattle.ownerUserId = newOwnerUserId;
      cattle.farmId = newFarmId;
      cattle.updatedAt = DateTime.now();

      _logger.info('Cattle $cattle.tagId ownership transferred from $oldOwner to $newOwnerUserId');
      return true;
    } catch (e) {
      _logger.severe('Error transferring cattle ownership: $e');
      return false;
    }
  }

  /// Get user's cattle count
  int getUserCattleCount(List<CattleIsar> allCattle) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return 0;

    return allCattle.where((cattle) => 
      cattle.ownerUserId == currentUser.businessId
    ).length;
  }

  /// Get user's active cattle count
  int getUserActiveCattleCount(List<CattleIsar> allCattle) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return 0;

    return allCattle.where((cattle) => 
      cattle.ownerUserId == currentUser.businessId && 
      cattle.status == CattleStatus.active
    ).length;
  }

  /// Check if user has reached cattle limit (if implemented)
  bool hasReachedCattleLimit(List<CattleIsar> allCattle, {int? customLimit}) {
    // For now, no limits. In a premium version, you might implement limits
    return false;
  }

  /// Get ownership statistics
  OwnershipStats getOwnershipStats(List<CattleIsar> allCattle) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return OwnershipStats(
        totalCattle: 0,
        activeCattle: 0,
        soldCattle: 0,
        deceasedCattle: 0,
        transferredCattle: 0,
      );
    }

    final userCattle = allCattle.where((cattle) => 
      cattle.ownerUserId == currentUser.businessId
    ).toList();

    return OwnershipStats(
      totalCattle: userCattle.length,
      activeCattle: userCattle.where((c) => c.status == CattleStatus.active).length,
      soldCattle: userCattle.where((c) => c.status == CattleStatus.sold).length,
      deceasedCattle: userCattle.where((c) => c.status == CattleStatus.deceased).length,
      transferredCattle: userCattle.where((c) => c.status == CattleStatus.transferred).length,
    );
  }

  /// Validate data access before operations
  bool validateDataAccess({
    required String operation,
    required String resourceType,
    required String resourceId,
  }) {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      _logger.warning('Unauthorized access attempt: $operation on $resourceType:$resourceId');
      return false;
    }

    _logger.info('Data access validated: $currentUser.email performing $operation on $resourceType:$resourceId');
    return true;
  }

  /// Log data access for audit purposes
  void logDataAccess({
    required String operation,
    required String resourceType,
    required String resourceId,
    bool success = true,
  }) {
    final currentUser = _authService.currentUser;
    final logEntry = {
      'timestamp': DateTime.now().toIso8601String(),
      'userId': currentUser?.businessId,
      'userEmail': currentUser?.email,
      'operation': operation,
      'resourceType': resourceType,
      'resourceId': resourceId,
      'success': success,
    };

    _logger.info('Data access log: $logEntry');
    
    // In production, you would store this in an audit log database
  }

  /// Private helper methods
  bool _canAccessFarm(String farmId) {
    // For now, just check if user has any farm with this ID
    // In a full implementation, you'd check farm membership/sharing
    return false;
  }

  /// Check if current user is authenticated
  bool get isAuthenticated => _authService.isAuthenticated;

  /// Get current user
  UserIsar? get currentUser => _authService.currentUser;
}

/// Statistics about user's data ownership
class OwnershipStats {
  final int totalCattle;
  final int activeCattle;
  final int soldCattle;
  final int deceasedCattle;
  final int transferredCattle;

  OwnershipStats({
    required this.totalCattle,
    required this.activeCattle,
    required this.soldCattle,
    required this.deceasedCattle,
    required this.transferredCattle,
  });

  Map<String, dynamic> toJson() => {
    'totalCattle': totalCattle,
    'activeCattle': activeCattle,
    'soldCattle': soldCattle,
    'deceasedCattle': deceasedCattle,
    'transferredCattle': transferredCattle,
  };
}

/// Enum for different permission levels (for future use)
enum PermissionLevel {
  none,
  read,
  write,
  admin,
}

/// Data access result
class DataAccessResult {
  final bool allowed;
  final String? reason;

  DataAccessResult.allowed() : allowed = true, reason = null;
  DataAccessResult.denied(this.reason) : allowed = false;
}
