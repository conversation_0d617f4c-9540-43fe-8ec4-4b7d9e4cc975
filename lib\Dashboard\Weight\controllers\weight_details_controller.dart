import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'dart:async';

import '../../Cattle/models/cattle_isar.dart';
import '../models/weight_record_isar.dart';
import '../models/weight_insights_models.dart';
import '../services/weight_repository.dart';
import '../services/weight_insights_service.dart';

/// Controller for managing weight details screen state and data
/// Following the breeding details controller pattern with real-time stream synchronization
class WeightDetailsController extends ChangeNotifier {
  final WeightRepository _weightRepository = GetIt.instance<WeightRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  bool _isLoading = true;
  String? _error;
  CattleIsar? _cattle;

  // Weight data
  List<WeightRecordIsar> _weightRecords = [];
  List<WeightRecordIsar> _filteredRecords = [];

  // Real-time synchronization using streams (following breeding details pattern)
  StreamSubscription<List<WeightRecordIsar>>? _weightStreamSubscription;

  // Filter and sort state
  DateTime? _startDate;
  DateTime? _endDate;
  String? _sortBy;

  // Analytics data
  WeightInsightsData _analytics = WeightInsightsData.empty;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  CattleIsar? get cattle => _cattle;
  List<WeightRecordIsar> get weightRecords => _weightRecords;
  List<WeightRecordIsar> get allRecords => _weightRecords;
  List<WeightRecordIsar> get filteredRecords => _filteredRecords;

  // Filter getters
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String? get sortBy => _sortBy;

  // Data state getters
  bool get hasData => _weightRecords.isNotEmpty;

  // Analytics getter
  WeightInsightsData get analytics => _analytics;

  // Computed properties for analytics
  int get totalWeightRecords => _weightRecords.length;
  
  double get currentWeight {
    if (_weightRecords.isEmpty) return 0.0;
    final sorted = List<WeightRecordIsar>.from(_weightRecords)
      ..sort((a, b) => (b.date!).compareTo(a.date!));
    return sorted.first.weight;
  }
  
  double get averageWeight {
    if (_weightRecords.isEmpty) return 0.0;
    final totalWeight = _weightRecords.fold(0.0, (sum, record) => sum + record.weight);
    return totalWeight / _weightRecords.length;
  }
  
  double get weightGain {
    if (_weightRecords.length < 2) return 0.0;
    final sorted = List<WeightRecordIsar>.from(_weightRecords)
      ..sort((a, b) => (a.date!).compareTo(b.date!));
    final firstWeight = sorted.first.weight;
    final lastWeight = sorted.last.weight;
    return lastWeight - firstWeight;
  }
  
  List<WeightRecordIsar> get recentRecords {
    final sorted = List<WeightRecordIsar>.from(_weightRecords)
      ..sort((a, b) => (b.date!).compareTo(a.date!));
    return sorted.take(10).toList();
  }

  double get monthlyGrowthRate {
    if (_weightRecords.length < 2) return 0.0;
    
    final sorted = List<WeightRecordIsar>.from(_weightRecords)
      ..sort((a, b) => (a.date!).compareTo(b.date!));
    
    final firstRecord = sorted.first;
    final lastRecord = sorted.last;
    
    if (firstRecord.date == null || lastRecord.date == null) return 0.0;
    
    final daysDifference = lastRecord.date!.difference(firstRecord.date!).inDays;
    if (daysDifference <= 0) return 0.0;
    
    final weightDifference = lastRecord.weight - firstRecord.weight;
    const monthlyDays = 30.0;
    
    return (weightDifference / daysDifference) * monthlyDays;
  }

  /// Initialize controller with cattle data
  void initialize(CattleIsar cattle) {
    _cattle = cattle;
    _initializeStreamListeners();
    // No need for manual data loading - streams will handle it automatically
  }

  /// Initialize stream listeners for real-time updates following breeding details pattern
  void _initializeStreamListeners() {
    if (_cattle?.businessId == null) return;

    debugPrint('🔧 WEIGHT DETAILS: Initializing stream listeners for cattle: ${_cattle!.tagId} (businessId: ${_cattle!.businessId})');

    // Weight records stream - filtered by cattle businessId (weight records use IsarLink to cattle)
    _weightStreamSubscription = _isar.weightRecordIsars
        .where()
        .watch(fireImmediately: true)
        .listen((allWeightRecords) {
      // Filter for this specific cattle using the IsarLink relationship
      final filteredRecords = allWeightRecords.where((record) =>
        record.cattle.value?.businessId == _cattle!.businessId).toList();

      debugPrint('🔄 WEIGHT DETAILS - WEIGHT STREAM: Received $filteredRecords.length weight records for cattle ${_cattle!.tagId}');
      _weightRecords = filteredRecords;

      // Apply filters and calculate analytics
      _applyFilters();
      _calculateAnalytics();

      _setLoading(false);
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Weight details - weight stream error: $error');
      _setError('Failed to load weight data: $error');
    });

    debugPrint('✅ WEIGHT DETAILS: Stream listeners initialized successfully');
  }

  /// Refresh all data - streams handle this automatically, but kept for compatibility
  Future<void> refresh() async {
    // With streams, data refreshes automatically
    // This method is kept for manual refresh if needed
    if (_cattle != null) {
      _initializeStreamListeners();
    }
  }

  /// Set date filter range
  void setDateFilter(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    _applyFilters();
    notifyListeners();
  }

  /// Set sort criteria
  void setSortBy(String? sortBy) {
    _sortBy = sortBy;
    _applyFilters();
    notifyListeners();
  }

  /// Clear all filters
  void clearFilters() {
    _startDate = null;
    _endDate = null;
    _sortBy = null;
    _applyFilters();
    notifyListeners();
  }

  /// Apply current filters to the records
  void _applyFilters() {
    List<WeightRecordIsar> filtered = List.from(_weightRecords);

    // Apply date filter
    if (_startDate != null || _endDate != null) {
      filtered = filtered.where((record) {
        if (record.date == null) return false;

        if (_startDate != null && record.date!.isBefore(_startDate!)) {
          return false;
        }
        if (_endDate != null && record.date!.isAfter(_endDate!)) {
          return false;
        }
        return true;
      }).toList();
    }

    // Apply sorting
    if (_sortBy != null) {
      switch (_sortBy) {
        case 'Date':
          filtered.sort((a, b) => (b.date!).compareTo(a.date!));
          break;
        case 'Weight':
          filtered.sort((a, b) => b.weight.compareTo(a.weight));
          break;
      }
    } else {
      // Default sort by date (newest first)
      filtered.sort((a, b) => (b.date!).compareTo(a.date!));
    }

    _filteredRecords = filtered;
  }

  /// Calculate analytics for the current cattle
  void _calculateAnalytics() {
    if (_cattle == null || _weightRecords.isEmpty) {
      _analytics = WeightInsightsData.empty;
      return;
    }

    // Use the insights service to calculate analytics
    _analytics = WeightInsightsService.calculateInsights(_weightRecords, [_cattle!]);
  }

  // CRUD Methods - Stream-Only Pattern
  // These methods only update the database, stream handles UI updates automatically
  // Following the breeding details controller pattern for real-time synchronization

  /// Add a new weight record - stream handles UI update automatically
  Future<bool> addWeightRecord(WeightRecordIsar record) async {
    try {
      await _weightRepository.saveRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to add weight record: $e');
      return false;
    }
  }

  /// Update an existing weight record - stream handles UI update automatically
  Future<bool> updateWeightRecord(WeightRecordIsar record) async {
    try {
      await _weightRepository.saveRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to update weight record: $e');
      return false;
    }
  }

  /// Delete a weight record - stream handles UI update automatically
  Future<bool> deleteWeightRecord(int recordId) async {
    try {
      await _weightRepository.deleteRecord(recordId);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to delete weight record: $e');
      return false;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }



  @override
  void dispose() {
    // Clean up stream subscriptions
    _weightStreamSubscription?.cancel();
    super.dispose();
  }
}
