import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import '../controllers/health_controller.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../dialogs/vaccination_form_dialog.dart';
import '../../../utils/message_utils.dart';
import '../tabs/health_records_tab.dart';
import '../tabs/health_analytics_tab.dart';
import '../tabs/health_insights_tab.dart';
import '../tabs/vaccinations_tab.dart';
import '../services/health_insights_service.dart';
import '../../../routes/app_routes.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum
import '../../User Account/guards/demo_guard.dart';

 // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

/// Health screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class HealthScreen extends StatelessWidget {
  const HealthScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HealthController(),
      child: const _HealthScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _HealthScreenContent extends StatefulWidget {
  const _HealthScreenContent();

  @override
  State<_HealthScreenContent> createState() => _HealthScreenContentState();
}

class _HealthScreenContentState extends State<_HealthScreenContent>
    with TickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddHealthDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_health_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_health_record');
      return;
    }

    final healthController = context.read<HealthController>();

    showDialog(
      context: context,
      builder: (context) => HealthRecordFormDialog(
        cattle: healthController.cattle,
        onSave: (record) async {
          try {
            await healthController.addHealthRecord(record);
            if (!mounted) return;
            if (context.mounted) {
              HealthMessageUtils.showSuccess(context, HealthMessageUtils.healthRecordCreated());
            }
          } catch (e) {
            if (!mounted) return;
            if (context.mounted) {
              HealthMessageUtils.showError(context, 'Error adding health record');
            }
          }
        },
      ),
    );
  }

  void _showAddVaccinationDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_health_record')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_health_record');
      return;
    }

    final healthController = context.read<HealthController>();

    if (healthController.cattle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No cattle available for vaccination records.'),
          duration: Duration(milliseconds: 1000),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => VaccinationFormDialog(
        cattle: healthController.cattle,
        cattleId: healthController.cattle.first.tagId ?? '',
        onSave: (record) async {
          try {
            await healthController.addVaccinationRecord(record);
            if (!mounted) return true;
            if (context.mounted) {
              HealthMessageUtils.showSuccess(context, HealthMessageUtils.vaccinationRecorded());
            }
            return true;
          } catch (e) {
            if (!mounted) return false;
            if (context.mounted) {
              HealthMessageUtils.showError(context, 'Error adding vaccination record');
            }
            return false;
          }
        },
      ),
    );
  }

  void _getCurrentTabAction() {
    switch (_tabController.index) {
      case 0:
        // Analytics tab - no FAB action
        break;
      case 1:
        _showAddHealthDialog();
        break;
      case 2:
        _showAddVaccinationDialog();
        break;
      case 3:
        // Insights tab - no FAB action
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Health Management',
      body: Consumer<HealthController>(
        builder: (context, healthController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.fourTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => HealthAnalyticsTab(controller: healthController),
              ),
              Builder(
                builder: (context) => const HealthRecordsTab(), // Uses Provider pattern
              ),
              Builder(
                builder: (context) => const VaccinationsTab(), // Uses Provider pattern
              ),
              Builder(
                builder: (context) => HealthInsightsTab(
                  // Ultimate Pure Dependency Injection: ALL dependencies provided by parent
                  // Widget has ZERO knowledge of dependency creation - perfect architectural purity
                  controller: healthController,
                  insightsService: GetIt.instance<HealthInsightsService>(),
                ),
              ),
            ],
            labels: const ['Analytics', 'Health', 'Vaccines', 'Insights'],
            icons: const [Icons.analytics, Icons.medical_services, Icons.vaccines, Icons.lightbulb],
            showFABs: const [false, true, true, false], // FAB disabled for Analytics and Insights
          );

          // Handle different states
          if (healthController.state == ControllerState.loading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (healthController.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    healthController.errorMessage ?? 'Failed to load health data',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {}, // No manual retry needed - reactive streams auto-recover
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return _tabManager!;
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.healthReport,
          ),
          tooltip: 'View Health Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _getCurrentTabAction,
            tooltip: 'Add Record',
            backgroundColor: AppColors.healthHeader,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}
