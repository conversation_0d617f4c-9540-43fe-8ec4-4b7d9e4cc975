import 'package:flutter/material.dart';
import '../services/breeding_analytics_service.dart';

/// Service for generating breeding management insights and recommendations
/// Extracted from UI layer for better testability and reusability
/// Uses dependency injection pattern for architectural consistency
class BreedingInsightsService {
  // Business Rule Thresholds - Centralized for easy maintenance and clarity
  // These constants define the business logic for breeding management insights

  /// Conception rate below this threshold is considered "low" requiring attention
  static const double _lowConceptionRateThreshold = 0.6; // 60%

  /// Conception rate above this threshold is considered "excellent"
  static const double _excellentConceptionRateThreshold = 0.85; // 85%

  /// Number of breeding records below this threshold is considered "insufficient data"
  static const int _minBreedingRecordsThreshold = 5;

  /// Number of female cattle below this threshold is considered "small breeding herd"
  static const int _smallBreedingHerdThreshold = 10;

  /// Number of female cattle above this threshold is considered "large breeding herd"
  static const int _largeBreedingHerdThreshold = 50;

  /// Generate comprehensive insights for breeding management
  /// This is the main entry point for the insights tab
  List<BreedingInsight> generateInsights(BreedingAnalyticsResult analytics) {
    final insights = <BreedingInsight>[];

    // Conception Rate Analysis
    final conceptionInsight = _analyzeConceptionRate(analytics);
    if (conceptionInsight != null) {
      insights.add(conceptionInsight);
    }

    // Breeding Herd Size Analysis
    final herdSizeInsight = _analyzeBreedingHerdSize(analytics);
    if (herdSizeInsight != null) {
      insights.add(herdSizeInsight);
    }

    // Breeding Method Analysis
    final methodInsight = _analyzeBreedingMethods(analytics);
    if (methodInsight != null) {
      insights.add(methodInsight);
    }

    // Pregnancy Management Analysis
    final pregnancyInsight = _analyzePregnancyManagement(analytics);
    if (pregnancyInsight != null) {
      insights.add(pregnancyInsight);
    }

    // General Best Practices (always shown)
    insights.add(_getBreedingBestPractices());

    return insights;
  }

  /// Analyze conception rate and provide insights
  BreedingInsight? _analyzeConceptionRate(BreedingAnalyticsResult analytics) {
    if (analytics.totalBreedingRecords < _minBreedingRecordsThreshold) {
      return BreedingInsight(
        title: 'Insufficient Breeding Data',
        description: 'You need more breeding records to analyze conception rates effectively.',
        icon: Icons.info_outline,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Record all breeding activities consistently',
          'Track breeding outcomes (successful/failed)',
          'Monitor heat cycles and breeding windows',
          'Maintain detailed breeding records',
        ],
      );
    }

    final conceptionRate = analytics.conceptionRate;

    if (conceptionRate < _lowConceptionRateThreshold) {
      return BreedingInsight(
        title: 'Low Conception Rate Alert',
        description: 'Your conception rate of ${(conceptionRate * 100).toStringAsFixed(1)}% is below optimal levels.',
        icon: Icons.warning,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Review breeding timing and heat detection',
          'Evaluate bull fertility or AI quality',
          'Check cattle nutrition and body condition',
          'Consider veterinary consultation',
          'Review breeding protocols and techniques',
        ],
      );
    } else if (conceptionRate > _excellentConceptionRateThreshold) {
      return BreedingInsight(
        title: 'Excellent Conception Rate',
        description: 'Your conception rate of ${(conceptionRate * 100).toStringAsFixed(1)}% is excellent!',
        icon: Icons.star,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current breeding practices',
          'Share successful techniques with other farmers',
          'Monitor and maintain current standards',
          'Consider expanding breeding program',
        ],
      );
    }

    return null; // Average conception rate - no specific insight needed
  }

  /// Analyze breeding herd size and provide insights
  BreedingInsight? _analyzeBreedingHerdSize(BreedingAnalyticsResult analytics) {
    // Note: This would need access to female cattle count
    // For now, we'll use total breeding records as a proxy
    final breedingActivity = analytics.totalBreedingRecords;

    if (breedingActivity < _smallBreedingHerdThreshold) {
      return BreedingInsight(
        title: 'Small Breeding Operation',
        description: 'Your breeding operation is small-scale, allowing for individual attention to each animal.',
        icon: Icons.pets,
        color: Colors.blue,
        priority: InsightPriority.medium,
        recommendations: [
          'Focus on individual animal care and monitoring',
          'Maintain detailed records for each breeding',
          'Consider selective breeding for quality',
          'Monitor each pregnancy closely',
        ],
      );
    } else if (breedingActivity > _largeBreedingHerdThreshold) {
      return BreedingInsight(
        title: 'Large-Scale Breeding Operation',
        description: 'Your large breeding operation requires systematic management approaches.',
        icon: Icons.business,
        color: Colors.purple,
        priority: InsightPriority.medium,
        recommendations: [
          'Implement systematic breeding protocols',
          'Use technology for heat detection',
          'Consider batch breeding strategies',
          'Maintain comprehensive record systems',
          'Regular veterinary consultations',
        ],
      );
    }

    return null;
  }

  /// Analyze breeding methods and provide insights
  BreedingInsight? _analyzeBreedingMethods(BreedingAnalyticsResult analytics) {
    final methodDistribution = analytics.breedingMethodDistribution;
    
    if (methodDistribution.isEmpty) {
      return null;
    }

    final totalBreedings = methodDistribution.values.fold(0, (sum, count) => sum + count);
    final aiPercentage = (methodDistribution['AI'] ?? 0) / totalBreedings;
    final naturalPercentage = (methodDistribution['Natural'] ?? 0) / totalBreedings;

    if (aiPercentage > 0.8) {
      return BreedingInsight(
        title: 'AI-Focused Breeding Program',
        description: 'Your breeding program heavily relies on artificial insemination.',
        icon: Icons.science,
        color: Colors.teal,
        priority: InsightPriority.low,
        recommendations: [
          'Ensure proper AI technique and timing',
          'Maintain cold chain for semen storage',
          'Train staff in AI procedures',
          'Monitor AI success rates by technician',
        ],
      );
    } else if (naturalPercentage > 0.8) {
      return BreedingInsight(
        title: 'Natural Breeding Program',
        description: 'Your breeding program primarily uses natural breeding methods.',
        icon: Icons.nature,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Ensure bull fertility testing',
          'Monitor bull-to-cow ratios',
          'Consider genetic diversity',
          'Regular bull health checks',
        ],
      );
    }

    return null;
  }

  /// Analyze pregnancy management and provide insights
  BreedingInsight? _analyzePregnancyManagement(BreedingAnalyticsResult analytics) {
    if (analytics.overduePregnancies > 0) {
      return BreedingInsight(
        title: 'Overdue Pregnancies Alert',
        description: 'You have $analytics.overduePregnancies overdue pregnancies requiring attention.',
        icon: Icons.schedule,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Schedule immediate veterinary examination',
          'Check for pregnancy complications',
          'Prepare for potential calving assistance',
          'Monitor closely for signs of labor',
        ],
      );
    }

    if (analytics.upcomingCalvings > 0) {
      return BreedingInsight(
        title: 'Upcoming Calvings',
        description: 'You have $analytics.upcomingCalvings calvings expected soon.',
        icon: Icons.event,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Prepare calving facilities',
          'Ensure emergency supplies are ready',
          'Monitor pregnant cows closely',
          'Have veterinary contact information ready',
        ],
      );
    }

    return null;
  }

  /// Get general breeding best practices
  BreedingInsight _getBreedingBestPractices() {
    return BreedingInsight(
      title: 'Breeding Management Best Practices',
      description: 'Follow these best practices to optimize your breeding program and improve herd genetics.',
      icon: Icons.star,
      color: Colors.green,
      priority: InsightPriority.medium,
      recommendations: [
        'Maintain detailed breeding records',
        'Monitor heat cycles and breeding windows',
        'Track conception and calving rates',
        'Plan breeding for optimal calving seasons',
        'Consider genetic diversity in breeding decisions',
        'Regular pregnancy checks by veterinarian',
        'Maintain proper nutrition during breeding',
        'Keep breeding facilities clean and safe',
      ],
    );
  }
}

/// Data classes for breeding insights
class BreedingInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  BreedingInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
