import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../controllers/transaction_controller.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../models/transaction_isar.dart';

class TransactionRecordsTab extends StatefulWidget {
  final TransactionController? controller; // Made optional to support Provider pattern

  const TransactionRecordsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<TransactionRecordsTab> createState() => _TransactionRecordsTabState();
}

class _TransactionRecordsTabState extends State<TransactionRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  TransactionController get _controller => widget.controller ?? context.read<TransactionController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // Apply filters at database level for ultimate scalability
    _controller.applyFilters(const TransactionFilterState());
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    // Check if we have data to display
    if (_controller.totalTransactions == 0) {
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    // Get transaction records data - now pre-filtered at database level
    final transactions = _controller.filteredTransactions;
    final allTransactionsCount = _controller.totalTransactions; // This represents total before filtering

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.transaction,
          moduleName: 'transaction',
          sortFields: const [...SortField.commonFields, ...SortField.transactionFields],
          searchHint: 'Search transactions by description, category, or amount...',
          totalCount: allTransactionsCount,
          filteredCount: transactions.length,
        ),

        // Transaction Records List - data is already filtered at database level
        Expanded(
          child: transactions.isEmpty
              ? _buildEmptyState(allTransactionsCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    // UX Decision: Pull-to-refresh acts as a complete reset, clearing filters
                    // to provide users with a "fresh start" experience. This is intuitive
                    // behavior when users want to see all data without current filter constraints.
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: transactions.length,
                    itemBuilder: (context, index) {
                      final transaction = transactions[index];
                      return _buildTransactionRecordCard(transaction);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  /// Consolidated empty state builder - single source of truth for all empty states
  ///
  /// [isCompletelyEmpty] - true when no transactions exist at all, false when transactions exist but filters hide them
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    // Get the tab color for Records tab (index 1) from transaction module
    const tabColor = AppColors.transactionHeader;

    if (isCompletelyEmpty) {
      // No transactions exist - show call-to-action to add first record
      return UniversalTabEmptyState.forTab(
        title: 'No Transaction Records',
        message: 'Add your first transaction to start tracking your farm\'s finances.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddTransactionDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Transactions exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Transactions',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildTransactionRecordCard(TransactionIsar transaction) {
    // Determine if income or expense for dynamic colors
    final isIncome = transaction.categoryType.toLowerCase() == 'income';
    final amountColor = isIncome ? Colors.green : Colors.red;

    // Format row 1: Date + Amount
    String dateText = _formatDate(transaction.date);
    String amountText = '${isIncome ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}';

    // Format row 2: Category and payment method
    // Use title field which contains the category name for display
    String categoryText = transaction.title.isNotEmpty ? transaction.title : 'Unknown Category';
    String paymentMethodText = transaction.paymentMethod.isNotEmpty ? transaction.paymentMethod : 'Unknown Method';

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: amountText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: isIncome ? Icons.arrow_upward : Icons.arrow_downward,
      row2Left: categoryText,
      row2Right: paymentMethodText,
      row2LeftIcon: Icons.category,
      row2RightIcon: _getPaymentMethodIcon(transaction.paymentMethod),
      notes: transaction.description.isNotEmpty ? transaction.description : null,
      primaryColor: AppColors.transactionHeader,
      row1RightColor: amountColor,
      onTap: () => _showEditTransactionDialog(transaction),
      onEdit: () => _showEditTransactionDialog(transaction),
      onDelete: () => _showDeleteConfirmation(transaction),
    );
  }



  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Consolidated dialog helper for both add and edit operations
  ///
  /// [transaction] - null for add operation, existing transaction for edit operation
  void _showTransactionFormDialog([TransactionIsar? transaction]) {

    showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        transaction: transaction, // null for add, existing transaction for edit
        categories: _controller.allCategories,
        onTransactionAdded: () {
          // Reactive streams will handle the update automatically
        },
      ),
    );
  }

  /// Show dialog to add new transaction
  void _showAddTransactionDialog() => _showTransactionFormDialog();

  /// Show dialog to edit existing transaction
  void _showEditTransactionDialog(TransactionIsar transaction) => _showTransactionFormDialog(transaction);

  void _showDeleteConfirmation(TransactionIsar transaction) {
    final categoryName = transaction.title.isNotEmpty ? transaction.title : 'Unknown';
    final recordDescription = 'transaction "${transaction.description.isNotEmpty ? transaction.description : 'Untitled'}" ($categoryName)';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text('Are you sure you want to delete this $recordDescription?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _controller.deleteTransaction(transaction.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }

  IconData _getPaymentMethodIcon(String? paymentMethod) {
    switch (paymentMethod?.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'credit card':
        return Icons.credit_card;
      case 'debit card':
        return Icons.payment;
      case 'bank transfer':
        return Icons.account_balance;
      case 'mobile payment':
        return Icons.phone_android;
      case 'check':
        return Icons.receipt;
      default:
        return Icons.payment;
    }
  }
}
