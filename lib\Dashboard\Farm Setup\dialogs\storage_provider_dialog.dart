import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../models/backup_settings_isar.dart';
import '../services/farm_setup_repository.dart';

/// Dialog for configuring storage provider settings
/// Follows established patterns using UniversalFormDialog and UniversalFormField
class StorageProviderDialog extends StatefulWidget {
  final BackupSettingsIsar currentSettings;
  final FarmSetupRepository farmSetupRepository;

  const StorageProviderDialog({
    super.key,
    required this.currentSettings,
    required this.farmSetupRepository,
  });

  @override
  State<StorageProviderDialog> createState() => _StorageProviderDialogState();
}

class _StorageProviderDialogState extends State<StorageProviderDialog> {
  static final Logger _logger = Logger('StorageProviderDialog');
  
  late BackupStorageProvider _selectedProvider;
  late bool _autoBackupEnabled;
  late int _autoBackupFrequency;
  late int _cloudBackupRetentionDays;
  late int _maxCloudBackups;
  
  // Google Drive settings
  String? _googleDriveAccountEmail;
  bool _isGoogleDriveAuthenticated = false;



  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
    _checkAuthenticationStatus();
  }

  void _initializeSettings() {
    _selectedProvider = widget.currentSettings.storageProvider;
    _autoBackupEnabled = widget.currentSettings.autoBackupEnabled;
    _autoBackupFrequency = widget.currentSettings.autoBackupFrequency;
    _cloudBackupRetentionDays = widget.currentSettings.cloudBackupRetentionDays;
    _maxCloudBackups = widget.currentSettings.maxCloudBackups;
    _googleDriveAccountEmail = widget.currentSettings.googleDriveAccountEmail;
  }

  Future<void> _checkAuthenticationStatus() async {
    try {
      _isGoogleDriveAuthenticated = await widget.farmSetupRepository
          .isCloudAuthenticated(BackupStorageProvider.googleDrive);
    } catch (e) {
      _logger.warning('Error checking authentication status: $e');
    } finally {
      if (mounted) {
        setState(() {});
      }
    }
  }

  Future<void> _handleAuthentication(BackupStorageProvider provider) async {
    try {
      final success = await widget.farmSetupRepository.signInToCloud(provider);
      
      if (success) {
        await _checkAuthenticationStatus();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully authenticated with $provider.name'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to authenticate with $provider.name'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _logger.severe('Error during authentication: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Authentication error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleSignOut(BackupStorageProvider provider) async {
    try {
      await widget.farmSetupRepository.signOutFromCloud(provider);
      await _checkAuthenticationStatus();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Signed out from $provider.name'),
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error during sign out: $e');
    }
  }

  Future<void> _handleSave() async {
    setState(() => _isSaving = true);
    
    try {
      final updatedSettings = widget.currentSettings.copyWith(
        storageProvider: _selectedProvider,
        autoBackupEnabled: _autoBackupEnabled,
        autoBackupFrequency: _autoBackupFrequency,
        cloudBackupRetentionDays: _cloudBackupRetentionDays,
        maxCloudBackups: _maxCloudBackups,
        googleDriveEnabled: _selectedProvider == BackupStorageProvider.googleDrive,
        googleDriveAccountEmail: _googleDriveAccountEmail,
      );

      await widget.farmSetupRepository.saveBackupSettings(updatedSettings);
      
      if (mounted) {
        Navigator.of(context).pop(updatedSettings);
      }
    } catch (e) {
      _logger.severe('Error saving storage provider settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return UniversalFormDialog(
      title: 'Storage Provider Settings',
      headerIcon: Icons.cloud,
      formContent: _buildFormContent(),
      actionButtons: UniversalDialogButtons.cancelUpdateRow(
        onCancel: () => Navigator.of(context).pop(),
        onUpdate: _handleSave,
        updateText: 'Save',
        isUpdating: _isSaving,
      ),
      maxWidth: 500,
      maxHeight: 600,
    );
  }

  Widget _buildFormContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Storage Provider Selection
        UniversalFormField.dropdownField<BackupStorageProvider>(
          label: 'Storage Provider',
          value: _selectedProvider,
          items: BackupStorageProvider.values.map((provider) {
            return DropdownMenuItem(
              value: provider,
              child: Row(
                children: [
                  Icon(_getProviderIcon(provider), size: 20),
                  const SizedBox(width: 8),
                  Text(_getProviderDisplayName(provider)),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedProvider = value!;
            });
          },
          prefixIcon: Icons.storage,
          prefixIconColor: Colors.blue,
        ),
        UniversalFormField.spacing,

        // Authentication Status and Actions
        if (_selectedProvider != BackupStorageProvider.local) ...[
          _buildAuthenticationSection(),
          UniversalFormField.spacing,
        ],

        // Auto Backup Settings
        _buildAutoBackupSection(),
        UniversalFormField.spacing,

        // Cloud-specific settings
        if (_selectedProvider != BackupStorageProvider.local) ...[
          _buildCloudSettings(),
        ],
      ],
    );
  }

  Widget _buildAuthenticationSection() {
    final isAuthenticated = _selectedProvider == BackupStorageProvider.googleDrive
        ? _isGoogleDriveAuthenticated
        : false;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isAuthenticated ? Icons.check_circle : Icons.error,
                  color: isAuthenticated ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  isAuthenticated ? 'Authenticated' : 'Not Authenticated',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isAuthenticated ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                if (!isAuthenticated)
                  ElevatedButton.icon(
                    onPressed: () => _handleAuthentication(_selectedProvider),
                    icon: const Icon(Icons.login),
                    label: const Text('Sign In'),
                  )
                else
                  ElevatedButton.icon(
                    onPressed: () => _handleSignOut(_selectedProvider),
                    icon: const Icon(Icons.logout),
                    label: const Text('Sign Out'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoBackupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Auto Backup Settings',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            SwitchListTile(
              title: const Text('Enable Auto Backup'),
              subtitle: const Text('Automatically create backups at regular intervals'),
              value: _autoBackupEnabled,
              onChanged: (value) {
                setState(() {
                  _autoBackupEnabled = value;
                });
              },
            ),
            if (_autoBackupEnabled) ...[
              const SizedBox(height: 8),
              UniversalFormField.numberField(
                label: 'Backup Frequency (days)',
                initialValue: _autoBackupFrequency.toString(),
                onChanged: (value) {
                  final frequency = int.tryParse(value);
                  if (frequency != null && frequency > 0) {
                    _autoBackupFrequency = frequency;
                  }
                },
                prefixIcon: Icons.schedule,
                prefixIconColor: Colors.orange,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCloudSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cloud Storage Settings',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            UniversalFormField.numberField(
              label: 'Retention Period (days)',
              initialValue: _cloudBackupRetentionDays.toString(),
              onChanged: (value) {
                final days = int.tryParse(value);
                if (days != null && days > 0) {
                  _cloudBackupRetentionDays = days;
                }
              },
              prefixIcon: Icons.history,
              prefixIconColor: Colors.purple,
            ),
            UniversalFormField.spacing,
            UniversalFormField.numberField(
              label: 'Maximum Backups',
              initialValue: _maxCloudBackups.toString(),
              onChanged: (value) {
                final max = int.tryParse(value);
                if (max != null && max > 0) {
                  _maxCloudBackups = max;
                }
              },
              prefixIcon: Icons.storage,
              prefixIconColor: Colors.teal,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getProviderIcon(BackupStorageProvider provider) {
    switch (provider) {
      case BackupStorageProvider.local:
        return Icons.phone_android;
      case BackupStorageProvider.googleDrive:
        return Icons.cloud;
    }
  }

  String _getProviderDisplayName(BackupStorageProvider provider) {
    switch (provider) {
      case BackupStorageProvider.local:
        return 'Local Storage';
      case BackupStorageProvider.googleDrive:
        return 'Google Drive';
    }
  }
}
