import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_constants.dart';

// ============================================================================
// UNIVERSAL TAB SYSTEM - Minimalist approach with predefined color sequences
// ============================================================================

// ============================================================================
// UNIVERSAL COLOR CONSTANTS
// ============================================================================

/// Fixed color sequence for universal tab system
/// Colors are automatically assigned based on tab count
class UniversalTabColors {
  // Fixed color order: Blue, Green, Purple, Indigo, Teal
  static const List<Color> _colors = [
    Colors.blue,
    Colors.green,
    Colors.purple,
    Colors.indigo,
    Colors.teal,
  ];

  /// Get colors for specific tab count
  static List<Color> getColors(int tabCount) {
    assert(tabCount >= 2 && tabCount <= 5, 'Tab count must be between 2 and 5');
    return _colors.take(tabCount).toList();
  }
}


/// Tab configuration for any screen
class UniversalTabConfig {
  final String label;
  final IconData icon;
  final Color color;
  final bool showFAB;

  const UniversalTabConfig({
    required this.label,
    required this.icon,
    required this.color,
    this.showFAB = false,
  });
}

/// Universal tab configurations with predefined color sequences
class UniversalTabConfigs {

  // ============================================================================
  // DEFAULT LABELS AND ICONS
  // ============================================================================

  static const List<String> _defaultTwoLabels = ['Analytics', 'Records'];
  static const List<String> _defaultThreeLabels = ['Analytics', 'Records', 'Insights'];
  static const List<String> _defaultFourLabels = ['Analytics', 'Records', 'Custom', 'Insights'];
  static const List<String> _defaultFiveLabels = ['Analytics', 'Records', 'Custom 1', 'Custom 2', 'Insights'];

  static const List<IconData> _defaultTwoIcons = [Icons.analytics, Icons.list_alt];
  static const List<IconData> _defaultThreeIcons = [Icons.analytics, Icons.list_alt, Icons.lightbulb];
  static const List<IconData> _defaultFourIcons = [Icons.analytics, Icons.list_alt, Icons.extension, Icons.lightbulb];
  static const List<IconData> _defaultFiveIcons = [Icons.analytics, Icons.list_alt, Icons.extension, Icons.more_horiz, Icons.lightbulb];

  // ============================================================================
  // 2-TAB CONFIGURATION
  // ============================================================================

  /// Creates a 2-tab configuration with automatic color assignment (Blue, Green)
  static List<UniversalTabConfig> twoTabs({
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
  }) {
    final tabLabels = labels ?? _defaultTwoLabels;
    final tabIcons = icons ?? _defaultTwoIcons;
    final colors = UniversalTabColors.getColors(2);
    final fabStates = showFABs ?? [false, true]; // Default: FAB on Records tab

    assert(tabLabels.length == 2, 'Must provide exactly 2 labels');
    assert(tabIcons.length == 2, 'Must provide exactly 2 icons');

    return List.generate(2, (index) => UniversalTabConfig(
      label: tabLabels[index],
      icon: tabIcons[index],
      color: colors[index],
      showFAB: fabStates[index],
    ));
  }

  // ============================================================================
  // 3-TAB CONFIGURATION
  // ============================================================================

  /// Creates a 3-tab configuration with automatic color assignment (Blue, Green, Purple)
  static List<UniversalTabConfig> threeTabs({
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
  }) {
    final tabLabels = labels ?? _defaultThreeLabels;
    final tabIcons = icons ?? _defaultThreeIcons;
    final colors = UniversalTabColors.getColors(3);
    final fabStates = showFABs ?? [false, true, false]; // Default: FAB on Records tab

    assert(tabLabels.length == 3, 'Must provide exactly 3 labels');
    assert(tabIcons.length == 3, 'Must provide exactly 3 icons');

    return List.generate(3, (index) => UniversalTabConfig(
      label: tabLabels[index],
      icon: tabIcons[index],
      color: colors[index],
      showFAB: fabStates[index],
    ));
  }

  // ============================================================================
  // 4-TAB CONFIGURATION
  // ============================================================================

  /// Creates a 4-tab configuration with automatic color assignment (Blue, Green, Purple, Indigo)
  static List<UniversalTabConfig> fourTabs({
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
  }) {
    final tabLabels = labels ?? _defaultFourLabels;
    final tabIcons = icons ?? _defaultFourIcons;
    final colors = UniversalTabColors.getColors(4);
    final fabStates = showFABs ?? [false, true, false, false]; // Default: FAB on Records tab

    assert(tabLabels.length == 4, 'Must provide exactly 4 labels');
    assert(tabIcons.length == 4, 'Must provide exactly 4 icons');

    return List.generate(4, (index) => UniversalTabConfig(
      label: tabLabels[index],
      icon: tabIcons[index],
      color: colors[index],
      showFAB: fabStates[index],
    ));
  }

  // ============================================================================
  // 5-TAB CONFIGURATION
  // ============================================================================

  /// Creates a 5-tab configuration with automatic color assignment (Blue, Green, Purple, Indigo, Teal)
  static List<UniversalTabConfig> fiveTabs({
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
  }) {
    final tabLabels = labels ?? _defaultFiveLabels;
    final tabIcons = icons ?? _defaultFiveIcons;
    final colors = UniversalTabColors.getColors(5);
    final fabStates = showFABs ?? [false, true, false, false, false]; // Default: FAB on Records tab

    assert(tabLabels.length == 5, 'Must provide exactly 5 labels');
    assert(tabIcons.length == 5, 'Must provide exactly 5 icons');

    return List.generate(5, (index) => UniversalTabConfig(
      label: tabLabels[index],
      icon: tabIcons[index],
      color: colors[index],
      showFAB: fabStates[index],
    ));
  }

  /// Get tabs for health categories (3-tab layout)
  static List<Tab> getHealthCategoryTabs() {
    return [
      const Tab(text: 'Health Issues', icon: Icon(Icons.medical_information)),
      const Tab(text: 'Treatments', icon: Icon(Icons.medication)),
      const Tab(text: 'Vaccines', icon: Icon(Icons.vaccines)),
    ];
  }
}

/// Universal Tab Bar Widget
class UniversalTabBar extends StatelessWidget {
  final List<UniversalTabConfig> tabConfigs;
  final TabController controller;
  final Color? indicatorColor;
  final bool isScrollable;

  const UniversalTabBar({
    Key? key,
    required this.tabConfigs,
    required this.controller,
    this.indicatorColor,
    this.isScrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final currentIndex = controller.index;
        return TabBar(
          controller: controller,
          isScrollable: isScrollable,
          tabAlignment: isScrollable ? TabAlignment.start : TabAlignment.fill,
          indicatorColor: indicatorColor ?? Colors.blue,
          labelColor: null, // We'll handle colors per tab
          unselectedLabelColor: null, // We'll handle colors per tab
          tabs: tabConfigs.asMap().entries.map((entry) {
            final index = entry.key;
            final config = entry.value;
            final isActive = currentIndex == index;
            final activeColor = config.color;
            final inactiveColor = Color.lerp(config.color, Colors.grey[400]!, 0.6) ?? 
                                 config.color.withValues(alpha: 0.4);

            return Tab(
              icon: Icon(
                config.icon,
                color: isActive ? activeColor : inactiveColor,
              ),
              child: Text(
                config.label,
                style: TextStyle(
                  color: isActive ? activeColor : inactiveColor,
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  // Factory constructors based on number of tabs with automatic color assignment
  factory UniversalTabBar.twoTabs({
    required TabController controller,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.twoTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabBar.threeTabs({
    required TabController controller,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.threeTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabBar.fourTabs({
    required TabController controller,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.fourTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: false, // 4 tabs should take even space
    );
  }

  factory UniversalTabBar.fiveTabs({
    required TabController controller,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.fiveTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: true, // 5 tabs should be scrollable
    );
  }
}

/// Universal FAB System - Completely module-agnostic
class UniversalFAB {

  /// Creates a standard add FAB with consistent styling
  static FloatingActionButton add({
    required VoidCallback onPressed,
    String tooltip = 'Add',
    Color? backgroundColor,
    bool mini = false,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      mini: mini,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.add),
    );
  }

  /// Creates a save FAB
  static FloatingActionButton save({
    required VoidCallback onPressed,
    String tooltip = 'Save',
    Color? backgroundColor,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.save),
    );
  }

  /// Creates an edit FAB
  static FloatingActionButton edit({
    required VoidCallback onPressed,
    String tooltip = 'Edit',
    Color? backgroundColor,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.edit),
    );
  }

  /// Creates a delete FAB
  static FloatingActionButton delete({
    required VoidCallback onPressed,
    String tooltip = 'Delete',
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: Colors.red,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.delete),
    );
  }

  /// Creates an extended FAB
  static FloatingActionButton extended({
    required VoidCallback onPressed,
    required Widget label,
    required Widget icon,
    String? tooltip,
    Color? backgroundColor,
  }) {
    return FloatingActionButton.extended(
      onPressed: onPressed,
      label: label,
      icon: icon,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
    );
  }
}

/// Universal Tab Screen System
/// Combines tab bar and FAB management in one widget
class UniversalTabManager extends StatelessWidget {
  final List<UniversalTabConfig> tabConfigs;
  final TabController controller;
  final List<Widget> tabViews;
  final Color? indicatorColor;
  final bool isScrollable;

  const UniversalTabManager({
    Key? key,
    required this.tabConfigs,
    required this.controller,
    required this.tabViews,
    this.indicatorColor,
    this.isScrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Universal Tab Bar
        UniversalTabBar(
          tabConfigs: tabConfigs,
          controller: controller,
          indicatorColor: indicatorColor,
          isScrollable: isScrollable,
        ),
        // Tab Views
        Expanded(
          child: TabBarView(
            controller: controller,
            children: tabViews,
          ),
        ),
      ],
    );
  }

  /// Get the current FAB based on selected tab
  /// Returns a FAB if the current tab should show one
  Widget? getCurrentFAB({
    VoidCallback? onPressed,
    String? tooltip,
    Color? backgroundColor,
  }) {
    if (controller.index < 0 || controller.index >= tabConfigs.length) {
      return null;
    }

    final currentConfig = tabConfigs[controller.index];
    if (!currentConfig.showFAB || onPressed == null) {
      return null;
    }

    return UniversalFAB.add(
      onPressed: onPressed,
      tooltip: tooltip ?? 'Add',
      backgroundColor: backgroundColor ?? currentConfig.color,
    );
  }

  // Factory constructors based on number of tabs with automatic color assignment
  factory UniversalTabManager.twoTabs({
    required TabController controller,
    required List<Widget> tabViews,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.twoTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabManager.threeTabs({
    required TabController controller,
    required List<Widget> tabViews,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.threeTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabManager.fourTabs({
    required TabController controller,
    required List<Widget> tabViews,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.fourTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: false, // 4 tabs should take even space
    );
  }

  factory UniversalTabManager.fiveTabs({
    required TabController controller,
    required List<Widget> tabViews,
    List<String>? labels,
    List<IconData>? icons,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.fiveTabs(
        labels: labels,
        icons: icons,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: true, // 5 tabs should fit in single row with even spacing
    );
  }
}

/// Universal Empty State System for Tab-Based Screens
///
/// Provides empty states that automatically adapt to the current tab's color
/// and theme, ensuring consistent visual design across all modules.
class UniversalTabEmptyState extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final Color tabColor;
  final Widget? action;
  final int tabIndex;

  const UniversalTabEmptyState({
    super.key,
    required this.title,
    required this.message,
    required this.tabColor,
    required this.tabIndex,
    this.icon,
    this.action,
  });

  /// Factory constructor that automatically determines the appropriate icon
  /// based on the tab index
  factory UniversalTabEmptyState.forTab({
    required String title,
    required String message,
    required Color tabColor,
    required int tabIndex,
    Widget? action,
  }) {
    IconData defaultIcon;

    // Determine icon based on tab index (common pattern across modules)
    switch (tabIndex) {
      case 0: // Analytics tab
        defaultIcon = Icons.analytics_outlined;
        break;
      case 1: // Records tab
        defaultIcon = Icons.list_alt_outlined;
        break;
      case 2: // Insights tab
        defaultIcon = Icons.lightbulb_outlined;
        break;
      case 3: // Additional tab
        defaultIcon = Icons.extension_outlined;
        break;
      case 4: // Additional tab
        defaultIcon = Icons.more_horiz_outlined;
        break;
      default:
        defaultIcon = Icons.inbox_outlined;
    }

    return UniversalTabEmptyState(
      title: title,
      message: message,
      tabColor: tabColor,
      tabIndex: tabIndex,
      icon: defaultIcon,
      action: action,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon with tab color
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: tabColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 64,
                color: tabColor,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: tabColor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Message
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),

            // Action button (if provided)
            if (action != null) ...[
              const SizedBox(height: 32),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}

/// Pre-built action buttons for empty states that match tab colors
class TabEmptyStateActions {
  /// Creates an "Add First Record" button with tab color
  static Widget addFirstRecord({
    required VoidCallback onPressed,
    required Color tabColor,
    String text = 'Add First Record',
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.add),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: tabColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Creates a "Clear Filters" button with tab color
  static Widget clearFilters({
    required VoidCallback onPressed,
    required Color tabColor,
    String text = 'Clear Filters',
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.clear_all),
      label: Text(text),
      style: OutlinedButton.styleFrom(
        foregroundColor: tabColor,
        side: BorderSide(color: tabColor),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Creates a "Retry" button with tab color
  static Widget retry({
    required VoidCallback onPressed,
    required Color tabColor,
    String text = 'Try Again',
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.refresh),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: tabColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }


}

// ============================================================================
// USAGE DOCUMENTATION AND EXAMPLES
// ============================================================================

/// # Universal Tab System Usage Guide
///
/// This file provides a minimalist, universal tab system with predefined color
/// sequences that automatically assigns colors based on tab count.
///
/// ## Quick Start - Universal Tab Patterns
///
/// ### 2-Tab Configuration (Blue, Green):
/// ```dart
/// // Default labels: Analytics, Records
/// _tabManager = UniversalTabManager.twoTabs(
///   controller: _tabController,
///   tabViews: [AnalyticsTab(), RecordsTab()],
/// );
///
/// // Custom labels
/// _tabManager = UniversalTabManager.twoTabs(
///   controller: _tabController,
///   tabViews: [OverviewTab(), DetailsTab()],
///   labels: ['Overview', 'Details'],
/// );
/// ```
///
/// ### 3-Tab Configuration (Blue, Green, Purple):
/// ```dart
/// // Default labels: Analytics, Records, Insights
/// _tabManager = UniversalTabManager.threeTabs(
///   controller: _tabController,
///   tabViews: [AnalyticsTab(), RecordsTab(), InsightsTab()],
/// );
///
/// // Custom labels and icons
/// _tabManager = UniversalTabManager.threeTabs(
///   controller: _tabController,
///   tabViews: [OverviewTab(), FamilyTreeTab(), AnalyticsTab()],
///   labels: ['Overview', 'Family Tree', 'Analytics'],
///   icons: [Icons.info_outline, Icons.account_tree, Icons.analytics],
/// );
/// ```
///
/// ### 4-Tab Configuration (Blue, Green, Purple, Indigo):
/// ```dart
/// _tabManager = UniversalTabManager.fourTabs(
///   controller: _tabController,
///   tabViews: [Tab1(), Tab2(), Tab3(), Tab4()],
///   labels: ['Breeding', 'Pregnancy', 'Delivery', 'Analytics'],
///   icons: [Icons.favorite, Icons.pregnant_woman, Icons.child_care, Icons.analytics],
///   showFABs: [true, true, true, false], // FABs on first 3 tabs
/// );
/// ```
///
/// ### 5-Tab Configuration (Blue, Green, Purple, Indigo, Teal):
/// ```dart
/// _tabManager = UniversalTabManager.fiveTabs(
///   controller: _tabController,
///   tabViews: [Tab1(), Tab2(), Tab3(), Tab4(), Tab5()],
///   labels: ['Analytics', 'Breeding', 'Pregnancy', 'Delivery', 'Insights'],
///   showFABs: [false, true, true, true, false], // FABs on record tabs
/// );
/// ```
///
/// ## Predefined Color Sequence
///
/// Colors are automatically assigned in this fixed order:
/// 1. **Blue** (Colors.blue)
/// 2. **Green** (Colors.green)
/// 3. **Purple** (Colors.purple)
/// 4. **Indigo** (Colors.indigo)
/// 5. **Teal** (Colors.teal)
///
/// - 2 tabs → Blue, Green
/// - 3 tabs → Blue, Green, Purple
/// - 4 tabs → Blue, Green, Purple, Indigo
/// - 5 tabs → Blue, Green, Purple, Indigo, Teal
///
/// ## Default Labels and Icons
///
/// ### Default Labels:
/// - 2 tabs: Analytics, Records
/// - 3 tabs: Analytics, Records, Insights
/// - 4 tabs: Analytics, Records, Custom, Insights
/// - 5 tabs: Analytics, Records, Custom 1, Custom 2, Insights
///
/// ### Default Icons:
/// - Analytics: Icons.analytics
/// - Records: Icons.list_alt
/// - Insights: Icons.lightbulb
/// - Custom: Icons.extension, Icons.more_horiz
///
/// ## FAB Configuration
///
/// Default FAB behavior:
/// - **Records tab** (index 1): Shows FAB
/// - **All other tabs**: No FAB
/// - Override with custom `showFABs` parameter
///
/// ## Migration from Legacy Code
///
/// Replace old patterns:
/// ```dart
/// // OLD - Don't use
/// ModuleTabPresets.cattleMain()
/// StandardTabPatterns.analyticsRecordsInsights(colors: [...])
///
/// // NEW - Use instead
/// UniversalTabConfigs.threeTabs()
/// UniversalTabManager.threeTabs(...)
/// ```
