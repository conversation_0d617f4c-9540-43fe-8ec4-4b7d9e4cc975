import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../constants/app_colors.dart';
import '../../Transactions/models/category_isar.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import 'package:get_it/get_it.dart';

// --- Constants ---
class _AppStrings {
  static const String addHealthTitle = 'Add Health Record';
  static const String editHealthTitle = 'Edit Health Record';
  static const String cattleLabel = 'Cattle';
  static const String dateLabel = 'Date';
  static const String conditionLabel = 'Condition/Diagnosis';
  static const String treatmentLabel = 'Treatment';
  static const String veterinarianLabel = 'Veterinarian';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';

  // Validation messages

  // Success messages
  static const String addSuccess = 'Health record added successfully';
  static const String updateSuccess = 'Health record updated successfully';

  // Validation messages - removed unused constants
}

class HealthRecordFormDialog extends StatefulWidget {
  final HealthRecordIsar? healthRecord;
  final List<CattleIsar> cattle;
  final Future<void> Function(HealthRecordIsar)? onSave;

  const HealthRecordFormDialog({
    Key? key,
    this.healthRecord,
    required this.cattle,
    this.onSave,
  }) : super(key: key);

  @override
  State<HealthRecordFormDialog> createState() => _HealthRecordFormDialogState();
}

class _HealthRecordFormDialogState extends State<HealthRecordFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();

  // --- State Controllers and Variables ---
  List<CategoryIsar> _healthIssueCategories = [];
  List<CategoryIsar> _treatmentCategories = [];
  // Use controllers for text fields and simple variables for other types.
  String? _selectedCondition;
  String? _selectedTreatment;
  late final TextEditingController _veterinarianController;
  late final TextEditingController _costController;
  late final TextEditingController _notesController;

  String? _selectedCattleId;
  DateTime _selectedDate = DateTime.now();
  bool _showOptionalFields = false; // Toggle for optional fields visibility
  bool _isLoadingCategories = true;


  @override
  void initState() {
    super.initState();
    final record = widget.healthRecord;

    // Initialize dropdown selections and controllers with existing data
    _selectedCondition = record?.details ?? record?.condition;
    _selectedTreatment = record?.treatment;
    _veterinarianController = TextEditingController(text: record?.veterinarian ?? '');
    _costController = TextEditingController(text: record?.cost?.toString() ?? '');
    _notesController = TextEditingController(text: record?.notes ?? '');

    // Load categories
    _loadCategories();

    // Set initial values for non-text fields
    _selectedDate = record?.date ?? DateTime.now();

    // Safely set the initial cattle ID
    if (record != null && widget.cattle.any((c) => c.tagId == record.cattleTagId)) {
      _selectedCattleId = record.cattleTagId;
    } else if (widget.cattle.isNotEmpty) {
      _selectedCattleId = widget.cattle.first.tagId;
    }

    // Show optional fields if any optional data exists
    if (record != null &&
        (record.veterinarian?.isNotEmpty == true ||
         record.cost != null && record.cost! > 0 ||
         record.notes?.isNotEmpty == true)) {
      _showOptionalFields = true;
    }
  }

  Future<void> _loadCategories() async {
    try {
      final results = await Future.wait([
        _farmSetupRepository.getHealthIssueCategories(),
        _farmSetupRepository.getTreatmentCategories(),
      ]);

      setState(() {
        _healthIssueCategories = results[0];
        _treatmentCategories = results[1];
        _isLoadingCategories = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCategories = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load categories: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    _veterinarianController.dispose();
    _costController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    // Validate the form. If it fails, do nothing.
    if (!(_formKey.currentState?.validate() ?? false)) {
      return;
    }



    try {
      // Create or update the record object from form data
      final HealthRecordIsar record;

      if (widget.healthRecord != null) {
        // Update existing record using copyWith to preserve ID and businessId
        record = widget.healthRecord!.copyWith(
          cattleTagId: _selectedCattleId ?? '',
          date: _selectedDate,
          details: _selectedCondition ?? '',
          treatment: _selectedTreatment ?? '',
          notes: _notesController.text.trim(),
          cost: double.tryParse(_costController.text.trim()),
          veterinarian: _veterinarianController.text.trim(),
        );
      } else {
        // Create new record
        record = HealthRecordIsar.create(
          cattleTagId: _selectedCattleId ?? '',
          date: _selectedDate,
          details: _selectedCondition ?? '',
          treatment: _selectedTreatment ?? '',
          notes: _notesController.text.trim(),
          cost: double.tryParse(_costController.text.trim()),
          veterinarian: _veterinarianController.text.trim(),
        );
      }

      // Await the save operation if a callback is provided
      if (widget.onSave != null) {
        await widget.onSave!(record);
      }
      
      // Show success message and pop the dialog if the widget is still mounted
      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.healthRecord != null
              ? _AppStrings.updateSuccess
              : _AppStrings.addSuccess,
        );
        Navigator.of(context).pop(record); // Pop with the saved record
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, '$_AppStrings.saveError: $e');
      }
    } finally {
      // Ensure the saving state is always reset
      if (mounted) {

      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show a loading dialog if cattle list is empty and being fetched
    if (widget.cattle.isEmpty) {
      return const Dialog(
        child: SizedBox(
          height: 200,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text("Loading Cattle Data..."),
              ],
            ),
          ),
        ),
      );
    }
    
    return UniversalFormDialog(
      title: widget.healthRecord == null ? _AppStrings.addHealthTitle : _AppStrings.editHealthTitle,
      headerIcon: Icons.medical_services,
      formContent: _buildFormContent(),
      actionButtons: UniversalDialogButtons.cancelSaveRow(
        onCancel: () => Navigator.of(context).pop(),
        onSave: _handleSave,
      ),
    );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Cattle Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.cattleLabel,
            value: _selectedCattleId,
            items: widget.cattle.map((cattle) {
              final cattleName = cattle.name ?? 'Unknown';
              final tagId = cattle.tagId ?? '';
              final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
              return DropdownMenuItem(
                value: cattle.tagId,
                child: Text(displayName, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (value) => setState(() => _selectedCattleId = value),
            prefixIcon: Icons.pets,
            prefixIconColor: Colors.green, // Changed from forbidden brown
            validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
          ),
          UniversalFormField.spacing,

          // Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.dateLabel,
            value: _selectedDate,
            onChanged: (date) {
              setState(() {
                _selectedDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            lastDate: DateTime.now(),
          ),
          UniversalFormField.spacing,

          // Condition/Health Issue Dropdown
          _isLoadingCategories
              ? const Center(child: CircularProgressIndicator())
              : UniversalFormField.dropdownField<String>(
                  label: _AppStrings.conditionLabel,
                  value: _selectedCondition,
                  items: _healthIssueCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.name,
                      child: Text(category.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCondition = value;
                    });
                  },
                  prefixIcon: Icons.medical_information,
                  prefixIconColor: Colors.red,
                  validator: (value) => UniversalFormField.dropdownValidator(value, 'condition'),
                ),
          UniversalFormField.spacing,

          // Treatment Dropdown
          _isLoadingCategories
              ? const Center(child: CircularProgressIndicator())
              : UniversalFormField.dropdownField<String>(
                  label: _AppStrings.treatmentLabel,
                  value: _selectedTreatment,
                  items: _treatmentCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.name,
                      child: Text(category.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedTreatment = value;
                    });
                  },
                  prefixIcon: Icons.healing,
                  prefixIconColor: Colors.green,
                  validator: (value) => UniversalFormField.dropdownValidator(value, 'treatment'),
                ),
          UniversalFormField.spacing,

          // Optional Fields Toggle
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showOptionalFields = !_showOptionalFields;
                });
              },
              icon: Icon(
                _showOptionalFields
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: AppColors.healthHeader,
              ),
              label: Text(
                _showOptionalFields
                    ? 'Hide Optional Information'
                    : 'Show Optional Information',
                style: const TextStyle(
                  color: AppColors.healthHeader,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: AppColors.healthHeader,
                  width: 1.5,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Optional Fields (shown when toggle is enabled)
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Veterinarian Field
            UniversalFormField.textField(
              label: _AppStrings.veterinarianLabel,
              controller: _veterinarianController,
              prefixIcon: Icons.person,
              prefixIconColor: Colors.purple,
            ),
            UniversalFormField.spacing,

            // Cost Field
            UniversalFormField.numberField(
              label: _AppStrings.costLabel,
              controller: _costController,
              allowDecimals: true,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.indigo,
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.note_alt,
              prefixIconColor: Colors.teal,
            ),
          ],
        ],
      ),
    );
  }



}