# Events Module Documentation

## Overview

The Events Module is a comprehensive event management system for the Flutter cattle management application. It provides automated event tracking, intelligent scheduling, and seamless integration with existing modules (Cattle, Health, Breeding, etc.) while following established architectural patterns.

## Architecture

The Events module follows the established modular architecture pattern with clear separation of concerns:

```
lib/Dashboard/Events/
├── controllers/          # State management and business logic
├── models/              # Data models and enums
├── services/            # Business services and repositories
├── screens/             # Main UI screens
├── tabs/                # Tab-based UI components
├── dialogs/             # Modal dialogs and forms
└── widgets/             # Reusable UI components
```

## Core Components

### Models

#### EventIsar
The main event data model with comprehensive fields for event management:

```dart
/// Core event model for cattle management activities
/// 
/// Supports:
/// - Manual and automated event creation
/// - Recurring events with flexible patterns
/// - Rich metadata (location, weather, costs)
/// - Integration with other modules
/// - Notification and reminder system
@collection
class EventIsar {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true)
  String? businessId;           // Unique business identifier
  
  @Index()
  String? cattleTagId;          // Associated cattle
  
  @Index()
  String? eventTypeId;          // Event type reference
  
  @Index()
  EventCategory? category;      // Health, Breeding, Feeding, etc.
  
  String? title;                // Event title
  String? description;          // Detailed description
  String? notes;                // Additional notes
  
  @Index()
  DateTime? scheduledDate;      // When event is scheduled
  DateTime? completedDate;      // When event was completed
  
  @Index()
  EventStatus? status;          // Scheduled, Completed, Overdue, etc.
  
  EventPriority? priority;      // Low, Medium, High, Critical
  
  // Automation fields
  bool? isAutoGenerated;        // Created automatically
  String? sourceModule;         // Which module created it
  String? sourceRecordId;       // Source record reference
  
  // Recurrence fields
  bool? isRecurring;            // Is this a recurring event
  RecurrencePattern? recurrencePattern; // Daily, Weekly, Monthly, etc.
  int? recurrenceInterval;      // Every N days/weeks/months
  DateTime? recurrenceEndDate;  // When recurrence stops
  String? parentEventId;        // Parent event for recurring series
  
  // Notification fields
  bool? notificationsEnabled;   // Enable notifications
  List<int>? reminderMinutes;   // Reminder intervals [1440, 60] = 1 day, 1 hour
  
  // Cost tracking
  double? estimatedCost;        // Estimated cost
  double? actualCost;           // Actual cost incurred
  
  // Location and context
  String? location;             // Where event takes place
  String? weatherConditions;    // Weather at time of event
  
  // Completion tracking
  String? completedBy;          // Who completed the event
  String? completionNotes;      // Notes added upon completion
  
  // Metadata
  DateTime? createdAt;          // When record was created
  DateTime? updatedAt;          // When record was last updated
}
```

#### EventTypeIsar
Defines types of events with default settings and automation rules:

```dart
/// Event type definition with automation and default settings
/// 
/// Provides:
/// - Categorization of events
/// - Default settings for new events
/// - Automation rules and triggers
/// - Visual customization (colors, icons)
@collection
class EventTypeIsar {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true)
  String? businessId;           // Unique identifier
  
  String? name;                 // Display name
  String? description;          // Description
  EventCategory? category;      // Which category this belongs to
  String? iconName;             // Icon identifier
  String? colorHex;             // Color for UI display
  
  // Default settings for events of this type
  EventPriority? defaultPriority;        // Default priority level
  List<int>? defaultReminderMinutes;     // Default reminder intervals
  int? defaultDurationMinutes;           // Expected duration
  
  // Automation settings
  bool? canBeAutoGenerated;              // Can be created automatically
  String? autoGenerationRules;           // JSON rules for automation
  
  // Recurrence settings
  bool? supportsRecurrence;              // Supports recurring events
  RecurrencePattern? defaultRecurrencePattern; // Default recurrence
  
  bool? isActive;               // Is this type active
  DateTime? createdAt;          // Creation timestamp
  DateTime? updatedAt;          // Last update timestamp
}
```

#### EventAttachmentIsar
Handles file attachments for events:

```dart
/// File attachment for events (photos, documents, etc.)
/// 
/// Supports:
/// - Multiple file types (images, documents, videos)
/// - Thumbnail generation for images
/// - File metadata and validation
/// - Integration with backup system
@collection
class EventAttachmentIsar {
  Id id = Isar.autoIncrement;
  
  @Index()
  String? eventBusinessId;      // Associated event
  
  String? fileName;             // Original filename
  String? filePath;             // Local file path
  String? fileType;             // 'image', 'document', 'video'
  int? fileSize;                // File size in bytes
  String? mimeType;             // MIME type
  
  // Image-specific fields
  int? imageWidth;              // Image width in pixels
  int? imageHeight;             // Image height in pixels
  String? thumbnailPath;        // Thumbnail file path
  
  DateTime? createdAt;          // When attachment was added
}
```

### Enums

#### EventCategory
```dart
/// Categories for organizing events
enum EventCategory {
  health,        // Health-related events (vaccinations, treatments)
  breeding,      // Breeding activities (mating, pregnancy checks)
  feeding,       // Feed and nutrition management
  management,    // General management (tagging, moving)
  maintenance,   // Equipment and facility maintenance
  financial,     // Financial transactions and costs
  other          // Miscellaneous events
}
```

#### EventStatus
```dart
/// Current status of an event
enum EventStatus {
  scheduled,     // Event is scheduled for future
  inProgress,    // Event is currently happening
  completed,     // Event has been completed
  cancelled,     // Event was cancelled
  overdue        // Event is past due and not completed
}
```

#### EventPriority
```dart
/// Priority levels for events
enum EventPriority {
  low,           // Low priority, can be delayed
  medium,        // Normal priority
  high,          // High priority, should be done soon
  critical       // Critical priority, urgent attention needed
}
```

#### RecurrencePattern
```dart
/// Patterns for recurring events
enum RecurrencePattern {
  daily,         // Every day
  weekly,        // Every week
  monthly,       // Every month
  yearly,        // Every year
  custom         // Custom interval
}
```

## Services

### EventsRepository

The repository provides pure reactive streams and CRUD operations:

```dart
/// Pure reactive repository for Events module database operations
/// 
/// Key Features:
/// - Stream-based reactive updates
/// - Simple CRUD operations
/// - No business logic (pure data layer)
/// - Exception transparency (bubbles up naturally)
class EventsRepository {
  /// Watch all events with real-time updates
  /// 
  /// Returns a stream that emits the complete list of events
  /// whenever any event is added, updated, or deleted.
  /// 
  /// Example:
  /// ```dart
  /// eventsRepository.watchAllEvents().listen((events) {
  ///   print('Total events: ${events.length}');
  /// });
  /// ```
  Stream<List<EventIsar>> watchAllEvents();
  
  /// Watch all event types with real-time updates
  Stream<List<EventTypeIsar>> watchAllEventTypes();
  
  /// Watch attachments for a specific event
  /// 
  /// [eventBusinessId] The business ID of the event
  Stream<List<EventAttachmentIsar>> watchEventAttachments(String eventBusinessId);
  
  /// Save (create or update) an event
  /// 
  /// Uses Isar's native upsert functionality.
  /// If the event has an ID, it will be updated.
  /// If not, a new event will be created.
  /// 
  /// [event] The event to save
  /// 
  /// Throws [DatabaseException] if save fails
  Future<void> saveEvent(EventIsar event);
  
  /// Delete an event by its database ID
  /// 
  /// [id] The Isar database ID (not business ID)
  /// 
  /// Throws [DatabaseException] if delete fails
  Future<void> deleteEvent(int id);
  
  /// Save (create or update) an event type
  Future<void> saveEventType(EventTypeIsar eventType);
  
  /// Delete an event type by its database ID
  Future<void> deleteEventType(int id);
  
  /// Save (create or update) an event attachment
  Future<void> saveEventAttachment(EventAttachmentIsar attachment);
  
  /// Delete an event attachment by its database ID
  Future<void> deleteEventAttachment(int id);
  
  /// Get all events (for analytics and reporting)
  /// 
  /// Returns a snapshot of all events at the time of call.
  /// For real-time updates, use [watchAllEvents] instead.
  Future<List<EventIsar>> getAllEvents();
  
  /// Get events within a date range
  /// 
  /// [start] Start date (inclusive)
  /// [end] End date (inclusive)
  Future<List<EventIsar>> getEventsByDateRange(DateTime start, DateTime end);
  
  /// Get events for a specific cattle
  /// 
  /// [cattleTagId] The tag ID of the cattle
  Future<List<EventIsar>> getEventsByCattle(String cattleTagId);
  
  /// Get overdue events
  /// 
  /// Returns events that are scheduled in the past but not completed
  Future<List<EventIsar>> getOverdueEvents();
  
  /// Get upcoming events within specified days
  /// 
  /// [days] Number of days to look ahead
  Future<List<EventIsar>> getUpcomingEvents(int days);
}
```

### EventsController

The main controller manages state and coordinates between UI and data:

```dart
/// Reactive controller for the main events screen using Dual-Stream Pattern
/// 
/// Key Features:
/// - Dual-stream architecture (filtered for UI, unfiltered for analytics)
/// - Real-time reactive updates
/// - Advanced filtering and search
/// - Performance optimization with caching
/// - Error handling and state management
class EventsController extends ChangeNotifier {
  /// Current controller state
  ControllerState get state;
  
  /// Error message if state is error
  String? get errorMessage;
  
  /// Filtered events for UI display
  /// 
  /// This list reflects the current filter state and is what
  /// should be displayed in the UI components.
  List<EventIsar> get events;
  
  /// Complete unfiltered events for analytics
  /// 
  /// This list always contains all events and is used for
  /// analytics calculations to ensure accuracy.
  List<EventIsar> get unfilteredEvents;
  
  /// Analytics result calculated from unfiltered data
  EventAnalyticsResult get analytics;
  
  /// Current filter state
  EventFilterState get currentFilters;
  
  /// Apply filters to the event list
  /// 
  /// Creates a separate filtered stream without affecting analytics.
  /// 
  /// [filterState] The filter criteria to apply
  /// 
  /// Example:
  /// ```dart
  /// controller.applyFilters(EventFilterState(
  ///   category: EventCategory.health,
  ///   status: EventStatus.scheduled,
  ///   searchQuery: 'vaccination',
  /// ));
  /// ```
  void applyFilters(EventFilterState filterState);
  
  /// Clear all active filters
  void clearFilters();
  
  /// Apply search filter only
  /// 
  /// [searchQuery] Text to search for in event titles, descriptions, and notes
  void applySearchFilter(String searchQuery);
  
  /// Apply date range filter
  /// 
  /// [startDate] Start of date range (inclusive)
  /// [endDate] End of date range (inclusive)
  void applyDateRangeFilter(DateTime? startDate, DateTime? endDate);
  
  /// Apply category filter
  /// 
  /// [category] Event category to filter by
  void applyCategoryFilter(EventCategory? category);
  
  /// Apply status filter
  /// 
  /// [status] Event status to filter by
  void applyStatusFilter(EventStatus? status);
  
  /// Apply cattle filter
  /// 
  /// [cattleTagId] Tag ID of cattle to filter by
  void applyCattleFilter(String? cattleTagId);
  
  /// Add a new event
  /// 
  /// [event] The event to add
  /// 
  /// Throws [ValidationException] if event data is invalid
  /// Throws [DatabaseException] if save fails
  Future<void> addEvent(EventIsar event);
  
  /// Update an existing event
  /// 
  /// [event] The event to update
  Future<void> updateEvent(EventIsar event);
  
  /// Delete an event
  /// 
  /// [businessId] The business ID of the event to delete
  Future<void> deleteEvent(String businessId);
  
  /// Mark an event as completed
  /// 
  /// [businessId] The business ID of the event
  /// [completionNotes] Optional notes about completion
  Future<void> completeEvent(String businessId, String completionNotes);
  
  /// Refresh all data
  /// 
  /// Forces a refresh of all streams and recalculates analytics
  Future<void> refresh();
  
  /// Get paginated events for performance
  /// 
  /// [page] Page number (0-based)
  /// [pageSize] Number of events per page
  Future<PaginatedResult<EventIsar>> getPaginatedEvents({
    int page = 0,
    int pageSize = 50,
  });
  
  /// Get optimized calendar events for date range
  /// 
  /// Returns a map of dates to events for efficient calendar rendering
  /// 
  /// [startDate] Start of date range
  /// [endDate] End of date range
  Map<DateTime, List<EventIsar>> getOptimizedCalendarEvents(
    DateTime startDate,
    DateTime endDate,
  );
  
  /// Get lazy-loaded attachments for an event
  /// 
  /// [eventBusinessId] The business ID of the event
  Future<List<EventAttachmentIsar>> getLazyAttachments(String eventBusinessId);
}
```

### EventAutomationService

Handles automatic event creation from other modules:

```dart
/// Service for automatic event creation from other modules
/// 
/// Integrates with Health, Breeding, Transactions, and other modules
/// to automatically create relevant events when activities occur.
class EventAutomationService {
  /// Create a health-related event
  /// 
  /// Called automatically when health records are created.
  /// 
  /// [cattleTagId] The cattle this event is for
  /// [healthRecordId] Reference to the health record
  /// [eventType] Type of health event (vaccination, treatment, etc.)
  /// [date] Date of the health activity
  /// [notes] Optional notes
  /// [diagnosis] Medical diagnosis if applicable
  /// [treatment] Treatment administered
  /// [cost] Cost of the health activity
  /// 
  /// Example:
  /// ```dart
  /// await EventAutomationService.createHealthEvent(
  ///   cattleTagId: 'C001',
  ///   healthRecordId: 'health-123',
  ///   eventType: 'vaccination',
  ///   date: DateTime.now(),
  ///   notes: 'Annual FMD vaccination',
  /// );
  /// ```
  static Future<void> createHealthEvent({
    required String cattleTagId,
    required String healthRecordId,
    required String eventType,
    required DateTime date,
    String? notes,
    String? diagnosis,
    String? treatment,
    double? cost,
  });
  
  /// Create a breeding-related event
  /// 
  /// Called automatically when breeding records are created.
  /// Also schedules follow-up pregnancy checks if requested.
  /// 
  /// [cattleTagId] The cattle this event is for
  /// [breedingRecordId] Reference to the breeding record
  /// [breedingDate] Date of breeding
  /// [method] Breeding method (natural, AI, etc.)
  /// [bullIdOrType] Bull ID or type used
  /// [schedulePregnancyCheck] Whether to schedule pregnancy check
  /// [cost] Cost of breeding activity
  static Future<void> createBreedingEvent({
    required String cattleTagId,
    required String breedingRecordId,
    required DateTime breedingDate,
    String? method,
    String? bullIdOrType,
    bool schedulePregnancyCheck = true,
    double? cost,
  });
  
  /// Create a transaction-related event
  /// 
  /// Called automatically when transactions are recorded.
  /// 
  /// [cattleTagId] The cattle this event is for
  /// [transactionId] Reference to the transaction
  /// [transactionType] Type of transaction (purchase, sale, etc.)
  /// [date] Date of transaction
  /// [amount] Transaction amount
  /// [notes] Optional notes
  static Future<void> createTransactionEvent({
    required String cattleTagId,
    required String transactionId,
    required String transactionType,
    required DateTime date,
    double? amount,
    String? notes,
  });
  
  /// Create a weight monitoring event
  /// 
  /// Called automatically when weight records are added.
  /// Can schedule future weight measurements.
  /// 
  /// [cattleTagId] The cattle this event is for
  /// [weight] Recorded weight
  /// [date] Date of weighing
  /// [scheduleNextWeighing] Whether to schedule next weighing
  static Future<void> createWeightEvent({
    required String cattleTagId,
    required double weight,
    required DateTime date,
    bool scheduleNextWeighing = true,
  });
}
```

### EventNotificationService

Manages notifications and reminders for events:

```dart
/// Service for managing event notifications and reminders
/// 
/// Handles:
/// - Scheduling event reminders based on event dates
/// - Sending overdue event notifications
/// - Managing upcoming event notifications
/// - Cancelling notifications when events are completed
/// - Respecting user notification preferences
class EventNotificationService {
  /// Schedule reminders for an event
  /// 
  /// Creates notifications based on the event's reminder settings.
  /// 
  /// [event] The event to schedule reminders for
  /// 
  /// Example:
  /// ```dart
  /// final event = EventIsar()
  ///   ..reminderMinutes = [1440, 60]; // 1 day and 1 hour before
  /// 
  /// await notificationService.scheduleEventReminders(event);
  /// ```
  Future<void> scheduleEventReminders(EventIsar event);
  
  /// Cancel all reminders for an event
  /// 
  /// Typically called when an event is completed or cancelled.
  /// 
  /// [eventBusinessId] The business ID of the event
  Future<void> cancelEventReminders(String eventBusinessId);
  
  /// Send notifications for overdue events
  /// 
  /// Finds all events that are past their scheduled date but not completed,
  /// and sends appropriate notifications.
  Future<void> sendOverdueNotifications();
  
  /// Send notifications for upcoming events
  /// 
  /// Finds events that are due soon and sends reminder notifications.
  Future<void> sendUpcomingEventNotifications();
  
  /// Initialize background notification processing
  /// 
  /// Starts a timer that periodically checks for overdue and upcoming events.
  void initialize();
  
  /// Stop background processing and clean up resources
  void dispose();
}
```

### EventAnalyticsService

Provides comprehensive analytics and insights:

```dart
/// Service for calculating event analytics and insights
/// 
/// Provides comprehensive metrics including:
/// - Event completion rates and trends
/// - Category and status distributions
/// - Cost analysis and budgeting insights
/// - Automation effectiveness metrics
/// - Performance indicators
class EventAnalyticsService {
  /// Calculate comprehensive analytics for events
  /// 
  /// [events] List of events to analyze
  /// [eventTypes] List of event types for context
  /// [attachments] List of attachments for analysis
  /// [cattle] List of cattle for per-animal metrics
  /// 
  /// Returns [EventAnalyticsResult] with all calculated metrics
  static EventAnalyticsResult calculateAnalytics(
    List<EventIsar> events,
    List<EventTypeIsar> eventTypes,
    List<EventAttachmentIsar> attachments,
    List<CattleIsar> cattle,
  );
  
  /// Calculate completion rate for events
  /// 
  /// [events] Events to analyze
  /// 
  /// Returns completion rate as percentage (0.0 to 100.0)
  static double calculateCompletionRate(List<EventIsar> events);
  
  /// Calculate average completion time for events
  /// 
  /// [events] Completed events to analyze
  /// 
  /// Returns average time in hours between scheduled and completed dates
  static double calculateAverageCompletionTime(List<EventIsar> events);
  
  /// Get event distribution by category
  /// 
  /// [events] Events to analyze
  /// 
  /// Returns map of category names to event counts
  static Map<String, int> getEventsByCategory(List<EventIsar> events);
  
  /// Get event distribution by status
  /// 
  /// [events] Events to analyze
  /// 
  /// Returns map of status names to event counts
  static Map<String, int> getEventsByStatus(List<EventIsar> events);
  
  /// Calculate cost metrics
  /// 
  /// [events] Events with cost data to analyze
  /// 
  /// Returns tuple of (totalEstimated, totalActual, average)
  static (double, double, double) calculateCostMetrics(List<EventIsar> events);
  
  /// Calculate automation effectiveness
  /// 
  /// [events] Events to analyze
  /// 
  /// Returns percentage of events that were auto-generated
  static double calculateAutomationRate(List<EventIsar> events);
}
```

## UI Components

### EventsScreen

The main screen with tabbed interface:

```dart
/// Main events screen with tabbed interface
/// 
/// Features:
/// - Calendar view for visual event planning
/// - List view with advanced filtering
/// - Analytics view with comprehensive metrics
/// - Search and filter capabilities
/// - Event creation and editing
class EventsScreen extends StatelessWidget {
  /// Creates the events screen with provider-managed controller
  const EventsScreen({Key? key}) : super(key: key);
}
```

### EventFormDialog

Dialog for creating and editing events:

```dart
/// Dialog for creating and editing events
/// 
/// Features:
/// - Comprehensive form with all event fields
/// - Cattle selection with search
/// - Event type selection with category filtering
/// - Date/time picker with validation
/// - Recurrence settings for recurring events
/// - Attachment management
/// - Form validation with error messages
class EventFormDialog extends StatefulWidget {
  /// The event to edit (null for new event)
  final EventIsar? event;
  
  /// List of available cattle for selection
  final List<CattleIsar> cattle;
  
  /// List of available event types
  final List<EventTypeIsar> eventTypes;
  
  /// Pre-selected cattle (for quick event creation)
  final CattleIsar? selectedCattle;
  
  /// Pre-selected date (for calendar-based creation)
  final DateTime? selectedDate;
  
  /// Creates an event form dialog
  const EventFormDialog({
    Key? key,
    this.event,
    required this.cattle,
    required this.eventTypes,
    this.selectedCattle,
    this.selectedDate,
  }) : super(key: key);
}
```

## Integration with Other Modules

### Health Module Integration

The Events module automatically creates events when health activities occur:

```dart
// In HealthRepository.saveHealthRecord()
await _eventAutomationService.createHealthEvent(
  cattleTagId: record.cattleId,
  healthRecordId: record.recordId,
  eventType: record.recordType,
  date: record.date,
  notes: record.details,
);
```

### Breeding Module Integration

Breeding activities automatically create events and schedule follow-ups:

```dart
// In BreedingRepository.saveBreedingRecord()
await _eventAutomationService.createBreedingEvent(
  cattleTagId: record.cattleId,
  breedingRecordId: record.businessId,
  breedingDate: record.date,
  schedulePregnancyCheck: true,
);
```

### Transactions Module Integration

Financial transactions create corresponding events:

```dart
// In TransactionsRepository.saveTransaction()
await _eventAutomationService.createTransactionEvent(
  cattleTagId: transaction.cattleId,
  transactionId: transaction.businessId,
  transactionType: transaction.type,
  date: transaction.date,
  amount: transaction.amount,
);
```

## Performance Optimization

The Events module includes several performance optimizations:

### Caching
- Analytics calculations are cached to avoid repeated computations
- Calendar events are cached by date range
- Attachment loading is lazy and cached

### Pagination
- Large event lists support pagination for better performance
- Configurable page sizes for different use cases

### Database Indexing
- Key fields are indexed for fast queries (date, cattle ID, event type, status)
- Composite indexes for common filter combinations

### Stream Management
- Efficient stream subscriptions with automatic cleanup
- Separate streams for filtered and unfiltered data
- Performance monitoring and optimization

## Error Handling

The Events module follows a layered error handling approach:

### Repository Level
- Pure exceptions bubble up naturally
- No error handling at data layer for maximum transparency

### Controller Level
- Catches and handles exceptions appropriately
- Sets error state with user-friendly messages
- Logs errors for debugging

### UI Level
- Displays error states using established message utilities
- Provides retry mechanisms where appropriate
- Graceful degradation for non-critical failures

## Testing

The Events module includes comprehensive testing:

### Unit Tests
- Model validation and serialization
- Repository CRUD operations
- Controller state management
- Service business logic
- Analytics calculations

### Widget Tests
- Screen rendering and navigation
- Dialog functionality and validation
- Component interactions
- Error state display

### Integration Tests
- Cross-module integration
- Database operations
- Notification system
- Performance benchmarks

## Usage Examples

### Creating a Manual Event

```dart
final event = EventIsar()
  ..businessId = 'event-001'
  ..cattleTagId = 'C001'
  ..eventTypeId = 'vaccination'
  ..title = 'Annual Vaccination'
  ..description = 'FMD and other routine vaccinations'
  ..scheduledDate = DateTime.now().add(Duration(days: 7))
  ..status = EventStatus.scheduled
  ..priority = EventPriority.high
  ..category = EventCategory.health
  ..notificationsEnabled = true
  ..reminderMinutes = [1440, 60]; // 1 day and 1 hour reminders

await eventsController.addEvent(event);
```

### Filtering Events

```dart
// Filter by category and status
eventsController.applyFilters(EventFilterState(
  category: EventCategory.health,
  status: EventStatus.scheduled,
));

// Search events
eventsController.applySearchFilter('vaccination');

// Filter by date range
eventsController.applyDateRangeFilter(
  DateTime.now(),
  DateTime.now().add(Duration(days: 30)),
);

// Clear all filters
eventsController.clearFilters();
```

### Getting Analytics

```dart
final analytics = eventsController.analytics;

print('Total events: ${analytics.totalEvents}');
print('Completion rate: ${analytics.completionRate}%');
print('Overdue events: ${analytics.overdueEvents}');
print('Events by category: ${analytics.eventsByCategory}');
print('Average cost: \$${analytics.averageEventCost}');
```

### Setting Up Notifications

```dart
final event = EventIsar()
  ..notificationsEnabled = true
  ..reminderMinutes = [2880, 1440, 60]; // 2 days, 1 day, 1 hour

await notificationService.scheduleEventReminders(event);
```

## Best Practices

### Event Creation
- Always set a business ID for events
- Use appropriate categories and priorities
- Include descriptive titles and notes
- Set realistic scheduled dates

### Performance
- Use pagination for large event lists
- Apply filters at the database level
- Cache frequently accessed data
- Clean up resources properly

### Error Handling
- Validate event data before saving
- Handle network failures gracefully
- Provide meaningful error messages
- Log errors for debugging

### Testing
- Test all CRUD operations
- Verify filter functionality
- Test error scenarios
- Include performance tests

## Migration and Upgrades

When upgrading the Events module:

1. **Database Schema Changes**: Use Isar migrations for schema updates
2. **Data Migration**: Provide scripts for data transformation
3. **API Changes**: Maintain backward compatibility where possible
4. **Testing**: Thoroughly test migrations with real data

## Troubleshooting

### Common Issues

#### Events Not Appearing
- Check if filters are applied
- Verify database initialization
- Check stream subscriptions

#### Notifications Not Working
- Verify notification permissions
- Check notification settings
- Ensure background processing is enabled

#### Performance Issues
- Check for memory leaks in streams
- Verify cache effectiveness
- Monitor database query performance

#### Integration Problems
- Verify service registration in DI
- Check automation service configuration
- Validate cross-module data consistency

### Debug Tools

The Events module includes debug logging:

```dart
// Enable debug logging
Logger.root.level = Level.ALL;
Logger.root.onRecord.listen((record) {
  print('${record.level.name}: ${record.time}: ${record.message}');
});
```

### Performance Monitoring

Monitor key metrics:
- Event load times
- Filter response times
- Memory usage
- Database query performance

## Contributing

When contributing to the Events module:

1. Follow the established architectural patterns
2. Maintain comprehensive documentation
3. Include appropriate tests
4. Follow the coding standards
5. Update this documentation for significant changes

## API Reference

For detailed API documentation, run:

```bash
dart doc
```

This will generate comprehensive API documentation from the dartdoc comments in the code.