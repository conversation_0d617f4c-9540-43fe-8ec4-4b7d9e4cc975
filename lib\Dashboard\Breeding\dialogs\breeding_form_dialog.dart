import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:intl/intl.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/breeding_record_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

final _logger = Logger('BreedingFormDialog');

// --- Constants ---
class _AppStrings {
  static const String addBreedingTitle = 'Add Breeding Record';
  static const String editBreedingTitle = 'Edit Breeding Record';
  static const String cattleLabel = 'Cattle';
  static const String bullLabel = 'Bull/Semen ID';
  static const String breedingDateLabel = 'Breeding Date';
  static const String methodLabel = 'Breeding Method';
  static const String statusLabel = 'Status';
  static const String expectedDateLabel = 'Expected Calving Date';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';

  // Validation messages
  static const String cattleRequired = 'Please select a cattle';
  static const String dateRequired = 'Breeding date is required';

}

class BreedingFormDialog extends StatefulWidget {
  final BreedingRecordIsar? record;
  final String? initialCattleId;
  final List<CattleIsar>? preloadedCattle;
  final Map<String, AnimalTypeIsar>? preloadedAnimalTypes;
  final Function(BreedingRecordIsar)? onSave;

  const BreedingFormDialog({
    Key? key,
    this.record,
    this.initialCattleId,
    this.preloadedCattle,
    this.preloadedAnimalTypes,
    this.onSave,
  }) : super(key: key);

  @override
  State<BreedingFormDialog> createState() => _BreedingFormDialogState();
}

class _BreedingFormDialogState extends State<BreedingFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _bullController = TextEditingController();
  final TextEditingController _costController = TextEditingController();

  List<CattleIsar> _allCattle = [];
  Map<String, AnimalTypeIsar> _animalTypes = {};
  bool _isLoading = true;
  bool _isSaving = false;
  String? _selectedCattleId;
  DateTime _breedingDate = DateTime.now();
  String _status = 'Pending';
  String _method = 'AI';
  DateTime? _expectedDate;
  bool _showOptionalFields = false;

  // Status options
  final List<String> _statusOptions = ['Pending', 'Confirmed', 'Completed', 'Failed'];

  // Method options
  final List<String> _methodOptions = ['AI', 'Natural', 'ET', 'Other'];

  @override
  void initState() {
    super.initState();
    _logger.info('initState called');
    _loadData();

    // If editing, populate form with existing data
    if (widget.record != null) {
      _selectedCattleId = widget.record!.cattleId;
      _bullController.text = widget.record!.bullIdOrType ?? '';
      _breedingDate = widget.record!.date ?? DateTime.now();

      // Ensure the status is valid and exists in our status options
      final recordStatus = widget.record!.status ?? 'Pending';
      _status = _statusOptions.contains(recordStatus) ? recordStatus : 'Pending';

      // Ensure the method is valid and exists in our method options
      final recordMethod = widget.record!.method ?? 'AI';
      _method = _methodOptions.contains(recordMethod) ? recordMethod : 'AI';
      _notesController.text = widget.record!.notes ?? '';
      _costController.text = widget.record!.cost?.toString() ?? '';
      _expectedDate = widget.record!.expectedDate;
    } else if (widget.initialCattleId != null) {
      _selectedCattleId = widget.initialCattleId;
    }
  }

  /// Get the next sequence number for breeding records for this cattle
  Future<int> _getNextBreedingSequenceNumber(String cattleTagId) async {
    try {
      // Query the database to get the count of existing breeding records for this cattle
      final isar = GetIt.instance<Isar>();
      final existingRecords = await isar.breedingRecordIsars
          .filter()
          .cattleIdEqualTo(cattleTagId)
          .findAll();

      // Return the next sequence number (count + 1)
      return existingRecords.length + 1;
    } catch (e) {
      debugPrint('Error getting breeding sequence number: $e');
      return 1; // Default to 1 if there's an error
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _bullController.dispose();
    _costController.dispose();
    super.dispose();
  }



  Future<void> _loadData() async {
    _logger.info('_loadData called');
    try {
      // Use preloaded data if available
      if (widget.preloadedCattle != null && widget.preloadedAnimalTypes != null) {
        _logger.info('Using preloaded data');

        // Only show female cattle
        final femaleCattle =
            widget.preloadedCattle!.where((c) => c.gender == CattleGender.female).toList();

        if (mounted) {
          setState(() {
            _allCattle = femaleCattle;
            _animalTypes = widget.preloadedAnimalTypes!;

            // Validate selected cattle ID exists in the filtered list (using tagId)
            if (_selectedCattleId != null &&
                !femaleCattle.any((c) => c.tagId == _selectedCattleId)) {
              _selectedCattleId = null;
            }

            // If there's no pre-selected cattle, use the first one if available
            if (_selectedCattleId == null && femaleCattle.isNotEmpty) {
              _selectedCattleId = femaleCattle.first.tagId;
            }

            _updateExpectedDate();
            _isLoading = false;
          });
        }
      } else {
        // No preloaded data available
        if (mounted) {
          setState(() {
            _allCattle = [];
            _animalTypes = {};
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context, 'Error loading cattle: $e');
        setState(() => _isLoading = false);
      }
    }
  }

  void _updateExpectedDate() {
    if (_selectedCattleId != null) {
      final selectedCattle = _allCattle.firstWhere(
        (cattle) => cattle.tagId == _selectedCattleId,
        orElse: () => _allCattle.first,
      );

      final animalType = _animalTypes[selectedCattle.animalTypeId ?? ''];
      if (animalType != null) {
        setState(() {
          _expectedDate = _breedingDate.add(
            Duration(days: animalType.defaultGestationDays ?? 283),
          );
        });
      }
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      // Validate required fields
      if (_selectedCattleId == null) {
        throw Exception(_AppStrings.cattleRequired);
      }

      // Create or update a BreedingRecordIsar object
      final BreedingRecordIsar breedingRecord;

      if (widget.record != null) {
        // Update existing record
        breedingRecord = widget.record!.copyWith(
          cattleId: _selectedCattleId,
          bullIdOrType: _bullController.text.trim(),
          date: _breedingDate,
          method: _method,
          status: _status,
          expectedDate: _expectedDate,
          cost: _costController.text.trim().isNotEmpty ? double.tryParse(_costController.text.trim()) : null,
          notes: _notesController.text.trim(),
        );
      } else {
        // Create new record with standardized business ID format
        // Get the next sequence number for this cattle's breeding records
        final sequenceNumber = await _getNextBreedingSequenceNumber(_selectedCattleId!);
        final standardizedBusinessId = BreedingRecordIsar.generateFormattedId(_selectedCattleId!, sequenceNumber);

        breedingRecord = BreedingRecordIsar.create(
          cattleId: _selectedCattleId!, // This is now the tagId
          bullIdOrType: _bullController.text.trim(),
          date: _breedingDate,
          method: _method,
          status: _status,
          expectedDate: _expectedDate,
          cost: _costController.text.trim().isNotEmpty ? double.tryParse(_costController.text.trim()) : null,
          notes: _notesController.text.trim(),
          businessId: standardizedBusinessId, // e.g., "B1-Breeding-1"
        );
      }

      // Debug log to validate model BEFORE saving
      debugPrint('=== BREEDING RECORD CREATION ===');
      debugPrint('Selected Cattle ID: $_selectedCattleId');
      debugPrint('Breeding Record cattleId: $breedingRecord.cattleId');
      debugPrint('Breeding Record businessId: $breedingRecord.businessId');
      debugPrint('Full BreedingRecord: ${breedingRecord.toMap()}');
      debugPrint('=== END BREEDING RECORD CREATION ===');

      // Call the save callback if provided
      if (widget.onSave != null) {
        debugPrint('📝 BREEDING FORM: Calling onSave callback');
        widget.onSave!(breedingRecord);
        debugPrint('✅ BREEDING FORM: onSave callback completed');
      } else {
        debugPrint('⚠️ BREEDING FORM: No onSave callback provided');
      }

      // Close dialog and let parent handle success message
      if (mounted) {
        Navigator.of(context).pop(breedingRecord);
      }
    } catch (e) {
      debugPrint('ERROR: Failed to save breeding record: $e');
      if (mounted) {
        setState(() => _isSaving = false);
        BreedingMessageUtils.showError(context,
            'Error: ${e.toString().replaceAll('Exception: ', '')}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Dialog(
        child: SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return widget.record == null
        ? UniversalFormDialog(
            title: _AppStrings.addBreedingTitle,
            headerIcon: Icons.favorite, // Breeding-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
              addText: 'Save', // User preference: Use 'Save' instead of 'Add'
              isAdding: _isSaving,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editBreedingTitle,
            headerIcon: Icons.favorite, // Breeding-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
              updateText: 'Save', // User preference: Use 'Save' instead of 'Update'
              isUpdating: _isSaving,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Cattle Selection Dropdown
          UniversalFormField.dropdownField<String>(
                label: _AppStrings.cattleLabel,
                value: _selectedCattleId,
                items: _allCattle.map<DropdownMenuItem<String>>((cattle) {
                  final cattleName = cattle.name ?? 'Unknown';
                  final tagId = cattle.tagId ?? '';
                  final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
                  return DropdownMenuItem(
                    value: cattle.tagId, // Use tagId as the value
                    child: Text(displayName, overflow: TextOverflow.ellipsis),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCattleId = value;
                _updateExpectedDate();
              });
            },
            prefixIcon: Icons.pets,
            prefixIconColor: Colors.green, // Changed from brown (forbidden)
            validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
          ),
          UniversalFormField.spacing,

          // Breeding Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.breedingDateLabel,
            value: _breedingDate,
            onChanged: (date) {
              setState(() {
                _breedingDate = date ?? DateTime.now();
                _updateExpectedDate();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.purple,
            validator: (date) {
              if (date == null) {
                return _AppStrings.dateRequired;
              }
              return null;
            },
            lastDate: DateTime.now(),
          ),
          UniversalFormField.spacing,

          // Breeding Method Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.methodLabel,
            value: _method,
            items: _methodOptions.map<DropdownMenuItem<String>>((method) {
              return DropdownMenuItem(
                value: method,
                child: Text(method),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _method = value!;
              });
            },
            prefixIcon: Icons.science,
            prefixIconColor: Colors.teal,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'breeding method'),
          ),
          UniversalFormField.spacing,

          // Status Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.statusLabel,
            value: _status,
            items: _statusOptions.map<DropdownMenuItem<String>>((status) {
              return DropdownMenuItem(
                value: status,
                child: Text(status),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _status = value!;
              });
            },
            prefixIcon: Icons.info,
            prefixIconColor: Colors.indigo,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'status'),
          ),
          UniversalFormField.spacing,

          // Expected Calving Date Display (Read-only)
          if (_expectedDate != null) ...[
            UniversalFormField.textField(
              label: _AppStrings.expectedDateLabel,
              initialValue: DateFormat('MMMM dd, yyyy').format(_expectedDate!),
              readOnly: true,
              prefixIcon: Icons.event_available,
              prefixIconColor: Colors.green,
            ),
            UniversalFormField.spacing,
          ],

          UniversalFormField.sectionSpacing,

          // Optional Information Toggle Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showOptionalFields = !_showOptionalFields;
                });
              },
              icon: Icon(
                _showOptionalFields
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF2E7D32),
              ),
              label: Text(
                _showOptionalFields
                    ? 'Hide Optional Information'
                    : 'Show Optional Information',
                style: const TextStyle(
                  color: Color(0xFF2E7D32),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: Color(0xFF2E7D32),
                  width: 1.5,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          // Optional Fields - Conditionally Displayed
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Bull/Semen ID Field
            UniversalFormField.textField(
              label: _AppStrings.bullLabel,
              controller: _bullController,
              prefixIcon: Icons.male,
              prefixIconColor: Colors.blue,
              // No validator - field is optional
            ),
            UniversalFormField.spacing,

            // Cost Field
            UniversalFormField.numberField(
              label: _AppStrings.costLabel,
              controller: _costController,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.green,
              // No validator - field is optional
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.notes,
              prefixIconColor: Colors.cyan,
            ),
          ],
        ],
      ),
    );
  }
}
