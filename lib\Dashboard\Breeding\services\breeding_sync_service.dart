import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import '../../../services/logging_service.dart';
import '../../../config/api_config.dart';
import 'package:get_it/get_it.dart';
import '../models/breeding_record_isar.dart';
import 'breeding_repository.dart';

/// Dedicated bidirectional sync service for Breeding module
/// Separates sync logic from repository CRUD operations
/// Follows consistent pattern across all modules
class BreedingSyncService {
  final BreedingRepository _breedingRepository = GetIt.instance<BreedingRepository>();
  
  // Sync-related constants
  static const String _lastSyncKey = 'last_breeding_sync';
  final LoggingService _logger = LoggingService();

  /// Get last sync time for incremental sync
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSync = prefs.getString(_lastSyncKey);
    return lastSync != null ? DateTime.parse(lastSync) : null;
  }

  /// Set last sync time
  Future<void> setLastSyncTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, time.toIso8601String());
  }

  /// Get all breeding records for sync
  Future<List<BreedingRecordIsar>> getAllBreedingRecords() async {
    final isar = GetIt.instance<Isar>();
    return await isar.breedingRecordIsars.where().findAll();
  }

  /// Get breeding records modified since last sync
  Future<List<BreedingRecordIsar>> getModifiedBreedingRecordsSince(DateTime? lastSync) async {
    if (lastSync == null) {
      return await getAllBreedingRecords();
    }
    
    final isar = GetIt.instance<Isar>();
    return await isar.breedingRecordIsars
        .filter()
        .updatedAtGreaterThan(lastSync)
        .findAll();
  }

  /// Convert breeding record to sync-friendly map
  Map<String, dynamic> _breedingRecordToSyncMap(BreedingRecordIsar record) {
    return {
      'id': record.businessId,
      'cattleId': record.cattleId,
      'bullIdOrType': record.bullIdOrType,
      'date': record.date?.toIso8601String(),
      'method': record.method,
      'status': record.status,
      'expectedDate': record.expectedDate?.toIso8601String(),
      'notes': record.notes,
      'cost': record.cost,
      'createdAt': record.createdAt?.toIso8601String(),
      'updatedAt': record.updatedAt?.toIso8601String(),
    };
  }

  /// Convert sync map to breeding record
  BreedingRecordIsar _breedingRecordFromSyncMap(Map<String, dynamic> map) {
    final record = BreedingRecordIsar()
      ..businessId = map['id'] as String?
      ..cattleId = map['cattleId'] as String?
      ..bullIdOrType = map['bullIdOrType'] as String?
      ..method = map['method'] as String?
      ..status = map['status'] as String?
      ..notes = map['notes'] as String?
      ..cost = map['cost'] != null ? (map['cost'] as num).toDouble() : null;

    // Handle dates
    if (map['date'] != null) {
      record.date = DateTime.parse(map['date']);
    }
    if (map['expectedDate'] != null) {
      record.expectedDate = DateTime.parse(map['expectedDate']);
    }
    if (map['createdAt'] != null) {
      record.createdAt = DateTime.parse(map['createdAt']);
    }
    if (map['updatedAt'] != null) {
      record.updatedAt = DateTime.parse(map['updatedAt']);
    }

    return record;
  }

  /// Bidirectional sync with external API
  Future<bool> syncData() async {
    try {
      // Check if API sync is enabled
      if (!ApiConfig.isApiSyncAvailable) {
        _logger.info('Breeding sync skipped - API sync disabled (local-only mode)');
        return true; // Return success for local-only mode
      }

      final lastSync = await getLastSyncTime();
      final localRecords = await getModifiedBreedingRecordsSince(lastSync);

      // Prepare data for API
      final syncData = {
        'lastSync': lastSync?.toIso8601String(),
        'records': localRecords.map((r) => _breedingRecordToSyncMap(r)).toList(),
      };

      final response = await http.post(
        Uri.parse(ApiConfig.breedingSync),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode(syncData),
      ).timeout(ApiConfig.syncTimeout);

      if (response.statusCode == 200) {
        if (!response.body.startsWith('{')) {
          _logger.error(
              'Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          return false;
        }

        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (!responseData.containsKey('records')) {
          _logger.error('Invalid response format: Missing records field');
          return false;
        }

        // Process server records and handle conflicts
        final List<dynamic> serverRecords = responseData['records'];
        await _processServerBreedingRecords(serverRecords);

        await setLastSyncTime(DateTime.now());
        _logger.info('Breeding records synchronized successfully');
        return true;
      } else {
        _logger.error('Failed to sync breeding records: $response.statusCode');
        return false;
      }
    } catch (e) {
      _logger.error('Error syncing breeding records: $e');
      return false;
    }
  }

  /// Process server records and handle conflicts
  Future<void> _processServerBreedingRecords(List<dynamic> serverRecords) async {
    for (final recordData in serverRecords) {
      try {
        final serverRecord = _breedingRecordFromSyncMap(recordData);
        final isar = GetIt.instance<Isar>();
        final existingRecord = await isar.breedingRecordIsars
            .filter()
            .businessIdEqualTo(serverRecord.businessId)
            .findFirst();

        if (existingRecord == null) {
          // New record from server
          await _breedingRepository.saveBreedingRecord(serverRecord);
        } else {
          // Handle conflict resolution - server wins for now
          final resolvedRecord = _resolveBreedingConflict(existingRecord, serverRecord);
          await _breedingRepository.saveBreedingRecord(resolvedRecord);
        }
      } catch (e) {
        _logger.error('Error processing server breeding record: $e');
      }
    }
  }

  /// Simple conflict resolution - server wins
  /// In a more sophisticated implementation, this could use timestamps,
  /// user preferences, or merge strategies
  BreedingRecordIsar _resolveBreedingConflict(BreedingRecordIsar local, BreedingRecordIsar server) {
    // For now, server record wins in conflicts
    // Keep the local Isar ID but use server data
    server.id = local.id;
    return server;
  }
}
