import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/notification_settings_controller.dart';
import '../../../constants/app_colors.dart';

/// Screen for managing notification retention and cleanup settings
class NotificationRetentionSettingsScreen extends StatefulWidget {
  const NotificationRetentionSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationRetentionSettingsScreen> createState() => _NotificationRetentionSettingsScreenState();
}

class _NotificationRetentionSettingsScreenState extends State<NotificationRetentionSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationSettingsController>().loadSettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Retention & Cleanup'),
      ),
      body: Consumer<NotificationSettingsController>(
        builder: (context, controller, child) {
          if (controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: controller.loadSettings,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final settings = controller.settings;
          if (settings == null) {
            return const Center(child: Text('No settings available'));
          }

          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Storage Overview Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Storage Overview',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Total Notifications:'),
                          Text(
                            '1,234', // This would come from actual count
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Unread Notifications:'),
                          Text(
                            '45', // This would come from actual count
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppColors.notificationStatusColors['unread'],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Storage Used:'),
                          Text(
                            '2.3 MB', // This would come from actual calculation
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Auto-deletion Settings
              _buildSectionHeader('Auto-Deletion Settings'),
              ListTile(
                title: const Text('Maximum Notifications to Keep'),
                subtitle: Text('Currently: ${settings.maxNotificationsToKeep}'),
                trailing: const Icon(Icons.edit),
                onTap: () => _showNumberPickerDialog(
                  context,
                  'Maximum Notifications',
                  settings.maxNotificationsToKeep,
                  (value) => controller.updateRetentionSetting('maxNotificationsToKeep', value),
                ),
              ),
              ListTile(
                title: const Text('Auto-delete after (days)'),
                subtitle: Text('Delete all notifications after ${settings.autoDeleteAfterDays} days'),
                trailing: const Icon(Icons.edit),
                onTap: () => _showNumberPickerDialog(
                  context,
                  'Auto-delete Days',
                  settings.autoDeleteAfterDays,
                  (value) => controller.updateRetentionSetting('autoDeleteAfterDays', value),
                ),
              ),
              SwitchListTile(
                title: const Text('Auto-delete Read Notifications'),
                subtitle: const Text('Automatically delete read notifications'),
                value: settings.autoDeleteReadNotifications,
                onChanged: (value) => controller.updateRetentionSetting('autoDeleteReadNotifications', value),
              ),
              if (settings.autoDeleteReadNotifications)
                ListTile(
                  title: const Text('Delete read notifications after (days)'),
                  subtitle: Text('Delete read notifications after ${settings.autoDeleteReadAfterDays} days'),
                  trailing: const Icon(Icons.edit),
                  onTap: () => _showNumberPickerDialog(
                    context,
                    'Delete Read After Days',
                    settings.autoDeleteReadAfterDays,
                    (value) => controller.updateRetentionSetting('autoDeleteReadAfterDays', value),
                  ),
                ),

              const SizedBox(height: 24),

              // Manual Cleanup Actions
              _buildSectionHeader('Manual Cleanup'),
              ListTile(
                title: const Text('Clean Up Old Notifications'),
                subtitle: const Text('Remove notifications older than 90 days'),
                leading: const Icon(Icons.cleaning_services),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showCleanupDialog(context, controller),
              ),
              ListTile(
                title: const Text('Clean Up Read Notifications'),
                subtitle: const Text('Remove all read notifications'),
                leading: const Icon(Icons.mark_email_read),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showReadCleanupDialog(context, controller),
              ),
              ListTile(
                title: const Text('Export Before Cleanup'),
                subtitle: const Text('Export notifications to CSV before deletion'),
                leading: const Icon(Icons.download),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showExportDialog(context, controller),
              ),

              const SizedBox(height: 24),

              // Archive Settings
              _buildSectionHeader('Archive Settings'),
              ListTile(
                title: const Text('Archive Old Notifications'),
                subtitle: const Text('Move old notifications to archive instead of deleting'),
                trailing: Switch(
                  value: false, // This would come from settings
                  onChanged: (value) {
                    // Implement archive setting
                  },
                ),
              ),
              ListTile(
                title: const Text('View Archived Notifications'),
                subtitle: const Text('Browse and manage archived notifications'),
                leading: const Icon(Icons.archive),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // Navigate to archived notifications screen
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.notificationHeader,
        ),
      ),
    );
  }

  void _showNumberPickerDialog(
    BuildContext context,
    String title,
    int currentValue,
    Function(int) onChanged,
  ) {
    final controller = TextEditingController(text: currentValue.toString());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Value',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final value = int.tryParse(controller.text);
              if (value != null && value > 0) {
                onChanged(value);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showCleanupDialog(BuildContext context, NotificationSettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clean Up Old Notifications'),
        content: const Text(
          'This will permanently delete all notifications older than 90 days. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Implement cleanup logic
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Cleanup completed')),
              );
            },
            child: const Text('Clean Up'),
          ),
        ],
      ),
    );
  }

  void _showReadCleanupDialog(BuildContext context, NotificationSettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clean Up Read Notifications'),
        content: const Text(
          'This will permanently delete all read notifications. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Implement read cleanup logic
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Read notifications cleaned up')),
              );
            },
            child: const Text('Clean Up'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog(BuildContext context, NotificationSettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Notifications'),
        content: const Text(
          'Export all notifications to a CSV file before cleanup?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Implement export logic
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Export completed')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }
}