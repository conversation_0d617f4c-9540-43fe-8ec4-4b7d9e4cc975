import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'dart:math' as math;
import '../../Cattle/models/cattle_isar.dart';
import '../models/delivery_record_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

final _logger = Logger('DeliveryFormDialog');

// --- Constants ---
class _AppStrings {
  static const String addDeliveryTitle = 'Record Calving';
  static const String editDeliveryTitle = 'Edit Calving Record';
  static const String motherLabel = 'Mother';
  static const String deliveryDateLabel = 'Delivery Date';
  static const String deliveryTypeLabel = 'Delivery Type';
  static const String numberOfCalvesLabel = 'Number of Calves';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';
  static const String calfNameLabel = 'Calf Name';
  static const String calfGenderLabel = 'Calf Gender';

  // Validation messages
  static const String motherRequired = 'Please select a mother';
  static const String dateRequired = 'Delivery date is required';

}

class DeliveryFormDialog extends StatefulWidget {
  final DeliveryRecordIsar? record;
  final String? motherTagId;
  final List<CattleIsar> existingCattle;
  final Function(DeliveryRecordIsar)? onSave;

  const DeliveryFormDialog({
    Key? key,
    this.record,
    this.motherTagId,
    required this.existingCattle,
    this.onSave,
  }) : super(key: key);

  @override
  State<DeliveryFormDialog> createState() => _DeliveryFormDialogState();
}

class _DeliveryFormDialogState extends State<DeliveryFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _costController = TextEditingController();
  final Isar _isar = GetIt.instance<Isar>();

  final bool _isLoading = false;
  bool _isSaving = false;
  String? _selectedMotherTagId;
  DateTime _deliveryDate = DateTime.now();
  String _deliveryType = 'Normal';
  String _numberOfCalves = 'Single';
  bool _showOptionalFields = false;

  // Dynamic calf fields (no tag ID controllers - auto-generated)
  final List<TextEditingController> _calfNameControllers = [];
  final List<String> _calfGenders = [];
  List<String> _generatedTagIds = []; // Store auto-generated tag IDs

  // Delivery type options
  final List<String> _deliveryTypeOptions = [
    'Normal',
    'Assisted',
    'C-Section',
  ];

  // Number of calves options
  final List<String> _numberOfCalvesOptions = [
    'Single',
    'Twins',
    'Triplets',
  ];

  // Gender options for calves
  final List<String> _genderOptions = [
    'Male',
    'Female',
  ];

  @override
  void initState() {
    super.initState();
    _logger.info('initState called');

    // If editing, populate form with existing data
    if (widget.record != null) {
      _selectedMotherTagId = widget.record!.cattleId;
      _deliveryDate = widget.record!.deliveryDate ?? DateTime.now();
      _deliveryType = widget.record!.deliveryType ?? 'Normal';
      _numberOfCalves = _convertIntToString(widget.record!.numberOfCalves);
      _notesController.text = widget.record!.notes ?? '';
      _costController.text = widget.record!.cost?.toString() ?? '';
    } else {
      _selectedMotherTagId = widget.motherTagId;
    }

    // Initialize calf fields based on number of calves
    _initializeCalfFields();
  }

  @override
  void dispose() {
    _notesController.dispose();
    _costController.dispose();

    // Dispose calf name controllers
    for (var controller in _calfNameControllers) {
      controller.dispose();
    }

    super.dispose();
  }

  // Initialize calf fields based on number of calves
  void _initializeCalfFields() {
    // Clear existing controllers
    for (var controller in _calfNameControllers) {
      controller.dispose();
    }

    _calfNameControllers.clear();
    _calfGenders.clear();
    _generatedTagIds.clear();

    // Get the number of calves as integer
    final calfCount = _convertStringToInt(_numberOfCalves);

    // Create new controllers for each calf
    for (int i = 0; i < calfCount; i++) {
      _calfNameControllers.add(TextEditingController());
      _calfGenders.add('Male'); // Default to Male
      _generatedTagIds.add(''); // Will be generated on save
    }
  }

  // Update calf fields when number of calves changes
  void _updateCalfFields(String newValue) {
    if (newValue == _numberOfCalves) return;

    setState(() {
      _numberOfCalves = newValue;
      _initializeCalfFields();
    });
  }

  // Generate tag IDs for calves (matching cattle form dialog logic)
  Future<List<String>> _generateCalfTagIds(int count) async {
    List<String> tagIds = [];

    try {
      // Get the mother cattle to determine animal type
      CattleIsar? motherCattle;
      if (_selectedMotherTagId != null) {
        motherCattle = widget.existingCattle.firstWhere(
          (cattle) => cattle.tagId == _selectedMotherTagId,
          orElse: () => CattleIsar(),
        );
      }

      // Get animal type prefix (same logic as cattle form dialog)
      String prefix = 'C'; // Default fallback
      if (motherCattle?.animalTypeId != null) {
        try {
          // Query animal types to get the prefix
          final animalTypes = await _isar.animalTypeIsars.where().findAll();
          final animalType = animalTypes.firstWhere(
            (type) => type.businessId == motherCattle!.animalTypeId,
            orElse: () => AnimalTypeIsar(),
          );
          prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'C';
        } catch (e) {
          _logger.warning('Error getting animal type for prefix: $e');
          prefix = 'C'; // Fallback to 'C' for cattle
        }
      }

      // Get all existing cattle to find the highest number for this prefix
      final allCattle = await _isar.cattleIsars.where().findAll();
      int maxNumber = 0;
      final regExp = RegExp('^$prefix(\\d+)\$');

      for (final cattle in allCattle) {
        final tagId = cattle.tagId;
        if (tagId != null && regExp.hasMatch(tagId)) {
          final match = regExp.firstMatch(tagId);
          if (match != null && match.groupCount >= 1) {
            final number = int.tryParse(match.group(1) ?? '0') ?? 0;
            maxNumber = math.max(maxNumber, number);
          }
        }
      }

      // Generate sequential tag IDs starting from the next available number
      for (int i = 0; i < count; i++) {
        tagIds.add('$prefix${maxNumber + 1 + i}');
      }

      return tagIds;
    } catch (e) {
      _logger.warning('Error generating calf tag IDs: $e');
      // Fallback to simple numbering with 'C' prefix
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      for (int i = 0; i < count; i++) {
        tagIds.add('C${timestamp + i}');
      }
      return tagIds;
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      // Validate required fields
      if (_selectedMotherTagId == null) {
        throw Exception(_AppStrings.motherRequired);
      }

      // Generate tag IDs for calves
      final calfCount = _convertStringToInt(_numberOfCalves);
      _generatedTagIds = await _generateCalfTagIds(calfCount);

      // Create or update a DeliveryRecordIsar object
      final DeliveryRecordIsar deliveryRecord;

      if (widget.record != null) {
        // Update existing record
        deliveryRecord = DeliveryRecordIsar()
          ..id = widget.record!.id
          ..businessId = widget.record!.businessId
          ..cattleId = _selectedMotherTagId
          ..deliveryDate = _deliveryDate
          ..deliveryType = _deliveryType
          ..numberOfCalves = _convertStringToInt(_numberOfCalves)
          ..cost = _costController.text.trim().isNotEmpty ? double.tryParse(_costController.text.trim()) : null
          ..notes = _notesController.text.trim()
          ..status = 'Completed'
          ..createdAt = widget.record!.createdAt
          ..updatedAt = DateTime.now();
      } else {
        // Create new record with standardized business ID format
        final sequenceNumber = await _getNextDeliverySequenceNumber(_selectedMotherTagId!);
        final standardizedBusinessId = '${_selectedMotherTagId!}-Delivery-$sequenceNumber';

        deliveryRecord = DeliveryRecordIsar()
          ..businessId = standardizedBusinessId // e.g., "B1-Delivery-1"
          ..cattleId = _selectedMotherTagId! // This is now the tagId
          ..deliveryDate = _deliveryDate
          ..deliveryType = _deliveryType
          ..numberOfCalves = _convertStringToInt(_numberOfCalves)
          ..cost = _costController.text.trim().isNotEmpty ? double.tryParse(_costController.text.trim()) : null
          ..notes = _notesController.text.trim()
          ..status = 'Completed'
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
      }

      // Call the save callback if provided
      if (widget.onSave != null) {
        widget.onSave!(deliveryRecord);
      }

      // Debug log to validate model
      debugPrint('Saved DeliveryRecord: ${deliveryRecord.toMap()}');

      // Close dialog and let parent handle success message
      if (mounted) {
        Navigator.of(context).pop(deliveryRecord);
      }
    } catch (e) {
      debugPrint('ERROR: Failed to save delivery record: $e');
      if (mounted) {
        setState(() => _isSaving = false);
        BreedingMessageUtils.showError(context,
            'Error: ${e.toString().replaceAll('Exception: ', '')}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Dialog(
        child: SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return widget.record == null
        ? UniversalFormDialog(
            title: _AppStrings.addDeliveryTitle,
            headerIcon: Icons.child_care, // Delivery-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
              addText: 'Save', // User preference: Use 'Save' instead of 'Add'
              isAdding: _isSaving,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editDeliveryTitle,
            headerIcon: Icons.child_care, // Delivery-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
              updateText: 'Save', // User preference: Use 'Save' instead of 'Update'
              isUpdating: _isSaving,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Mother Selection Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.motherLabel,
            value: _selectedMotherTagId,
            items: widget.existingCattle
                .where((cattle) => cattle.gender == CattleGender.female)
                .map<DropdownMenuItem<String>>((cattle) {
              final cattleName = cattle.name ?? 'Unknown';
              final tagId = cattle.tagId ?? '';
              final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
              return DropdownMenuItem(
                value: cattle.tagId, // Use tagId as the value
                child: Text(displayName, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedMotherTagId = value;
              });
            },
            prefixIcon: Icons.female,
            prefixIconColor: Colors.pink,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'mother'),
          ),
          UniversalFormField.spacing,

          // Delivery Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.deliveryDateLabel,
            value: _deliveryDate,
            onChanged: (date) {
              setState(() {
                _deliveryDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            validator: (date) {
              if (date == null) {
                return _AppStrings.dateRequired;
              }
              return null;
            },
            lastDate: DateTime.now(),
          ),
          UniversalFormField.spacing,

          // Delivery Type Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.deliveryTypeLabel,
            value: _deliveryType,
            items: _deliveryTypeOptions.map<DropdownMenuItem<String>>((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _deliveryType = value!;
              });
            },
            prefixIcon: Icons.medical_services,
            prefixIconColor: Colors.red,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'delivery type'),
          ),
          UniversalFormField.spacing,

          // Number of Calves Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.numberOfCalvesLabel,
            value: _numberOfCalves,
            items: _numberOfCalvesOptions.map<DropdownMenuItem<String>>((number) {
              return DropdownMenuItem(
                value: number,
                child: Text(number),
              );
            }).toList(),
            onChanged: (value) {
              _updateCalfFields(value!);
            },
            prefixIcon: Icons.child_care,
            prefixIconColor: Colors.purple,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'number of calves'),
          ),
          UniversalFormField.spacing,

          // Calf Information Fields (Required)
          ..._buildCalfFields(),

          UniversalFormField.sectionSpacing,

          // Optional Information Toggle Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showOptionalFields = !_showOptionalFields;
                });
              },
              icon: Icon(
                _showOptionalFields
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF2E7D32),
              ),
              label: Text(
                _showOptionalFields
                    ? 'Hide Optional Information'
                    : 'Show Optional Information',
                style: const TextStyle(
                  color: Color(0xFF2E7D32),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: Color(0xFF2E7D32),
                  width: 1.5,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          // Optional Fields - Conditionally Displayed
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Cost Field
            UniversalFormField.numberField(
              label: _AppStrings.costLabel,
              controller: _costController,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.green,
              // No validator - field is optional
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.notes,
              prefixIconColor: Colors.cyan,
            ),
          ],
        ],
      ),
    );
  }

  // Build dynamic calf fields based on number of calves
  List<Widget> _buildCalfFields() {
    List<Widget> calfWidgets = [];

    final calfCount = _convertStringToInt(_numberOfCalves);

    for (int i = 0; i < calfCount; i++) {
      calfWidgets.addAll([
        // Calf section header
        Padding(
          padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
          child: Text(
            'Calf ${i + 1} Information',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2E7D32),
            ),
          ),
        ),

        // Calf Name Field
        UniversalFormField.textField(
          label: _AppStrings.calfNameLabel,
          controller: _calfNameControllers[i],
          prefixIcon: Icons.pets,
          prefixIconColor: Colors.blue,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter name for Calf ${i + 1}';
            }
            return null;
          },
        ),
        UniversalFormField.spacing,

        // Calf Gender Dropdown
        UniversalFormField.dropdownField<String>(
          label: _AppStrings.calfGenderLabel,
          value: _calfGenders[i],
          items: _genderOptions.map<DropdownMenuItem<String>>((gender) {
            return DropdownMenuItem(
              value: gender,
              child: Text(gender),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _calfGenders[i] = value!;
            });
          },
          prefixIcon: Icons.wc,
          prefixIconColor: Colors.purple,
          validator: (value) => UniversalFormField.dropdownValidator(value, 'calf gender'),
        ),
      ]);
    }

    return calfWidgets;
  }

  /// Convert string representation to integer for numberOfCalves
  int _convertStringToInt(String value) {
    switch (value) {
      case 'Single':
        return 1;
      case 'Twins':
        return 2;
      case 'Triplets':
        return 3;
      default:
        return 1;
    }
  }

  /// Convert integer to string representation for numberOfCalves
  String _convertIntToString(int value) {
    switch (value) {
      case 1:
        return 'Single';
      case 2:
        return 'Twins';
      case 3:
        return 'Triplets';
      default:
        return 'Single';
    }
  }

  /// Get the next sequence number for delivery records for this cattle
  Future<int> _getNextDeliverySequenceNumber(String cattleTagId) async {
    try {
      // Query the database to get the count of existing delivery records for this cattle
      final isar = GetIt.instance<Isar>();
      final existingRecords = await isar.deliveryRecordIsars
          .filter()
          .cattleIdEqualTo(cattleTagId)
          .findAll();

      // Return the next sequence number (count + 1)
      return existingRecords.length + 1;
    } catch (e) {
      debugPrint('Error getting delivery sequence number: $e');
      return 1; // Default to 1 if there's an error
    }
  }
}
