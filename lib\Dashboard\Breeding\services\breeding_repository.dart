
import 'package:flutter/foundation.dart';
import 'package:isar/isar.dart';
import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../models/delivery_record_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Breeding module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
/// Sync functionality moved to dedicated BreedingSyncService for clean separation
class BreedingRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  BreedingRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE BREEDING STREAMS ===//

  /// Watches all breeding records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<BreedingRecordIsar>> watchAllBreedingRecords() {
    return _isar.breedingRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all pregnancy records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<PregnancyRecordIsar>> watchAllPregnancyRecords() {
    return _isar.pregnancyRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all delivery records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<DeliveryRecordIsar>> watchAllDeliveryRecords() {
    return _isar.deliveryRecordIsars.where().watch(fireImmediately: true);
  }

  //=== BREEDING RECORDS CRUD ===//

  /// Save (add or update) a breeding record using Isar's native upsert
  Future<void> saveBreedingRecord(BreedingRecordIsar record) async {
    debugPrint('🔄 BREEDING REPOSITORY: Saving breeding record');
    debugPrint('   Record ID: $record.businessId');
    debugPrint('   Cattle ID: $record.cattleId');
    debugPrint('   Method: $record.method');
    debugPrint('   Status: $record.status');

    final result = await _isar.writeTxn(() async {
      return await _isar.breedingRecordIsars.put(record);
    });

    debugPrint('✅ BREEDING REPOSITORY: Breeding record saved successfully');
    debugPrint('   Isar ID assigned: $result');

    // Verify the record was actually saved
    final savedRecord = await _isar.breedingRecordIsars.get(result);
    if (savedRecord != null) {
      debugPrint('✅ BREEDING REPOSITORY: Record verification successful');
      debugPrint('   Verified businessId: $savedRecord.businessId');
    } else {
      debugPrint('❌ BREEDING REPOSITORY: Record verification FAILED - record not found!');
    }

    // Check total count in database
    final totalCount = await _isar.breedingRecordIsars.count();
    debugPrint('📊 BREEDING REPOSITORY: Total breeding records in database: $totalCount');
  }

  /// Delete a breeding record by its Isar ID
  Future<void> deleteBreedingRecord(int id) async {
    debugPrint('🗑️ BREEDING REPOSITORY: Deleting breeding record with ID: $id');

    // Check record exists before deletion
    final recordBefore = await _isar.breedingRecordIsars.get(id);
    debugPrint('🔍 BREEDING REPOSITORY: Record before deletion: ${recordBefore?.businessId}');

    final deleteResult = await _isar.writeTxn(() async {
      return await _isar.breedingRecordIsars.delete(id);
    });

    debugPrint('✅ BREEDING REPOSITORY: Breeding record deleted successfully');
    debugPrint('   Delete result: $deleteResult');

    // Verify deletion
    final recordAfter = await _isar.breedingRecordIsars.get(id);
    debugPrint('🔍 BREEDING REPOSITORY: Record after deletion: ${recordAfter?.businessId ?? 'null (deleted)'}');

    // Check total count
    final totalCount = await _isar.breedingRecordIsars.count();
    debugPrint('📊 BREEDING REPOSITORY: Total records after deletion: $totalCount');
  }

  //=== PREGNANCY RECORDS CRUD ===//

  /// Save (add or update) a pregnancy record using Isar's native upsert
  Future<void> savePregnancyRecord(PregnancyRecordIsar record) async {
    debugPrint('🔄 BREEDING REPOSITORY: Saving pregnancy record');
    debugPrint('   Record ID: $record.businessId');
    debugPrint('   Cattle ID: $record.cattleId');
    debugPrint('   Status: $record.status');

    await _isar.writeTxn(() async {
      await _isar.pregnancyRecordIsars.put(record);
    });

    debugPrint('✅ BREEDING REPOSITORY: Pregnancy record saved successfully');
  }

  /// Delete a pregnancy record by its Isar ID
  Future<void> deletePregnancyRecord(int id) async {
    await _isar.writeTxn(() async {
      await _isar.pregnancyRecordIsars.delete(id);
    });
  }

  //=== DELIVERY RECORDS CRUD ===//

  /// Save (add or update) a delivery record using Isar's native upsert
  Future<void> saveDeliveryRecord(DeliveryRecordIsar record) async {
    debugPrint('🔄 BREEDING REPOSITORY: Saving delivery record');
    debugPrint('   Record ID: $record.businessId');
    debugPrint('   Cattle ID: $record.cattleId');
    debugPrint('   Number of Calves: $record.numberOfCalves');

    await _isar.writeTxn(() async {
      await _isar.deliveryRecordIsars.put(record);
    });

    debugPrint('✅ BREEDING REPOSITORY: Delivery record saved successfully');
  }

  /// Delete a delivery record by its Isar ID
  Future<void> deleteDeliveryRecord(int id) async {
    await _isar.writeTxn(() async {
      await _isar.deliveryRecordIsars.delete(id);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all breeding records (for reports and analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<BreedingRecordIsar>> getAllBreedingRecords() async {
    return await _isar.breedingRecordIsars.where().findAll();
  }

  /// Get breeding records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<BreedingRecordIsar>> getBreedingRecordsForCattle(String cattleId) async {
    return await _isar.breedingRecordIsars
        .filter()
        .cattleIdEqualTo(cattleId)
        .findAll();
  }

  /// Get pregnancy records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<PregnancyRecordIsar>> getPregnancyRecordsForCattle(String cattleId) async {
    return await _isar.pregnancyRecordIsars
        .filter()
        .cattleIdEqualTo(cattleId)
        .findAll();
  }

  /// Get delivery records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<DeliveryRecordIsar>> getDeliveryRecordsForCattle(String cattleId) async {
    return await _isar.deliveryRecordIsars
        .filter()
        .cattleIdEqualTo(cattleId)
        .findAll();
  }
}