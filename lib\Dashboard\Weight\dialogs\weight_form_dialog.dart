import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/weight_record_isar.dart';
import '../services/weight_repository.dart';
import '../services/weight_analytics_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

// --- Constants ---
class _AppStrings {
  static const String addWeightTitle = 'Add Weight Record';
  static const String editWeightTitle = 'Edit Weight Record';
  static const String cattleLabel = 'Select Cattle';
  static const String weightLabel = 'Weight';
  static const String unitLabel = 'Unit';
  static const String measurementDateLabel = 'Measurement Date';
  static const String measurementMethodLabel = 'Measurement Method';
  static const String notesLabel = 'Notes (Optional)';

  // Validation messages
  static const String weightRequired = 'Please enter weight';
  static const String weightInvalid = 'Please enter valid weight';

  // Success messages
  static const String addSuccess = 'Weight record added successfully';
  static const String updateSuccess = 'Weight record updated successfully';
}

/// Weight form dialog with reactive updates - no callbacks needed
class WeightFormDialog extends StatefulWidget {
  final List<CattleIsar> cattle;
  final WeightRecordIsar? existingRecord; // For editing
  final CattleIsar? preSelectedCattle; // For pre-selecting specific cattle

  const WeightFormDialog({
    Key? key,
    required this.cattle,
    this.existingRecord,
    this.preSelectedCattle,
  }) : super(key: key);

  @override
  State<WeightFormDialog> createState() => _WeightFormDialogState();
}

class _WeightFormDialogState extends State<WeightFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final WeightRepository _weightRepository = GetIt.I<WeightRepository>();
  final Uuid _uuid = const Uuid();

  // Form controllers
  final _weightController = TextEditingController();
  final _notesController = TextEditingController();
  final _measuredByController = TextEditingController();
  final _bodyConditionNotesController = TextEditingController();

  // Form data with new enum system
  CattleIsar? _selectedCattle;
  DateTime _measurementDate = DateTime.now();
  WeightUnit _weightUnit = WeightUnit.kg;
  MeasurementMethod _measurementMethod = MeasurementMethod.scale;
  String _measurementLocation = 'barn';
  MeasurementQuality _measurementQuality = MeasurementQuality.good;
  HealthStatus _healthStatus = HealthStatus.healthy;
  FeedingStatus _feedingStatus = FeedingStatus.normal;
  double? _bodyConditionScore;
  bool _isPregnant = false;
  int? _pregnancyStage;
  Season _season = Season.spring;
  FeedQuality _feedQuality = FeedQuality.good;
  bool _isEstimate = false;
  double _confidenceLevel = 1.0;

  // Loading state
  bool _isSaving = false;

  // Colors - Green theme for header and save button
  static const _headerColor = Color(0xFF2E7D32);         // Green for header and save button
  static const _measurementColor = Color(0xFF7B1FA2);    // Purple for Measurement Details
  static const _notesColor = Color(0xFF1976D2);          // Blue for Notes

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.existingRecord != null) {
      final record = widget.existingRecord!;
      _weightController.text = record.weight.toString();
      _notesController.text = record.notes ?? '';
      _measuredByController.text = record.measuredBy ?? '';
      _bodyConditionNotesController.text = record.bodyConditionNotes ?? '';
      
      // Get cattle from IsarLink relationship
      _selectedCattle = record.cattle.value ?? widget.cattle.first;

      _measurementDate = record.measurementDate ?? DateTime.now();
      _weightUnit = record.weightUnit;
      _measurementMethod = record.measurementMethod;
      _measurementLocation = record.measurementLocation ?? 'barn';
      _measurementQuality = record.measurementQuality;
      _healthStatus = record.healthStatus;
      _feedingStatus = record.feedingStatus;
      _bodyConditionScore = record.bodyConditionScore;
      _isPregnant = record.isPregnant ?? false;
      _pregnancyStage = record.pregnancyStage;
      _season = record.season;
      _feedQuality = record.feedQuality;
      _isEstimate = record.isEstimate ?? false;
      _confidenceLevel = record.confidenceLevel ?? 1.0;
    } else {
      // Use pre-selected cattle if provided, otherwise use first cattle
      _selectedCattle = widget.preSelectedCattle ??
          (widget.cattle.isNotEmpty ? widget.cattle.first : null);
    }
  }

  @override
  void dispose() {
    _weightController.dispose();
    _notesController.dispose();
    _measuredByController.dispose();
    _bodyConditionNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.existingRecord == null
        ? UniversalFormDialog(
            title: _AppStrings.addWeightTitle,
            headerIcon: Icons.scale, // Weight-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _saveRecord,
              addText: 'Save', // User preference: Use 'Save' instead of 'Add'
              isAdding: _isSaving,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editWeightTitle,
            headerIcon: Icons.scale, // Weight-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _saveRecord,
              updateText: 'Save', // User preference: Use 'Save' instead of 'Update'
              isUpdating: _isSaving,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildCompactForm(),
        ],
      ),
    );
  }



  Widget _buildCompactForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Cattle selection
        UniversalFormField.dropdownField<CattleIsar>(
          label: _AppStrings.cattleLabel,
          value: _selectedCattle,
          items: widget.cattle.map((cattle) {
            final cattleName = cattle.name ?? 'Unknown';
            final tagId = cattle.tagId ?? '';
            final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
            return DropdownMenuItem(
              value: cattle,
              child: Text(displayName, overflow: TextOverflow.ellipsis),
            );
          }).toList(),
          onChanged: (value) => setState(() => _selectedCattle = value),
          prefixIcon: Icons.pets,
          prefixIconColor: _headerColor,
          validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
        ),
        UniversalFormField.spacing,

        // Weight input
        Row(
          children: [
            Expanded(
              flex: 3,
              child: UniversalFormField.numberField(
                label: _AppStrings.weightLabel,
                controller: _weightController,
                allowDecimals: true,
                prefixIcon: Icons.scale,
                prefixIconColor: _measurementColor,
                validator: (value) {
                  if (value == null || value.isEmpty) return _AppStrings.weightRequired;
                  final weight = double.tryParse(value);
                  if (weight == null || weight <= 0) return _AppStrings.weightInvalid;
                  return null;
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 2,
              child: UniversalFormField.dropdownField<WeightUnit>(
                label: _AppStrings.unitLabel,
                value: _weightUnit,
                items: WeightUnit.values.map((unit) {
                  return DropdownMenuItem(
                    value: unit,
                    child: Text(
                      unit.displayName,
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList(),
                onChanged: (value) => setState(() => _weightUnit = value!),
              ),
            ),
          ],
        ),
        UniversalFormField.spacing,

        // Measurement date
        UniversalFormField.dateField(
          context: context,
          label: _AppStrings.measurementDateLabel,
          value: _measurementDate,
          onChanged: (date) {
            setState(() {
              _measurementDate = date ?? DateTime.now();
            });
          },
          prefixIcon: Icons.calendar_today,
          prefixIconColor: _headerColor,
          lastDate: DateTime.now(),
        ),
        UniversalFormField.spacing,

        // Measurement method
        UniversalFormField.dropdownField<MeasurementMethod>(
          label: _AppStrings.measurementMethodLabel,
          value: _measurementMethod,
          items: MeasurementMethod.values.map((method) {
            return DropdownMenuItem(
              value: method,
              child: Text(method.displayName),
            );
          }).toList(),
          onChanged: (value) => setState(() => _measurementMethod = value!),
          prefixIcon: Icons.straighten,
          prefixIconColor: _measurementColor,
        ),
        UniversalFormField.spacing,

        // Notes
        UniversalFormField.multilineField(
          label: _AppStrings.notesLabel,
          controller: _notesController,
          maxLines: 3,
          prefixIcon: Icons.note,
          prefixIconColor: _notesColor,
        ),
      ],
    );
  }




  Future<void> _saveRecord() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCattle == null) return;

    // Context is used with mounted checks, which is safe in StatefulWidget

    setState(() => _isSaving = true);

    try {
      final record = WeightRecordIsar()
        ..weight = double.parse(_weightController.text)
        ..weightUnit = _weightUnit
        ..measurementDate = _measurementDate
        ..measurementMethod = _measurementMethod
        ..measurementLocation = _measurementLocation
        ..measuredBy = _measuredByController.text.isNotEmpty ? _measuredByController.text : null
        ..bodyConditionScore = _bodyConditionScore
        ..bodyConditionNotes = _bodyConditionNotesController.text.isNotEmpty ? _bodyConditionNotesController.text : null
        ..healthStatus = _healthStatus
        ..feedingStatus = _feedingStatus
        ..isPregnant = _isPregnant
        ..pregnancyStage = _pregnancyStage
        ..season = _season
        ..feedQuality = _feedQuality
        ..measurementQuality = _measurementQuality
        ..notes = _notesController.text.isNotEmpty ? _notesController.text : null
        ..isEstimate = _isEstimate
        ..confidenceLevel = _confidenceLevel;

      // Set up IsarLink relationship
      record.cattle.value = _selectedCattle;

      if (widget.existingRecord != null) {
        // Editing existing record
        record.id = widget.existingRecord!.id;
        record.businessId = widget.existingRecord!.businessId;
        record.createdAt = widget.existingRecord!.createdAt;
        record.updatedAt = DateTime.now();
        await _weightRepository.updateRecord(record);
      } else {
        // Adding new record - prepare with analytics service
        record.businessId = _uuid.v4();
        record.createdAt = DateTime.now();
        record.updatedAt = DateTime.now();

        // Get previous record for growth calculations
        final allRecords = await _weightRepository.watchAllWeightRecords().first;
        final cattleRecords = allRecords.where((r) => r.cattle.value?.businessId == _selectedCattle!.businessId).toList();
        final previousRecord = cattleRecords.isEmpty ? null : cattleRecords.first;

        // Use analytics service to populate growth data
        final preparedRecord = WeightAnalyticsService.populateGrowthData(record, previousRecord);

        await _weightRepository.addRecord(preparedRecord);
      }

      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.existingRecord != null
              ? _AppStrings.updateSuccess
              : _AppStrings.addSuccess,
        );

        // No callback needed - reactive streams handle updates!
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, '$_AppStrings.saveError: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
