import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/milk_record_isar.dart';
import '../../Events/models/event_isar.dart';
import '../../Events/services/events_repository.dart';

/// Milk Event Integration Service
///
/// Handles creation of events related to milk records following established patterns.
/// Uses dependency injection for EventsRepository and follows the established
/// cross-module integration patterns.
class MilkEventIntegration {
  // Repository for event operations - use GetIt dependency injection
  EventsRepository get _eventsRepository => GetIt.instance<EventsRepository>();

  static const Uuid _uuid = Uuid();

  MilkEventIntegration();

  /// Create milk-related events
  Future<void> createMilkEvents(MilkRecordIsar milkRecord) async {
    try {
      // Create milking schedule event
      if (milkRecord.date != null) {
        await _createMilkingEvent(milkRecord);
      }

      // Create quality check event if fat content is recorded
      if (milkRecord.date != null && milkRecord.fatContent != null) {
        await _createQualityCheckEvent(milkRecord);
      }
    } catch (e) {
      // Let exceptions bubble up naturally - no logging in integration service
      rethrow;
    }
  }

  /// Create milking event
  Future<void> _createMilkingEvent(MilkRecordIsar milkRecord) async {
    final event = EventIsar()
      ..businessId = _uuid.v4()
      ..title = 'Milking - ${milkRecord.cattleTagNumber ?? milkRecord.cattleBusinessId}'
      ..description = 'Milking session for cattle ${milkRecord.cattleTagNumber ?? milkRecord.cattleBusinessId} - ${milkRecord.totalAmount?.toStringAsFixed(1) ?? '0.0'}L'
      ..category = EventCategory.management
      ..priority = EventPriority.medium
      ..status = EventStatus.completed
      ..scheduledDate = milkRecord.date!
      ..cattleTagId = milkRecord.cattleTagNumber ?? milkRecord.cattleBusinessId
      ..isAutoGenerated = true
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventsRepository.saveEvent(event);
  }

  /// Create quality check event
  Future<void> _createQualityCheckEvent(MilkRecordIsar milkRecord) async {
    final event = EventIsar()
      ..businessId = _uuid.v4()
      ..title = 'Milk Quality Check - ${milkRecord.cattleTagNumber ?? milkRecord.cattleBusinessId}'
      ..description = 'Quality check for milk from cattle ${milkRecord.cattleTagNumber ?? milkRecord.cattleBusinessId} - Fat: ${milkRecord.fatContent?.toStringAsFixed(1) ?? 'N/A'}%'
      ..type = 'quality_check'
      ..category = EventCategory.health
      ..priority = EventPriority.high
      ..status = EventStatus.completed
      ..scheduledDate = milkRecord.date!
      ..startTime = milkRecord.date!
      ..endTime = milkRecord.date!.add(const Duration(minutes: 30))
      ..cattleBusinessId = milkRecord.cattleBusinessId
      ..cattleTagId = milkRecord.cattleTagNumber
      ..isAutoGenerated = true
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventsRepository.saveEvent(event);
  }

  /// Update milk events when a milk record is updated
  Future<void> updateMilkEvents(MilkRecordIsar milkRecord) async {
    try {
      // Get existing events for this cattle and milk record
      final events = await _eventsRepository.getEventsByCattleBusinessId(milkRecord.cattleBusinessId ?? '');

      for (final event in events) {
        if (event.isAutoGenerated == true) {
          // Update the milking event with new data
          event.description = 'Milking session for cattle ${milkRecord.cattleTagNumber ?? milkRecord.cattleBusinessId} - ${milkRecord.totalAmount?.toStringAsFixed(1) ?? '0.0'}L';
          event.scheduledDate = milkRecord.date!;
          event.cattleTagId = milkRecord.cattleTagNumber ?? milkRecord.cattleBusinessId;
          event.updatedAt = DateTime.now();

          await _eventsRepository.saveEvent(event);
        }
      }
    } catch (e) {
      // Let exceptions bubble up naturally
      rethrow;
    }
  }

  /// Delete milk events when a milk record is deleted
  Future<void> deleteMilkEvents(String cattleBusinessId, String milkRecordBusinessId) async {
    try {
      final events = await _eventsRepository.getEventsByCattle(cattleBusinessId);

      final milkEvents = events.where((event) =>
          event.isAutoGenerated == true &&
          event.cattleTagId == cattleBusinessId
        ).toList();

      for (final event in milkEvents) {
        await _eventsRepository.deleteEvent(event.id);
      }
    } catch (e) {
      // Let exceptions bubble up naturally
      rethrow;
    }
  }

  /// Create daily milking schedule for a cattle
  Future<void> createDailyMilkingSchedule({
    required String cattleBusinessId,
    required String cattleTagId,
    required DateTime milkingTime,
  }) async {
    try {
      // Check if a milking schedule already exists for this cattle
      final existingEvents = await _eventsRepository.getEventsByCattle(cattleBusinessId);
        final existingMilkingEvent = existingEvents.where(
          (event) => event.isRecurring == true,
        ).firstOrNull;

      if (existingMilkingEvent == null) {
        // Create new daily milking schedule
        final event = EventIsar()
          ..businessId = _uuid.v4()
          ..title = 'Daily Milking - $cattleTagId'
          ..description = 'Daily milking schedule for cattle $cattleTagId'
          ..category = EventCategory.production
          ..priority = EventPriority.medium
          ..status = EventStatus.pending
          ..scheduledDate = milkingTime
          ..cattleTagId = cattleBusinessId
          ..isRecurring = true
          ..isAutoGenerated = true
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        await _eventsRepository.saveEvent(event);
      }
    } catch (e) {
      // Let exceptions bubble up naturally
      rethrow;
    }
  }
}