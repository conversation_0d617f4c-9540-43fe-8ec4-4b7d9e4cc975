import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:intl/intl.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

final _logger = Logger('PregnancyFormDialog');

// --- Constants ---
class _AppStrings {
  static const String addPregnancyTitle = 'Record Pregnancy';
  static const String editPregnancyTitle = 'Edit Pregnancy Record';
  static const String cattleLabel = 'Cattle';
  static const String startDateLabel = 'Pregnancy Start Date';
  static const String statusLabel = 'Status';
  static const String expectedDateLabel = 'Expected Calving Date';
  static const String actualDateLabel = 'Actual Calving Date';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';

  // Validation messages
  static const String cattleRequired = 'Please select a cattle';
  static const String dateRequired = 'Start date is required';

}

class PregnancyFormDialog extends StatefulWidget {
  final PregnancyRecordIsar? record;
  final String? initialCattleId;
  final String? breedingRecordId;
  final List<CattleIsar>? preloadedCattle;
  final Map<String, AnimalTypeIsar>? preloadedAnimalTypes;
  final Function(PregnancyRecordIsar)? onSave;

  const PregnancyFormDialog({
    Key? key,
    this.record,
    this.initialCattleId,
    this.breedingRecordId,
    this.preloadedCattle,
    this.preloadedAnimalTypes,
    this.onSave,
  }) : super(key: key);

  @override
  State<PregnancyFormDialog> createState() => _PregnancyFormDialogState();
}

class _PregnancyFormDialogState extends State<PregnancyFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _costController = TextEditingController();

  List<CattleIsar> _allCattle = [];

  bool _isLoading = false;
  bool _isSaving = false;
  String? _selectedCattleId;
  DateTime _startDate = DateTime.now();
  String _status = 'Confirmed';
  DateTime? _expectedCalvingDate;
  DateTime? _actualCalvingDate;
  bool _showOptionalFields = false;

  final List<String> _statusOptions = [
    'Confirmed',
    'Completed',
    'Abortion',
  ];

  @override
  void initState() {
    super.initState();
    _logger.info('initState called');
    _loadData();

    // If editing, populate form with existing data
    if (widget.record != null) {
      _selectedCattleId = widget.record!.cattleId;
      _startDate = widget.record!.startDate ?? DateTime.now();
      _status = widget.record!.status ?? 'Confirmed';
      _notesController.text = widget.record!.notes ?? '';
      _costController.text = widget.record!.cost?.toString() ?? '';
      _expectedCalvingDate = widget.record!.expectedCalvingDate;
      _actualCalvingDate = widget.record!.actualCalvingDate;
    } else if (widget.initialCattleId != null) {
      _selectedCattleId = widget.initialCattleId;
    }

    // Calculate expected calving date
    _updateExpectedCalvingDate();
  }

  /// Get the next sequence number for pregnancy records for this cattle
  Future<int> _getNextPregnancySequenceNumber(String cattleTagId) async {
    try {
      // Query the database to get the count of existing pregnancy records for this cattle
      final isar = GetIt.instance<Isar>();
      final existingRecords = await isar.pregnancyRecordIsars
          .filter()
          .cattleIdEqualTo(cattleTagId)
          .findAll();

      // Return the next sequence number (count + 1)
      return existingRecords.length + 1;
    } catch (e) {
      debugPrint('Error getting pregnancy sequence number: $e');
      return 1; // Default to 1 if there's an error
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _costController.dispose();
    super.dispose();
  }

  void _updateExpectedCalvingDate() {
    // Calculate expected calving date (approximately 280 days from start date)
    setState(() {
      _expectedCalvingDate = _startDate.add(const Duration(days: 280));
    });
  }

  Future<void> _loadData() async {
    _logger.info('_loadData called');
    try {
      // Use preloaded data if available
      if (widget.preloadedCattle != null && widget.preloadedAnimalTypes != null) {
        _logger.info('Using preloaded data');
        // Only show female cattle
        final femaleCattle =
            widget.preloadedCattle!.where((c) => c.gender == CattleGender.female).toList();

        if (mounted) {
          setState(() {
            _allCattle = femaleCattle;


            // If there's no pre-selected cattle, use the first one if available
            if (_selectedCattleId == null && femaleCattle.isNotEmpty) {
              _selectedCattleId = femaleCattle.first.tagId;
            }

            _isLoading = false;
          });
        }
      } else {
        // No preloaded data available
        if (mounted) {
          setState(() {
            _allCattle = [];

            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context, 'Error loading cattle: $e');
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      // Validate required fields
      if (_selectedCattleId == null) {
        throw Exception(_AppStrings.cattleRequired);
      }

      // Create or update a PregnancyRecordIsar object
      final PregnancyRecordIsar pregnancyRecord;

      if (widget.record != null) {
        // Update existing record
        pregnancyRecord = PregnancyRecordIsar()
          ..id = widget.record!.id
          ..businessId = widget.record!.businessId
          ..cattleId = _selectedCattleId
          ..startDate = _startDate
          ..status = _status
          ..expectedCalvingDate = _expectedCalvingDate
          ..actualCalvingDate = _actualCalvingDate
          ..cost = _costController.text.trim().isNotEmpty ? double.tryParse(_costController.text.trim()) : null
          ..notes = _notesController.text.trim()
          ..breedingRecordId = widget.breedingRecordId
          ..createdAt = widget.record!.createdAt
          ..updatedAt = DateTime.now();
      } else {
        // Create new record with standardized business ID format
        final sequenceNumber = await _getNextPregnancySequenceNumber(_selectedCattleId!);
        final standardizedBusinessId = '${_selectedCattleId!}-Pregnancy-$sequenceNumber';

        pregnancyRecord = PregnancyRecordIsar()
          ..businessId = standardizedBusinessId // e.g., "B1-Pregnancy-1"
          ..cattleId = _selectedCattleId! // This is now the tagId
          ..startDate = _startDate
          ..status = _status
          ..expectedCalvingDate = _expectedCalvingDate
          ..actualCalvingDate = _actualCalvingDate
          ..cost = _costController.text.trim().isNotEmpty ? double.tryParse(_costController.text.trim()) : null
          ..notes = _notesController.text.trim()
          ..breedingRecordId = widget.breedingRecordId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
      }

      // Call the save callback if provided
      if (widget.onSave != null) {
        widget.onSave!(pregnancyRecord);
      }

      // Debug log to validate model
      debugPrint('Saved PregnancyRecord: ${pregnancyRecord.toMap()}');

      // Close dialog and let parent handle success message
      if (mounted) {
        Navigator.of(context).pop(pregnancyRecord);
      }
    } catch (e) {
      debugPrint('ERROR: Failed to save pregnancy record: $e');
      if (mounted) {
        setState(() => _isSaving = false);
        BreedingMessageUtils.showError(context,
            'Error: ${e.toString().replaceAll('Exception: ', '')}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Dialog(
        child: SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return widget.record == null
        ? UniversalFormDialog(
            title: _AppStrings.addPregnancyTitle,
            headerIcon: Icons.pregnant_woman, // Pregnancy-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
              addText: 'Save', // User preference: Use 'Save' instead of 'Add'
              isAdding: _isSaving,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editPregnancyTitle,
            headerIcon: Icons.pregnant_woman, // Pregnancy-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
              updateText: 'Save', // User preference: Use 'Save' instead of 'Update'
              isUpdating: _isSaving,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Cattle Selection Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.cattleLabel,
            value: _selectedCattleId,
            items: _allCattle.map<DropdownMenuItem<String>>((cattle) {
              final cattleName = cattle.name ?? 'Unknown';
              final tagId = cattle.tagId ?? '';
              final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
              return DropdownMenuItem(
                value: cattle.tagId, // Use tagId as the value
                child: Text(displayName, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCattleId = value;
                _updateExpectedCalvingDate();
              });
            },
            prefixIcon: Icons.pets,
            prefixIconColor: Colors.green,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
          ),
          UniversalFormField.spacing,

          // Pregnancy Start Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.startDateLabel,
            value: _startDate,
            onChanged: (date) {
              setState(() {
                _startDate = date ?? DateTime.now();
                _updateExpectedCalvingDate();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            validator: (date) {
              if (date == null) {
                return _AppStrings.dateRequired;
              }
              return null;
            },
            lastDate: DateTime.now(),
          ),
          UniversalFormField.spacing,

          // Status Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.statusLabel,
            value: _status,
            items: _statusOptions.map<DropdownMenuItem<String>>((status) {
              return DropdownMenuItem(
                value: status,
                child: Text(status),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _status = value!;
              });
            },
            prefixIcon: Icons.info,
            prefixIconColor: Colors.indigo,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'status'),
          ),
          UniversalFormField.spacing,

          // Expected Calving Date Display (Read-only)
          if (_expectedCalvingDate != null) ...[
            UniversalFormField.textField(
              label: _AppStrings.expectedDateLabel,
              initialValue: DateFormat('MMMM dd, yyyy').format(_expectedCalvingDate!),
              readOnly: true,
              prefixIcon: Icons.event_available,
              prefixIconColor: Colors.green,
            ),
            UniversalFormField.spacing,
          ],

          UniversalFormField.sectionSpacing,

          // Optional Information Toggle Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showOptionalFields = !_showOptionalFields;
                });
              },
              icon: Icon(
                _showOptionalFields
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF2E7D32),
              ),
              label: Text(
                _showOptionalFields
                    ? 'Hide Optional Information'
                    : 'Show Optional Information',
                style: const TextStyle(
                  color: Color(0xFF2E7D32),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: Color(0xFF2E7D32),
                  width: 1.5,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          // Optional Fields - Conditionally Displayed
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Actual Calving Date (for completed pregnancies)
            if (_status == 'Completed') ...[
              UniversalFormField.dateField(
                context: context,
                label: _AppStrings.actualDateLabel,
                value: _actualCalvingDate,
                onChanged: (date) {
                  setState(() {
                    _actualCalvingDate = date;
                  });
                },
                prefixIcon: Icons.child_care,
                prefixIconColor: Colors.purple,
                lastDate: DateTime.now(),
              ),
              UniversalFormField.spacing,
            ],

            // Cost Field
            UniversalFormField.numberField(
              label: _AppStrings.costLabel,
              controller: _costController,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.green,
              // No validator - field is optional
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.notes,
              prefixIconColor: Colors.cyan,
            ),
          ],
        ],
      ),
    );
  }
}
