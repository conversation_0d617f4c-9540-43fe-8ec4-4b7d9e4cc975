import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/breeding_details_controller.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_layout.dart'; // For ResponsiveGrid
import '../../widgets/universal_info_card.dart'; // For UniversalInfoCard



/// Data class for analytics info cards to provide type safety
class AnalyticsCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String? insight;

  const AnalyticsCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.insight,
  });
}

class BreedingDetailsAnalyticsTab extends StatelessWidget {
  const BreedingDetailsAnalyticsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<BreedingDetailsController>(
      builder: (context, controller, child) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        if (controller.cattle == null) {
          return UniversalTabEmptyState.forTab(
            title: 'No Cattle Data',
            message: 'Cattle information not available',
            tabColor: AppColors.breedingKpiColors[0],
            tabIndex: 0,
          );
        }

        final hasAnyData = controller.breedingRecords.isNotEmpty ||
                          controller.pregnancyRecords.isNotEmpty ||
                          controller.deliveryRecords.isNotEmpty;

        if (!hasAnyData) {
          return UniversalTabEmptyState.forTab(
            title: 'No Breeding Analytics Data',
            message: 'Add breeding records for ${controller.cattle!.name} to see comprehensive analytics.',
            tabColor: AppColors.breedingKpiColors[0],
            tabIndex: 0,
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breeding Performance Metrics
              _buildBreedingPerformance(context, controller),
              const SizedBox(height: 24),

              // Pregnancy Analytics
              _buildPregnancyAnalytics(context, controller),
              const SizedBox(height: 24),

              // Delivery Analytics
              _buildDeliveryAnalytics(context, controller),
              const SizedBox(height: 24),

              // Financial Analytics
              _buildFinancialAnalytics(context, controller),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBreedingPerformance(BuildContext context, BreedingDetailsController controller) {
    return _buildGridSection(
      context,
      title: 'Breeding Performance',
      subtitle: 'Key breeding metrics and success rates',
      icon: Icons.trending_up,
      headerColor: AppColors.breedingHeader,
      cardData: _buildBreedingPerformanceCards(controller),
    );
  }

  List<AnalyticsCardData> _buildBreedingPerformanceCards(BreedingDetailsController controller) {

    return [
      AnalyticsCardData(
        title: 'Total Attempts',
        value: controller.totalBreedingAttempts.toString(),
        subtitle: 'All breeding attempts',
        icon: Icons.repeat,
        color: AppColors.breedingKpiColors[0],
        insight: 'Total breeding attempts recorded',
      ),
      AnalyticsCardData(
        title: 'Pending',
        value: controller.pendingBreedings.toString(),
        subtitle: 'Awaiting confirmation',
        icon: Icons.schedule,
        color: Colors.orange,
        insight: 'Breeding attempts awaiting confirmation',
      ),
      AnalyticsCardData(
        title: 'Confirmed',
        value: controller.confirmedBreedings.toString(),
        subtitle: 'Confirmed pregnancies',
        icon: Icons.verified,
        color: Colors.blue,
        insight: 'Confirmed pregnancies awaiting delivery',
      ),
      AnalyticsCardData(
        title: 'Completed',
        value: controller.completedBreedings.toString(),
        subtitle: 'Successful breedings',
        icon: Icons.check_circle,
        color: Colors.green,
        insight: 'Successfully completed breedings with calving',
      ),
      AnalyticsCardData(
        title: 'Failed',
        value: controller.failedBreedings.toString(),
        subtitle: 'Unsuccessful attempts',
        icon: Icons.cancel,
        color: Colors.red,
        insight: 'Unsuccessful breeding attempts',
      ),
      AnalyticsCardData(
        title: 'Success Rate',
        value: '${controller.conceptionRate.toStringAsFixed(1)}%',
        subtitle: 'Conception rate',
        icon: Icons.trending_up,
        color: _getSuccessRateColor(controller.conceptionRate),
        insight: _getConceptionRateInsight(controller.conceptionRate),
      ),
    ];
  }

  Widget _buildPregnancyAnalytics(BuildContext context, BreedingDetailsController controller) {
    return _buildGridSection(
      context,
      title: 'Pregnancy Analytics',
      subtitle: 'Pregnancy tracking and outcomes',
      icon: Icons.pregnant_woman,
      headerColor: AppColors.breedingHeader,
      cardData: _buildPregnancyAnalyticsCards(controller),
    );
  }

  List<AnalyticsCardData> _buildPregnancyAnalyticsCards(BreedingDetailsController controller) {

    return [
      AnalyticsCardData(
        title: 'Total Pregnancies',
        value: controller.totalPregnancies.toString(),
        subtitle: 'All pregnancy records',
        icon: Icons.pregnant_woman,
        color: AppColors.breedingKpiColors[0],
        insight: 'Total pregnancy records',
      ),
      AnalyticsCardData(
        title: 'Confirmed',
        value: controller.confirmedPregnancies.toString(),
        subtitle: 'Currently pregnant',
        icon: Icons.check_circle,
        color: Colors.green,
        insight: 'Active confirmed pregnancies',
      ),
      AnalyticsCardData(
        title: 'Completed',
        value: controller.successfulPregnancies.toString(),
        subtitle: 'Successful births',
        icon: Icons.child_care,
        color: Colors.purple,
        insight: 'Successfully completed pregnancies',
      ),
      AnalyticsCardData(
        title: 'Abortions',
        value: controller.abortedPregnancies.toString(),
        subtitle: 'Pregnancy losses',
        icon: Icons.warning,
        color: Colors.red,
        insight: 'Pregnancy losses or abortions',
      ),
      AnalyticsCardData(
        title: 'Success Rate',
        value: controller.totalPregnancies > 0
            ? '${(controller.successfulPregnancies / controller.totalPregnancies * 100).toStringAsFixed(1)}%'
            : 'N/A',
        subtitle: 'Completion rate',
        icon: Icons.trending_up,
        color: AppColors.breedingKpiColors[3],
        insight: 'Percentage of successful pregnancies',
      ),
      AnalyticsCardData(
        title: 'Current Status',
        value: controller.isCurrentlyPregnant ? 'Pregnant' : 'Open',
        subtitle: 'Breeding status',
        icon: controller.isCurrentlyPregnant ? Icons.pregnant_woman : Icons.circle_outlined,
        color: controller.isCurrentlyPregnant ? Colors.green : Colors.orange,
        insight: controller.isCurrentlyPregnant
            ? 'Currently confirmed pregnant'
            : 'Not currently pregnant',
      ),
    ];
  }

  Widget _buildDeliveryAnalytics(BuildContext context, BreedingDetailsController controller) {
    return _buildGridSection(
      context,
      title: 'Delivery Analytics',
      subtitle: 'Calving statistics and outcomes',
      icon: Icons.child_care,
      headerColor: AppColors.breedingHeader,
      cardData: _buildDeliveryAnalyticsCards(controller),
    );
  }

  List<AnalyticsCardData> _buildDeliveryAnalyticsCards(BreedingDetailsController controller) {

    return [
      AnalyticsCardData(
        title: 'Total Deliveries',
        value: controller.totalDeliveries.toString(),
        subtitle: 'Calving events',
        icon: Icons.child_care,
        color: AppColors.breedingKpiColors[0],
        insight: 'Total successful deliveries',
      ),
      AnalyticsCardData(
        title: 'Total Calves',
        value: controller.totalCalves.toString(),
        subtitle: 'Offspring produced',
        icon: Icons.people,
        color: AppColors.breedingKpiColors[1],
        insight: 'Total calves born from all deliveries',
      ),
      AnalyticsCardData(
        title: 'Successful Deliveries',
        value: controller.successfulDeliveries.toString(),
        subtitle: 'No complications',
        icon: Icons.check_circle,
        color: Colors.green,
        insight: 'Deliveries without complications',
      ),
      AnalyticsCardData(
        title: 'Avg. Calves',
        value: controller.totalDeliveries > 0
            ? (controller.totalCalves / controller.totalDeliveries).toStringAsFixed(1)
            : '0.0',
        subtitle: 'Per delivery',
        icon: Icons.calculate,
        color: AppColors.breedingKpiColors[2],
        insight: 'Average calves per delivery event',
      ),
      AnalyticsCardData(
        title: 'Delivery Rate',
        value: controller.totalPregnancies > 0
            ? '${(controller.totalDeliveries / controller.totalPregnancies * 100).toStringAsFixed(1)}%'
            : 'N/A',
        subtitle: 'Pregnancies to delivery',
        icon: Icons.trending_up,
        color: AppColors.breedingKpiColors[3],
        insight: 'Percentage of pregnancies resulting in delivery',
      ),
      AnalyticsCardData(
        title: 'Success Rate',
        value: controller.totalDeliveries > 0
            ? '${(controller.successfulDeliveries / controller.totalDeliveries * 100).toStringAsFixed(1)}%'
            : 'N/A',
        subtitle: 'Complication-free',
        icon: Icons.health_and_safety,
        color: Colors.teal,
        insight: 'Percentage of deliveries without complications',
      ),
    ];
  }

  Widget _buildFinancialAnalytics(BuildContext context, BreedingDetailsController controller) {
    return _buildGridSection(
      context,
      title: 'Financial Analytics',
      subtitle: 'Breeding costs and returns',
      icon: Icons.attach_money,
      headerColor: AppColors.breedingHeader,
      cardData: _buildFinancialAnalyticsCards(controller),
    );
  }

  List<AnalyticsCardData> _buildFinancialAnalyticsCards(BreedingDetailsController controller) {
    // Placeholder financial calculations - would need actual cost data
    return [
      AnalyticsCardData(
        title: 'Breeding Costs',
        value: 'N/A',
        subtitle: 'Total breeding expenses',
        icon: Icons.money_off,
        color: AppColors.breedingKpiColors[0],
        insight: 'Breeding service and medication costs',
      ),
      AnalyticsCardData(
        title: 'Revenue per Calf',
        value: 'N/A',
        subtitle: 'Average calf value',
        icon: Icons.trending_up,
        color: AppColors.breedingKpiColors[1],
        insight: 'Market value of calves produced',
      ),
      AnalyticsCardData(
        title: 'ROI',
        value: 'N/A',
        subtitle: 'Return on investment',
        icon: Icons.percent,
        color: AppColors.breedingKpiColors[2],
        insight: 'Breeding program profitability',
      ),
      AnalyticsCardData(
        title: 'Cost per Pregnancy',
        value: 'N/A',
        subtitle: 'Average cost to achieve pregnancy',
        icon: Icons.calculate,
        color: AppColors.breedingKpiColors[3],
        insight: 'Efficiency of breeding investments',
      ),
    ];
  }

  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<AnalyticsCardData> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Icon(icon, color: headerColor, size: 24),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: headerColor,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 2. Build the grid using universal component
        _buildMetricCardGrid(cardData),
      ],
    );
  }

  // Build a grid of metric cards using universal responsive grid
  Widget _buildMetricCardGrid(List<AnalyticsCardData> cardData) {
    return ResponsiveGrid.cards(
      children: cardData.map((data) => _buildMetricCard(data)).toList(),
    );
  }

  // Build individual metric card using universal component
  Widget _buildMetricCard(AnalyticsCardData data) {
    return UniversalInfoCard(
      title: data.title,
      value: data.value,
      subtitle: data.subtitle,
      icon: data.icon,
      color: data.color,
      insight: data.insight,
    );
  }

  // Helper methods
  Color _getSuccessRateColor(double rate) {
    if (rate >= 80) return Colors.green;
    if (rate >= 60) return Colors.orange;
    return Colors.red;
  }

  String _getConceptionRateInsight(double rate) {
    if (rate >= 80) return 'Excellent conception rate';
    if (rate >= 60) return 'Good conception rate';
    if (rate >= 40) return 'Average conception rate';
    return 'Consider improving breeding practices';
  }
}
