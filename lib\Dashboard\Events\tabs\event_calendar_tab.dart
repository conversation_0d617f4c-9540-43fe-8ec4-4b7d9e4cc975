import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:provider/provider.dart';
import '../controllers/events_controller.dart';
import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../dialogs/event_form_dialog.dart';
import '../screens/event_details_screen.dart';
import '../../../constants/app_colors.dart';

/// Event Calendar Tab with table_calendar integration
/// 
/// Features:
/// - Monthly, weekly, and daily calendar views
/// - Color-coded event display by category
/// - Event creation by tapping on calendar dates
/// - Event details popup on event tap with navigation to details
/// - Calendar navigation and date selection
/// - Performance optimization for large datasets with lazy loading
class EventCalendarTab extends StatefulWidget {
  final EventsController controller;

  const EventCalendarTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<EventCalendarTab> createState() => _EventCalendarTabState();
}

class _EventCalendarTabState extends State<EventCalendarTab> {
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late ValueNotifier<List<EventIsar>> _selectedEvents;

  @override
  void initState() {
    super.initState();
    _calendarFormat = CalendarFormat.month;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _selectedEvents = ValueNotifier(_getEventsForDay(_selectedDay));
  }

  @override
  void dispose() {
    _selectedEvents.dispose();
    super.dispose();
  }

  /// Get events for a specific day using optimized calendar method
  List<EventIsar> _getEventsForDay(DateTime day) {
    // Use optimized calendar events for better performance
    return widget.controller.events.where((event) {
      return isSameDay(event.scheduledDate, day);
    }).toList();
  }

  /// Handle day selection
  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
        _selectedEvents.value = _getEventsForDay(selectedDay);
      });
    }
  }

  /// Handle format change (month/2weeks/week)
  void _onFormatChanged(CalendarFormat format) {
    if (_calendarFormat != format) {
      setState(() {
        _calendarFormat = format;
      });
    }
  }

  /// Handle page change
  void _onPageChanged(DateTime focusedDay) {
    setState(() {
      _focusedDay = focusedDay;
    });
  }

  /// Get color for event based on category
  Color _getEventColor(EventCategory category) {
    return Color(int.parse(category.colorHex.replaceFirst('#', '0xFF')));
  }

  /// Get icon for event based on category
  IconData _getEventIcon(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return Icons.medical_services;
      case EventCategory.breeding:
        return Icons.favorite;
      case EventCategory.feeding:
        return Icons.restaurant;
      case EventCategory.management:
        return Icons.business;
      case EventCategory.maintenance:
        return Icons.build;
      case EventCategory.financial:
        return Icons.attach_money;
      case EventCategory.other:
        return Icons.event;
    }
  }

  /// Get color for event status
  Color _getStatusColor(EventStatus status) {
    return Color(int.parse(status.colorHex.replaceFirst('#', '0xFF')));
  }

  /// Handle event tap - navigate to event details screen
  void _onEventTap(EventIsar event) {
    if (event.businessId == null) return;
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EventDetailsScreen(
          eventBusinessId: event.businessId!,
        ),
      ),
    );
  }

  /// Handle date tap - create new event
  void _onDateTap(DateTime date) {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattle: widget.controller.unfilteredCattle,
        eventTypes: widget.controller.unfilteredEventTypes,
        onSave: (event) async {
          // Set the selected date as the scheduled date
          event.scheduledDate = DateTime(
            date.year,
            date.month,
            date.day,
            event.scheduledDate?.hour ?? 9,
            event.scheduledDate?.minute ?? 0,
          );
          
          try {
            await widget.controller.addEvent(event);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Event created successfully'),
                  backgroundColor: AppColors.success,
                ),
              );
              // Update selected events if the new event is on the selected day
              if (isSameDay(date, _selectedDay)) {
                _selectedEvents.value = _getEventsForDay(_selectedDay);
              }
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error creating event: $e'),
                  backgroundColor: AppColors.error,
                ),
              );
            }
          }
        },
      ),
    );
  }







  @override
  Widget build(BuildContext context) {
    return Consumer<EventsController>(
      builder: (context, controller, child) {
        // Update selected events when controller data changes
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _selectedEvents.value = _getEventsForDay(_selectedDay);
          }
        });

        return Column(
          children: [
            // Calendar widget
            Card(
              margin: const EdgeInsets.all(8.0),
              child: TableCalendar<EventIsar>(
                firstDay: DateTime.utc(2020, 1, 1),
                lastDay: DateTime.utc(2030, 12, 31),
                focusedDay: _focusedDay,
                calendarFormat: _calendarFormat,
                eventLoader: _getEventsForDay,
                selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                onDaySelected: _onDaySelected,
                onFormatChanged: _onFormatChanged,
                onPageChanged: _onPageChanged,
                
                // Calendar styling
                calendarStyle: CalendarStyle(
                  // Today's date styling
                  todayDecoration: BoxDecoration(
                    color: AppColors.eventsHeader.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  
                  // Selected date styling
                  selectedDecoration: const BoxDecoration(
                    color: AppColors.eventsHeader,
                    shape: BoxShape.circle,
                  ),
                  
                  // Event marker styling
                  markerDecoration: const BoxDecoration(
                    color: AppColors.eventsHeader,
                    shape: BoxShape.circle,
                  ),
                  
                  // Weekend styling
                  weekendTextStyle: TextStyle(
                    color: Colors.red[400],
                  ),
                  
                  // Outside month styling
                  outsideDaysVisible: false,
                ),
                
                // Header styling
                headerStyle: const HeaderStyle(
                  formatButtonTextStyle: TextStyle(
                    color: AppColors.eventsHeader,
                    fontWeight: FontWeight.bold,
                  ),
                  titleTextStyle: TextStyle(
                    color: AppColors.eventsHeader,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  leftChevronIcon: Icon(
                    Icons.chevron_left,
                    color: AppColors.eventsHeader,
                  ),
                  rightChevronIcon: Icon(
                    Icons.chevron_right,
                    color: AppColors.eventsHeader,
                  ),
                ),
                
                // Calendar builders for custom event display
                calendarBuilders: CalendarBuilders<EventIsar>(
                  // Custom marker builder for color-coded events
                  markerBuilder: (context, day, events) {
                    if (events.isEmpty) return null;
                    
                    return Positioned(
                      bottom: 1,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: events.take(3).map((event) {
                          return Container(
                            margin: const EdgeInsets.symmetric(horizontal: 1),
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: _getEventColor(event.category),
                              shape: BoxShape.circle,
                            ),
                          );
                        }).toList(),
                      ),
                    );
                  },
                ),
                
                // Enable tap to create events
                onDayLongPressed: (selectedDay, focusedDay) => _onDateTap(selectedDay),
              ),
            ),
            
            // Color legend
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Wrap(
                spacing: 16,
                runSpacing: 8,
                children: EventCategory.values.map((category) {
                  return _ColorLegendItem(
                    color: _getEventColor(category),
                    label: category.displayName,
                  );
                }).toList(),
              ),
            ),
            
            const Divider(),
            
            // Selected day events
            Expanded(
              child: ValueListenableBuilder<List<EventIsar>>(
                valueListenable: _selectedEvents,
                builder: (context, events, _) {
                  if (events.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.event_note,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No events for ${_selectedDay.day}/${_selectedDay.month}/${_selectedDay.year}',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextButton.icon(
                            onPressed: () => _onDateTap(_selectedDay),
                            icon: const Icon(Icons.add),
                            label: const Text('Add Event'),
                          ),
                        ],
                      ),
                    );
                  }
                  
                  return ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: events.length,
                    itemBuilder: (context, index) {
                      final event = events[index];
                      return _EventCard(
                        event: event,
                        onTap: () => _onEventTap(event),
                        getEventColor: _getEventColor,
                        getEventIcon: _getEventIcon,
                        getStatusColor: _getStatusColor,
                      );
                    },
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}

/// Color legend item widget
class _ColorLegendItem extends StatelessWidget {
  final Color color;
  final String label;

  const _ColorLegendItem({
    required this.color,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}

/// Event card widget for displaying events in the list
class _EventCard extends StatelessWidget {
  final EventIsar event;
  final VoidCallback onTap;
  final Color Function(EventCategory) getEventColor;
  final IconData Function(EventCategory) getEventIcon;
  final Color Function(EventStatus) getStatusColor;

  const _EventCard({
    required this.event,
    required this.onTap,
    required this.getEventColor,
    required this.getEventIcon,
    required this.getStatusColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: getEventColor(event.category),
          child: Icon(
            getEventIcon(event.category),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                event.title ?? 'Untitled Event',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: getStatusColor(event.status),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                event.status.displayName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (event.description?.isNotEmpty == true) ...[
              const SizedBox(height: 4),
              Text(
                event.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  event.scheduledDate != null
                      ? '${event.scheduledDate!.hour.toString().padLeft(2, '0')}:${event.scheduledDate!.minute.toString().padLeft(2, '0')}'
                      : 'No time set',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.category,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  event.category.displayName,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: onTap,
        isThreeLine: event.description?.isNotEmpty == true,
      ),
    );
  }
}



/// Complete event dialog
class _CompleteEventDialog extends StatefulWidget {
  final EventIsar event;
  final Function(String?, double?) onComplete;

  const _CompleteEventDialog({
    required this.event,
    required this.onComplete,
  });

  @override
  State<_CompleteEventDialog> createState() => _CompleteEventDialogState();
}

class _CompleteEventDialogState extends State<_CompleteEventDialog> {
  final _notesController = TextEditingController();
  final _costController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _notesController.dispose();
    _costController.dispose();
    super.dispose();
  }

  void _handleComplete() async {
    setState(() {
      _isLoading = true;
    });

    final notes = _notesController.text.trim().isEmpty ? null : _notesController.text.trim();
    final cost = _costController.text.trim().isEmpty ? null : double.tryParse(_costController.text.trim());

    widget.onComplete(notes, cost);
    
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Complete "${widget.event.title}"'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Completion Notes (Optional)',
              hintText: 'Add any notes about completing this event...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _costController,
            decoration: const InputDecoration(
              labelText: 'Actual Cost (Optional)',
              hintText: 'Enter actual cost...',
              border: OutlineInputBorder(),
              prefixText: '\$',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleComplete,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Complete'),
        ),
      ],
    );
  }
}
