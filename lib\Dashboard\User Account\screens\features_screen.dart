import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_bar.dart';

/// Comprehensive features showcase screen
/// Displays all available modules and their key features in an organized layout
class FeaturesScreen extends StatelessWidget {
  const FeaturesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBarConfig.withBack(
        title: 'App Features',
        context: context,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeaderSection(),
            const SizedBox(height: 24),

            // Cattle Management Module
            _buildModuleSection(
              title: 'Cattle Management',
              icon: Icons.pets,
              color: AppColors.cattleHeader,
              features: [
                const _FeatureItem(
                  icon: Icons.add_circle_outline,
                  title: 'Add & Edit Cattle',
                  description: 'Register new cattle with detailed information including breed, age, and health status',
                ),
                const _FeatureItem(
                  icon: Icons.search,
                  title: 'Advanced Search & Filtering',
                  description: 'Quickly find cattle by tag ID, breed, gender, or any custom criteria',
                ),
                const _FeatureItem(
                  icon: Icons.info_outline,
                  title: 'Detailed Cattle Profiles',
                  description: 'View comprehensive information including photos, lineage, and history',
                ),
                const _FeatureItem(
                  icon: Icons.analytics,
                  title: 'Cattle Analytics',
                  description: 'Track herd composition, breed distribution, and growth trends',
                ),
              ],
            ),

            // Health Records Module
            _buildModuleSection(
              title: 'Health Records',
              icon: Icons.medical_services,
              color: AppColors.healthHeader,
              features: [
                const _FeatureItem(
                  icon: Icons.healing,
                  title: 'Health Record Tracking',
                  description: 'Maintain detailed health records for each animal with treatment history',
                ),
                const _FeatureItem(
                  icon: Icons.vaccines,
                  title: 'Vaccination Management',
                  description: 'Schedule and track vaccinations with automatic reminders',
                ),
                const _FeatureItem(
                  icon: Icons.notification_important,
                  title: 'Health Alerts',
                  description: 'Get notified about upcoming treatments and health checkups',
                ),
                const _FeatureItem(
                  icon: Icons.trending_up,
                  title: 'Health Analytics',
                  description: 'Monitor herd health trends and identify potential issues early',
                ),
              ],
            ),

            // Breeding Management Module
            _buildModuleSection(
              title: 'Breeding Management',
              icon: Icons.favorite,
              color: AppColors.breedingHeader,
              features: [
                const _FeatureItem(
                  icon: Icons.calendar_today,
                  title: 'Breeding Schedule',
                  description: 'Plan and track breeding activities with detailed records',
                ),
                const _FeatureItem(
                  icon: Icons.pregnant_woman,
                  title: 'Pregnancy Tracking',
                  description: 'Monitor pregnancy progress with due date calculations',
                ),
                const _FeatureItem(
                  icon: Icons.child_care,
                  title: 'Delivery Records',
                  description: 'Record birth details and track offspring information',
                ),
                const _FeatureItem(
                  icon: Icons.insights,
                  title: 'Breeding Insights',
                  description: 'Analyze breeding success rates and genetic improvements',
                ),
              ],
            ),

            // Milk Records Module
            _buildModuleSection(
              title: 'Milk Records',
              icon: Icons.local_drink,
              color: AppColors.milkHeader,
              features: [
                const _FeatureItem(
                  icon: Icons.add_box,
                  title: 'Daily Milk Recording',
                  description: 'Record daily milk production for each cow with session details',
                ),
                const _FeatureItem(
                  icon: Icons.point_of_sale,
                  title: 'Milk Sales Tracking',
                  description: 'Track milk sales, pricing, and customer information',
                ),
                const _FeatureItem(
                  icon: Icons.bar_chart,
                  title: 'Production Analytics',
                  description: 'Analyze milk production trends and identify top performers',
                ),
                const _FeatureItem(
                  icon: Icons.inventory,
                  title: 'Inventory Management',
                  description: 'Monitor available milk stock and sales history',
                ),
              ],
            ),

            // Weight Tracking Module
            _buildModuleSection(
              title: 'Weight Tracking',
              icon: Icons.monitor_weight,
              color: const Color(0xFF2E7D32),
              features: [
                const _FeatureItem(
                  icon: Icons.scale,
                  title: 'Weight Recording',
                  description: 'Record regular weight measurements for growth monitoring',
                ),
                const _FeatureItem(
                  icon: Icons.trending_up,
                  title: 'Growth Analysis',
                  description: 'Track weight gain patterns and growth rates over time',
                ),
                const _FeatureItem(
                  icon: Icons.compare_arrows,
                  title: 'Weight Comparisons',
                  description: 'Compare weights across different cattle and time periods',
                ),
                const _FeatureItem(
                  icon: Icons.assessment,
                  title: 'Performance Reports',
                  description: 'Generate detailed weight and growth performance reports',
                ),
              ],
            ),

            // Events Management Module
            _buildModuleSection(
              title: 'Events Management',
              icon: Icons.event_note,
              color: AppColors.eventsHeader,
              features: [
                const _FeatureItem(
                  icon: Icons.add_task,
                  title: 'Event Scheduling',
                  description: 'Schedule and track important farm events and activities',
                ),
                const _FeatureItem(
                  icon: Icons.notifications_active,
                  title: 'Event Reminders',
                  description: 'Get timely notifications for upcoming events and tasks',
                ),
                const _FeatureItem(
                  icon: Icons.category,
                  title: 'Event Categories',
                  description: 'Organize events by type: health, breeding, feeding, etc.',
                ),
                const _FeatureItem(
                  icon: Icons.history,
                  title: 'Event History',
                  description: 'Maintain complete history of all farm activities',
                ),
              ],
            ),

            // Transactions Module
            _buildModuleSection(
              title: 'Financial Transactions',
              icon: Icons.account_balance_wallet,
              color: AppColors.transactionHeader,
              features: [
                const _FeatureItem(
                  icon: Icons.add_circle,
                  title: 'Income & Expense Tracking',
                  description: 'Record all farm-related income and expenses with categories',
                ),
                const _FeatureItem(
                  icon: Icons.receipt_long,
                  title: 'Transaction History',
                  description: 'Maintain detailed records of all financial transactions',
                ),
                const _FeatureItem(
                  icon: Icons.pie_chart,
                  title: 'Financial Analytics',
                  description: 'Analyze profitability, expenses, and financial trends',
                ),
                const _FeatureItem(
                  icon: Icons.calculate,
                  title: 'Profit/Loss Reports',
                  description: 'Generate comprehensive financial reports and summaries',
                ),
              ],
            ),

            // Farm Setup Module
            _buildModuleSection(
              title: 'Farm Setup & Configuration',
              icon: Icons.settings,
              color: Colors.indigo,
              features: [
                const _FeatureItem(
                  icon: Icons.business,
                  title: 'Farm Information',
                  description: 'Configure farm details, location, and basic information',
                ),
                const _FeatureItem(
                  icon: Icons.category,
                  title: 'Custom Categories',
                  description: 'Set up custom categories for health, expenses, and events',
                ),
                const _FeatureItem(
                  icon: Icons.pets,
                  title: 'Breed Management',
                  description: 'Manage cattle breeds and animal types for your farm',
                ),
                const _FeatureItem(
                  icon: Icons.backup,
                  title: 'Data Backup',
                  description: 'Secure your farm data with automated backup solutions',
                ),
              ],
            ),

            // Settings & Profile Module
            _buildModuleSection(
              title: 'Settings & Profile',
              icon: Icons.person_outline,
              color: Colors.teal,
              features: [
                const _FeatureItem(
                  icon: Icons.account_circle,
                  title: 'User Profile',
                  description: 'Manage your personal profile and account information',
                ),
                const _FeatureItem(
                  icon: Icons.language,
                  title: 'Language Settings',
                  description: 'Choose your preferred language for the application',
                ),
                const _FeatureItem(
                  icon: Icons.notifications,
                  title: 'Notification Preferences',
                  description: 'Customize alerts and notification settings',
                ),
                const _FeatureItem(
                  icon: Icons.security,
                  title: 'Privacy & Security',
                  description: 'Control data privacy and security settings',
                ),
              ],
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomCTA(context),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary.withValues(alpha: 0.1), AppColors.primary.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.2),
            offset: const Offset(0, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.star,
                  color: AppColors.primary,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Complete Farm Management',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Everything you need to manage your cattle farm efficiently',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.primary.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModuleSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<_FeatureItem> features,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Module Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
              ],
            ),
          ),

          // Features List
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: features.map((feature) => _buildFeatureItem(feature, color)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(_FeatureItem feature, Color moduleColor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: moduleColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              feature.icon,
              color: moduleColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  feature.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomCTA(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'Start Exploring Demo',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Try all features with sample data',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FeatureItem {
  final IconData icon;
  final String title;
  final String description;

  const _FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
  });
}