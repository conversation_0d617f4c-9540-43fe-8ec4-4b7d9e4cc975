import 'package:isar/isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../core/base/base_repository.dart';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';

/// Pure reactive repository for Notifications module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class NotificationRepository extends BaseRepository<NotificationIsar> {

  // Public constructor with explicit dependency injection
  NotificationRepository(IsarService isarService) : super(isarService);

  //=== REACTIVE NOTIFICATIONS STREAMS ===//

  /// Watches all notifications with reactive updates
  /// The controller is responsible for all filtering and sorting
  @override
  Stream<List<NotificationIsar>> watchAll() {
    return isar.notificationIsars.where().watch(fireImmediately: true);
  }

  /// Watches all notifications with reactive updates (alias for backward compatibility)
  Stream<List<NotificationIsar>> watchAllNotifications() {
    return watchAll();
  }

  //=== NOTIFICATIONS CRUD ===//

  /// Save (add or update) a notification using Isar's native upsert
  @override
  Future<void> save(NotificationIsar notification) async {
    await isar.writeTxn(() async {
      await isar.notificationIsars.put(notification);
    });
  }

  /// Save (add or update) a notification using Isar's native upsert (alias for backward compatibility)
  Future<void> saveNotification(NotificationIsar notification) async {
    await save(notification);
  }

  /// Delete a notification by its Isar ID
  @override
  Future<void> delete(int id) async {
    await isar.writeTxn(() async {
      await isar.notificationIsars.delete(id);
    });
  }

  /// Delete a notification by its Isar ID (alias for backward compatibility)
  Future<void> deleteNotification(int id) async {
    await delete(id);
  }

  /// Mark notification as read by business ID
  Future<void> markAsRead(String businessId) async {
    final notification = await isar.notificationIsars.getByBusinessId(businessId);
    if (notification != null) {
      notification.status = NotificationStatus.read;
      notification.readAt = DateTime.now();
      notification.updatedAt = DateTime.now();
      await save(notification);
    }
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all notifications (for analytics and reports)
  /// Returns a Future<List> for one-time data fetching
  @override
  Future<List<NotificationIsar>> getAll() async {
    return await isar.notificationIsars.where().findAll();
  }

  /// Get all notifications (for analytics and reports) - alias for backward compatibility
  Future<List<NotificationIsar>> getAllNotifications() async {
    return await getAll();
  }

  /// Get a notification by its business ID
  Future<NotificationIsar?> getNotificationById(String businessId) async {
    return await _isar.notificationIsars.getByBusinessId(businessId);
  }

  /// Get notifications with filtering (for controller use)
  Future<List<NotificationIsar>> getNotifications({NotificationFilter? filter}) async {
    if (filter == null) {
      return await getAllNotifications();
    }

    var query = isar.notificationIsars.filter();

    if (filter.status != null) {
      query = query.statusEqualTo(filter.status!);
    }

    if (filter.category != null && filter.category!.isNotEmpty) {
      query = query.categoryEqualTo(filter.category!);
    }

    if (filter.cattleId != null && filter.cattleId!.isNotEmpty) {
      query = query.cattleIdEqualTo(filter.cattleId!);
    }

    if (filter.fromDate != null && filter.toDate != null) {
      query = query.createdAtBetween(filter.fromDate!, filter.toDate!);
    }

    return await query.findAll();
  }

  /// Get notifications by status for analytics
  Future<List<NotificationIsar>> getNotificationsByStatus(NotificationStatus status) async {
    return await isar.notificationIsars
        .filter()
        .statusEqualTo(status)
        .findAll();
  }

  /// Get notifications by category for analytics
  Future<List<NotificationIsar>> getNotificationsByCategory(String category) async {
    return await isar.notificationIsars
        .filter()
        .categoryEqualTo(category)
        .findAll();
  }

  /// Get notifications by date range for analytics
  @override
  Future<List<NotificationIsar>> getByDateRange(DateTime startDate, DateTime endDate) async {
    return await isar.notificationIsars
        .filter()
        .createdAtBetween(startDate, endDate)
        .findAll();
  }

  /// Get notifications by date range for analytics (alias for backward compatibility)
  Future<List<NotificationIsar>> getNotificationsByDateRange(DateTime startDate, DateTime endDate) async {
    return await getByDateRange(startDate, endDate);
  }

  /// Get unread count for analytics
  Future<int> getUnreadCount() async {
    return await isar.notificationIsars
        .filter()
        .statusEqualTo(NotificationStatus.unread)
        .count();
  }

  /// Delete notification by business ID
  @override
  Future<void> deleteByBusinessId(String businessId) async {
    final notification = await isar.notificationIsars.getByBusinessId(businessId);
    if (notification != null) {
      await delete(notification.id);
    }
  }

  /// Delete notification by business ID (alias for backward compatibility)
  Future<void> deleteNotificationByBusinessId(String businessId) async {
    await deleteByBusinessId(businessId);
  }
}
