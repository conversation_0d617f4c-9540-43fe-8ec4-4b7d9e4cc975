import 'package:flutter/material.dart';
import '../controllers/weight_details_controller.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../widgets/universal_info_card.dart';

/// Data class for analytics info cards to provide type safety
class AnalyticsCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String? insight;

  const AnalyticsCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.insight,
  });
}


class WeightDetailsAnalyticsTab extends StatefulWidget {
  final WeightDetailsController controller;

  const WeightDetailsAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<WeightDetailsAnalyticsTab> createState() => _WeightDetailsAnalyticsTabState();
}

class _WeightDetailsAnalyticsTabState extends State<WeightDetailsAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Handle loading state
        if (widget.controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // Handle error state
        if (widget.controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => widget.controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        // Handle cattle data missing
        if (widget.controller.cattle == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No Cattle Data',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Cattle information not available',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        // Handle no weight records
        if (widget.controller.weightRecords.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.monitor_weight,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No Weight Analytics',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Add weight records for ${widget.controller.cattle!.name} to view detailed analytics and growth trends.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(kPaddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Weight Performance Metrics
              _buildWeightPerformance(widget.controller),
              const SizedBox(height: kSpacingLarge),

              // Growth Analytics
              _buildGrowthAnalytics(widget.controller),
              const SizedBox(height: kSpacingLarge),

              // Health Analytics
              _buildHealthAnalytics(widget.controller),
            ],
          ),
        );
      },
    );
  }

  /// Build weight performance metrics section
  Widget _buildWeightPerformance(WeightDetailsController controller) {
    final cardData = _buildWeightPerformanceCards(controller);
    return _buildAnalyticsSection(
      title: 'Weight Performance',
      subtitle: 'Key weight metrics and growth indicators',
      icon: Icons.monitor_weight,
      headerColor: AppColors.weightHeader,
      cardData: cardData,
    );
  }

  /// Build growth analytics section
  Widget _buildGrowthAnalytics(WeightDetailsController controller) {
    final cardData = _buildGrowthAnalyticsCards(controller);
    return _buildAnalyticsSection(
      title: 'Growth Analytics',
      subtitle: 'Growth trends and development patterns',
      icon: Icons.trending_up,
      headerColor: AppColors.weightHeader,
      cardData: cardData,
    );
  }

  /// Build health analytics section
  Widget _buildHealthAnalytics(WeightDetailsController controller) {
    final cardData = _buildHealthAnalyticsCards(controller);
    return _buildAnalyticsSection(
      title: 'Health Indicators',
      subtitle: 'Weight-based health and condition metrics',
      icon: Icons.health_and_safety,
      headerColor: AppColors.weightHeader,
      cardData: cardData,
    );
  }

  /// Build weight performance cards using actual controller data
  List<AnalyticsCardData> _buildWeightPerformanceCards(WeightDetailsController controller) {
    return [
      AnalyticsCardData(
        title: 'Total Records',
        value: controller.totalWeightRecords.toString(),
        subtitle: 'Weight measurements',
        icon: Icons.scale,
        color: AppColors.weightKpiColors[0],
        insight: 'Total weight records tracked for this cattle',
      ),
      AnalyticsCardData(
        title: 'Current Weight',
        value: controller.currentWeight > 0 ? '${controller.currentWeight.toStringAsFixed(1)} kg' : 'N/A',
        subtitle: 'Latest measurement',
        icon: Icons.monitor_weight,
        color: AppColors.weightKpiColors[1],
        insight: 'Most recent weight recorded',
      ),
      AnalyticsCardData(
        title: 'Average Weight',
        value: controller.averageWeight > 0 ? '${controller.averageWeight.toStringAsFixed(1)} kg' : 'N/A',
        subtitle: 'Overall average',
        icon: Icons.balance,
        color: AppColors.weightKpiColors[2],
        insight: 'Average across all measurements',
      ),
      AnalyticsCardData(
        title: 'Weight Gain',
        value: controller.weightGain != 0 ? '${controller.weightGain >= 0 ? '+' : ''}${controller.weightGain.toStringAsFixed(1)} kg' : 'N/A',
        subtitle: 'Total change',
        icon: controller.weightGain >= 0 ? Icons.trending_up : Icons.trending_down,
        color: controller.weightGain >= 0 ? Colors.green : Colors.red,
        insight: 'Total weight change since first record',
      ),
    ];
  }

  /// Build growth analytics cards using WeightInsightsData
  List<AnalyticsCardData> _buildGrowthAnalyticsCards(WeightDetailsController controller) {
    final analytics = controller.analytics;

    return [
      AnalyticsCardData(
        title: 'Daily Gain',
        value: analytics.performance.averageDailyGain > 0 ? '${analytics.performance.averageDailyGain.toStringAsFixed(2)} kg/day' : 'N/A',
        subtitle: 'Average daily gain',
        icon: Icons.trending_up,
        color: AppColors.weightKpiColors[0],
        insight: analytics.performance.growthTrendDescription,
      ),
      AnalyticsCardData(
        title: 'Growth Trend',
        value: analytics.performance.growthTrend,
        subtitle: 'Current pattern',
        icon: _getGrowthTrendIcon(analytics.performance.growthTrend),
        color: _getGrowthTrendColor(analytics.performance.growthTrend),
        insight: analytics.performance.growthTrendDescription,
      ),
      AnalyticsCardData(
        title: 'Monthly Change',
        value: analytics.trends.monthlyChange != 0 ? '${analytics.trends.monthlyChange >= 0 ? '+' : ''}${analytics.trends.monthlyChange.toStringAsFixed(1)} kg' : 'N/A',
        subtitle: 'Last month change',
        icon: analytics.trends.monthlyChange >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
        color: analytics.trends.monthlyChange >= 0 ? Colors.green : Colors.red,
        insight: 'Weight change in the last month',
      ),
      AnalyticsCardData(
        title: 'Predicted Next',
        value: analytics.trends.predictedNextMonth > 0 ? '${analytics.trends.predictedNextMonth.toStringAsFixed(1)} kg' : 'N/A',
        subtitle: 'Next month prediction',
        icon: Icons.psychology,
        color: AppColors.weightKpiColors[3],
        insight: 'AI-predicted weight for next month',
      ),
    ];
  }

  /// Build health analytics cards using WeightInsightsData
  List<AnalyticsCardData> _buildHealthAnalyticsCards(WeightDetailsController controller) {
    final analytics = controller.analytics;

    return [
      AnalyticsCardData(
        title: 'Body Condition',
        value: analytics.health.averageBodyCondition > 0 ? analytics.health.averageBodyCondition.toStringAsFixed(1) : 'N/A',
        subtitle: 'Average score',
        icon: Icons.fitness_center,
        color: AppColors.weightKpiColors[0],
        insight: 'Average body condition score across all records',
      ),
      AnalyticsCardData(
        title: 'Consistency',
        value: analytics.health.consistencyRating,
        subtitle: 'Measurement reliability',
        icon: Icons.verified,
        color: _getConsistencyColor(analytics.health.consistencyRating),
        insight: analytics.health.consistencyDescription,
      ),
      AnalyticsCardData(
        title: 'Health Alerts',
        value: analytics.health.healthAlerts.toString(),
        subtitle: 'Active alerts',
        icon: Icons.warning,
        color: analytics.health.healthAlerts > 0 ? Colors.red : Colors.green,
        insight: analytics.health.healthAlerts > 0 ? 'Health concerns detected' : 'No health concerns',
      ),
      AnalyticsCardData(
        title: 'Seasonal Pattern',
        value: analytics.trends.seasonalPattern,
        subtitle: 'Weight pattern',
        icon: Icons.calendar_view_month,
        color: AppColors.weightKpiColors[3],
        insight: 'Seasonal weight change pattern',
      ),
    ];
  }

  /// Build analytics section with consistent styling following cattle module pattern
  Widget _buildAnalyticsSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<AnalyticsCardData> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        ResponsiveGrid.cards(
          children: cardData.map((data) => _buildMetricCard(data)).toList(),
        ),
      ],
    );
  }

  /// Build individual metric card from typed data
  Widget _buildMetricCard(AnalyticsCardData data) {
    return UniversalInfoCard(
      title: data.title,
      value: data.value,
      subtitle: data.subtitle,
      icon: data.icon,
      color: data.color,
      insight: data.insight,
    );
  }

  // Helper methods for analytics data formatting
  IconData _getGrowthTrendIcon(String trend) {
    switch (trend.toLowerCase()) {
      case 'increasing':
        return Icons.trending_up;
      case 'decreasing':
        return Icons.trending_down;
      case 'stable':
        return Icons.trending_flat;
      default:
        return Icons.help_outline;
    }
  }

  Color _getGrowthTrendColor(String trend) {
    switch (trend.toLowerCase()) {
      case 'increasing':
        return Colors.green;
      case 'decreasing':
        return Colors.red;
      case 'stable':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Color _getConsistencyColor(String rating) {
    switch (rating.toLowerCase()) {
      case 'excellent':
        return Colors.green;
      case 'good':
        return Colors.blue;
      case 'fair':
        return Colors.orange;
      case 'poor':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
