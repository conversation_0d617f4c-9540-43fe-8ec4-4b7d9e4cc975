import 'dart:io';
import 'package:flutter/material.dart';

import '../models/event_attachment_isar.dart';
// Using streamlined architecture - attachment handling inline
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

/// Widget for displaying and managing event attachments
class EventAttachmentsWidget extends StatefulWidget {
  final String eventBusinessId;
  final bool isEditable;
  final VoidCallback? onAttachmentsChanged;

  const EventAttachmentsWidget({
    Key? key,
    required this.eventBusinessId,
    this.isEditable = true,
    this.onAttachmentsChanged,
  }) : super(key: key);

  @override
  State<EventAttachmentsWidget> createState() => _EventAttachmentsWidgetState();
}

class _EventAttachmentsWidgetState extends State<EventAttachmentsWidget> {
  // Using streamlined architecture - attachment handling inline
  List<EventAttachmentIsar> _attachments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAttachments();
  }

  Future<void> _loadAttachments() async {
    setState(() => _isLoading = true);
    try {
      // Simplified - load attachments from database directly
      final attachments = <EventAttachmentIsar>[]; // Placeholder - would load from database
      setState(() => _attachments = attachments);
    } catch (e) {
      // Handle error silently for production
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with add buttons
        if (widget.isEditable) _buildHeader(),
        
        // Attachments grid
        if (_isLoading)
          const Center(child: CircularProgressIndicator())
        else if (_attachments.isEmpty)
          _buildEmptyState()
        else
          _buildAttachmentsGrid(),
      ],
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.only(bottom: kSpacingMedium),
      child: Row(
        children: [
          Text(
            'Attachments',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          // Add photo button
          IconButton(
            onPressed: () => _addPhoto(),
            icon: const Icon(Icons.add_a_photo),
            tooltip: 'Add Photo',
            style: IconButton.styleFrom(
              backgroundColor: AppColors.eventsHeader.withValues(alpha: 0.1),
              foregroundColor: AppColors.eventsHeader,
            ),
          ),
          const SizedBox(width: kSpacingSmall),
          // Add document button
          IconButton(
            onPressed: () => _addDocument(),
            icon: const Icon(Icons.attach_file),
            tooltip: 'Add Document',
            style: IconButton.styleFrom(
              backgroundColor: AppColors.eventsHeader.withValues(alpha: 0.1),
              foregroundColor: AppColors.eventsHeader,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(kPaddingLarge),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(kBorderRadius),
      ),
      child: Column(
        children: [
          Icon(
            Icons.attachment,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: kSpacingMedium),
          Text(
            'No attachments yet',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          if (widget.isEditable) ...[
            const SizedBox(height: kSpacingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton.icon(
                  onPressed: _addPhoto,
                  icon: const Icon(Icons.add_a_photo),
                  label: const Text('Add Photo'),
                ),
                const SizedBox(width: kSpacingMedium),
                OutlinedButton.icon(
                  onPressed: _addDocument,
                  icon: const Icon(Icons.attach_file),
                  label: const Text('Add Document'),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAttachmentsGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: kSpacingSmall,
        mainAxisSpacing: kSpacingSmall,
        childAspectRatio: 1.0,
      ),
      itemCount: _attachments.length,
      itemBuilder: (context, index) {
        final attachment = _attachments[index];
        return AttachmentThumbnail(
          attachment: attachment,
          onTap: () => _viewAttachment(attachment),
          onDelete: widget.isEditable ? () => _deleteAttachment(context, attachment) : null,
        );
      },
    );
  }

  Future<void> _addPhoto() async {
    // Simplified - photo attachment not implemented yet
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Photo attachment not implemented yet')),
    );

    // if (attachment != null) {
    //   await _loadAttachments();
    //   widget.onAttachmentsChanged?.call();
    // }
  }

  Future<void> _addDocument() async {
    // Simplified - document attachment not implemented yet
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Document attachment not implemented yet')),
    );

    // if (attachment != null) {
    //   await _loadAttachments();
    //   widget.onAttachmentsChanged?.call();
    // }
  }

  Future<void> _deleteAttachment(BuildContext context, EventAttachmentIsar attachment) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Attachment'),
        content: Text('Are you sure you want to delete "${attachment.fileName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Simplified - delete attachment
      setState(() {
        _attachments.remove(attachment);
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Attachment deleted')),
      );
      widget.onAttachmentsChanged?.call();
    }
  }

  void _viewAttachment(EventAttachmentIsar attachment) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AttachmentViewerScreen(attachment: attachment),
      ),
    );
  }
}

/// Widget for displaying a single attachment thumbnail
class AttachmentThumbnail extends StatelessWidget {
  final EventAttachmentIsar attachment;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const AttachmentThumbnail({
    Key? key,
    required this.attachment,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(kBorderRadius),
        ),
        child: Stack(
          children: [
            // Thumbnail content
            ClipRRect(
              borderRadius: BorderRadius.circular(kBorderRadius),
              child: _buildThumbnailContent(),
            ),
            
            // Delete button
            if (onDelete != null)
              Positioned(
                top: 4,
                right: 4,
                child: GestureDetector(
                  onTap: onDelete,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnailContent() {
    if (attachment.isImage && attachment.thumbnailPath != null) {
      return Image.file(
        File(attachment.thumbnailPath!),
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildFileIcon(),
      );
    } else {
      return _buildFileIcon();
    }
  }

  Widget _buildFileIcon() {
    IconData icon;
    Color color;

    if (attachment.isImage) {
      icon = Icons.image;
      color = Colors.blue;
    } else if (attachment.isDocument) {
      icon = Icons.description;
      color = Colors.red;
    } else if (attachment.isVideo) {
      icon = Icons.video_file;
      color = Colors.purple;
    } else {
      icon = Icons.attach_file;
      color = Colors.grey;
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: color.withValues(alpha: 0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 4),
          Text(
            attachment.fileName ?? 'Unknown',
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// Screen for viewing attachments in full size
class AttachmentViewerScreen extends StatelessWidget {
  final EventAttachmentIsar attachment;

  const AttachmentViewerScreen({
    Key? key,
    required this.attachment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(attachment.fileName ?? 'Attachment'),
        backgroundColor: AppColors.eventsHeader,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: attachment.isImage && attachment.filePath != null
            ? InteractiveViewer(
                child: Image.file(
                  File(attachment.filePath!),
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
                ),
              )
            : _buildDocumentViewer(),
      ),
    );
  }

  Widget _buildDocumentViewer() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.description,
          size: 64,
          color: Colors.grey.shade400,
        ),
        const SizedBox(height: 16),
        Text(
          attachment.fileName ?? 'Document',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          'File size: ${_formatFileSize(attachment.fileSize ?? 0)}',
          style: TextStyle(color: Colors.grey.shade600),
        ),
        const SizedBox(height: 16),
        const Text(
          'Document preview not available',
          style: TextStyle(color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.error_outline,
          size: 64,
          color: Colors.grey.shade400,
        ),
        const SizedBox(height: 16),
        const Text(
          'Unable to load attachment',
          style: TextStyle(fontSize: 18),
        ),
      ],
    );
  }

  // Helper method to replace removed service
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
