import 'package:flutter/material.dart';
import '../services/health_analytics_service.dart';

/// Service for generating health management insights and recommendations
/// Extracted from UI layer for better testability and reusability
/// Uses dependency injection pattern for architectural consistency
class HealthInsightsService {
  // Business Rule Thresholds - Centralized for easy maintenance and clarity
  // These constants define the business logic for health management insights

  /// Health score below this threshold is considered "poor" requiring attention
  static const double _poorHealthScoreThreshold = 60.0;

  /// Health score above this threshold is considered "excellent"
  static const double _excellentHealthScoreThreshold = 85.0;

  /// Number of health records below this threshold is considered "insufficient data"
  static const int _minHealthRecordsThreshold = 3;

  /// Number of overdue vaccinations above this threshold is considered "high"
  static const int _highOverdueVaccinationsThreshold = 3;

  /// Number of active treatments above this threshold is considered "concerning"
  static const int _concerningActiveTreatmentsThreshold = 5;

  /// Generate comprehensive insights for health management
  /// This is the main entry point for the insights tab
  List<HealthInsight> generateInsights(HealthAnalyticsResult analytics) {
    final insights = <HealthInsight>[];

    // Health Score Analysis
    final healthScoreInsight = _analyzeHealthScore(analytics);
    if (healthScoreInsight != null) {
      insights.add(healthScoreInsight);
    }

    // Vaccination Management Analysis
    final vaccinationInsight = _analyzeVaccinations(analytics);
    if (vaccinationInsight != null) {
      insights.add(vaccinationInsight);
    }

    // Treatment Analysis
    final treatmentInsight = _analyzeTreatments(analytics);
    if (treatmentInsight != null) {
      insights.add(treatmentInsight);
    }

    // Health Records Analysis
    final recordsInsight = _analyzeHealthRecords(analytics);
    if (recordsInsight != null) {
      insights.add(recordsInsight);
    }

    // General Best Practices (always shown)
    insights.add(_getHealthBestPractices());

    return insights;
  }

  /// Analyze health score and provide insights
  HealthInsight? _analyzeHealthScore(HealthAnalyticsResult analytics) {
    if (analytics.totalHealthRecords < _minHealthRecordsThreshold) {
      return HealthInsight(
        title: 'Insufficient Health Data',
        description: 'You need more health records to analyze overall health scores effectively.',
        icon: Icons.info_outline,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Record regular health checkups for all cattle',
          'Track vaccination schedules consistently',
          'Document any treatments or medications',
          'Monitor for signs of illness or injury',
        ],
      );
    }

    final healthScore = analytics.averageHealthScore;

    if (healthScore < _poorHealthScoreThreshold) {
      return HealthInsight(
        title: 'Poor Health Score Alert',
        description: 'Your herd\'s average health score of ${healthScore.toStringAsFixed(1)} indicates health concerns.',
        icon: Icons.warning,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Schedule immediate veterinary consultation',
          'Review current treatment protocols',
          'Improve nutrition and housing conditions',
          'Increase health monitoring frequency',
          'Consider preventive health measures',
        ],
      );
    } else if (healthScore > _excellentHealthScoreThreshold) {
      return HealthInsight(
        title: 'Excellent Health Management',
        description: 'Your herd\'s average health score of ${healthScore.toStringAsFixed(1)} is excellent!',
        icon: Icons.star,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current health management practices',
          'Maintain regular veterinary checkups',
          'Share successful practices with other farmers',
          'Consider expanding preventive care programs',
        ],
      );
    }

    return null; // Average health score - no specific insight needed
  }

  /// Analyze vaccination management and provide insights
  HealthInsight? _analyzeVaccinations(HealthAnalyticsResult analytics) {
    if (analytics.overdueVaccinations == 0) {
      return HealthInsight(
        title: 'Vaccination Schedule Up-to-Date',
        description: 'Great job! All vaccinations are current with no overdue vaccines.',
        icon: Icons.check_circle,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue maintaining vaccination schedules',
          'Set up reminders for upcoming vaccinations',
          'Keep vaccination records organized',
        ],
      );
    } else if (analytics.overdueVaccinations > _highOverdueVaccinationsThreshold) {
      return HealthInsight(
        title: 'High Number of Overdue Vaccinations',
        description: 'You have $analytics.overdueVaccinations overdue vaccinations requiring immediate attention.',
        icon: Icons.schedule,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Schedule overdue vaccinations immediately',
          'Contact veterinarian for vaccination plan',
          'Set up automated vaccination reminders',
          'Review vaccination protocols and schedules',
          'Consider batch vaccination for efficiency',
        ],
      );
    } else {
      return HealthInsight(
        title: 'Some Overdue Vaccinations',
        description: 'You have $analytics.overdueVaccinations overdue vaccinations that need attention.',
        icon: Icons.schedule,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Schedule overdue vaccinations soon',
          'Review vaccination calendar',
          'Set up better reminder systems',
          'Ensure adequate vaccine supplies',
        ],
      );
    }
  }

  /// Analyze treatment patterns and provide insights
  HealthInsight? _analyzeTreatments(HealthAnalyticsResult analytics) {
    if (analytics.activeRecords > _concerningActiveTreatmentsThreshold) {
      return HealthInsight(
        title: 'High Number of Active Treatments',
        description: 'You have $analytics.activeRecords active treatments, which may indicate health issues.',
        icon: Icons.medical_services,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Review treatment effectiveness',
          'Consult veterinarian about treatment patterns',
          'Investigate potential causes of health issues',
          'Consider preventive health measures',
          'Improve housing and nutrition conditions',
        ],
      );
    }

    final treatmentSuccessRate = analytics.treatmentSuccessRate;
    if (treatmentSuccessRate < 0.7 && analytics.treatmentRecords > 5) {
      return HealthInsight(
        title: 'Low Treatment Success Rate',
        description: 'Your treatment success rate of ${(treatmentSuccessRate * 100).toStringAsFixed(1)}% could be improved.',
        icon: Icons.trending_down,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Review treatment protocols with veterinarian',
          'Ensure proper medication administration',
          'Improve early disease detection',
          'Consider alternative treatment approaches',
          'Monitor treatment compliance closely',
        ],
      );
    }

    return null;
  }

  /// Analyze health records patterns and provide insights
  HealthInsight? _analyzeHealthRecords(HealthAnalyticsResult analytics) {
    if (analytics.totalHealthRecords == 0) {
      return HealthInsight(
        title: 'Start Health Record Tracking',
        description: 'You haven\'t added any health records yet. Start tracking your cattle\'s health for optimal care.',
        icon: Icons.medical_services,
        color: Colors.blue,
        priority: InsightPriority.high,
        recommendations: [
          'Record regular health checkups',
          'Track vaccination schedules',
          'Document any treatments or medications',
          'Monitor for signs of illness or injury',
          'Establish baseline health metrics',
        ],
      );
    }

    final chronicConditions = analytics.chronicConditions;
    if (chronicConditions.isNotEmpty) {
      return HealthInsight(
        title: 'Chronic Conditions Detected',
        description: 'You have $chronicConditions.length chronic conditions requiring ongoing management.',
        icon: Icons.monitor_heart,
        color: Colors.purple,
        priority: InsightPriority.medium,
        recommendations: [
          'Develop long-term management plans',
          'Schedule regular monitoring for chronic conditions',
          'Work with veterinarian on treatment protocols',
          'Consider genetic factors in breeding decisions',
          'Maintain detailed condition tracking',
        ],
      );
    }

    return null;
  }

  /// Get general health management best practices
  HealthInsight _getHealthBestPractices() {
    return HealthInsight(
      title: 'Health Management Best Practices',
      description: 'Follow these best practices to maintain optimal cattle health and ensure regulatory compliance.',
      icon: Icons.health_and_safety,
      color: Colors.green,
      priority: InsightPriority.medium,
      recommendations: [
        'Schedule regular veterinary checkups',
        'Maintain up-to-date vaccination records',
        'Monitor cattle daily for health issues',
        'Keep detailed treatment and medication records',
        'Implement biosecurity measures',
        'Provide proper nutrition and clean water',
        'Maintain clean and comfortable housing',
        'Follow withdrawal periods for medications',
      ],
    );
  }
}

/// Data classes for health insights
class HealthInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  HealthInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
