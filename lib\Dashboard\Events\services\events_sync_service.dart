import 'dart:convert';
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';
import '../../../config/api_config.dart';
import '../../../services/database/isar_service.dart';
import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';

/// Events synchronization service for bidirectional API sync
/// 
/// This service handles:
/// - Uploading local events to the server
/// - Downloading server events to local database
/// - Conflict resolution based on timestamps
/// - Batch processing for performance
/// - Error handling and retry logic
class EventsSyncService {
  final IsarService _isarService = GetIt.instance<IsarService>();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();

  /// Sync events data bidirectionally
  Future<bool> syncData() async {
    try {
      if (!ApiConfig.isApiSyncAvailable) {
        return true;
      }

      final farm = await _farmSetupRepository.getCurrentFarm();
      if (farm == null || farm.farmBusinessId == null) {
        return false;
      }

      final isar = _isarService.isar;
      final farmId = farm.farmBusinessId!;

      // Get last sync timestamp
      final lastSync = await _getLastSyncTimestamp(isar);
      
      // 1. Upload local changes to server
      final uploadSuccess = await _uploadLocalChanges(isar, farmId, lastSync);
      if (!uploadSuccess) {
        return false;
      }

      // 2. Download server changes to local
      final downloadSuccess = await _downloadServerChanges(isar, farmId, lastSync);
      if (!downloadSuccess) {
        return false;
      }

      // 3. Update last sync timestamp
      await _updateLastSyncTimestamp(isar);

      return true;
    } catch (e) {
      developer.log('Events sync error: $e', error: e);
      return false;
    }
  }

  /// Upload local events to server
  Future<bool> _uploadLocalChanges(Isar isar, String farmId, DateTime? lastSync) async {
    try {
      // Get local events that need to be synced
      final localEvents = await isar.eventIsars
          .filter()
          .not()
          .businessIdIsNull()
          .and()
          .updatedAtGreaterThan(lastSync ?? DateTime.fromMillisecondsSinceEpoch(0))
          .findAll();

      if (localEvents.isEmpty) {
        return true;
      }

      // Convert events to JSON
      final eventsJson = localEvents.map((event) => _eventToJson(event)).toList();

      final response = await http.post(
        Uri.parse('${ApiConfig.apiBaseUrl}/api/events/batch-sync'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${ApiConfig.defaultHeaders['Authorization'] ?? ''}',
          'X-Farm-ID': farmId,
        },
        body: jsonEncode({'events': eventsJson}),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        
        // Update server IDs for newly created events
        if (responseData['events'] != null) {
          await _updateServerEventIds(isar, responseData['events']);
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      developer.log('Error uploading events: $e', error: e);
      return false;
    }
  }

  /// Download server events to local database
  Future<bool> _downloadServerChanges(Isar isar, String farmId, DateTime? lastSync) async {
    try {
      final params = {
        'farm_id': farmId,
        if (lastSync != null) 'updated_after': lastSync.toIso8601String(),
      };

      final response = await http.get(
        Uri.parse('${ApiConfig.apiBaseUrl}/api/events')
            .replace(queryParameters: params),
        headers: {
          'Authorization': 'Bearer ${ApiConfig.defaultHeaders['Authorization'] ?? ''}',
          'X-Farm-ID': farmId,
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        
        if (responseData['events'] != null) {
          final serverEvents = List<Map<String, dynamic>>.from(responseData['events']);
          await _processServerEvents(isar, serverEvents);
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      developer.log('Error downloading events: $e', error: e);
      return false;
    }
  }

  /// Process server events and update local database
  Future<void> _processServerEvents(Isar isar, List<Map<String, dynamic>> serverEvents) async {
    await isar.writeTxn(() async {
      for (final serverEvent in serverEvents) {
        final businessId = serverEvent['business_id'] as String?;
        if (businessId == null) continue;

        // Check if event exists locally
        final localEvent = await isar.eventIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (localEvent != null) {
          // Update existing event if server version is newer
          final serverUpdatedAt = DateTime.parse(serverEvent['updated_at']);
          if (serverUpdatedAt.isAfter(localEvent.updatedAt ?? DateTime.fromMillisecondsSinceEpoch(0))) {
            await _updateLocalEvent(isar, localEvent, serverEvent);
          }
        } else {
          // Create new local event
          await _createLocalEvent(isar, serverEvent);
        }
      }
    });
  }

  /// Update existing local event with server data
  Future<void> _updateLocalEvent(Isar isar, EventIsar localEvent, Map<String, dynamic> serverEvent) async {
    localEvent
      ..title = serverEvent['title'] ?? localEvent.title
      ..description = serverEvent['description']
      ..notes = serverEvent['notes']
      ..scheduledDate = serverEvent['scheduled_date'] != null 
          ? DateTime.parse(serverEvent['scheduled_date']) 
          : localEvent.scheduledDate
      ..completedDate = serverEvent['completed_date'] != null 
          ? DateTime.parse(serverEvent['completed_date']) 
          : localEvent.completedDate
      ..status = _parseEventStatus(serverEvent['status'])
      ..priority = _parseEventPriority(serverEvent['priority'])
      ..cattleTagId = serverEvent['cattle_tag_id']
      ..eventTypeId = serverEvent['event_type_id']
      ..category = _parseEventCategory(serverEvent['category'])
      ..location = serverEvent['location']
      ..estimatedCost = serverEvent['estimated_cost']?.toDouble()
      ..actualCost = serverEvent['actual_cost']?.toDouble()
      ..completedBy = serverEvent['completed_by']
      ..completionNotes = serverEvent['completion_notes']
      ..updatedAt = DateTime.parse(serverEvent['updated_at']);

    await isar.eventIsars.put(localEvent);
  }

  /// Create new local event from server data
  Future<void> _createLocalEvent(Isar isar, Map<String, dynamic> serverEvent) async {
    final newEvent = EventIsar()
      ..businessId = serverEvent['business_id']
      ..title = serverEvent['title']
      ..description = serverEvent['description']
      ..notes = serverEvent['notes']
      ..scheduledDate = serverEvent['scheduled_date'] != null 
          ? DateTime.parse(serverEvent['scheduled_date']) 
          : null
      ..completedDate = serverEvent['completed_date'] != null 
          ? DateTime.parse(serverEvent['completed_date']) 
          : null
      ..status = _parseEventStatus(serverEvent['status'])
      ..priority = _parseEventPriority(serverEvent['priority'])
      ..cattleTagId = serverEvent['cattle_tag_id']
      ..eventTypeId = serverEvent['event_type_id']
      ..category = _parseEventCategory(serverEvent['category'])
      ..location = serverEvent['location']
      ..estimatedCost = serverEvent['estimated_cost']?.toDouble()
      ..actualCost = serverEvent['actual_cost']?.toDouble()
      ..completedBy = serverEvent['completed_by']
      ..completionNotes = serverEvent['completion_notes']
      ..createdAt = DateTime.parse(serverEvent['created_at'])
      ..updatedAt = DateTime.parse(serverEvent['updated_at']);

    await isar.eventIsars.put(newEvent);
  }

  /// Update server event IDs for newly created events
  Future<void> _updateServerEventIds(Isar isar, List<dynamic> serverEvents) async {
    await isar.writeTxn(() async {
      for (final serverEvent in serverEvents) {
        final localBusinessId = serverEvent['local_business_id'];
        final serverBusinessId = serverEvent['business_id'];
        
        if (localBusinessId != null && serverBusinessId != null) {
          final localEvent = await isar.eventIsars
              .filter()
              .businessIdEqualTo(localBusinessId)
              .findFirst();
              
          if (localEvent != null) {
            localEvent.businessId = serverBusinessId;
            await isar.eventIsars.put(localEvent);
          }
        }
      }
    });
  }

  /// Get last sync timestamp
  Future<DateTime?> _getLastSyncTimestamp(Isar isar) async {
    // This would typically be stored in a sync metadata collection
    // For now, return the most recent updatedAt from events
    final latestEvent = await isar.eventIsars
        .where()
        .sortByUpdatedAtDesc()
        .findFirst();
    
    return latestEvent?.updatedAt;
  }

  /// Update last sync timestamp
  Future<void> _updateLastSyncTimestamp(Isar isar) async {
    // This would typically update a sync metadata collection
    // Implementation depends on your sync tracking strategy
  }

  /// Convert EventIsar to JSON for API
  Map<String, dynamic> _eventToJson(EventIsar event) {
    return {
      'business_id': event.businessId,
      'cattle_tag_id': event.cattleTagId,
      'event_type_id': event.eventTypeId,
      'title': event.title,
      'description': event.description,
      'notes': event.notes,
      'scheduled_date': event.scheduledDate?.toIso8601String(),
      'completed_date': event.completedDate?.toIso8601String(),
      'status': event.status.name,
      'priority': event.priority.name,
      'category': event.category.name,
      'location': event.location,
      'estimated_cost': event.estimatedCost,
      'actual_cost': event.actualCost,
      'completed_by': event.completedBy,
      'completion_notes': event.completionNotes,
      'created_at': event.createdAt?.toIso8601String(),
      'updated_at': event.updatedAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
    };
  }

  /// Parse event status from string
  EventStatus _parseEventStatus(String? status) {
    try {
      return EventStatus.values.firstWhere(
        (e) => e.name == status,
        orElse: () => EventStatus.scheduled,
      );
    } catch (_) {
      return EventStatus.scheduled;
    }
  }

  /// Parse event priority from string
  EventPriority _parseEventPriority(String? priority) {
    try {
      return EventPriority.values.firstWhere(
        (e) => e.name == priority,
        orElse: () => EventPriority.medium,
      );
    } catch (_) {
      return EventPriority.medium;
    }
  }

  /// Parse event category from string
  EventCategory _parseEventCategory(String? category) {
    try {
      return EventCategory.values.firstWhere(
        (e) => e.name == category,
        orElse: () => EventCategory.other,
      );
    } catch (_) {
      return EventCategory.other;
    }
  }
}
