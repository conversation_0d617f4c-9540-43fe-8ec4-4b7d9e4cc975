import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/breeding_controller.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';
import '../dialogs/pregnancy_form_dialog.dart';
import '../models/pregnancy_record_isar.dart';
import 'package:intl/intl.dart';

class PregnancyRecordsTab extends StatefulWidget {
  final BreedingController? controller; // Made optional to support Provider pattern

  const PregnancyRecordsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<PregnancyRecordsTab> createState() => _PregnancyRecordsTabState();
}

class _PregnancyRecordsTabState extends State<PregnancyRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  BreedingController get _controller => widget.controller ?? context.read<BreedingController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // Apply filters at database level for ultimate scalability
    _controller.applyFilters(_filterController.toBreedingFilterState());
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    // Check if we have data to display
    if (_controller.totalPregnancyRecords == 0) {
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    // Get pregnancy records data - now pre-filtered at database level
    final records = _controller.unfilteredPregnancyRecords;
    final allRecordsCount = _controller.totalPregnancyRecords; // This represents total before filtering

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.breeding,
          moduleName: 'pregnancy',
          sortFields: const [...SortField.commonFields, ...SortField.breedingFields],
          searchHint: 'Search pregnancy records by cattle, status, or notes...',
          totalCount: allRecordsCount,
          filteredCount: records.length,
        ),

        // Pregnancy Records List - data is already filtered at database level
        Expanded(
          child: records.isEmpty
              ? _buildEmptyState(allRecordsCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: records.length,
                    itemBuilder: (context, index) {
                      final record = records[index];
                      return _buildPregnancyCard(record);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  /// Consolidated empty state builder - single source of truth for all empty states
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    const tabColor = AppColors.breedingHeader;

    if (isCompletelyEmpty) {
      // No pregnancy records exist - show call-to-action to add first record
      return UniversalTabEmptyState.forTab(
        title: 'No Pregnancy Records',
        message: 'Add your first pregnancy record to start tracking pregnancies.',
        tabColor: tabColor,
        tabIndex: 2, // Pregnancy tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddPregnancyDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Pregnancy records exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Pregnancy Records',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 2, // Pregnancy tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildPregnancyCard(PregnancyRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleId);

    // Format dates
    String startDateText = record.startDate != null 
        ? DateFormat('MMM dd, yyyy').format(record.startDate!)
        : 'Unknown date';
    
    String statusText = record.status ?? 'Unknown';
    
    // Format row 1: Start date + status
    // Format row 2: Cattle name + expected date
    String cattleName = cattle?.name ?? 'Unknown Cattle';
    if (cattle?.tagId != null && cattle!.tagId!.isNotEmpty) {
      cattleName += ' (${cattle.tagId!.toUpperCase()})';
    }
    
    String expectedDateText = record.expectedCalvingDate != null
        ? 'Due: ${DateFormat('MMM dd, yyyy').format(record.expectedCalvingDate!)}'
        : 'Due date unknown';

    return UniversalRecordCard(
      row1Left: startDateText,
      row1Right: statusText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: _getStatusIcon(record.status),
      row2Left: cattleName,
      row2Right: expectedDateText,
      row2LeftIcon: Icons.pets,
      row2RightIcon: Icons.event_available,
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.breedingHeader,
      onTap: () => _navigateToPregnancyDetail(record),
      onEdit: () => _showEditPregnancyDialog(record),
      onDelete: () => _showDeleteConfirmation(record),
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return Icons.check_circle;
      case 'completed':
        return Icons.child_care;
      case 'abortion':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  void _navigateToPregnancyDetail(PregnancyRecordIsar record) {
    // TODO: Implement pregnancy detail navigation when pregnancy details screen is available
    // For now, show a placeholder message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Pregnancy details screen coming soon')),
    );
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Consolidated dialog helper for both add and edit operations
  void _showPregnancyRecordFormDialog([PregnancyRecordIsar? record]) {
    final isEditing = record != null;

    // Real-time check - no artificial delays
    if (_controller.state == ControllerState.loading) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Loading cattle data...'),
          duration: Duration(milliseconds: 800), // Short, real-time feedback
        ),
      );
      return;
    }

    if (_controller.femaleCattleList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No active female cattle available for pregnancy records.'),
          duration: Duration(milliseconds: 1200), // Quick feedback
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => PregnancyFormDialog(
        record: record,
        preloadedCattle: _controller.femaleCattleList,
        preloadedAnimalTypes: _controller.animalTypesMap,
        onSave: (recordData) async {
          if (isEditing) {
            await _controller.updatePregnancyRecord(recordData);
          } else {
            await _controller.addPregnancyRecord(recordData);
          }
        },
      ),
    );
  }

  /// Show dialog to add new pregnancy record
  void _showAddPregnancyDialog() => _showPregnancyRecordFormDialog();

  /// Show dialog to edit existing pregnancy record
  void _showEditPregnancyDialog(PregnancyRecordIsar record) => _showPregnancyRecordFormDialog(record);

  void _showDeleteConfirmation(PregnancyRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleId);
    final recordDescription = 'pregnancy record for ${cattle?.name ?? 'Unknown cattle'} started on ${_formatDate(record.startDate)}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Pregnancy Record'),
        content: Text('Are you sure you want to delete this $recordDescription?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _controller.deletePregnancyRecord(record.businessId!);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}
