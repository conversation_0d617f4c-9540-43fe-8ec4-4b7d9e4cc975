import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_bar.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

class HelpScreen extends StatefulWidget {
  const HelpScreen({Key? key}) : super(key: key);

  @override
  State<HelpScreen> createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredFaqs = [];
  bool _isSearching = false;
  String? _selectedCategory;

  final List<Map<String, dynamic>> _allFaqs = [
    {
      'question': 'How do I add a new animal to my farm?',
      'answer':
          'To add a new animal, go to the Dashboard and tap on the "Cattle" tab. Then tap the "+" button in the bottom right corner. Fill in the details and tap "Save".',
      'category': 'Cattle Management',
      'priority': 'high',
    },
    {
      'question': 'How do I record a breeding event?',
      'answer':
          'Navigate to the Breeding section, select the female animal, then tap "Add Breeding Record". Enter the breeding details including the sire information and save the record.',
      'category': 'Breeding',
      'priority': 'high',
    },
    {
      'question': 'How do I track vaccinations and treatments?',
      'answer':
          'Go to the Health section, select the animal, then tap "Add Health Record". Choose the type of treatment, enter the details, and save the record.',
      'category': 'Health Records',
      'priority': 'high',
    },
    {
      'question': 'How do I track milk production?',
      'answer':
          'Go to the Milk section, select the animal, then tap "Add Milk Record". Enter the date, quantity, and quality parameters, then save the record.',
      'category': 'Cattle Management',
      'priority': 'high',
    },
    {
      'question': 'How do I set up my farm information?',
      'answer':
          'From the Dashboard, open the side menu and tap "Farm Setup". Here you can update your farm details, manage settings, and configure your cattle management system.',
      'category': 'Getting Started',
      'priority': 'high',
    },
    {
      'question': 'How do I monitor pregnancy progress?',
      'answer':
          'Navigate to the Breeding section, then tap on "Pregnancy Records". You\'ll see all active pregnancies with progress tracking and expected calving dates.',
      'category': 'Breeding',
      'priority': 'medium',
    },
    {
      'question': 'How do I generate reports?',
      'answer':
          'Navigate to the Reports & Analytics section, select the type of report you want to generate, set any filters or parameters, and tap "Generate Report".',
      'category': 'Reports & Analytics',
      'priority': 'medium',
    },
    {
      'question': 'How do I set up notifications for important events?',
      'answer':
          'Go to Settings > Notifications, and toggle on the types of notifications you want to receive. You can set custom reminders for vaccinations, heat cycles, and more.',
      'category': 'Settings & Account',
      'priority': 'medium',
    },
    {
      'question': 'How do I update my profile information?',
      'answer':
          'Open the side menu, tap on "Profile", update your information, and tap the save icon in the top right corner.',
      'category': 'Settings & Account',
      'priority': 'low',
    },
    {
      'question': 'How do I backup my data to Google Drive?',
      'answer':
          'Go to Settings > Data Management, then tap "Backup to Google Drive". Sign in with your Google account and your data will be automatically backed up.',
      'category': 'Data Management',
      'priority': 'medium',
    },
    {
      'question': 'How do I record weight measurements?',
      'answer':
          'Navigate to the Weight section, select the animal, then tap "Add Weight Record". Enter the weight, date, and any notes, then save the record.',
      'category': 'Cattle Management',
      'priority': 'medium',
    },
    {
      'question': 'How do I track financial transactions?',
      'answer':
          'Go to the Transactions section, tap the "+" button, select income or expense, enter the details including amount and category, then save.',
      'category': 'Financial Management',
      'priority': 'medium',
    },
  ];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _isSearching = query.isNotEmpty;
      if (_isSearching) {
        _filteredFaqs = _allFaqs
            .where((faq) =>
                faq['question'].toString().toLowerCase().contains(query) ||
                faq['answer'].toString().toLowerCase().contains(query))
            .toList();
      } else {
        _filteredFaqs = [];
      }
    });
  }

  void _navigateToCategory(String category) {
    setState(() {
      _selectedCategory = category;
      _isSearching = true;
      _filteredFaqs = _allFaqs
          .where((faq) => faq['category'].toString() == category)
          .toList();
      _searchController.text = ''; // Clear the search field
    });

    // Show a snackbar with the category name
    MessageUtils.showInfo(context, 'Showing help topics for: $category');
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = null;
      _isSearching = false;
      _filteredFaqs = [];
      _searchController.clear();
    });
  }

  Future<void> _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query:
          'subject=Support Request - Cattle Manager App&body=Hello Support Team,%0A%0AI need help with the following issue:%0A%0A[Please describe your issue here]%0A%0AApp Version: 1.0.0%0ADevice: Mobile%0A%0AThank you!',
    );

    try {
      final bool canLaunch = await canLaunchUrl(emailUri);
      if (canLaunch) {
        await launchUrl(
          emailUri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        if (mounted) {
          _showEmailFallbackDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        _showEmailFallbackDialog();
      }
    }
  }

  void _showEmailFallbackDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
          title: const Row(
            children: [
              Icon(Icons.email, color: AppColors.primary),
              SizedBox(width: kSpacingSmall),
              Text('Email Support'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Please contact our support team directly:',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: kSpacingMedium),
              Container(
                padding: const EdgeInsets.all(kPaddingMedium),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(kBorderRadius),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.email, size: kIconSizeSmall, color: AppColors.primary),
                        SizedBox(width: kSpacingSmall),
                        Text(
                          'Email:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    SelectableText(
                      '<EMAIL>',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.blue,
                      ),
                    ),
                    SizedBox(height: kSpacingMedium),
                    Row(
                      children: [
                        Icon(Icons.phone, size: kIconSizeSmall, color: AppColors.primary),
                        SizedBox(width: kSpacingSmall),
                        Text(
                          'Phone:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    SelectableText(
                      '+****************',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: kSpacingMedium),
              Text(
                'Available: Mon-Fri, 9AM-5PM EST',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(color: AppColors.primary)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _launchPhone();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Call Now'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _launchPhone() async {
    final Uri phoneUri = Uri(
      scheme: 'tel',
      path: '+15551234567',
    );

    try {
      final bool canLaunch = await canLaunchUrl(phoneUri);
      if (canLaunch) {
        await launchUrl(
          phoneUri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        if (mounted) {
          _showPhoneFallbackDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        _showPhoneFallbackDialog();
      }
    }
  }

  void _showPhoneFallbackDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
          title: const Row(
            children: [
              Icon(Icons.phone, color: AppColors.primary),
              SizedBox(width: kSpacingSmall),
              Text('Phone Support'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.phone_in_talk,
                size: 48,
                color: AppColors.primary,
              ),
              const SizedBox(height: kSpacingMedium),
              const Text(
                'Please call our support team directly:',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: kSpacingMedium),
              Container(
                padding: const EdgeInsets.all(kPaddingMedium),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(kBorderRadius),
                ),
                child: const SelectableText(
                  '+****************',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
              const SizedBox(height: kSpacingMedium),
              Text(
                'Available: Mon-Fri, 9AM-5PM EST',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(color: AppColors.primary)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _launchEmail();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Email Instead'),
            ),
          ],
        );
      },
    );
  }

  void _showLiveChatDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.info,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.chat_bubble,
                  color: Colors.white,
                  size: kIconSizeMedium,
                ),
              ),
              const SizedBox(width: kSpacingMedium),
              const Text('Live Chat Support'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.support_agent,
                size: 64,
                color: AppColors.info,
              ),
              const SizedBox(height: kSpacingMedium),
              const Text(
                'Live chat will be available in the next version of the app.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: kSpacingSmall),
              Text(
                'Please use email support for immediate assistance.',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK', style: TextStyle(color: AppColors.primary)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _launchEmail();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Email Support'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: 'Help & Support',
        context: context,
      ),
      backgroundColor: AppColors.background,
      body: ListView(
        padding: const EdgeInsets.all(kPaddingMedium),
        children: [
          _buildSearchSection(),
          const SizedBox(height: kSpacingLarge),
          if (_selectedCategory != null) _buildActiveFilter(),
          if (_selectedCategory != null) const SizedBox(height: kSpacingMedium),
          if (!_isSearching) _buildQuickActions(),
          if (!_isSearching) const SizedBox(height: kSpacingLarge),
          if (!_isSearching) _buildHelpCategories(),
          if (!_isSearching) const SizedBox(height: kSpacingLarge),
          _isSearching ? _buildSearchResults() : _buildPopularFAQs(),
          const SizedBox(height: kSpacingLarge),
          _buildContactSupport(),
          const SizedBox(height: kSpacingMedium),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Card(
      elevation: kCardElevation,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.search, color: AppColors.primary, size: kIconSizeMedium),
                SizedBox(width: kSpacingSmall),
                Text(
                  'Search Help Topics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingMedium),
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search for help topics, features, or questions...',
                hintStyle: TextStyle(color: Colors.grey[600]),
                prefixIcon: const Icon(Icons.search, color: AppColors.primary),
                suffixIcon: _isSearching
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: AppColors.primary),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _isSearching = false;
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(kBorderRadius),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(kBorderRadius),
                  borderSide: const BorderSide(color: AppColors.primary, width: 2),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: kPaddingMedium, vertical: kPaddingSmall),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: kPaddingMedium, vertical: kPaddingSmall),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(kBorderRadius),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.filter_list, color: AppColors.primary, size: kIconSizeSmall),
          const SizedBox(width: kSpacingSmall),
          Text(
            'Filtered by: $_selectedCategory',
            style: const TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: _clearFilters,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.close, color: Colors.white, size: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      elevation: kCardElevation,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.flash_on, color: AppColors.secondary, size: kIconSizeMedium),
                SizedBox(width: kSpacingSmall),
                Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingMedium),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'Contact Support',
                    Icons.support_agent,
                    AppColors.primary,
                    () => _launchEmail(),
                  ),
                ),
                const SizedBox(width: kSpacingMedium),
                Expanded(
                  child: _buildQuickActionButton(
                    'Video Tutorials',
                    Icons.play_circle_filled,
                    AppColors.info,
                    () => _showComingSoonDialog('Video Tutorials'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(kBorderRadius),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(kBorderRadius),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: kPaddingMedium, horizontal: kPaddingSmall),
          child: Column(
            children: [
              Icon(icon, color: color, size: kIconSizeLarge),
              const SizedBox(height: kSpacingSmall),
              Text(
                label,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_filteredFaqs.isEmpty) {
      return Card(
        elevation: kCardElevation,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
        child: Padding(
          padding: const EdgeInsets.all(kPaddingLarge),
          child: Column(
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: kSpacingMedium),
              const Text(
                'No results found',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
              const SizedBox(height: kSpacingSmall),
              Text(
                'Try different keywords or browse the categories below',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.search, color: AppColors.primary, size: kIconSizeMedium),
            const SizedBox(width: kSpacingSmall),
            Text(
              'Search Results ($_filteredFaqs.length)',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: kSpacingMedium),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _filteredFaqs.length,
          itemBuilder: (context, index) {
            final faq = _filteredFaqs[index];
            return _buildFAQCard(faq, index);
          },
        ),
      ],
    );
  }

  Widget _buildHelpCategories() {
    final categories = [
      {
        'title': 'Getting Started',
        'subtitle': 'Farm setup & basics',
        'icon': Icons.rocket_launch,
        'color': AppColors.primary,
        'count': _allFaqs.where((faq) => faq['category'] == 'Getting Started').length,
      },
      {
        'title': 'Cattle Management',
        'subtitle': 'Animals, records & tracking',
        'icon': Icons.pets,
        'color': AppColors.cattleHeader,
        'count': _allFaqs.where((faq) => faq['category'] == 'Cattle Management').length,
      },
      {
        'title': 'Breeding',
        'subtitle': 'Breeding & pregnancy tracking',
        'icon': Icons.favorite,
        'color': AppColors.breedingHeader,
        'count': _allFaqs.where((faq) => faq['category'] == 'Breeding').length,
      },
      {
        'title': 'Health Records',
        'subtitle': 'Vaccinations & treatments',
        'icon': Icons.medical_services,
        'color': AppColors.healthHeader,
        'count': _allFaqs.where((faq) => faq['category'] == 'Health Records').length,
      },
      {
        'title': 'Financial Management',
        'subtitle': 'Transactions & expenses',
        'icon': Icons.account_balance_wallet,
        'color': AppColors.transactionHeader,
        'count': _allFaqs.where((faq) => faq['category'] == 'Financial Management').length,
      },
      {
        'title': 'Data Management',
        'subtitle': 'Backup & sync',
        'icon': Icons.cloud_sync,
        'color': AppColors.info,
        'count': _allFaqs.where((faq) => faq['category'] == 'Data Management').length,
      },
      {
        'title': 'Reports & Analytics',
        'subtitle': 'Insights & reports',
        'icon': Icons.analytics,
        'color': AppColors.eventsHeader,
        'count': _allFaqs.where((faq) => faq['category'] == 'Reports & Analytics').length,
      },
      {
        'title': 'Settings & Account',
        'subtitle': 'Profile & preferences',
        'icon': Icons.settings,
        'color': AppColors.milkHeader,
        'count': _allFaqs.where((faq) => faq['category'] == 'Settings & Account').length,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.category, color: AppColors.primary, size: kIconSizeMedium),
            SizedBox(width: kSpacingSmall),
            Text(
              'Browse by Category',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: kSpacingMedium),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: kSpacingMedium,
            mainAxisSpacing: kSpacingMedium,
            childAspectRatio: 1.5, // Increased from 1.3 to give more height
          ),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return _buildCategoryCard(category);
          },
        ),
      ],
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category) {
    return Card(
      elevation: kCardElevation,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
      child: InkWell(
        onTap: () => _navigateToCategory(category['title'] as String),
        borderRadius: BorderRadius.circular(kBorderRadius),
        child: Container(
          padding: const EdgeInsets.all(12), // Reduced from kPaddingMedium
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(kBorderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                (category['color'] as Color).withValues(alpha: 0.1),
                (category['color'] as Color).withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Added to prevent overflow
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6), // Reduced from 8
                    decoration: BoxDecoration(
                      color: category['color'] as Color,
                      borderRadius: BorderRadius.circular(6), // Reduced from 8
                    ),
                    child: Icon(
                      category['icon'] as IconData,
                      color: Colors.white,
                      size: 20, // Reduced from kIconSizeMedium
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // Reduced padding
                    decoration: BoxDecoration(
                      color: (category['color'] as Color).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10), // Reduced from 12
                    ),
                    child: Text(
                      '${category['count']}',
                      style: TextStyle(
                        color: category['color'] as Color,
                        fontWeight: FontWeight.bold,
                        fontSize: 10, // Reduced from 12
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6), // Reduced from kSpacingSmall
              Flexible( // Added Flexible to prevent overflow
                child: Text(
                  category['title'] as String,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14, // Reduced from 16
                    color: AppColors.onSurface,
                  ),
                  maxLines: 2, // Added max lines
                  overflow: TextOverflow.ellipsis, // Added overflow handling
                ),
              ),
              const SizedBox(height: 2), // Reduced from 4
              Flexible( // Added Flexible to prevent overflow
                child: Text(
                  category['subtitle'] as String,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 11, // Reduced from 12
                  ),
                  maxLines: 2, // Added max lines
                  overflow: TextOverflow.ellipsis, // Added overflow handling
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPopularFAQs() {
    // Get high priority FAQs
    final popularFaqs = _allFaqs.where((faq) => faq['priority'] == 'high').take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.star, color: AppColors.warning, size: kIconSizeMedium),
            SizedBox(width: kSpacingSmall),
            Text(
              'Popular Questions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: kSpacingMedium),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: popularFaqs.length,
          itemBuilder: (context, index) {
            final faq = popularFaqs[index];
            return _buildFAQCard(faq, index);
          },
        ),
        const SizedBox(height: kSpacingMedium),
        Center(
          child: OutlinedButton.icon(
            icon: const Icon(Icons.list_alt, color: AppColors.primary),
            label: const Text('View All FAQs', style: TextStyle(color: AppColors.primary)),
            onPressed: () {
              setState(() {
                _isSearching = true;
                _filteredFaqs = List.from(_allFaqs);
                _searchController.text = '';
              });
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
              padding: const EdgeInsets.symmetric(horizontal: kPaddingLarge, vertical: kPaddingMedium),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFAQCard(Map<String, dynamic> faq, int index) {
    final priority = faq['priority'] as String;
    final priorityColor = priority == 'high'
        ? AppColors.error
        : priority == 'medium'
            ? AppColors.warning
            : AppColors.success;

    return Card(
      elevation: kCardElevation,
      margin: const EdgeInsets.only(bottom: kSpacingSmall),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          tilePadding: const EdgeInsets.symmetric(horizontal: kPaddingMedium, vertical: kPaddingSmall),
          childrenPadding: const EdgeInsets.fromLTRB(kPaddingMedium, 0, kPaddingMedium, kPaddingMedium),
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: priorityColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.help_outline,
              color: priorityColor,
              size: kIconSizeSmall,
            ),
          ),
          title: Text(
            faq['question'] as String,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: AppColors.onSurface,
            ),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: priorityColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    faq['category'] as String,
                    style: TextStyle(
                      fontSize: 10,
                      color: priorityColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: kSpacingSmall),
                if (priority == 'high')
                  Icon(Icons.star, color: priorityColor, size: 12),
              ],
            ),
          ),
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(kPaddingMedium),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(kBorderRadius),
              ),
              child: Text(
                faq['answer'] as String,
                style: TextStyle(
                  fontSize: 15,
                  height: 1.5,
                  color: Colors.grey[800],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSupport() {
    return Card(
      elevation: kCardElevation,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(kBorderRadius),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary.withValues(alpha: 0.05),
              AppColors.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(kPaddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.support_agent,
                      color: Colors.white,
                      size: kIconSizeMedium,
                    ),
                  ),
                  const SizedBox(width: kSpacingMedium),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Need Personal Assistance?',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                        ),
                        Text(
                          'Our agricultural experts are here to help',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: kSpacingLarge),
              Row(
                children: [
                  Expanded(
                    child: _buildSupportButton(
                      'Email Support',
                      Icons.email,
                      AppColors.primary,
                      'Get detailed help via email',
                      _launchEmail,
                      isPrimary: true,
                    ),
                  ),
                  const SizedBox(width: kSpacingMedium),
                  Expanded(
                    child: _buildSupportButton(
                      'Live Chat',
                      Icons.chat_bubble,
                      AppColors.info,
                      'Chat with our team',
                      _showLiveChatDialog,
                      isPrimary: false,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: kSpacingMedium),
              const Divider(),
              const SizedBox(height: kSpacingMedium),
              _buildContactInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSupportButton(String label, IconData icon, Color color, String subtitle, VoidCallback onTap, {required bool isPrimary}) {
    return Material(
      color: isPrimary ? color : color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(kBorderRadius),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(kBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(kPaddingMedium),
          child: Column(
            children: [
              Icon(
                icon,
                color: isPrimary ? Colors.white : color,
                size: kIconSizeLarge,
              ),
              const SizedBox(height: kSpacingSmall),
              Text(
                label,
                style: TextStyle(
                  color: isPrimary ? Colors.white : color,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isPrimary ? Colors.white.withValues(alpha: 0.8) : color.withValues(alpha: 0.7),
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactInfo() {
    return Column(
      children: [
        GestureDetector(
          onTap: _launchPhone,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: kPaddingSmall),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.phone, size: kIconSizeSmall, color: AppColors.primary),
                SizedBox(width: kSpacingSmall),
                Text(
                  'Call us: +****************',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: kSpacingSmall),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.access_time, size: kIconSizeSmall, color: Colors.grey[600]),
            const SizedBox(width: kSpacingSmall),
            Text(
              'Available: Mon-Fri, 9AM-5PM EST',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 13,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showComingSoonDialog(String feature) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(kBorderRadius)),
          title: const Row(
            children: [
              Icon(Icons.construction, color: AppColors.warning),
              SizedBox(width: kSpacingSmall),
              Text('Coming Soon'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.rocket_launch,
                size: 48,
                color: AppColors.primary,
              ),
              const SizedBox(height: kSpacingMedium),
              Text(
                '$feature will be available in the next version of the app.',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: kSpacingSmall),
              Text(
                'Stay tuned for updates!',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK', style: TextStyle(color: AppColors.primary)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _launchEmail();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Contact Support'),
            ),
          ],
        );
      },
    );
  }
}
