import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'milk_record_isar.g.dart';

/// Milk record model for Isar database
@collection
class MilkRecordIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the milk record - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Farm business ID for multi-tenancy support
  @Index()
  String? farmBusinessId;

  /// Cattle business ID associated with this milk record
  @Index()
  String? cattleBusinessId;

  /// Date and time of milking
  @Index()
  DateTime? date;

  /// Morning milk amount in liters
  double? morningAmount;

  /// Evening milk amount in liters
  double? eveningAmount;

  /// Total milk amount in liters
  double? totalAmount;

  /// Milk quality grade
  String? quality;

  /// Fat content percentage
  double? fatContent;

  /// Protein content percentage
  double? proteinContent;

  /// Somatic cell count
  int? somaticCellCount;

  /// Temperature at collection
  double? temperature;

  /// Notes about the milking session
  String? notes;

  /// Creation timestamp
  DateTime? createdAt;

  /// Last update timestamp
  DateTime? updatedAt;

  /// Created by user ID
  String? createdBy;

  /// Updated by user ID
  String? updatedBy;

  MilkRecordIsar();

  /// Generate a business ID for milk records
  static String generateBusinessId(String cattleId, DateTime date) {
    final dateStr = "${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}";
    final uniqueKey = "$cattleId-$dateStr";
    return "milk_${const Uuid().v5(Namespace.oid.value, uniqueKey)}";
  }

  /// Convert to map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'businessId': businessId,
      'farmBusinessId': farmBusinessId,
      'cattleBusinessId': cattleBusinessId,
      'date': date?.toIso8601String(),
      'morningAmount': morningAmount,
      'eveningAmount': eveningAmount,
      'totalAmount': totalAmount,
      'quality': quality,
      'fatContent': fatContent,
      'proteinContent': proteinContent,
      'somaticCellCount': somaticCellCount,
      'temperature': temperature,
      'notes': notes,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  /// Create from map for JSON deserialization
  factory MilkRecordIsar.fromMap(Map<String, dynamic> map) {
    return MilkRecordIsar()
      ..id = map['id'] ?? Isar.autoIncrement
      ..businessId = map['businessId']
      ..farmBusinessId = map['farmBusinessId']
      ..cattleBusinessId = map['cattleBusinessId']
      ..date = map['date'] != null ? DateTime.parse(map['date']) : null
      ..morningAmount = map['morningAmount']?.toDouble()
      ..eveningAmount = map['eveningAmount']?.toDouble()
      ..totalAmount = map['totalAmount']?.toDouble()
      ..quality = map['quality']
      ..fatContent = map['fatContent']?.toDouble()
      ..proteinContent = map['proteinContent']?.toDouble()
      ..somaticCellCount = map['somaticCellCount']
      ..temperature = map['temperature']?.toDouble()
      ..notes = map['notes']
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : DateTime.now()
      ..createdBy = map['createdBy']
      ..updatedBy = map['updatedBy'];
  }
}
