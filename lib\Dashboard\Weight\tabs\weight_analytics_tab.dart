import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/weight_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_tabs.dart';
import '../../../shared/models/info_card_data.dart';



class WeightAnalyticsTab extends StatefulWidget {
  final WeightController controller;

  const WeightAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<WeightAnalyticsTab> createState() => _WeightAnalyticsTabState();
}

class _WeightAnalyticsTabState extends State<WeightAnalyticsTab> {
  int _selectedChartIndex = 0; // 0: Growth Trends, 1: Performance Distribution

  // Use global constants instead of local ones
  static const _fallbackColor = AppColors.fallback;

  // Common chart configuration
  static const _chartRadius = 80.0;
  static const _chartCenterSpace = 50.0;
  static const _chartSectionsSpace = 2.0;
  static const _chartHeight = 240.0;

  // Common text styles
  static const _chartTitleStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  @override
  void initState() {
    super.initState();
    // Data loading is now handled by the controller
  }

  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<Map<String, dynamic>> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header using universal component
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),

        // 2. Use ResponsiveGrid.cards for consistent responsive behavior
        ResponsiveGrid.cards(
          children: cardData.map((data) {
            return UniversalInfoCard(
              title: data['title'] as String,
              value: data['value'] as String,
              subtitle: data['subtitle'] as String?,
              icon: data['icon'] as IconData,
              color: data['color'] as Color,
              badge: data['badge'] as String?,
              insight: data['insight'] as String?,
            );
          }).toList(),
        ),
      ],
    );
  }

  // Universal pie chart builder - pure UI component without business logic
  Widget _buildUniversalPieChart(Map<String, dynamic> data, Map<String, Color> colors, {String? emptyMessage}) {
    if (data.isEmpty || widget.controller.allRecords.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    // Ensure we have valid data before rendering
    final validEntries = data.entries.where((entry) => (entry.value as num) > 0).toList();
    if (validEntries.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    final total = validEntries.fold<double>(0, (sum, entry) => sum + (entry.value as num).toDouble());
    final sections = validEntries.map((entry) {
      final value = (entry.value as num).toDouble();
      final percentage = (value / total) * 100;
      final sectionColor = colors[entry.key] ?? _fallbackColor;

      return PieChartSectionData(
        color: sectionColor,
        value: value,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: _chartRadius,
        titleStyle: _chartTitleStyle,
      );
    }).toList();

    // Add a small delay to ensure smooth rendering
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: _chartCenterSpace,
          sectionsSpace: _chartSectionsSpace,
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Check if we have data to display
        if (widget.controller.allRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Weight Data',
            message: 'Add weight records to your herd to view comprehensive analytics and insights.',
            tabColor: AppColors.weightHeader,
            tabIndex: 0, // Analytics tab
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () {
                // Navigate to add weight record screen
                Navigator.of(context).pushNamed('/weight/add');
              },
              tabColor: AppColors.weightHeader,
            ),
          );
        }

    // Define sections for clean, declarative layout
    final sections = [
      _buildEnhancedKPIDashboard(context),
      _buildWeightTrendAnalytics(context),
      _buildPerformanceAnalytics(context),
      _buildWeightOverview(context),
    ];

    // Use RefreshIndicator with SingleChildScrollView for pull-to-refresh functionality
    return RefreshIndicator(
      onRefresh: () async {
        await widget.controller.refresh();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(kPaddingMedium), // Use global constant
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (int i = 0; i < sections.length; i++) ...[
              sections[i],
              if (i < sections.length - 1) const SizedBox(height: kSpacingLarge), // Use global constant
            ],
          ],
        ),
      ),
    );
      },
    );
  }

  Widget _buildEnhancedKPIDashboard(BuildContext context) {
    final kpiColors = _getKPIColors();
    final kpiCards = _buildKPICards(kpiColors);

    return _buildGridSection(
      title: 'Key Performance Indicators',
      subtitle: 'Essential metrics for weight management',
      icon: Icons.dashboard_outlined,
      headerColor: AppColors.weightHeader,
      cardData: kpiCards.map((card) => card.toMap()..['badge'] = null).toList(),
    );
  }

  /// Build KPI cards using InfoCardData for better type safety
  List<InfoCardData> _buildKPICards(List<Color> kpiColors) {
    return [
      InfoCardData(
        title: 'Total Records',
        value: widget.controller.allRecords.length.toString(),
        subtitle: 'weight records',
        icon: Icons.monitor_weight,
        color: kpiColors[0],
        insight: 'Total weight entries',
      ),
      InfoCardData(
        title: 'Cattle Tracked',
        value: widget.controller.allCattle.length.toString(),
        subtitle: 'animals monitored',
        icon: Icons.pets,
        color: kpiColors[1],
        insight: 'Animals with weight data',
      ),
      InfoCardData(
        title: 'Avg Daily Gain',
        value: '${widget.controller.insights.performance.averageDailyGain.toStringAsFixed(2)} kg',
        subtitle: 'daily weight gain',
        icon: Icons.trending_up,
        color: kpiColors[2],
        insight: 'Growth performance',
      ),
      InfoCardData(
        title: 'Top Performer',
        value: widget.controller.insights.performance.topPerformerName.isNotEmpty ? widget.controller.insights.performance.topPerformerName : 'N/A',
        subtitle: 'best growth rate',
        icon: Icons.star,
        color: kpiColors[3],
        insight: 'Fastest growing animal',
      ),
      InfoCardData(
        title: 'Growth Trend',
        value: widget.controller.insights.performance.growthTrend,
        subtitle: 'overall trend',
        icon: Icons.assessment,
        color: kpiColors[4],
        insight: 'Herd growth pattern',
      ),
      InfoCardData(
        title: 'Latest Weight',
        value: widget.controller.allRecords.isNotEmpty ? '${widget.controller.allRecords.last.weight.toStringAsFixed(1)} kg' : 'N/A',
        subtitle: 'most recent entry',
        icon: Icons.schedule,
        color: kpiColors[5],
        insight: 'Recent measurement',
      ),
    ];
  }

  Widget _buildWeightTrendAnalytics(BuildContext context) {
    final trendData = _buildTrendData();
    final performanceData = _buildPerformanceData();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Weight Composition Analytics',
          icon: Icons.pie_chart_outline,
          color: AppColors.weightHeader,
          subtitle: 'Detailed breakdown of weight trends and performance',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Toggle buttons for chart selection
        _buildChartToggleButtons(context),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Single chart display based on selection
        _buildSelectedChart(context, trendData, performanceData),
      ],
    );
  }

  Widget _buildChartToggleButtons(BuildContext context) {
    final chartOptions = [
      {'title': 'Growth Trends', 'icon': Icons.trending_up},
      {'title': 'Performance', 'icon': Icons.assessment},
    ];

    // Use colors from AppColors instead of hardcoded values
    final toggleColors = [
      AppColors.weightKpiColors[0], // Green
      AppColors.weightKpiColors[1], // Blue
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chartOptions.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedChartIndex == index;

        return ChoiceChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected ? Colors.white : toggleColors[index],
              ),
              const SizedBox(width: 6),
              Text(
                option['title'] as String,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: isSelected ? Colors.white : toggleColors[index],
                ),
              ),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedChartIndex = index;
              });
            }
          },
          selectedColor: toggleColors[index],
          backgroundColor: Colors.white,
          side: BorderSide(
            color: toggleColors[index],
            width: 2,
          ),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        );
      }).toList(),
    );
  }

  Widget _buildSelectedChart(
    BuildContext context,
    Map<String, double> trendData,
    Map<String, double> performanceData,
  ) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildEnhancedChart(
          context,
          'Growth Trend Distribution',
          _buildUniversalPieChart(trendData, _getTrendColors()),
          _buildEnhancedLegend(trendData, _getTrendColors()),
          Icons.trending_up,
        );
      case 1:
        return _buildEnhancedChart(
          context,
          'Performance Distribution',
          _buildUniversalPieChart(performanceData, _getPerformanceColors()),
          _buildEnhancedLegend(performanceData, _getPerformanceColors()),
          Icons.assessment,
        );
      default:
        return _buildEnhancedChart(
          context,
          'Growth Trend Distribution',
          _buildUniversalPieChart(trendData, _getTrendColors()),
          _buildEnhancedLegend(trendData, _getTrendColors()),
          Icons.trending_up,
        );
    }
  }

  Widget _buildEnhancedChart(
    BuildContext context,
    String title,
    Widget chart,
    Widget? legend,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(kBorderRadius * 2), // Use global constant with multiplier
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingLarge), // Use global constant
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.weightHeader.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.weightHeader,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingLarge), // Use global constant
            SizedBox(height: _chartHeight, child: Center(child: chart)), // Chart height and centered
            if (legend != null) ...[
              const SizedBox(height: kSpacingMedium), // Use global constant
              legend,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedLegend(Map<String, dynamic> data, Map<String, Color> colors) {
    if (data.isEmpty) return const SizedBox.shrink();

    final total = data.values.fold<double>(0, (sum, value) => sum + (value as num).toDouble());

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: data.entries.map((entry) {
        final value = (entry.value as num).toDouble();
        final percentage = total > 0 ? ((value / total) * 100).toStringAsFixed(1) : '0';
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: colors[entry.key]?.withValues(alpha: 0.1) ?? _fallbackColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: colors[entry.key] ?? _fallbackColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '$entry.key (${value.toStringAsFixed(1)}) $percentage%',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPerformanceAnalytics(BuildContext context) {
    final colors = _getInsightColors();
    final performanceCards = _buildPerformanceCards(colors);

    return _buildGridSection(
      title: 'Performance Analytics',
      subtitle: 'Growth performance and weight gain metrics',
      icon: Icons.timeline,
      headerColor: Colors.green,
      cardData: performanceCards.map((card) => card.toMap()).toList(),
    );
  }

  /// Build performance analytics cards
  List<InfoCardData> _buildPerformanceCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Top Performer',
        value: widget.controller.insights.performance.topPerformerName.isNotEmpty ? widget.controller.insights.performance.topPerformerName : 'N/A',
        subtitle: 'best growth rate',
        icon: Icons.star,
        color: colors[0],
        insight: '${widget.controller.insights.performance.topPerformerGainRate.toStringAsFixed(2)} kg/day',
      ),
      InfoCardData(
        title: 'Avg Daily Gain',
        value: '${widget.controller.insights.performance.averageDailyGain.toStringAsFixed(2)} kg/day',
        subtitle: 'daily growth',
        icon: Icons.trending_up,
        color: colors[1],
        insight: 'Overall herd performance',
      ),
      InfoCardData(
        title: 'Growth Trend',
        value: widget.controller.insights.performance.growthTrend,
        subtitle: 'trend direction',
        icon: Icons.assessment,
        color: colors[2],
        insight: widget.controller.insights.performance.growthTrendDescription,
      ),
      InfoCardData(
        title: 'Records This Month',
        value: widget.controller.allRecords.where((record) => record.measurementDate?.isAfter(DateTime.now().subtract(const Duration(days: 30))) ?? false).length.toString(),
        subtitle: 'recent entries',
        icon: Icons.schedule,
        color: colors[3],
        insight: 'Recent activity',
      ),
    ];
  }

  Widget _buildWeightOverview(BuildContext context) {
    final overviewColors = _getOverviewColors();
    final overviewCards = _buildOverviewCards(overviewColors);

    return _buildGridSection(
      title: 'Weight Overview',
      subtitle: 'Overall weight management insights',
      icon: Icons.assessment,
      headerColor: Colors.blue,
      cardData: overviewCards.map((card) => card.toMap()).toList(),
    );
  }

  /// Build overview cards
  List<InfoCardData> _buildOverviewCards(List<Color> overviewColors) {
    final recentRecords = widget.controller.allRecords.where((record) => record.measurementDate?.isAfter(DateTime.now().subtract(const Duration(days: 30))) ?? false).toList();
    final averageWeight = widget.controller.allRecords.isNotEmpty ? widget.controller.allRecords.map((r) => r.weight).reduce((a, b) => a + b) / widget.controller.allRecords.length : 0.0;
    
    return [
      InfoCardData(
        title: 'Average Weight',
        value: '${averageWeight.toStringAsFixed(1)} kg',
        subtitle: 'herd average',
        icon: Icons.monitor_weight,
        color: overviewColors[0],
        insight: 'Overall herd weight',
      ),
      InfoCardData(
        title: 'Recent Records',
        value: recentRecords.length.toString(),
        subtitle: 'last 30 days',
        icon: Icons.schedule,
        color: overviewColors[1],
        insight: 'Recent activity',
      ),
      InfoCardData(
        title: 'Growth Rate',
        value: widget.controller.insights.performance.growthTrend,
        subtitle: 'trend status',
        icon: Icons.trending_up,
        color: overviewColors[2],
        insight: 'Performance indicator',
      ),
      InfoCardData(
        title: 'Monitoring Progress',
        value: '${((widget.controller.allRecords.length / (widget.controller.allCattle.isEmpty ? 1 : widget.controller.allCattle.length)) * 100).toStringAsFixed(0)}%',
        subtitle: 'tracking coverage',
        icon: Icons.visibility,
        color: overviewColors[3],
        insight: 'Monitoring completeness',
      ),
    ];
  }

  // Data builders for charts
  Map<String, double> _buildTrendData() {
    final trendData = <String, double>{};
    final insights = widget.controller.insights;

    // Build trend distribution based on growth trend
    switch (insights.performance.growthTrend.toLowerCase()) {
      case 'increasing':
        trendData['Increasing'] = 70.0;
        trendData['Stable'] = 20.0;
        trendData['Decreasing'] = 10.0;
        break;
      case 'stable':
        trendData['Stable'] = 60.0;
        trendData['Increasing'] = 25.0;
        trendData['Decreasing'] = 15.0;
        break;
      case 'decreasing':
        trendData['Decreasing'] = 50.0;
        trendData['Stable'] = 30.0;
        trendData['Increasing'] = 20.0;
        break;
      default:
        trendData['Unknown'] = 100.0;
    }

    return trendData;
  }

  Map<String, double> _buildPerformanceData() {
    final performanceData = <String, double>{};
    final insights = widget.controller.insights;

    // Build performance distribution based on average daily gain
    final avgGain = insights.performance.averageDailyGain;

    if (avgGain > 1.0) {
      performanceData['Excellent'] = 40.0;
      performanceData['Good'] = 35.0;
      performanceData['Average'] = 20.0;
      performanceData['Poor'] = 5.0;
    } else if (avgGain > 0.5) {
      performanceData['Good'] = 45.0;
      performanceData['Average'] = 30.0;
      performanceData['Excellent'] = 15.0;
      performanceData['Poor'] = 10.0;
    } else if (avgGain > 0.0) {
      performanceData['Average'] = 50.0;
      performanceData['Poor'] = 30.0;
      performanceData['Good'] = 15.0;
      performanceData['Excellent'] = 5.0;
    } else {
      performanceData['Poor'] = 60.0;
      performanceData['Average'] = 25.0;
      performanceData['Good'] = 10.0;
      performanceData['Excellent'] = 5.0;
    }

    return performanceData;
  }

  // Color Management - Different colors for each KPI card (multi-color rule)
  List<Color> _getKPIColors() => AppColors.weightKpiColors;
  List<Color> _getInsightColors() => AppColors.weightKpiColors;
  List<Color> _getOverviewColors() => AppColors.weightKpiColors;

  Map<String, Color> _getTrendColors() => AppColors.weightTrendColors;
  Map<String, Color> _getPerformanceColors() => AppColors.weightPerformanceColors;
}
