import 'package:flutter/material.dart';
import '../models/report_models.dart';

/// Filter Status Bar
/// 
/// Displays active filters and provides quick clear functionality.
class FilterStatusBar extends StatelessWidget {
  final FilterState filter;
  final VoidCallback onClearFilters;

  const FilterStatusBar({
    Key? key,
    required this.filter,
    required this.onClearFilters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!filter.hasFilters) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.filter_list,
            color: Colors.blue,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: _buildFilterChips(),
            ),
          ),
          TextButton.icon(
            onPressed: onClearFilters,
            icon: const Icon(Icons.clear, size: 16),
            label: const Text('Clear'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildFilterChips() {
    final chips = <Widget>[];

    // Date range filter
    if (filter.startDate != null || filter.endDate != null) {
      String dateText;
      if (filter.startDate != null && filter.endDate != null) {
        dateText = '${_formatDate(filter.startDate!)} - ${_formatDate(filter.endDate!)}';
      } else if (filter.startDate != null) {
        dateText = 'From ${_formatDate(filter.startDate!)}';
      } else {
        dateText = 'Until ${_formatDate(filter.endDate!)}';
      }
      
      chips.add(
        Chip(
          label: Text(dateText),
          avatar: const Icon(Icons.date_range, size: 16),
          backgroundColor: Colors.blue.withValues(alpha: 0.1),
          side: BorderSide(color: Colors.blue.withValues(alpha: 0.3)),
        ),
      );
    }

    // Cattle filter
    if (filter.cattleIds?.isNotEmpty == true) {
      chips.add(
        Chip(
          label: Text('${filter.cattleIds!.length} cattle selected'),
          avatar: const Icon(Icons.pets, size: 16),
          backgroundColor: Colors.green.withValues(alpha: 0.1),
          side: BorderSide(color: Colors.green.withValues(alpha: 0.3)),
        ),
      );
    }

    // Categories filter
    if (filter.categories?.isNotEmpty == true) {
      chips.add(
        Chip(
          label: Text('${filter.categories!.length} categories'),
          avatar: const Icon(Icons.category, size: 16),
          backgroundColor: Colors.orange.withValues(alpha: 0.1),
          side: BorderSide(color: Colors.orange.withValues(alpha: 0.3)),
        ),
      );
    }

    // Search query filter
    if (filter.searchQuery?.isNotEmpty == true) {
      chips.add(
        Chip(
          label: Text('Search: "${filter.searchQuery}"'),
          avatar: const Icon(Icons.search, size: 16),
          backgroundColor: Colors.purple.withValues(alpha: 0.1),
          side: BorderSide(color: Colors.purple.withValues(alpha: 0.3)),
        ),
      );
    }

    // Custom filters
    if (filter.customFilters?.isNotEmpty == true) {
      chips.add(
        Chip(
          label: Text('${filter.customFilters!.length} custom filters'),
          avatar: const Icon(Icons.tune, size: 16),
          backgroundColor: Colors.grey.withValues(alpha: 0.1),
          side: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
      );
    }

    return chips;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Filter Options Widget
/// 
/// Provides comprehensive filtering options for reports.
class FilterOptions extends StatefulWidget {
  final FilterState initialFilter;
  final Function(FilterState) onFilterChanged;
  final List<String>? availableCattleIds;
  final List<String>? availableCategories;

  const FilterOptions({
    Key? key,
    required this.initialFilter,
    required this.onFilterChanged,
    this.availableCattleIds,
    this.availableCategories,
  }) : super(key: key);

  @override
  State<FilterOptions> createState() => _FilterOptionsState();
}

class _FilterOptionsState extends State<FilterOptions> {
  late FilterState _currentFilter;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.initialFilter;
    _searchController.text = _currentFilter.searchQuery ?? '';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date range section
        _buildDateRangeSection(),
        const SizedBox(height: 16),
        
        // Cattle selection section
        if (widget.availableCattleIds?.isNotEmpty == true) ...[
          _buildCattleSelectionSection(),
          const SizedBox(height: 16),
        ],
        
        // Categories section
        if (widget.availableCategories?.isNotEmpty == true) ...[
          _buildCategoriesSection(),
          const SizedBox(height: 16),
        ],
        
        // Search section
        _buildSearchSection(),
        const SizedBox(height: 16),
        
        // Action buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date Range',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                'Start Date',
                _currentFilter.startDate,
                (date) => _updateFilter(_currentFilter.copyWith(startDate: date)),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateField(
                'End Date',
                _currentFilter.endDate,
                (date) => _updateFilter(_currentFilter.copyWith(endDate: date)),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Quick date range buttons
        Wrap(
          spacing: 8,
          children: [
            _buildQuickDateButton('Last 7 days', DateTime.now().subtract(const Duration(days: 7))),
            _buildQuickDateButton('Last 30 days', DateTime.now().subtract(const Duration(days: 30))),
            _buildQuickDateButton('Last 90 days', DateTime.now().subtract(const Duration(days: 90))),
            _buildQuickDateButton('This year', DateTime(DateTime.now().year, 1, 1)),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField(String label, DateTime? value, Function(DateTime?) onChanged) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: value ?? DateTime.now(),
          firstDate: DateTime.now().subtract(const Duration(days: 365 * 2)),
          lastDate: DateTime.now(),
        );
        onChanged(date);
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          value != null ? _formatDate(value) : 'Select date',
          style: TextStyle(
            color: value != null ? null : Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickDateButton(String label, DateTime startDate) {
    return OutlinedButton(
      onPressed: () {
        _updateFilter(_currentFilter.copyWith(
          startDate: startDate,
          endDate: DateTime.now(),
        ));
      },
      child: Text(label),
    );
  }

  Widget _buildCattleSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cattle Selection',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: ListView(
            children: widget.availableCattleIds!.map((cattleId) {
              final isSelected = _currentFilter.cattleIds?.contains(cattleId) ?? false;
              return CheckboxListTile(
                title: Text('Cattle $cattleId'),
                value: isSelected,
                onChanged: (selected) {
                  final currentIds = List<String>.from(_currentFilter.cattleIds ?? []);
                  if (selected == true) {
                    currentIds.add(cattleId);
                  } else {
                    currentIds.remove(cattleId);
                  }
                  _updateFilter(_currentFilter.copyWith(cattleIds: currentIds));
                },
                dense: true,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Categories',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: widget.availableCategories!.map((category) {
            final isSelected = _currentFilter.categories?.contains(category) ?? false;
            return FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                final currentCategories = List<String>.from(_currentFilter.categories ?? []);
                if (selected) {
                  currentCategories.add(category);
                } else {
                  currentCategories.remove(category);
                }
                _updateFilter(_currentFilter.copyWith(categories: currentCategories));
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSearchSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Search',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'Search in reports...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            _updateFilter(_currentFilter.copyWith(searchQuery: value.isEmpty ? null : value));
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () {
            setState(() {
              _currentFilter = const FilterState();
              _searchController.clear();
            });
            widget.onFilterChanged(_currentFilter);
          },
          child: const Text('Clear All'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: () {
            widget.onFilterChanged(_currentFilter);
          },
          child: const Text('Apply Filters'),
        ),
      ],
    );
  }

  void _updateFilter(FilterState newFilter) {
    setState(() {
      _currentFilter = newFilter;
    });
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}