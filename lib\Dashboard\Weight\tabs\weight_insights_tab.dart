import 'package:flutter/material.dart';

import '../controllers/weight_controller.dart';
import '../services/weight_insights_service.dart';
import '../models/weight_insights_models.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';

/// Weight Insights Tab - Displays AI-generated insights and recommendations
///
/// Architectural Pattern: Pure Dependency Injection (Ultimate Form)
/// - ALL dependencies REQUIRED via constructor for complete architectural purity
/// - ZERO knowledge of dependency creation/location within the widget
/// - Widget is a pure function of its inputs - perfect for testing and reusability
/// - Parent widgets are responsible for dependency resolution
class WeightInsightsTab extends StatelessWidget {
  final WeightController controller; // Required - pure dependency injection

  const WeightInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Pure dependency injection - all dependencies provided via constructor
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        // Check if we have data to display
        if (controller.totalWeightRecords == 0) {
          return UniversalTabEmptyState.forTab(
            title: 'No Data for Insights',
            message: 'Add weight records to get AI-powered growth insights and recommendations.',
            tabColor: AppColors.weightHeader,
            tabIndex: 2, // Insights tab
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () {
                // Navigate to records tab or show add dialog
              },
              tabColor: AppColors.weightHeader,
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(context),
              const SizedBox(height: 24),

              // Weight Growth Insights
              _buildWeightInsights(context),
              const SizedBox(height: 24),

              // Management Recommendations
              _buildManagementRecommendations(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.weightHeader,
            AppColors.weightHeader.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.weightHeader.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Weight Growth Insights',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Smart recommendations for optimizing your herd\'s growth',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildHeaderStat('Total Records', controller.totalWeightRecords.toString(), Icons.monitor_weight),
              ),
              const SizedBox(width: kSpacingSmall),
              Expanded(
                child: _buildHeaderStat('Avg Weight', '${controller.averageWeight.toStringAsFixed(0)}kg', Icons.trending_up),
              ),
              const SizedBox(width: kSpacingSmall),
              Expanded(
                child: _buildHeaderStat('Growth Rate', '${controller.averageGrowthRate.toStringAsFixed(1)}kg/mo', Icons.show_chart),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.8), size: 16),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16, // Reduced from 18 for better fit
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 11, // Reduced from 12 for better fit
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWeightInsights(BuildContext context) {
    // Pure dependency injection - use injected dependencies directly
    final insightsData = WeightInsightsService.calculateInsights(
      controller.unfilteredWeightRecords,
      controller.unfilteredCattle,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Growth Analysis',
          icon: Icons.trending_up,
          color: AppColors.weightHeader,
          subtitle: 'AI-powered insights and growth recommendations',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        if (insightsData.recommendations.isEmpty)
          _buildEmptyInsightsCard(context)
        else
          ...insightsData.recommendations.map((rec) => _buildRecommendationCard(context, rec)),
      ],
    );
  }

  Widget _buildManagementRecommendations(BuildContext context) {
    final insightsData = WeightInsightsService.calculateInsights(
      controller.unfilteredWeightRecords,
      controller.unfilteredCattle,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Management Recommendations',
          icon: Icons.lightbulb,
          color: AppColors.weightHeader,
          subtitle: 'Actionable recommendations for optimal weight management',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        ...insightsData.recommendations.map((rec) => _buildRecommendationCard(context, rec)),
      ],
    );
  }



  Widget _buildRecommendationCard(BuildContext context, WeightRecommendation recommendation) {
    // Get color based on priority
    Color priorityColor = _getPriorityColor(recommendation.priority);
    IconData categoryIcon = _getCategoryIcon(recommendation.category);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: priorityColor.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(categoryIcon, color: priorityColor, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: priorityColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  recommendation.priority,
                  style: TextStyle(
                    color: priorityColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            recommendation.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          if (recommendation.actionItems.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              'Action Items:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...recommendation.actionItems.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: priorityColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'nutrition':
        return Icons.restaurant;
      case 'health':
        return Icons.health_and_safety;
      case 'management':
        return Icons.settings;
      default:
        return Icons.lightbulb_outline;
    }
  }

  Widget _buildEmptyInsightsCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(Icons.lightbulb_outline, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Specific Insights Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add more weight records to unlock personalized growth insights',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
