import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../models/weight_record_isar.dart';
import '../models/weight_insights_models.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/weight_repository.dart';
import '../services/weight_insights_service.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
class WeightFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? cattleId;
  final String? measurementMethod;

  const WeightFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.cattleId,
    this.measurementMethod,
  });

  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (cattleId?.isNotEmpty ?? false) ||
      (measurementMethod?.isNotEmpty ?? false);

  static const WeightFilterState empty = WeightFilterState();
}

/// Enum for sort criteria
enum WeightSortBy {
  measurementDate,
  weight,
  cattleName,
  createdAt,
}

/// Enum for sort direction
enum SortDirection {
  ascending,
  descending,
}

/// Reactive controller for the main weight screen using Individual Stream Pattern
/// Following the breeding module template: separate streams for better reliability
/// Individual streams for weight records and cattle data
class WeightController with ChangeNotifier {
  // Repositories
  final WeightRepository _weightRepo = GetIt.I<WeightRepository>();
  final Isar _isar = GetIt.I<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Following breeding module pattern
  StreamSubscription<List<WeightRecordIsar>>? _weightStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Search debounce timer
  Timer? _searchDebounceTimer;
  static const Duration _searchDebounceDelay = Duration(milliseconds: 300);

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<WeightRecordIsar> _unfilteredRecords = []; // Complete dataset for analytics calculations
  List<WeightRecordIsar> _filteredRecords = []; // Filtered dataset for UI display
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  WeightInsightsData _insights = WeightInsightsData.empty;

  // Filter state
  DateTime? _startDate;
  DateTime? _endDate;
  String _searchQuery = '';

  // Sorting state
  WeightSortBy _sortBy = WeightSortBy.measurementDate;
  SortDirection _sortDirection = SortDirection.descending;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered weight records for UI display
  /// This is what the WeightRecordsTab should show
  List<WeightRecordIsar> get weightRecords => List.unmodifiable(_filteredRecords);

  /// Returns the complete unfiltered weight records for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<WeightRecordIsar> get unfilteredWeightRecords => List.unmodifiable(_unfilteredRecords);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  WeightInsightsData get insights => _insights;

  // Filter getters
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String get searchQuery => _searchQuery;
  bool get hasData => _unfilteredRecords.isNotEmpty;
  WeightSortBy get sortBy => _sortBy;
  SortDirection get sortDirection => _sortDirection;

  // Additional getters for tabs and screens
  List<WeightRecordIsar> get allRecords => _unfilteredRecords;
  List<WeightRecordIsar> get filteredRecords => _filteredRecords;
  List<CattleIsar> get allCattle => _unfilteredCattle;
  String? get error => _errorMessage;
  
  // Analytics getters
  int get totalWeightRecords => _unfilteredRecords.length;
  double get averageWeight => _insights.averageWeight;
  double get averageGrowthRate => _insights.averageGrowthRate;
  WeightInsightsData get analytics => _insights;
  
  // Helper methods
  CattleIsar? getCattle(String? cattleId) {
    if (cattleId == null) return null;
    return _unfilteredCattle.firstWhere(
      (cattle) => cattle.businessId == cattleId,
      orElse: () => _unfilteredCattle.first,
    );
  }

  // CRUD methods
  Future<void> addRecord(WeightRecordIsar record) async {
    await _weightRepo.saveRecord(record);
  }

  Future<void> updateRecord(WeightRecordIsar record) async {
    await _weightRepo.saveRecord(record);
  }

  Future<void> deleteRecord(int recordId) async {
    await _weightRepo.deleteRecord(recordId);
  }

  // Alias methods for compatibility
  Future<void> addWeightRecord(WeightRecordIsar record) async {
    await addRecord(record);
  }

  Future<void> updateWeightRecord(WeightRecordIsar record) async {
    await updateRecord(record);
  }

  Future<void> deleteWeightRecord(int recordId) async {
    await deleteRecord(recordId);
  }



  WeightController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates following breeding module pattern
  /// Using separate streams instead of StreamZip for better reliability
  void _initializeStreamListeners() {
    debugPrint('🔧 WEIGHT CONTROLLER: Initializing stream listeners (breeding module pattern)...');

    // Weight records stream - primary data source
    _weightStreamSubscription = _isar.weightRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((weightRecords) {
      debugPrint('🔄 WEIGHT STREAM: Received $weightRecords.length weight records');
      if (weightRecords.isNotEmpty) {
        debugPrint('   First record ID: $weightRecords.first.businessId');
      }
      _unfilteredRecords = weightRecords;
      _updateAnalytics(); // Update analytics immediately when weight records change
      _updateFilteredDataAndNotify();
    }, onError: (error) {
      debugPrint('❌ Weight stream error: $error');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to load weight data: $error';
      notifyListeners();
    });

    // Cattle stream
    _cattleStreamSubscription = _isar.cattleIsars.where()
        .watch(fireImmediately: true)
        .listen((cattle) {
      debugPrint('🔄 CATTLE STREAM: Received $cattle.length cattle records');
      _unfilteredCattle = cattle;
      _updateAnalytics();
    }, onError: (error) {
      debugPrint('❌ Cattle stream error: $error');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to load cattle data: $error';
      notifyListeners();
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredRecords = _unfilteredRecords;
    _hasActiveFilters = false;

    debugPrint('✅ WEIGHT CONTROLLER: Stream listeners initialized');
  }

  /// Update analytics using the complete unfiltered dataset
  /// Critical: This method ONLY uses unfiltered data to ensure analytics reflect the complete picture
  void _updateAnalytics() {
    try {
      if (_unfilteredRecords.isEmpty) {
        _insights = WeightInsightsData.empty;
      } else {
        _calculateAnalytics();
      }
      debugPrint('📊 WEIGHT CONTROLLER: Analytics updated');
    } catch (e) {
      debugPrint('❌ Error updating analytics: $e');
    }
  }

  /// Update filtered data and notify listeners
  void _updateFilteredDataAndNotify() {
    try {
      if (!_hasActiveFilters) {
        // No filters: filtered data equals unfiltered data
        _filteredRecords = List.from(_unfilteredRecords);
        _applySorting(_filteredRecords);
      }
      // If filters are active, filtered data is managed by the filtered stream

      _setState(ControllerState.loaded);
      debugPrint('🔔 WEIGHT CONTROLLER: Notifying UI listeners');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error updating filtered data: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to process weight data: $e';
      notifyListeners();
    }
  }



  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _insights = WeightInsightsService.calculateInsights(
      _unfilteredRecords, // Use unfiltered data for accurate analytics
      _unfilteredCattle,
    );
  }

  /// Apply filters at the database level for ultimate scalability
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters() async {
    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    final hasFilters = _searchQuery.isNotEmpty ||
                      _startDate != null ||
                      _endDate != null;

    _hasActiveFilters = hasFilters;

    if (hasFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery();

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredRecords = List.from(_unfilteredRecords);
      _applySorting(_filteredRecords);
      notifyListeners();
    }
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<WeightRecordIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredRecords = filteredList;

      // Apply sorting to filtered data
      _applySorting(_filteredRecords);

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on current filter state
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery() {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.weightRecordIsars.where();

    // Apply search filter at database level - searches cattle name and tagId
    if (_searchQuery.isNotEmpty) {
      final searchTerm = _searchQuery.toLowerCase();
      currentQuery = currentQuery.filter().cattle((q) => q
          .nameContains(searchTerm, caseSensitive: false)
          .or()
          .tagIdContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (_startDate != null) {
      currentQuery = currentQuery.filter().measurementDateGreaterThan(_startDate!);
    }
    if (_endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = _endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().measurementDateLessThan(inclusiveEndDate);
    }

    // Apply sorting at database level for optimal performance
    switch (_sortBy) {
      case WeightSortBy.measurementDate:
        currentQuery = _sortDirection == SortDirection.ascending
            ? currentQuery.sortByMeasurementDate()
            : currentQuery.sortByMeasurementDateDesc();
        break;
      case WeightSortBy.weight:
        currentQuery = _sortDirection == SortDirection.ascending
            ? currentQuery.sortByWeight()
            : currentQuery.sortByWeightDesc();
        break;
      case WeightSortBy.createdAt:
        currentQuery = _sortDirection == SortDirection.ascending
            ? currentQuery.sortByCreatedAt()
            : currentQuery.sortByCreatedAtDesc();
        break;
      default:
        currentQuery = currentQuery.sortByMeasurementDateDesc(); // Default sort
    }

    return currentQuery;
  }

  /// Apply sorting to the records list (for client-side sorting)
  void _applySorting(List<WeightRecordIsar> records) {
    records.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case WeightSortBy.measurementDate:
          comparison = (a.measurementDate ?? DateTime(0))
              .compareTo(b.measurementDate ?? DateTime(0));
          break;
        case WeightSortBy.weight:
          comparison = a.weight.compareTo(b.weight);
          break;
        case WeightSortBy.cattleName:
          final nameA = a.cattle.value?.name ?? '';
          final nameB = b.cattle.value?.name ?? '';
          comparison = nameA.compareTo(nameB);
          break;
        case WeightSortBy.createdAt:
          comparison = (a.createdAt ?? DateTime(0))
              .compareTo(b.createdAt ?? DateTime(0));
          break;
      }

      // Apply sort direction
      if (_sortDirection == SortDirection.descending) {
        comparison = -comparison;
      }

      // Secondary sort by createdAt if primary comparison is equal
      if (comparison == 0) {
        final secondaryComparison = (a.createdAt ?? DateTime(0))
            .compareTo(b.createdAt ?? DateTime(0));
        return _sortDirection == SortDirection.descending
            ? -secondaryComparison
            : secondaryComparison;
      }

      return comparison;
    });
  }

  /// Set date range filter
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    if (_startDate == startDate && _endDate == endDate) return;

    _startDate = startDate;
    _endDate = endDate;
    applyFilters();
  }

  /// Set search query filter with debouncing
  void setSearchQuery(String query) {
    if (_searchQuery == query) return;

    _searchQuery = query;

    // Cancel previous timer if it exists
    _searchDebounceTimer?.cancel();

    // Set up new debounced timer
    _searchDebounceTimer = Timer(_searchDebounceDelay, () {
      applyFilters();
    });
  }

  /// Set sorting criteria
  void setSort(WeightSortBy sortBy, SortDirection direction) {
    if (_sortBy == sortBy && _sortDirection == direction) return;

    _sortBy = sortBy;
    _sortDirection = direction;
    applyFilters();
  }

  /// Clear all filters
  void clearFilters() {
    _startDate = null;
    _endDate = null;
    _searchQuery = '';

    // Cancel any pending search debounce
    _searchDebounceTimer?.cancel();

    applyFilters();
  }

  /// Refresh all data - compatible with analytics tab refresh functionality
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      if (_unfilteredRecords.isNotEmpty) {
        _calculateAnalytics();
      }

      // Notify listeners of the refresh
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing weight data: $e\n$stackTrace');
      throw Exception('Failed to refresh weight data: ${e.toString()}');
    }
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  // _setError method removed - unused

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    // Cancel all stream subscriptions - following breeding module pattern
    _weightStreamSubscription?.cancel();
    _cattleStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
