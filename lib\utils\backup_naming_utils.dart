import 'package:intl/intl.dart';

/// Utility class for generating consistent, human-readable backup file names
class BackupNamingUtils {
  /// Generate a human-readable timestamp for backup files
  /// Format: YYYY-MM-DD_HH-MM
  /// Example: 2025-07-18_11-30
  static String _generateTimestamp() {
    final now = DateTime.now();
    final formatter = DateFormat('yyyy-MM-dd_HH-mm');
    return formatter.format(now);
  }

  /// Generate a local backup file name
  /// Format: CattleManager_YYYY-MM-DD_HH-MM.isar
  /// Example: CattleManager_2025-07-18_11-30.isar
  static String generateLocalBackupName() {
    final timestamp = _generateTimestamp();
    return 'CattleManager_$timestamp.isar';
  }

  /// Generate a cloud backup file name
  /// Format: CattleManager_{FarmName}_YYYY-MM-DD_HH-MM.json
  /// Example: CattleManager_MyFarm_2025-07-18_11-30.json
  static String generateCloudBackupName(String farmName) {
    final timestamp = _generateTimestamp();
    // Clean farm name: remove special characters and spaces, limit length
    final cleanFarmName = _cleanFarmName(farmName);
    return 'CattleManager_${cleanFarmName}_$timestamp.json';
  }

  /// Clean farm name for use in file names
  /// - Remove special characters
  /// - Replace spaces with underscores
  /// - Limit to 20 characters
  /// - Ensure it's not empty
  static String _cleanFarmName(String farmName) {
    if (farmName.isEmpty) {
      return 'Farm';
    }

    // Remove special characters and replace spaces with underscores
    String cleaned = farmName
        .replaceAll(RegExp(r'[^\w\s-]'), '') // Remove special chars except word chars, spaces, hyphens
        .replaceAll(RegExp(r'\s+'), '_') // Replace spaces with underscores
        .replaceAll(RegExp(r'_+'), '_') // Replace multiple underscores with single
        .replaceAll(RegExp(r'^_|_$'), ''); // Remove leading/trailing underscores

    // Limit length and ensure not empty
    if (cleaned.isEmpty) {
      return 'Farm';
    }

    return cleaned.length > 20 ? cleaned.substring(0, 20) : cleaned;
  }

  /// Check if a file name matches the new local backup naming pattern
  /// Used for cleanup operations
  static bool isNewLocalBackupFile(String fileName) {
    final pattern = RegExp(r'^CattleManager_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}\.isar$');
    return pattern.hasMatch(fileName);
  }

  /// Check if a file name matches the old local backup naming pattern
  /// Used for cleanup operations during transition period
  static bool isOldLocalBackupFile(String fileName) {
    final pattern = RegExp(r'^cattle_manager_\d+\.isar$');
    return pattern.hasMatch(fileName);
  }

  /// Check if a file name matches any local backup naming pattern (old or new)
  static bool isLocalBackupFile(String fileName) {
    return isNewLocalBackupFile(fileName) || isOldLocalBackupFile(fileName);
  }

  /// Check if a file name matches the new cloud backup naming pattern
  /// Used for cleanup operations
  static bool isNewCloudBackupFile(String fileName) {
    final pattern = RegExp(r'^CattleManager_\w+_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}\.json$');
    return pattern.hasMatch(fileName);
  }

  /// Check if a file name matches the old cloud backup naming pattern
  /// Used for cleanup operations during transition period
  static bool isOldCloudBackupFile(String fileName) {
    final pattern = RegExp(r'^cattle_manager_backup_.+\.json$');
    return pattern.hasMatch(fileName);
  }

  /// Check if a file name matches any cloud backup naming pattern (old or new)
  static bool isCloudBackupFile(String fileName) {
    return isNewCloudBackupFile(fileName) || isOldCloudBackupFile(fileName);
  }

  /// Extract timestamp from new backup file name for sorting
  /// Returns null if the file name doesn't match the new pattern
  static DateTime? extractTimestampFromNewBackupName(String fileName) {
    RegExp pattern;
    
    if (isNewLocalBackupFile(fileName)) {
      pattern = RegExp(r'CattleManager_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2})\.isar');
    } else if (isNewCloudBackupFile(fileName)) {
      pattern = RegExp(r'CattleManager_\w+_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2})\.json');
    } else {
      return null;
    }

    final match = pattern.firstMatch(fileName);
    if (match != null) {
      try {
        final timestampStr = match.group(1)!;
        final formatter = DateFormat('yyyy-MM-dd_HH-mm');
        return formatter.parse(timestampStr);
      } catch (e) {
        return null;
      }
    }
    
    return null;
  }
}
