.velydule effectimots  the Evenfor usingamples etailed exes dd providterfaces anll public iners aion covatent API documsiverehenis comp

Th
```e');
}error: $ted Unexpec print('
  (e) {  }
} catchtion}');
.operan: ${eperatio print('Ol) {
   ulration != nif (e.opeage}');
  essor: ${e.m errtabaseint('Da{
  pratch (e) eption ctabaseExc} on Da}');
  }
{e.fieldt('Field: $prin    {
ll) eld != nu(e.fiif ge}');
  e.messaerror: ${dation print('Vali (e) {
  tion catchxcepionEon Validatevent);
} .addEvent(rollerit cont
  awaart
try {

```dingdlError Han### ');
```

chmentsth} attaents.lengs ${attachmha'Event 
print(t-001');evenents('tLazyAttachmgeler.it controlments = awafinal attachzily
achments la
// Get att
);
)),30days: uration(().add(DateTime.now(),
  Dme.now(
  DateTidarEventstimizedCalener.getOpll = controventscalendarEal y
finentls efficint eveendar calet);

// Gs} events'totalItemult. of ${resngth}leitems.lt.suwing ${reint('Shoges}');
protalPault.tof ${res+ 1} .page esultPage ${r

print(': 20,
);pageSize
  e: 0,  pag
tedEvents(naPagier.getcontrollawait result = 
final tsven eaginated Get p//dart
mance

```erforn and PionatPagi
```

### ;arm',
)m Smith Fased frotes: 'Purch
  no.00,amount: 1500.now(),
  teTimedate: Da  e',
as'purchactionType: ans
  tr89',rans-7'tonId: ansacti,
  tr 'C001'agId:
  cattleTonEvent(ransactiteTcreace.onServiventAutomatiit Ewamatically
autot aaction eveneate transCr
// ,
);
00ost: 50.,
  cCheck: trueancyegndulePrsche  mination',
al Insed: 'Artificitho
  me.now(),DateTimeDate: ding56',
  breeng-4diree 'becordId:  breedingR,
agId: 'C001'tleTt(
  cateedingEveneBrce.creationServitomatwait EventAu
aallyt automaticreeding even/ Create b
/,
);
: 25.00cost  ination',
FMD vacc'Annual notes: ),
  ateTime.now(
  date: D,nation' 'vaccipe:  eventTyh-123',
 'healthRecordId:healt',
  'C001agId: 
  cattleTealthEvent(ce.createHervinSatioventAutomawait Etically
event automate health  Creaart
//

```d Automation`

###');
``cationsfig noti upcominngCountcomi('Sent $up();
printionsentNotificatpcomingEvndUrvice.setificationSenoait  aw =ingCount upcomnalmanually
fifications ing notiupcomend ;

// Sations')due notificoververdueCount nt('Sent $o;
pritions()tificaNodOverdueervice.sentionSt notificant = awaidueCouerov
final manuallys icationrdue notif/ Send ove
/;
nt-001')'eveminders(lEventRecancee.vicificationSer
await not reminders
// Cancelt);
inders(evenemleEventRheduervice.scnSnotificatioait e

aw befor houray and 1 d, 60]; // 1[1440utes = erMinmind
  ..red = truensEnableotificatio: 7))
  ..nn(daysd(Duratioow().ad DateTime.nDate =uledsched  ..
e'ination DuVacc= 'itle .t .
 'event-001'usinessId =   ..bventIsar()
 = El eventinan event
fers for aedule remind// Schdart
ons

```Notificati
### 
```
');lEvents}r.manuacontrollevents: ${ual e
print('Man');ratedEvents}autoGener.lle{contro $nts:enerated event('Auto-g
prionRate}%');tomati.auontrollere: ${ction rattomas
print('Au metric automationet);

// GntCost}'Eveverageller.antro$${covent: \ per ecostrage 
print('Avets}');talActualCosler.to{controlsts: \$$al co('Total actu}');
printoststedCalEstimaer.totontroll\$${ccosts: l estimated ta
print('Tost analysis
// Get co
});
events');ory: $count $categ{
  print('count) y, egor(catEach(n.foributiotegoryDistrory;
cayCategller.eventsBontroution = cribgoryDistl catea
finadattion istribu
// Get dts}');
endueEv.overtrollerents: ${con('Overdue evrinte}%');
ppletionRater.com ${controlltion rate:'Compleint(pr);
ents}'otalEvoller.tntrcovents: ${('Total enttrics
prit basic medart
// Gecs

```### Analyti`

ers();
``rFiltoller.cleatrrs
conltelear all fi/ C);

/ty.high,
ntPriori Eve  priority:ation',
vacciny: 'hQuerrc  seaheduled,
tStatus.scatus: Evenlth,
  steaategory.hgory: EventC(
  cateiltersipleFplyMultroller.apntters
coiliple fly mult
// App30)),
);
on(days: dd(Duratiw().a DateTime.no
 e.now(),
  DateTim(ltereFiteRangpplyDaroller.ater
conte range filat// Apply d

n');ccinatiohFilter('varcpplySea.a
controllerch filterpply sear
// Aealth);
ategory.hEventCyFilter(Categoroller.applyter
contrilry ftegoApply ca`dart
// ch

``and Sear# Filtering `

##);
``t-001'ennt('evr.deleteEveolleontrt
await ce even// Delete th);

',
ssfullyed succeon complet'Vaccinati',
  -001t(
  'eventteEvenoller.compleawait contrevent
ete the Complent);

// (evupdateEventtroller. cones';
awaitated nots = 'Updvent.notee event
ete thUpdat);

// vent(evendEntroller.ad
await cod the event
// Ad);
th,
egory.heal EventCatgory:  cate)),
 7(days:Durationd(ime.now().adate: DateTledD,
  scheduaccination'nual Vitle: 'Anon',
  t 'vaccinatid:peI
  eventTy01',C0: 'eTagId
  cattlent-001',essId: 'evbusinate(
  cretIsar.ent = Ev event
finala new evenate t
// Cre

```darManagementsic Event 

### Baese Exampl
## Usag;
}
```
 $message'eption:mationExc> 'Auto =g toString()rin
  Stverride);
  
  @o  }Id,
eRecord this.sourcModule,
   s.sourcehi   t {
 e,messag
    this.ption(nExceomatio const Aut
  
 ecordId;rceR? soual String fin
 record IDce e sour 
  /// ThceModule;
 ? sourringfinal St
   automationggeredtrihat  module tource /// The s 
 ;
 ng message final Stri message
 e error Th{
  ///on ents Exceptiion implemExceptionomatt
class Autars.

```dailmation ft autown when evenro
Thtion
xceponEAutomati

### 
}
```essage';tion: $mtabaseExcepng() => 'Da toStri
  String@override;
  
   })e,
 aus
    this.ceration,    this.opessage, {
his.m    t
ception(atabaseEx const D
 cause;
  ect?   final Objrror
rlying eundeThe // n;
  
  /peratioal String? o  find
t failen thae operatio  /// The;
  
ring messag
  final Stssageor meerr
  /// The on {Exceptiments on impleeExceptiabas
class Dat

```dartns fail.eratioase ophen databhrown wn

TceptiobaseEx

### Data;
}
```age'esson: $mExcepti'Validation => toString()ing 
  Strrride 
  @ove
  });
 s, this.detail
   ield,this.f{
    s.message,  thion(
   ionExceptiidat const Val  
  details;
ynamic>?g, dl Map<Strinils
  finaation detational valid
  /// Addi field;
  tring?n
  final Sioidatvalthat failed / The field 
  
  //ssage;ring meal St
  finor message erronvalidatie  {
  /// Thxceptionents Etion implemxcepdationEass Vali``dart
cl fails.

`lidation data va eventhen

Thrown wExceptionondati### Valins

eptio

## Exc```l;
}
stomIntervasCuortol get suppbols
  rvam inteorts custorn suppk if patte
  /// Chec
  val);e, int interaseDatDateTime bOccurrence(eNextatalculime c
  DateTrrence dateext occuculate n  /// Calerval;
  
ntdefaultI int get attern
 or the pl frvateinult  Get defa///  
  ;
ameget displayNtring 
  Sattern the pay name forGet displ//  / {
 ncePattern on RecurrensionernExteePattrrencion Recut
extensdar
```ods
tension Meth
#### Ex`
}
``  custom,
terval
 Custom in,
  
  ///
  yearlyEvery year
  
  /// nthly,h
  moy mont
  /// Every,
  ek
  weeklvery we
  /// Ely,
   daiEvery day
  {
  /// ePatternRecurrencrt
enum 
```dag events.
 recurrinforPatterns ern

ttrrencePa

### Recu
}
```t isUrgent;)
  bool geritical (high or crgentity is uiorpr// Check if er;
  
  /et sortOrdnt)
  int gurgegher = more ng (hie for sortimeric valunu Get  
  ///or;
 t col  Color gerity
ioprlor for the Get co 
  /// on;
 get ic IconData priority
 he  for ton/// Get ic 
  Name;
 splayget ditring iority
  Sor the pry name fladisp  /// Get ity {
entPrior on EvtensionorityExEventPritension 
ex
```dartn Methods
xtensio E`

####``itical,
}
ded
  cr neeentiongent attiority, urical pr
  /// Crit
  
  high,one soon be dldiority, shouigh pr
  
  /// H  medium,priority
/// Normal 
  
  
  low,eddelay can be rity,/// Low prio
  y {ioritenum EventPr```dart

.
entsfor evty levels Prioriity

ntPriorEve

### on;
}
```tineedsAttenet l gon
  boods attentihe event neendicates tstatus if /// Check i;
  
  veActibool get isve
  vent is acticates the e indistatus Check if 
  
  ///ed;et isCompletl gn
  boopletioicates comtatus indeck if s
  /// Ch color;
  etolor g  Che status
 for t/ Get colorn;
  
  //et icoconData gatus
  Ithe stor  Get icon f//
  
  /e;Nam displaying getatus
  Stre stme for thdisplay na  /// Get s {
n EventStatuExtension otStatuson Eventensi
exrtda

```dsMethoxtension ## E
```

##
}e,du over
 letedand not comp past due  isent /// Ev,
  
 
  cancelledelled cancent was  
  /// Evd,
letemped
  con completent has bee  
  /// Ev
gress,
  inProsresy in progrentl is curnt  /// Eve
  
eduled,
  schhe futured for t is schedule  /// Eventtus {
EventStat
enum 

```darevent.s of an tuCurrent staatus

ntStve### E


}
```ultPriority;t defatPriority ge Even
 tegory ca for therioritydefault pet 
  /// Gmation;
  upportsAutot sn
  bool geiotomatsupports auory if categeck // Ch 
  /or;
 et coly
  Color ghe categorlor for tGet co  /// ;
  
et icononData gegory
  Icatfor the cn et ico 
  /// GlayName;
  get disp
  Stringhe categorye for t display nam
  /// Getory {ategntC Even onoryExtensioventCategion Eextensart
ds

```dion Methons

#### Exte```r,
}
ts
  othelaneous even Miscel 
  ///,
 cial  finan and costs
ionsial transact Financ
  ///
  tenance,
  maintenanceacility main fipment andqu 
  /// E
 agement,
  man)ingving, sortgging, mot (tamenal manage// Generg,
  
  / feedin
 managementutrition  and n /// Feed
  
 ng,diing)
  breehecks, calvcy cegnan (mating, prvitiesctig aedin 
  /// Breth,
   healcheckups)
tments, ions, treats (vaccinatd evenlth-relate
  /// Heategory {enum EventCa```dart

nts.
ng eveanizies for orgategoriy

Cgorate## EventC
#ms

## Enu
```
essId); eventBusins(StringAttachmentgetLazyIsar>> tAttachmentist<Evenre<L`
Futu;
/// ``ments')ttach aents.length}{attachm $vent hasrint('E
/// p1');nt-00ents('eveyAttachmetLaztroller.gait con = awttachments/ final aart
//`d
/// ``/ Example:.
/// 
//optimizationazy loading th lts wittachmen of ast/ Returns li//nt
/// 
eveID of the e business Th] essIdeventBusin/// [nt
/// 
 eve an forattachmentsy-loaded / Get laz
);

//endDate,teTime ,
  Dame startDate DateTients(
 ndarEvCalezedetOptimi>> gsar<EventIe, ListimateTp<D``
Ma// `);
/
/// }nts');ength} eve${events.late.month}: {dte.day}/$ print('${da/  
// events) {te,((daents.forEachEvdar
/// calen
/// / );
//days: 30)),(Duration(addTime.now().Date///   now(),
   DateTime.//nts(
/ndarEveimizedCaleer.getOptontrollvents = cal calendarEinrt
/// f:
/// ```da Example/// 
e range
///End of dat/ [endDate] ge
// ran of dateate] StartrtDta
/// [s.
/// r renderinglendaicient cas for effo eventp of dates trns a ma
/// Retu range
/// ateents for dar ev calendptimizedt o

/// Ge
});ze = 50,  int pageSi 0,
 page =int  dEvents({
 getPaginateventIsar>>lt<EginatedResuuture<Pa```
F);
/// ength}'lt.items.lnts: ${resu/ print('Eveages}');
//totalPsult.1} of ${reage + lt.pPage ${resu/ print('
//);
/// 
/// 0,Size: 2///   page,
ge: 0   pants(
///atedEveinller.getPag controwait result = a// finalart
/```d:
/// / Example// 
//
/.etadatad mts anith even wnated resultagins p
/// Retur page
/// ts perof evener mbgeSize] Nupa// [based)
/(0- number age] Page// [p/ 
/ance
// for performnated events pagi
/// Getresh();
d> refvoie.
Future< be expensivy as it cangl/ Use sparinytics.
//es analalculatd recs aneamf all str refresh o/ Forces ata
/// 
//resh all da/// Ref``dart
ethods

` Utility M
####;
```
letionNotes)tring compd, SssIne(String busimpleteEventvoid> core<``
Futu);
/// `// ly',
/ccessfulcompleted suaccination  'V01',
///  ent-0 'ev
///  Event(er.complete controllwaitt
/// a/ ```darExample:
//
/// 
/// mpletionout conal notes abNotes] Optiocompletion/// [e event
s ID of thhe businesusinessId] T 
/// [b
///completedas ent k an evar
/// M);
sinessIdg burineteEvent(Stvoid> delture< ```
Fu);
///1'nt-00vent('eveer.deleteEtroll conwaitdart
/// a
/// ```Example:
///  ls
///faiete  if deln]eptiotabaseExc- [Da/// 
s invalidss ID ineusi] if borArgumentErr [ -
///hrows:/// Te
/// 
to delethe event ss ID of te busineId] Thness [busit
/// 
/// evenDelete an// vent);

/sar eventIateEvent(Eupdoid> re<vutu
F failsaten] if updtiotabaseExcep
/// - [Danvalidis i data ] if eventiononExceptatialid/// - [V/ Throws:

/// 
//ID)have valid pdate (must t to u] The evenevent [// 
///g event
/an existinUpdate 

/// );ntentIsar eveEvvent(> addE
Future<void;
/// ```ent(event)ddEver.a controllit
/// awa/// 
/// );
lth,egory.heantCatategory: Eve/   cs: 7)),
//ration(dayDuadd(().nowe: DateTime.cheduledDat   s',
/// Vaccination'Annuale: titl
///   on',ccinati: 'vaeIdyp   eventTC001',
/// 'tleTagId:at  c/ 01',
//t-0'even: ssId  busine
/// r.create(EventIsaal event = / fin`dart
//``ample:
///  Exs
/// 
///if save failtion] xcep [DatabaseE
/// - is invalidtaf event da inException]datioli:
/// - [Va/// Throws/ 
 to add
//] The event [eventt
/// 
///a new even
/// Add 

```darthodsRUD Met#### C

});
```
ng,isRecurri? 
  boolnerated,ol? isAutoGeId,
  bopentTyring? eveId,
  StTagg? cattle,
  Strin? prioritytPriority
  Even status,atus?entSt Evegory,
 y? catortegentCaate,
  Eve? endDim
  DateTte,ime? startDa,
  DateTrychQueg? seartrin  SFilters({
yMultiple
void appl/ ```// );
//
/: 30)),(daysonurati).add(DteTime.now( endDate: Daow(),
///  Time.nte: DateartDa',
///   stinationcc: 'vaarchQuery
///   seuled,atus.schedntSt: Evetus
///   staalth,Category.heory: Event categ/  (
//pleFiltersMultilytroller.app/// con// ```dart
ple:
/// Examy.
/// 
/imultaneouslters s fil severalpplyingmethod for aence Conveni/// / 
e
// at oncle filtersmultip// Apply ;

/ttleTagId)(String? caeFilterapplyCattlid 
vo```');
/// 001ter('CFilattleplyCtroller.aprt
/// conda/// ```le:
xamp/ 
/// Ey
// to filter btlef cat oag ID] TttleTagId 
/// [car
/// filtey cattle/// Appl
atus);
tStatus? stsFilter(Evend applyStatu/// ```
voiduled);
us.scheentStatFilter(Evusr.applyStatle controlrt
///```da// le:
// Exampy
/// 
//filter batus to  st Eventtatus]/ [s/ 
//ilter
//s fatu// Apply stry);

/go? cateCategoryter(EventilategoryFyC
void appl// ```h);
/tegory.healtter(EventCagoryFilateoller.applyCtrart
/// con
/// ```de: Exampl///// 
lter by
/ to firyEvent categoegory] [cat
/// / er
//ory filt categ/// Applye);

e? endDateTimtDate, Date? stareTimDatlter(eRangeFiat
void applyD// ```
/);//  30)),
/n(days:tio(Duradd.anow()   DateTime.///,
w()e.noDateTim  ter(
/// ilyDateRangeFplroller.aprt
/// cont ```da//
/le:mp
/// Exa/ e)
//ge (inclusiv ranof datete] End / [endDa
//sive)range (inclu of date rtartDate] Sta [st 
///
///ilterange fdate r/// Apply ;

hQuery)earcng strir(ShFilted applySearc ```
voi;
///cination')ter('vacearchFil.applyStrollerrt
/// con ```da
////// Example:// 
rch for
/o sea] Text tarchQuery/// [se/// 
ta.
elated da and r, notes,tionsips, descrtitlent ss evehes acro
/// Searc
/// filter onlyearch /// Apply s();

rsd clearFiltes.
voi all eventowto shr state lte fisets the/// 
/// Reilters
l active f Clear al
///,
});
false = EndDatebool clear,
  falsete = rStartDaea clBy,
  boolted? complen,
  Stringocatio  String? lg,
? isRecurrinted,
  boolAutoGenerais  bool? ypeId,
ring? eventT St
 leTagId,ing? catttrority,
  Srity? pri  EventPrios,
us? statu
  EventStatcategory,ntCategory? e,
  Eve? endDatateTimete,
  DDaTime? starty,
  Date searchQuering?ers({
  StrupdateFiltid lter
vote fir end da CleadDate][clearEnlter
/// - e fidatear start rtDate] CltaclearS/// - [son
y per Completed bedBy]/ - [completilter by
//cation to fion] Lo [locat// -urrence
/by recFilter g] [isRecurrinion
/// - omatauter by d] Filtenerate [isAutoGr by
/// -e to filteypt typeId] EventT[evenr by
/// -  to filtegId] CattleTa// - [cattleority
/ent priity] Ev- [prioratus
///  Event st - [status]
///egory category] Event// - [cat
/date rangeof ate] End  - [endD
///rangeof date t e] StarstartDat- [
/// search forxt to Query] Te// - [searchrs:
//// Parametea.
/// 
er criteriiltic fspecifating updhod for nience met 
/// Convers
///tedual parameith indivifilters w// Update 

/erState);lterState fiEventFiltFilters(void apply;
/// ```
 ))0)),
/// 3n(days:ioadd(DuratTime.now().tedDate: Da///   en.now(),
: DateTimeatestartD',
///   cinationy: 'vacuerearchQ/   s,
//scheduledtStatus.us: Evenstatth,
///   tegory.heal: EventCarycatego/   //State(
EventFilterlyFilters(er.apptroll/ conrt
///// ```daample:
/ 
/// Exto apply
//criteria filter ] The lterState/ 
/// [fi.
//nalyticsecting athout aff wieamered strilta separate ftes / Crea
/// 
// event listters to theApply fildart
/// thods

```# Filter Me
```

###cePattern;renecurByRet eventsring, int> g<StMappattern
rrence y recu grouped bents
/// Events;
eTimeEv ongetnts
int ne-time eveumber of o

/// NringEvents;ur rec getnts
intrring eveecuNumber of re;

/// rCattlPeerageEventst avgeble our cattle
de events pe Averagts;

///enthEvget cattleWis
int nth eve cattle witr of
/// Numbetle;
CateventsBy get  int>p<String,tle
Maped by catEvents grou

/// vent;ntsPerEageAttachmeeret ave g
doubler eventts pmene attach
/// Averags;
ttachment eventsWithAt getachments
in attwithents of evmber 

/// Nuts;achmenet totalAttts
int ghmenf attacl number o// Totaodule;

/BySourceMventset et> gtring, ine
Map<Smodulce  sourgrouped by/// Events onRate;

omatie get aute
doublentagrc as pe ratetion
/// AutomaalEvents;
 manuts
int getevenreated anually cumber of ms;

/// NEventeratedtoGenget auint d events
erateauto-gener of / Numb
//Cost;
rageEventet aveuble gcost
dorage event / Aveosts;

//tualCtotalAcdouble get  costs
l actual
/// Totats;
matedCost totalEstiuble gets
dod cosimateest Total pes;

///entTyEvctive ant gettypes
ievent ive er of act/// NumbTypes;

ventlEet tota g
intpest tyof evental number // To;

/nTimeageCompletiot averuble geurs
do time in hoetionge compl
/// Averah;
ontntsNextM eveh
int getontt m Events nexk;

///NextWee get eventst week
intexs n

/// EventsThisMonth;eventint get nth
his mots t/ Even

//;tsThisWeeket even gweek
int this 
/// Eventsrity;
entsByPrioet evint> gp<String, Maity
ory pried boup/ Events gr//tatus;

ntsByS get eveg, int>Map<Strintus
y stats grouped b Evenry;

///ategoByCtsen> get ev intMap<String,
 by categoryedEvents group;

/// ionRate complet gettage
doublepercenon rate as pleti;

/// ComtscomingEven upys)
int get(next 7 davents ing e of upcommber// NuEvents;

/et overduents
int gverdue eveber of o// Numents;

/edEvt schedulgent nts
iduled eveer of schemb/// NuEvents;

pletedet comt gts
ind evenpleteber of comNumts;

/// Evennt get totalevents
il number of 
/// Tota``dart

`pertiesalytics Pro
#### An
```
Filters; get currentrState
EventFilter state filte/ Current

//s;et analyticResult gtAnalyticsa
Evened daterunfiltm ted froalculas result cyticnal

/// Aattle;teredCr> get unfiltleIsaCat
List<stlittle nfiltered cate uComple/ ts;

//redAttachmen get unfiltechmentIsar>t<EventAttas
Lisntachmed atte unfiltereetpl/// Coms;

EventTypelteredunfieIsar> get <EventTyp
Listevent typesunfiltered omplete // C
/dEvents;
et unfiltere gentIsar>cy.
List<Evaccurato ensure ns alculatioics c
/// analyts used forand ievents  all contains always  listhis T// 
///
/icsfor analytered events nfiltlete u// Compevents;

/get <EventIsar> nts.
Listmpone con UI ilayedld be dispou
/// shand is whate lter stat fie currentreflects ths list 
/// Thiay
/// UI displ events for lterede;

/// FisagrorMesng? get ererror
Strie is  statmessage ifrror ;

/// Etete get staontrollerSta
Cler statet control// Curren
```dart
/s
ertietate Prop### S
```

#ontroller();
EventsCony injectipendencautomatic de with ontrollerreate c`dart
/// C``tor

### Construcstate.

#th reactive gement wimanar for event llen controller

MaitsControvenrs

### Eontrolle C```

##le,
);
r> cattattleIsa List<Cs,
  eventIsar>EventList<(
  ByCattleentsnt> getEvtring, ic Map<Sts.
staticounDs to event tag Ittle f caeturns map o
/// R
/// r contextttle list fo[cattle] Cayze
/// ents to anal Evs]ent/ 
/// [evion
// distributnttle eve/ Get cat
);

//,hments attacar>mentIsEventAttachList<  ents,
ar> evIsntist<Evecs(
  LentMetriAttachm calculate double)int,tatic (int, rEvent).
s, averagePentstachmesWithAtventachments, elAtt(totaecord with rns a r Retu 
///alyze
/// annts tots] Attachmeen// [attachmo analyze
/Events t[events] 
/// 
/// ent metricsattachmculate /// Cal);

tsvenntIsar> et<Evedule(LiseMotsBySourcenint> getEvp<String, atic Mants.
stated eve auto-generludes Only inc
///s.nt event coutole names e moduap of sourceturns m
/// 
/// R analyze toEventss] 
/// [event// 
/ulece modsournts by  eve/// Get;

ents)Isar> event(List<EvatenRmatioculateAutodouble cal
static ');
/// ```)}%AsFixed(1te.toStringe: ${raat rionomat print('Autents);
///nRate(evAutomatioce.calculatesServialytic = EventAn rate// final/ ```dart
/Example:
/// 
/// 0.0).
//10.0 to ed (0auto-generatwere t ents thaof evtage rcen Returns pe/// 
///o analyze
] Events t/// [eventsness
/// 
ffectiveutomation e aCalculates);

/// ar> event<EventIsetrics(ListstMlculateCo double) cae, double, (doublic```
stat/ ;
//sFixed(2)}')oStringA{average.te: \$$('Averag
/// print(2)}');ixedtoStringAsF${actual.tual: \$nt('Ac');
/// prixed(2)}FitoStringAsd.${estimated: \$imatent('Est;
/// privents)(etMetricsosateCalculicsService.cventAnalytge) = Etual, averaactimated, nal (es firt
///`da
/// ``/ Example:
//
/// ge). averaotalActual,ated, t(totalEstimith a record weturns /// 
/// Ro analyze
 cost data twithts  Even// [events]/// 
/ metrics
cost Calculate nts);

///r> evesa(List<EventIsByPriorityventnt> getE<String, iap.
static Mtst couns to evenity name priorrns map of 
/// Retuze
///nts to analy[events] Eve// 
/// ty
/y prioristribution bdint  Get eve///);

entsar> evIs<Eventatus(ListyStntsBetEve int> ging,tratic Map<Sunts.
stt comes to evenstatus nas map of 
/// Returne
/// nalyzs to as] Event [event/// 
///y status
ribution bt event dist;

/// Ge> events)EventIsarory(List<yCategsBgetEventnt> ring, itatic Map<St
s);
/// ```');
/// }ount events: $ct('$category  print) {
/// tegory, counrEach((caribution.fo);
/// distgory(eventsteetEventsByCacsService.galytitAnion = Eventributisinal d
/// fdart/// ```Example:

/// 
/// nts.to event couegory names  of cateturns map 
/// Ralyze
/// to anEvents[events] / 
/// 
//by categorytribution event dis/// Get ents);

sar> evt<EventIme(LisonTipletiageComculateAvercalouble c d
stati);
/// ```urs'xed(1)} hongAsFiritoStme.gTie: ${avimn tioge completprint('Avera/ events);
//letionTime(eAverageCompcalculatce.ticsServintAnalyTime = Evenal avg/ fi``dart
// `//Example:
//// 
/// ts.
mpleted even.0 if no cons 0etures.
/// Rpleted dated and comn schedultween hours betime irage turns ave// 
/// Reyze
/ents to analleted evts] Comp// [even/ 
/
//ventsme for empletion tie average coalculat

/// Cents);entIsar> ev(List<EvnRatepletioculateComle calstatic doub// ```
);
/ed(1)}%'oStringAsFixe: ${rate.tmpletion rat print('Co
///);ate(eventsmpletionRculateCoalcsService.calytitAn rate = Even
/// final ```dartxample:
///
/// E// 
/ 100.0).e (0.0 toas percentagion rate mplet/ Returns co
//
/// yzeents to analents] Ev 
/// [ev
///sor eventrate fletion ulate compalc
);

/// Car> cattle,tleIs
  List<Catttachments,ar> antIshmeacttList<EventA,
  pes> eventTyarentTypeIs<Evst
  Lients,sar> evEventI  List<Analytics(
alculatesResult calytic EventAnic```
stat/// 
osts}');ctualCics.totalAnalyt{al cost: \$$ta print('To;
///%')letionRate}lytics.comp rate: ${ana'Completionprint(/ 
//// );
/// 
/ttle,/   caments,
//attach/   tTypes,
//   evens,
///event   ytics(
///ulateAnallce.cayticsServic EventAnals = analyticnal/// fi/ ```dart
Example:
//// 
/// 
/etrics.lated mcuh all calult] witalyticsResntAn [Eve/ Returns/ 
//
//etricsper-animal mfor le attst of ctle] Li - [catlysis
///nts for anatachmeList of at] ttachments/ - [a
//or contextent types ff evpes] List o - [eventTyired)
///alyze (requevents to anof  List ts] - [evenmeters:
///
/// Paracs.
/// ailed metris detd returnanta ided daprovlyzes the  Ana/// 
///nts
cs for eveve analytinsipreheulate com/ Calc//art
ds

```detho Static M

####nsights.lytics and i anarehensivemp corovides
Pice
nalyticsServentA

### Evings);
```r settingsIsaionSettificats(NotnSettingificatiootpdateN uuture<void> settings
Ftification nos] The newngetti 
/// [s
/// eventsforon settings otificati// Update ns();

/ationSettingificsar?> getNotingsIicationSettre<Notif
Futunces.on preferetificatit norencururns the // 
/// Ret
/or eventss fettingtification sGet no);

/// ations(ictifmingEventNocot> sendUpFuture<ins sent.
tificationof noer e numbReturns th
/// 
/// lly.nuad malleca be so/ but can al//ng,
essiund proc backgro bytomaticallys called auod ihis meth/ T
/// 
//ns.catioer notifiremindand sends e due soon t artha events /// Finds// 
ng events
/mi upcoons forcatiotifi Send n

///();icationstifeNoendOverduuture<int> s sent.
Fnstioof notificanumber urns the 
/// Ret/// manually.
 be called so but can al///,
d processingrounackg by bticallymad autocalleethod is / This m
//ns.
/// notificatioappropriate nds // and sed,
/t completet noe buduled datir schepast the that are ts eveninds all/// 
/// F events
dueor overns fificatio/// Send not;

Id)inessentBusring evReminders(StlEventncecature<void> 
/// ```
Fuvent-001');rs('entRemindeEve.cancelrviceSetificationnoit  awaart
///// ```de:
/
/// Exampl/ t
// evens ID of thee busines ThtBusinessId]/// [evenled.
/// 
cancelleted or comps event in an led whe calypically
/// T// t
/evenrs for an inderemll Cancel a
/// vent);
entIsar e(EvRemindersuleEventoid> sched
Future<v``);
/// `enteminders(evEventRscheduleervice.ationSait notific
/// aw/// 
efore bnd 1 hour 1 day a40, 60]; //tes = [14Minu.remindere
///   .tru = tionsEnablednotifica ..///  
days: 7))dd(Duration(.now().ateTimee = DascheduledDat///   ..'
Duetion cinaVacle = '.tit///   .event-001'
 = 'Idusiness//   ..btIsar()
/vent = Even/ final e
//art/ ```dple:
// Exam
/// 
///nvalid is arettinger se remindn] iftioionExcepdat - [Valilid
///nvas null or iif event ientError] rgums:
/// - [Arow// Th
//// intervals
er ndmih ret witutes` lisminderMin `re
/// -trueset to ed` onsEnablficatioti/// - `ne:
ust havevent m
/// The  for
/// eminders schedule rent to The evevent]/ [
/// 
//settings. reminder nt's the evebased onications tifreates no/ 
/// C event
//nders for an remiuleched
/// S
```dartn Methods
otificatio
#### N`
se();
`` dispoe.
voide servicg down thin shutt when Call thisimers.
/// teans upg and clessin procgroundStops back/// es
/// 
sourcspose of re

/// Dialize();void initiion.
zatapp initialionce during is l th.
/// Calionsotificatmatic ning for autoocessd pr backgroun
/// Starts
/// servicefication ti noize thetial/// Init
``dards

`ecycle Metho
#### Lif```
});
ice,
rServrvice? isa,
  IsarSeryionsReposito? notificatitorynsRepostificatio
  Norvice({icationSentNotifction
Evejein dependency ith wservicetification eate no
/// Crrttor

```da## Construcents.

##nders for evand remiions atotific nges

ManationServiceNotificaentEv# `

##
});
``e,trutWeighing = cheduleNexl s
  booe date,ateTimuired D
  reqle weight, doubuiredeqId,
  rTagng cattleed Stri  requirt({
venightEteWereaoid> ce<vuturtic Fta
/// ```
s,
/// ); trueWeighing:xtduleNe///   sche(),
e.nowate: DateTim///   d 450.5,
ght:/   wei//01',
Id: 'C0   cattleTagtEvent(
///createWeighe.vicSerionAutomatent// await Evart
/```dmple:
/// /// Exa)
/// 
fault: trueighing (det wele nexer to schedughing] WhethxtWeileNe- [schedu/// 
equired)ighing (r] Date of weate/ - [dequired)
//d weight (r Recorde[weight]d)
/// - uireeq(rvent is for le this ettThe ca] attleTagId/// - [cters:
Parame// 
//// rements.
easueight mre wchedule futu.
/// Can se addedecords arght rweihen ally wed automatic
/// Call//  event
/ monitoringweightCreate a 
/// );
otes,
}ing? nnt,
  Strle? amoue,
  doubteTime datrequired Da
  onType,nsactiString trad ire
  requctionId, transared String
  requiagId,ttleT cangequired Stri rnt({
 nsactionEvecreateTraid> ture<votic Fu
sta
/// ```,
/// );rm'th Farom Smiurchased fnotes: 'P
///   1500.00,mount: (),
///   aateTime.now  date: D
/// se',urchaType: 'pnsaction
///   trans-789','tra: actionId trans/  'C001',
//TagId:   cattle/// (
ctionEventnsace.createTrarvinSeutomatioventAawait Eart
/// / ```d
//ple:/ 
/// Exam notes
//ptional Ootes]// - [n
/ounton amansactint] Tr [amoured)
/// -ction (requiransaof tte] Date  [dared)
/// -equiansaction (r tre] Type ofctionTypsa- [tran)
///  (requiredtransactiono the ence tionId] Refersact- [tran/// 
quired) for (reent is evcattle thisId] The - [cattleTagrs:
/// amete/ Par/// 
//orded.
ecre ractions ansy when tracallled automati 
/// Cald event
///n-relatesactioane a tr/// Creat
cost,
});

  double?  true,cyCheck =Pregnanhedule scpe,
  boolrTydOlIing? bulhod,
  Str metString?ngDate,
  ediateTime brered D
  requi,dingRecordId breered Stringd,
  requicattleTagIg rinred Stequi
  r({dingEventeateBreeid> crre<votustatic Fu
/ ```// );
//0.00,
/  cost: 5rue,
/// ancyCheck: tdulePregn   sche#123',
///ll ein Bupe: 'HolstullIdOrTy  bn',
///  Inseminatiocial 'Artifi method:///  ime.now(),
eTe: DatngDat breedi  //456',
/ 'breeding-ecordId:ngR breedi/  ',
//Id: 'C001tleTag//   cat
/dingEvent(Breervice.createAutomationSeawait Eventdart
/// e:
/// ```
/// Examplivity
/// eeding actf brst oost] Co- [cue)
/// ult: trefay check (dregnance phedulto sck] Whether hecPregnancyC- [schedulesed
/// D or type upe] Bull IOrTybullId
/// - [etc.)ral, AI, d (natu metho Breedingthod])
/// - [me(requiredding te of breeDate] Daeedingbr- [ed)
/// d (requir recorthe breedingto erence ecordId] RefgRdin- [bree
/// red)s for (requient ie this ev] The cattlttleTagId
/// - [ca:ersramet Pa///// 
/.
 requested checks if pregnancyes follow-upso schedulAl/ eated.
//are crds ing recor breedy whenallticlled automa/// Ca
/// 
ated eventeding-rele a bre
/// Creat
st,
}); double? coreatment,
 g? tintrnosis,
  S diagg?  Strin,
 notesString? date,
  red DateTimerequientType,
  String evrequired ordId,
  lthRecing heaquired Str
  reTagId,ing cattleequired Strt({
  rlthEven> createHeavoidure<ic Fut/ ```
stat/ );
//25.00,
// cost: n',
///   vaccinatioAnnual FMDnotes: 'w(),
///   DateTime.no///   date: on',
tiaccinaype: 'vtTven
///   e23',th-1ordId: 'heal  healthRec1',
/// TagId: 'C00le
///   cattent(althEvHervice.createAutomationSewait Eventt
/// a
/// ```dar// Example: 
////ls
ve faif saxception] itabaseEDa - [d
///is invali] if data ceptionlidationEx
/// - [Vaare missingrameters red paor] if requi[ArgumentErr- ws:
/// ro
/// Thtivity
/// alth ache the ofcost] Cost ed
/// - [steradmini Treatment nt]me/// - [treat
cablesis if appliagnocal diosis] Medi - [diagnl notes
///s] Optiona- [note// uired)
/y (reqth activit the healDate ofe] /// - [datd)
(requirealth event of heype] Type ntT/// - [eveequired)
 (rh recordltthe hea to erenceRefthRecordId] [heald)
/// - equiret is for (r this even] The cattleattleTagId- [c
/// arameters:
/// P
/// re created.records ahealth en whically utomat Called a/// 
///ted event
h-rela healte aCreat
/// rt
```daMethods
ic 

#### Statles.duer mo othtion from event creaes automatic

HandliceServionmatto# EventAu

##rity);
```y prioEventPrioritPriority(EventsByIsar>> get<List<Eventre
Futulter byy to fiprioritity] The /// [prior 
ity
///s by prior Get event;

///egory)y cattegorEventCaCategory(entsBygetEvntIsar>> e<List<Eveutury
F bltertegory to fi] The caorytegca 
/// [tegory
///y caents b
/// Get evatus);
Status sttus(EventventsBySta>> getEst<EventIsar
Future<Li by to filterhe statustatus] T/// [s/ 
tus
//ents by sta ev Get]);

///nt days = 7ts([iEventUpcoming>> getIsarenist<Evure<Llt: 7)
Futahead (defauo look s tf dayumber o// [days] Ns
/// 
/pecified dayithin s events wcoming/ Get upts();

//enrdueEvOvear>> getst<EventIs<Liutureompleted.
Fnot cbut st the paed in schedulre vents that a e
/// Returnsents
///  evt overdue
/// GeeTagId);
attlg ce(StrinntsByCattlgetEve>> st<EventIsarre<Lie
Futu the cattl oftag ID The gId]eTa
/// [cattl
/// c cattlecifir a spe events foet
/// G end);
DateTimeart, ateTime steRange(DyDatntsBsar>> getEveList<EventI```
Future<);
/// 0)),
/// n(days: 3(Duratio().add.nowateTime,
///   Dw().no/   DateTime
//teRange(etEventsByDaepository.gait rts = aw/ final even``dart
//
/// `/ Example:
///// usive)
inclte (d da [end] Ene)
///lusivdate (incrt] Start  
/// [staange
///ate r d aithinents wt ev;

/// Gevents()ar>> getAllEst<EventIs.
Future<Liadents] instelEvwatchAl [s, useupdatee eal-timor r
/// F of call.met the till events anapshot of aturns a s 
/// Re)
///snapshots (vent all e/// Get```dart
 Methods

## Query`

##nt id);
``ttachment(ileteEventA> de
Future<voidase IDatabts d ittachment by an event alete// Det);

/chmenr attahmentIsaentAttacttachment(EvaveEventAvoid> s
Future<hmentvent attacupdate) an eate or cre
/// Save (t id);
ntType(inve> deleteEoid
Future<vdatabase IDe by its an event typ Delete 
///
e);r eventTyppeIsaEventTyventType(saveEuture<void> ent type
F ev) antete or upda(crea/// Save 

t(int id);leteEvend> deture<voi fails
Fuationte operion] if deleseExcept[Databalid
/// - D is invaf Irror] irgumentE [A -:
///hrows T///

/// ID)siness  ID (not buaseatab The Isar d [id] 
///
/// database IDevent by itselete an t);

/// DtIsar evenvent(Evenoid> saveEFuture<v ```
vent);
///vent(eory.saveErepositt wai 
/// a );
///health,
///y.goratetCtegory: Even ca  : 7)),
///uration(daysnow().add(DTime.e: DateatscheduledDon',
///   inatinnual Vacc'Ae: //   titlon',
/cinatiac'vd: eI   eventTyp
///gId: 'C001',attleTa
///   cvent-001',sId: 'esines bu//  create(
/tIsar.= Evenvent 
/// final e``dart/// `/ Example:
//ls
/// 
ation faif save operException] i- [Database
/// invalidl or vent is nulor] if etErr/ - [Argumen:
//
/// Throws
/// ent to saveThe ev [event] .
/// 
///nctionalityve upsert fuatises Isar's n// 
/// Un event
/r update) ae o Save (creatdart
///

```ethodsRUD M
#### CsId);
```
sines eventBuStringments(hEventAttachwatcar>> entIsEventAttachmream<List<e event
StID of thhe business sinessId] T/ [eventBut
/// 
//evena specific or ments fatch attach// W;

/()EventTypestchAllIsar>> waventTypeam<List<Eres
Sttime update real- withesent typ all ev
/// Watch
();Events> watchAllt<EventIsar>
Stream<Lis ```/// });
///;
ength}')nts.l: ${evets updatedEven  print(') {
/// n((events).listehAllEvents(tory.watcsipoentsRe ev```dart
///ple:
/// amEx// 
/// 
/updates.ctive UI  reafor Use this ///hange.
ts cver evenits whenethat emm s a strea
/// Returns
/// teupda-time h realwitevents / Watch all art
//

```dhods Stream Met
####ce);
```
 isarServirServiceory(IsaventsRepositection
Ependency injory with de repositate
/// Cre
```darttor
### Construc
#ons.
tise operay for databatorreposie reactive tory

PurntsReposi## Eveces

#

## Serviate();
```esult validionRdatta
Validament ttache aidat Val//

/;s()ledeleteFid> Future<voifiles
ed ete associatDelts;

/// isget fileExure<bool> ut
F existsck if file;

/// ChensionExtefile? get ing
Stre extension/ Get filze;

//SiormattedFile get fe
Stringile siz formatted f/ GetVideo;

//isl get o
boot is a videmench if atta/// Checkment;

t isDocul gement
boo a docuttachment iseck if a

/// ChImage; get isboolage
nt is an im if attachme
/// Check``dart

`dsho MetNo |

#### | stampreation timeme?` | C`DateTiedAt` | |
| `creato path | N Thumbnail  `String?` |bnailPath` |thum | No |
| `t (if image) heighmageint?` | IHeight` | `mage
| `ie) | No |(if imagge width ma `int?` | IageWidth` |
| `im No |pe |IME tyng?` | M| `StriType` me|
| `mibytes | No in | File size int?` | `eSize`  `fil|
|ry | No ype catego` | File tring?e` | `StfileTyp |
| `ath | Nocal file ptring?` | Loh` | `S `filePat |
|ame | No filenginal Orig?` |e` | `StrinileNam Yes |
| `fD |ent Ievssociated ?` | Ang | `Strid`sinessI`eventBuYes |
| e ID | abasrated datto-gene | `Id` | Au`id`-|
| |----------------------|--------|------
|-ed | | Indexiptionescr Dy | Type |

| Properterties
#### Prop
for events.attachments le esents fir

ReprtachmentIsa### EventAt
```

lidate();ult vaResionata
Validatnt type de eveidatal

/// Vs);namic> ruleing, dyap<StrRules(Mmationoid setAutoMap
vfrom s tion ruleSet automa
/// tionRules;
 automamic>? geting, dyna
Map<Stres as Map rulautomation Get ;

///mationtopportsAut sugeol mation
bots auto type supporif/// Check con;

 get inDataonData
Icocon as Ic// Get i;

/or get color object
Colr as Coloret colodart
/// Gds

``` Metho###`

#
});
``true,= sActive   bool im,
iuriority.med= EventPultPriority ty defa EventPrioriription,
 sc? deString
  tegory,tCategory caenred Evuiname,
  reqed String  requirssId,
 tring businerequired S  eate({
peIsar.cr EventTy
factoryent typete a new ev// Creat
/
```dar
torsry Construc### Facto | No |

#e timestamp| Last updat`DateTime?` atedAt` | o |
| `updp | Nimestam tonatime?` | CreDateTiedAt` | `creatNo |
| `ctive |  Is abool?` |tive` | `Ac
| `isce | No |currenreult Defa| tern?` ncePaturre | `RecncePattern`ultRecurrefa |
| `derrence | Nopports recu| Suol?` rence` | `bocurRe `supports|
|ules | No omation rN auting?` | JSO `StrnRules` |Generatio `auto No |
|created |e auto-` | Can bool?ed` | `btoGenerat| `canBeAu | No |
ionpected duratint?` | Exnutes` | `onMitDuratidefaul
| `s | No |lt reminder>?` | Defau<ints` | `ListinuteminderM| `defaultRety | No |
ori pri` | Defaultrity?| `EventPrioiority` aultPr `defo |
|r for UI | N` | Colo | `String?| `colorHex`r | No |
 identifie | Icong?`rin `St |Name`o |
| `iconegory | Natry?` | CntCatego | `Eve`category` |
| ription | No` | Desc` | `String?iptionscr| `de | No |
isplay name` | D| `String?` me| `na(Unique) |
 Yes ier |s identifsines| Unique buring?`  `St |essId`inbus| `ID | Yes |
d database ateuto-gener `Id` | A| `id` |---------|
---------|-----|------|-
|--------ed | | Indexoncripti Desrty | Type |

| Properties# Propeles.

###tion ruutomaings and aefault settwith d events s types offinear

DentTypeIs### Eve`

e();
`` validatonResultValidati
datavent ate e/// Validrence();

xtOccurateNeenerntIsar? gents
Eveg evor recurrin frencee next occur// Generat
/
edDate,
});e? complet
  DateTims,ionNotecomplet?  Stringy,
 edBmpletg? co({
  Strinompleted markCted
voidas compleMark event / ation;

//nDurt completioring? gen
Stmpletiosince cod duration mattefort ;

/// GeduledtilSche get daysUne)
interdue if ovte (negativdaduled sche until  days

/// GetCompleted;l get ised
booomplet cf event is/// Check ipcoming;

ool get isU
bext 7 days)ng (within ns upcomit i if even/ Check//

 isOverdue;bool get
t is overdueeven Check if art
///``dhods

`

#### Met);
```dId,
}urceRecor sod Stringrequiodule,
  reeMg sourcrinSt required y,
 gorry cateentCategoquired Eve,
  redDatcheduleDateTime sired ,
  requitleed String tir
  requTypeId,venttring ed Sire  requd,
g cattleTagIred Strin requisinessId,
  String buuired
  reqd({utomateIsar.aEventry 
factoer modulenotht from avenmated e autote anea// Cr
/
edium,
});rity.mntPrioveority = Eity priEventPriorled,
  scheduStatus.= Events status  EventStatuategory,
  cntCategoryrequired EveedDate,
  schedulTime quired Dateitle,
  retring td S,
  requirepeIding eventTyired Str
  requId, cattleTagred StringequiessId,
  rsining burequired Str
  ar.create({tIsveny E
factorfieldsred ith requivent w eew Create a n
///
```dart
onstructorsory Cct

#### Fap | No |tamimesst update t?` | LaateTimedAt` | `D `update| No |
|p am timesttionme?` | CreaeTi`Datt` | createdA
| ` | No |on notesmpletig?` | Cotrin`S| tionNotes` `comple|
| o  event | No completedng?` | Wh`Stri` | etedByplo |
| `coms | Ncondition| Weather | `String?` Conditions` weather No |
| ` location |` | Eventring?tion` | `St|
| `loca cost | No  | Actual | `double?``actualCost`| No |
| imated cost Estble?` | st` | `doutedCoima| `est | No |
rvalsnder inte Remit>?` |st<in` | `LiMinutesreminder|
| `ed | No s enablcation` | Notifi `bool?d` |ionsEnableat
| `notific ID | No |ent eventg?` | Pard` | `StrinntIparentEveNo |
| `d date | rrence ene?` | RecuteTimdDate` | `DaeEncurrencre| No |
| `erval urrence intt?` | Recerval` | `inIntrrence| `recurn | No |
rrence patteern?` | RecuencePatt `Recurrttern` |Pa `recurrenceNo |
|vent | recurring e | Is ol?`ing` | `borrRecuo |
| `isence | Nd refercorreource | String?` cordId` | `SsourceRe
| `o | name | N module?` | Source` | `StringeModule|
| `sourcly | No utomaticalted a | Crea` | `bool?`rated`isAutoGene
| | No |rity level Prio?` | tPriorityity` | `Even
| `prior |s | Yes statu | Current?`Status | `Eventus`| `stat|
| No d completeent was  | When evteTime?`e` | `DaetedDat|
| `comples d | Yulent is sched` | When eveTime? | `Datete`Da`scheduled| s | No |
itional noteing?` | Addes` | `Str| `notn | No |
d descriptio?` | DetaileStringion` | `
| `descript | No |itleent tng?` | Evtle` | `Stri
| `tiYes |tegory | ca| Event ?` ventCategoryegory` | `E| `cat | Yes |
erence refEvent typering?` |  `SteId` |ypventT `eD | Yes |
|e tag Ied cattlatcisso A `String?` | |Id`ttleTag`ca |
| ique)(Uns | Yer ifieent idess busin?` | Uniquetring | `SusinessId`|
| `bes abase ID | Yted daterauto-gend` | A`id` | `I--|
| -|------------------|------|-|----------dexed |
 Inescription | DType |operty |  Pres

|### Properti

#vities.ement actitle managng catntidel represere event mo

The co EventIsar

###ls Mode

##-examples)(#usageamples]Usage Ex
6. [ceptions)exxceptions](# [Es)
5.enum](#)
4. [Enumsrollerscontollers](#)
3. [Contreservices](#sic
2. [Serv#models)Models](. [s

1ontentle of C# Tabon

#tatiAPI Documenle s Modu# Event