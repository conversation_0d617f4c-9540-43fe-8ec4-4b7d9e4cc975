import 'package:logging/logging.dart';
import '../../services/database/exceptions/database_exceptions.dart';

// Import typed models
import '../../Dashboard/Cattle/models/cattle_isar.dart';
// import '../../Dashboard/Health/models/health_record_isar.dart';
// import '../../Dashboard/Breeding/models/breeding_record_isar.dart';
// import '../../Dashboard/Breeding/models/pregnancy_record_isar.dart';
// import '../../Dashboard/Transactions/models/transaction_isar.dart';

// Import repositories for cross-record validation
import '../../Dashboard/Cattle/services/cattle_repository.dart';
import '../../Dashboard/Farm Setup/services/farm_setup_repository.dart';
// Removed missing import: user_repository.dart
import '../../Dashboard/User Account/models/user_isar.dart';
import '../../Dashboard/User Account/models/user_session_isar.dart';
import '../../Dashboard/User Account/models/user_settings_isar.dart';
// import '../../Dashboard/Health/services/health_repository.dart';
// import '../../Dashboard/Breeding/services/breeding_repository.dart';
// import '../../Dashboard/Transactions/services/transactions_repository.dart';

/// Service for handling validation logic across database operations
/// Rewritten to use strongly-typed models and proper dependency injection
class ValidationService {
  final Logger _logger = Logger('ValidationService');

  // Repository dependencies injected via constructor
  final CattleRepository _cattleRepository;
  final FarmSetupRepository _farmSetupRepository;
  // final UserRepository _userRepository; // Removed missing dependency
  // final HealthRepository _healthRepository;
  // final BreedingRepository _breedingRepository;
  // final TransactionsRepository _transactionsRepository;

  // Constructor with explicit dependency injection
  ValidationService(
    this._cattleRepository,
    this._farmSetupRepository,
    // this._userRepository, // Removed missing dependency
    // this._healthRepository,
    // this._breedingRepository,
    // this._transactionsRepository,
  );

  //=== CATTLE VALIDATION ===//

  /// Validate a cattle record with strongly-typed model
  Future<void> validateCattle(CattleIsar cattle, {bool isUpdate = false}) async {
    try {
      // Required field validation
      if (cattle.tagId?.isEmpty ?? true) {
        throw ValidationException('Tag ID is required');
      }

      if (cattle.name?.isEmpty ?? true) {
        throw ValidationException('Name is required');
      }

      if (cattle.animalTypeId?.isEmpty ?? true) {
        throw ValidationException('Animal type is required');
      }

      // Tag ID format validation
      if (cattle.tagId!.contains(' ')) {
        throw ValidationException('Tag ID should not contain spaces: $cattle.tagId');
      }

      // Cross-record validation: Check if animal type exists
      if (cattle.animalTypeId != null && cattle.animalTypeId!.isNotEmpty) {
        final animalType = await _farmSetupRepository.getAnimalTypeById(cattle.animalTypeId!);
        if (animalType == null) {
          throw ValidationException('Animal type not found: $cattle.animalTypeId');
        }
      }

      // Cross-record validation: Check if breed exists
      if (cattle.breedId != null && cattle.breedId!.isNotEmpty) {
        final breed = await _farmSetupRepository.getBreedCategoryById(cattle.breedId!);
        if (breed == null) {
          throw ValidationException('Breed not found: $cattle.breedId');
        }
      }

      // Check for duplicate tag ID (only for new records or when tag ID changes)
      if (!isUpdate || cattle.id == 0) {
        final existingCattle = await _cattleRepository.getCattleByTagId(cattle.tagId!);
        if (existingCattle != null && existingCattle.id != cattle.id) {
          throw ValidationException('Tag ID already exists: $cattle.tagId');
        }
      }

      _logger.info('Cattle validation passed for: $cattle.tagId');
    } catch (e) {
      _logger.warning('Cattle validation failed: $e');
      rethrow;
    }
  }
  
  //=== BREEDING VALIDATION ===//

  // /// Validate a breeding record with strongly-typed model
  // Future<void> validateBreedingRecord(BreedingRecordIsar breedingRecord) async {
  //   try {
  //     // Required field validation
  //     if (breedingRecord.cattleId?.isEmpty ?? true) {
  //       throw ValidationException('Cattle ID is required for breeding record');
  //     }

  //     if (breedingRecord.date == null) {
  //       throw ValidationException('Date is required for breeding record');
  //     }

  //     // Cross-record validation: Check if cattle exists
  //     if (breedingRecord.cattleId != null && breedingRecord.cattleId!.isNotEmpty) {
  //       final cattle = await _cattleRepository.getCattleByTagId(breedingRecord.cattleId!);
  //       if (cattle == null) {
  //         throw ValidationException('Cattle not found: $breedingRecord.cattleId');
  //       }

  //       // Validate that cattle is female for breeding
  //       if (cattle.gender != CattleGender.female) {
  //         throw ValidationException('Only female cattle can have breeding records');
  //       }
  //     }

  //     // Cross-record validation: Check if bull exists if bullId is provided
  //     if (breedingRecord.bullId != null && breedingRecord.bullId!.isNotEmpty) {
  //       final bull = await _cattleRepository.getCattleByTagId(breedingRecord.bullId!);
  //       if (bull == null) {
  //         throw ValidationException('Bull not found: $breedingRecord.bullId');
  //       }

  //       // Validate that bull is male
  //       if (bull.gender != CattleGender.male) {
  //         throw ValidationException('Bull must be male cattle');
  //       }
  //     }

  //     _logger.info('Breeding record validation passed for cattle: $breedingRecord.cattleId');
  //   } catch (e) {
  //     _logger.warning('Breeding record validation failed: $e');
  //     rethrow;
  //   }
  // }

  // /// Validate a pregnancy record with strongly-typed model
  // Future<void> validatePregnancyRecord(PregnancyRecordIsar pregnancyRecord) async {
  //   try {
  //     // Required field validation
  //     if (pregnancyRecord.cattleId?.isEmpty ?? true) {
  //       throw ValidationException('Cattle ID is required for pregnancy record');
  //     }

  //     if (pregnancyRecord.startDate == null) {
  //       throw ValidationException('Start date is required for pregnancy record');
  //     }

  //     // Cross-record validation: Check if cattle exists and is female
  //     if (pregnancyRecord.cattleId != null && pregnancyRecord.cattleId!.isNotEmpty) {
  //       final cattle = await _cattleRepository.getCattleByTagId(pregnancyRecord.cattleId!);
  //       if (cattle == null) {
  //         throw ValidationException('Cattle not found: $pregnancyRecord.cattleId');
  //       }

  //       // Validate that cattle is female for pregnancy
  //       if (cattle.gender != CattleGender.female) {
  //         throw ValidationException('Only female cattle can have pregnancy records');
  //       }
  //     }

  //     // Cross-record validation: Check if linked breeding record exists if provided
  //     if (pregnancyRecord.breedingRecordId != null && pregnancyRecord.breedingRecordId!.isNotEmpty) {
  //       final breedingRecord = await _breedingRepository.getBreedingRecordById(pregnancyRecord.breedingRecordId!);
  //       if (breedingRecord == null) {
  //         throw ValidationException('Breeding record not found: $pregnancyRecord.breedingRecordId');
  //       }
  //     }

  //     _logger.info('Pregnancy record validation passed for cattle: $pregnancyRecord.cattleId');
  //   } catch (e) {
  //     _logger.warning('Pregnancy record validation failed: $e');
  //     rethrow;
  //   }
  // }

  //=== HEALTH VALIDATION ===//

  // /// Validate a health record with strongly-typed model
  // Future<void> validateHealthRecord(HealthRecordIsar healthRecord) async {
  //   try {
  //     // Required field validation
  //     if (healthRecord.cattleId?.isEmpty ?? true) {
  //       throw ValidationException('Cattle ID is required for health record');
  //     }

  //     if (healthRecord.date == null) {
  //       throw ValidationException('Date is required for health record');
  //     }

  //     if (healthRecord.recordType?.isEmpty ?? true) {
  //       throw ValidationException('Record type is required for health record');
  //     }

  //     // Cross-record validation: Check if cattle exists
  //     if (healthRecord.cattleId != null && healthRecord.cattleId!.isNotEmpty) {
  //       final cattle = await _cattleRepository.getCattleByTagId(healthRecord.cattleId!);
  //       if (cattle == null) {
  //         throw ValidationException('Cattle not found: $healthRecord.cattleId');
  //       }
  //     }

  //     _logger.info('Health record validation passed for cattle: $healthRecord.cattleId');
  //   } catch (e) {
  //     _logger.warning('Health record validation failed: $e');
  //     rethrow;
  //   }
  // }
  //=== TRANSACTION VALIDATION ===//

  // /// Validate a transaction with strongly-typed model
  // Future<void> validateTransaction(TransactionIsar transaction) async {
  //   try {
  //     // Required field validation
  //     if (transaction.amount <= 0) {
  //       throw ValidationException('Transaction amount must be greater than zero');
  //     }

  //     if (transaction.category?.isEmpty ?? true) {
  //       throw ValidationException('Category is required for transaction');
  //     }

  //     if (transaction.categoryType?.isEmpty ?? true) {
  //       throw ValidationException('Transaction type is required');
  //     }

  //     if (transaction.date == null) {
  //       throw ValidationException('Date is required for transaction');
  //     }

  //     // Cross-record validation: Check if category exists (efficient targeted query)
  //     if (transaction.category != null && transaction.category!.isNotEmpty) {
  //       final categoryExists = await _transactionsRepository.getCategoryByName(transaction.category!);
  //       if (categoryExists == null) {
  //         throw ValidationException('Category not found: $transaction.category');
  //       }
  //     }

  //     _logger.info('Transaction validation passed for: $transaction.transactionId');
  //   } catch (e) {
  //     _logger.warning('Transaction validation failed: $e');
  //     rethrow;
  //   }
  // }
  //=== UTILITY VALIDATION METHODS ===//

  /// Validate date range
  void validateDateRange(DateTime startDate, DateTime endDate) {
    if (startDate.isAfter(endDate)) {
      _logger.warning('Invalid date range: start date is after end date');
      throw ValidationException('Start date cannot be after end date');
    }
  }

  /// Validate ID is not null or empty
  void validateId(String? id, String fieldName) {
    if (id == null || id.isEmpty) {
      _logger.warning('Invalid $fieldName: ID cannot be null or empty');
      throw ValidationException('$fieldName cannot be null or empty');
    }
  }

  /// Validate amount is positive number
  void validateAmount(double? amount, String fieldName) {
    if (amount == null || amount <= 0) {
      _logger.warning('Invalid $fieldName: must be a positive number');
      throw ValidationException('$fieldName must be a positive number');
    }
  }

  //=== USER ACCOUNT VALIDATION ===//

  /// Validate user registration data
  Future<void> validateUserRegistration(UserIsar user) async {
    try {
      // Required field validation
      if (user.email?.isEmpty ?? true) {
        throw ValidationException('Email is required');
      }

      if (user.username?.isEmpty ?? true) {
        throw ValidationException('Username is required');
      }

      if (user.firstName?.isEmpty ?? true) {
        throw ValidationException('First name is required');
      }

      if (user.lastName?.isEmpty ?? true) {
        throw ValidationException('Last name is required');
      }

      // Email format validation
      if (!_isValidEmail(user.email!)) {
        throw ValidationException('Please enter a valid email address');
      }

      // Username validation
      if (user.username!.length < 3) {
        throw ValidationException('Username must be at least 3 characters long');
      }

      if (!_isValidUsername(user.username!)) {
        throw ValidationException('Username can only contain letters, numbers, and underscores');
      }

      // Check for duplicate email
      // final existingUserByEmail = await _userRepository.getUserByEmail(user.email!);
      // if (existingUserByEmail != null) {
      //   throw ValidationException('An account with this email already exists');
      // }

      // Check for duplicate username
      // final existingUserByUsername = await _userRepository.getUserByUsername(user.username!);
      // if (existingUserByUsername != null) {
      //   throw ValidationException('This username is already taken');
      // }

      _logger.info('User registration validation passed for: $user.email');
    } catch (e) {
      _logger.severe('User registration validation failed: $e');
      rethrow;
    }
  }

  /// Validate user profile update
  Future<void> validateUserProfileUpdate(UserIsar user) async {
    try {
      // Required field validation
      if (user.firstName?.isEmpty ?? true) {
        throw ValidationException('First name is required');
      }

      if (user.lastName?.isEmpty ?? true) {
        throw ValidationException('Last name is required');
      }

      // Phone number validation (if provided)
      if (user.phoneNumber?.isNotEmpty == true && !_isValidPhoneNumber(user.phoneNumber!)) {
        throw ValidationException('Please enter a valid phone number');
      }

      _logger.info('User profile validation passed for: $user.email');
    } catch (e) {
      _logger.severe('User profile validation failed: $e');
      rethrow;
    }
  }

  /// Validate password strength
  void validatePassword(String password) {
    if (password.isEmpty) {
      throw ValidationException('Password is required');
    }

    if (password.length < 8) {
      throw ValidationException('Password must be at least 8 characters long');
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      throw ValidationException('Password must contain at least one uppercase letter');
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      throw ValidationException('Password must contain at least one lowercase letter');
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      throw ValidationException('Password must contain at least one number');
    }

    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      throw ValidationException('Password must contain at least one special character');
    }

    // Check for common weak passwords
    final commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];

    if (commonPasswords.contains(password.toLowerCase())) {
      throw ValidationException('Please choose a less common password');
    }
  }

  /// Validate user session
  void validateUserSession(UserSessionIsar session) {
    if (session.sessionToken?.isEmpty ?? true) {
      throw ValidationException('Session token is required');
    }

    if (session.userBusinessId?.isEmpty ?? true) {
      throw ValidationException('User business ID is required');
    }

    if (session.expiresAt == null) {
      throw ValidationException('Session expiry date is required');
    }

    if (session.expiresAt!.isBefore(DateTime.now())) {
      throw ValidationException('Session has expired');
    }
  }

  /// Validate user settings
  void validateUserSettings(UserSettingsIsar settings) {
    if (settings.userBusinessId?.isEmpty ?? true) {
      throw ValidationException('User business ID is required');
    }

    // Validate theme setting
    final validThemes = ['light', 'dark', 'system'];
    if (!validThemes.contains(settings.theme)) {
      throw ValidationException('Invalid theme setting');
    }

    // Validate time format
    final validTimeFormats = ['12h', '24h'];
    if (!validTimeFormats.contains(settings.timeFormat)) {
      throw ValidationException('Invalid time format setting');
    }

    // Validate date format
    if (settings.dateFormat.isEmpty) {
      throw ValidationException('Date format is required');
    }

    // Validate auto lock timeout
    if (settings.autoLockTimeoutMinutes < 1 || settings.autoLockTimeoutMinutes > 60) {
      throw ValidationException('Auto lock timeout must be between 1 and 60 minutes');
    }

    // Validate items per page
    if (settings.itemsPerPage < 5 || settings.itemsPerPage > 100) {
      throw ValidationException('Items per page must be between 5 and 100');
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate username format
  bool _isValidUsername(String username) {
    return RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username);
  }

  /// Validate phone number format (basic validation)
  bool _isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Check if it has a reasonable length (7-15 digits)
    return digitsOnly.length >= 7 && digitsOnly.length <= 15;
  }

}
