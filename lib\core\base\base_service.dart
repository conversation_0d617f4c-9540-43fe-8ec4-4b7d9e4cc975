import 'package:logging/logging.dart';

/// Result wrapper for service operations
/// Provides consistent error handling across all services
class ServiceResult<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  const ServiceResult._({
    this.data,
    this.error,
    required this.isSuccess,
  });

  /// Create a successful result
  factory ServiceResult.success(T data) {
    return ServiceResult._(data: data, isSuccess: true);
  }

  /// Create a failure result
  factory ServiceResult.failure(String error) {
    return ServiceResult._(error: error, isSuccess: false);
  }

  /// Create a failure result from exception
  factory ServiceResult.fromException(Exception exception) {
    return ServiceResult._(error: exception.toString(), isSuccess: false);
  }
}

/// Abstract base service for all module services
/// Defines the standard interface and patterns that all services must follow
/// 
/// Key principles:
/// - Business logic services with proper error handling
/// - Dependency injection for repositories and other services
/// - Consistent logging patterns
/// - Result-based return types for error handling
/// - Single responsibility principle
abstract class BaseService {
  final Logger _logger;

  /// Protected constructor with logger
  BaseService(String serviceName) : _logger = Logger(serviceName);

  /// Protected logger getter for concrete services
  Logger get logger => _logger;

  /// Log info message
  void logInfo(String message) {
    _logger.info(message);
  }

  /// Log warning message
  void logWarning(String message) {
    _logger.warning(message);
  }

  /// Log error message
  void logError(String message, [Object? error, StackTrace? stackTrace]) {
    _logger.severe(message, error, stackTrace);
  }

  /// Execute operation with error handling and logging
  Future<ServiceResult<T>> executeWithErrorHandling<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    try {
      logInfo('Starting $operationName');
      final result = await operation();
      logInfo('Completed $operationName successfully');
      return ServiceResult.success(result);
    } catch (e, stackTrace) {
      logError('Error in $operationName: $e', e, stackTrace);
      return ServiceResult.failure('Failed to $operationName: ${e.toString()}');
    }
  }

  /// Execute operation with error handling (synchronous)
  ServiceResult<T> executeWithErrorHandlingSync<T>(
    String operationName,
    T Function() operation,
  ) {
    try {
      logInfo('Starting $operationName');
      final result = operation();
      logInfo('Completed $operationName successfully');
      return ServiceResult.success(result);
    } catch (e, stackTrace) {
      logError('Error in $operationName: $e', e, stackTrace);
      return ServiceResult.failure('Failed to $operationName: ${e.toString()}');
    }
  }
}

/// Base analytics service for pure calculation services
/// All analytics services should extend this for consistency
/// 
/// Key principles:
/// - Pure calculation services with no state management
/// - Static methods for calculations
/// - Single-pass O(n) efficiency algorithms
/// - Immutable result objects
abstract class BaseAnalyticsService {
  // Private constructor to prevent instantiation
  BaseAnalyticsService._();

  /// All analytics services should implement static calculation methods
  /// Example pattern:
  /// static AnalyticsResult calculate(List<DataModel> data) { ... }
}

/// Base validation service for data validation
/// All validation services should extend this for consistency
abstract class BaseValidationService extends BaseService {
  BaseValidationService(String serviceName) : super(serviceName);

  /// Validate a single item
  /// Must be implemented by concrete validation services
  ServiceResult<bool> validate(dynamic item);

  /// Validate a list of items
  ServiceResult<List<String>> validateList(List<dynamic> items) {
    final errors = <String>[];
    
    for (int i = 0; i < items.length; i++) {
      final result = validate(items[i]);
      if (!result.isSuccess) {
        errors.add('Item ${i + 1}: ${result.error}');
      }
    }
    
    if (errors.isEmpty) {
      return ServiceResult.success([]);
    } else {
      return ServiceResult.success(errors);
    }
  }
}

/// Base sync service for data synchronization
/// All sync services should extend this for consistency
abstract class BaseSyncService extends BaseService {
  BaseSyncService(String serviceName) : super(serviceName);

  /// Sync data with external source
  /// Must be implemented by concrete sync services
  Future<ServiceResult<bool>> sync();

  /// Check sync status
  /// Must be implemented by concrete sync services
  Future<ServiceResult<SyncStatus>> getSyncStatus();
}

/// Sync status enumeration
enum SyncStatus {
  synced,
  pending,
  error,
  offline,
}

/// Base event integration service for cross-module events
/// All event integration services should extend this for consistency
abstract class BaseEventIntegrationService extends BaseService {
  BaseEventIntegrationService(String serviceName) : super(serviceName);

  /// Create events related to the module's data
  /// Must be implemented by concrete integration services
  Future<ServiceResult<bool>> createEvents(dynamic data);

  /// Update events when module data changes
  /// Must be implemented by concrete integration services
  Future<ServiceResult<bool>> updateEvents(dynamic data);

  /// Delete events when module data is deleted
  /// Must be implemented by concrete integration services
  Future<ServiceResult<bool>> deleteEvents(String dataId);
}
