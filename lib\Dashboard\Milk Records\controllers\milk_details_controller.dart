import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'dart:async';

import '../../Cattle/models/cattle_isar.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_repository.dart';


/// Controller for managing milk details screen state and data
/// Following breeding details controller pattern for real-time synchronization
class MilkDetailsController extends ChangeNotifier {
  final MilkRepository _milkRepository = GetIt.instance<MilkRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  bool _isLoading = true;
  String? _error;
  CattleIsar? _cattle;

  // Milk data
  List<MilkRecordIsar> _milkRecords = [];

  // Real-time synchronization using individual streams (following breeding details pattern)
  StreamSubscription<List<MilkRecordIsar>>? _milkRecordsStreamSubscription;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  CattleIsar? get cattle => _cattle;
  List<MilkRecordIsar> get milkRecords => _milkRecords;

  // Computed properties for analytics (following weight module pattern)
  int get totalMilkRecords => _milkRecords.length;

  double get totalMilkProduced {
    return _milkRecords.fold(0.0, (sum, record) => sum + (record.totalAmount ?? 0.0));
  }

  double get currentMilkProduction {
    if (_milkRecords.isEmpty) return 0.0;
    final sorted = List<MilkRecordIsar>.from(_milkRecords)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    return sorted.first.totalAmount ?? 0.0;
  }

  double get averageMilkProduction {
    if (_milkRecords.isEmpty) return 0.0;
    return totalMilkProduced / _milkRecords.length;
  }

  double get peakMilkProduction {
    if (_milkRecords.isEmpty) return 0.0;
    return _milkRecords.map((r) => r.totalAmount ?? 0.0).reduce((a, b) => a > b ? a : b);
  }
  
  List<MilkRecordIsar> get recentRecords {
    final sorted = List<MilkRecordIsar>.from(_milkRecords)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    return sorted.take(10).toList();
  }

  Map<String, int> get sessionDistribution {
    final distribution = <String, int>{};
    for (final record in _milkRecords) {
      final session = record.session ?? 'Unknown';
      distribution[session] = (distribution[session] ?? 0) + 1;
    }
    return distribution;
  }

  double get dailyAverage {
    if (_milkRecords.isEmpty) return 0.0;
    
    // Group records by date
    final dailyTotals = <String, double>{};
    for (final record in _milkRecords) {
      if (record.date != null) {
        final dateKey = '${record.date!.year}-${record.date!.month}-${record.date!.day}';
        dailyTotals[dateKey] = (dailyTotals[dateKey] ?? 0.0) + (record.quantity ?? 0.0);
      }
    }
    
    if (dailyTotals.isEmpty) return 0.0;
    final totalDays = dailyTotals.length;
    final totalMilk = dailyTotals.values.fold(0.0, (sum, daily) => sum + daily);
    
    return totalMilk / totalDays;
  }

  /// Initialize controller with cattle data and start real-time streams
  void initialize(CattleIsar cattle) {
    _cattle = cattle;
    _initializeStreamListeners();
    // No need for manual data loading - streams will handle it automatically
  }

  /// Initialize real-time stream listeners following breeding details pattern
  /// Using individual streams instead of StreamZip for better reliability
  void _initializeStreamListeners() {
    if (_cattle == null) {
      debugPrint('❌ MILK DETAILS: Cannot initialize stream listeners: cattle is null');
      return;
    }

    debugPrint('🔧 MILK DETAILS: Initializing stream listeners for cattle: ${_cattle!.tagId} (businessId: ${_cattle!.businessId})');

    // Milk records stream - filtered by cattle businessId (milk records use businessId in cattleBusinessId field)
    _milkRecordsStreamSubscription = _isar.milkRecordIsars
        .where()
        .filter()
        .cattleBusinessIdEqualTo(_cattle!.businessId)
        .watch(fireImmediately: true)
        .listen((milkRecords) {
      debugPrint('🔄 MILK DETAILS - MILK RECORDS STREAM: Received $milkRecords.length milk records for cattle ${_cattle!.tagId}');
      _milkRecords = milkRecords;
      _setLoading(false);
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Milk details - milk records stream error: $error');
      _setError('Failed to load milk records: $error');
    });

    // Note: Milk sales are not cattle-specific, so no milk sales stream needed here

    debugPrint('✅ MILK DETAILS: Individual stream listeners initialized');
  }

  /// Refresh all data - streams handle this automatically, but kept for compatibility
  Future<void> refresh() async {
    // With streams, data refreshes automatically
    // This method is kept for manual refresh if needed
    if (_cattle != null) {
      _initializeStreamListeners();
    }
  }

  // CRUD Methods - Stream-Only Pattern
  // These methods only update the database, stream handles UI updates automatically
  // Following the breeding details controller pattern for real-time synchronization

  /// Add a new milk record - stream handles UI update automatically
  Future<bool> addMilkRecord(MilkRecordIsar record) async {
    try {
      await _milkRepository.saveMilkRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to add milk record: $e');
      return false;
    }
  }

  /// Update an existing milk record - stream handles UI update automatically
  Future<bool> updateMilkRecord(MilkRecordIsar record) async {
    try {
      await _milkRepository.saveMilkRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to update milk record: $e');
      return false;
    }
  }

  /// Delete a milk record - stream handles UI update automatically
  Future<bool> deleteMilkRecord(String businessId) async {
    try {
      // Find the milk record by businessId first, then delete by Isar ID
      final record = _milkRecords.firstWhere(
        (r) => r.businessId == businessId,
        orElse: () => throw Exception('Milk record not found'),
      );
      await _milkRepository.deleteMilkRecord(record.id);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to delete milk record: $e');
      return false;
    }
  }

  // Note: Milk sales are handled at farm level, not individual cattle level
  // Milk sales CRUD methods removed as they are not cattle-specific

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }



  @override
  void dispose() {
    // Clean up all stream subscriptions - following breeding details controller pattern
    _milkRecordsStreamSubscription?.cancel();
    super.dispose();
  }
}
