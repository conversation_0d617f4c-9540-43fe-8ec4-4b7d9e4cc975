// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../Transactions/models/category_isar.dart';
import '../services/farm_setup_repository.dart';
import 'package:get_it/get_it.dart';
import '../../../widgets/icon_picker.dart';

// Define primary color as a constant for easier maintenance
const Color kPrimaryAppColor = Color(0xFF2E7D32);
const Color kIncomeCategoryColor = Colors.blue;

// Define common spacing constants
const double kSpacing = 8.0;
const double kSpacingMedium = 16.0;
const double kSpacingLarge = 24.0;

// Extension to add color functionality to CategoryIsar
extension CategoryColorExtension on CategoryIsar {
  static const String colorKey = 'color';

  // Getter for color
  Color get color {
    // Try to get color from the description field as a fallback
    // Format: "description|color:0xFF2E7D32"
    if (description.contains('|$colorKey:')) {
      try {
        final colorString = description.split('|$colorKey:')[1].split('|')[0];
        return Color(int.parse(colorString));
      } catch (e) {
        // Return default if parsing fails
        return _getDefaultColor();
      }
    }
    return _getDefaultColor();
  }

  // Setter for color through the description field
  set color(Color value) {
    // Extract existing description without color info
    String baseDescription = description;
    if (baseDescription.contains('|$colorKey:')) {
      baseDescription = baseDescription.split('|$colorKey:')[0];
    }

    // Append color info to description
    description = '$baseDescription|$colorKey:$value.value'; // ignore: deprecated_member_use (value is correct for storing ARGB int in description)
  }

  // Create a copy with color
  CategoryIsar copyWithColor(Color color) {
    final copy = copyWith();
    copy.color = color;
    return copy;
  }

  // Get default color based on name
  Color _getDefaultColor() {
    if (type == 'Income') {
      return kIncomeCategoryColor;
    } else if (type == 'Expense') {
      return kPrimaryAppColor;
    }

    // Generate color based on name
    int hash = name.hashCode;
    return Color.fromARGB(255, (hash & 0xFF0000) >> 16, (hash & 0x00FF00) >> 8,
        (hash & 0x0000FF));
  }
}

class IncomeCategoriesScreen extends StatefulWidget {
  const IncomeCategoriesScreen({super.key});

  @override
  State<IncomeCategoriesScreen> createState() => _IncomeCategoriesScreenState();
}

class _IncomeCategoriesScreenState extends State<IncomeCategoriesScreen>
    with SingleTickerProviderStateMixin {
  final List<CategoryIsar> _categories = [];
  final _nameController = TextEditingController();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  bool _isLoading = true;
  late AnimationController _animationController;
  bool _isInitialLoad = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _loadCategories();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() => _isLoading = true);
    try {
      final categories = await _farmSetupRepository.getIncomeCategories();
      setState(() {
        _categories.clear();
        _categories.addAll(categories);
        _isLoading = false;
      });

      // Play animation only on initial load
      if (_isInitialLoad && mounted) {
        _animationController.forward();
        _isInitialLoad = false;
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load categories: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddEditCategoryDialog([CategoryIsar? category]) {
    final bool isEditing = category != null;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: 5,
          backgroundColor: Colors.white,
          child: Container(
            padding: const EdgeInsets.all(kSpacingLarge),
            child: SingleChildScrollView(
              child: _IncomeCategoryForm(
                initialData: category,
                actionButtonText: isEditing ? 'Update' : 'Add Category',
                existingCategories: _categories,
                onSave: (CategoryIsar resultCategory) async {
                  // Store context-dependent objects before async gap
                  final navigator = Navigator.of(dialogContext);
                  final messenger = ScaffoldMessenger.of(context);

                  try {
                    if (isEditing) {
                      await _farmSetupRepository.updateCategory(resultCategory);
                    } else {
                      await _farmSetupRepository.createCategory(resultCategory);
                    }

                    // Update local state directly
                    if (mounted) {
                      setState(() {
                        if (isEditing) {
                          final index = _categories.indexWhere(
                              (c) => c.categoryId == resultCategory.categoryId);
                          if (index != -1) {
                            _categories[index] = resultCategory;
                          }
                        } else {
                          _categories.add(resultCategory);
                        }
                        // Keep list sorted
                        _categories.sort((a, b) => a.name.compareTo(b.name));
                      });
                    }

                    // Close dialog and show success message
                    if (navigator.mounted) {
                      navigator.pop();
                    }

                    if (messenger.mounted) {
                      messenger.showSnackBar(SnackBar(
                        content: Text(
                            'Category ${isEditing ? 'updated' : 'added'} successfully'),
                        backgroundColor: kPrimaryAppColor,
                      ));
                    }
                  } catch (e) {
                    String errorMessage = e.toString();

                    // Extract specific validation error message if present
                    if (errorMessage.contains('ValidationException')) {
                      if (errorMessage.contains('name already exists')) {
                        errorMessage =
                            'A category with this name already exists';
                      } else {
                        // Extract message after ValidationException:
                        final parts =
                            errorMessage.split('ValidationException:');
                        if (parts.length > 1) {
                          errorMessage = parts[1].trim();
                        }
                      }
                    }

                    if (messenger.mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(errorMessage),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }

  void _deleteCategory(CategoryIsar category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete "$category.name"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    ).then((confirm) async {
      if (confirm == true) {
        // Cache messenger before async operation
        final messenger = ScaffoldMessenger.of(context);

        try {
          await _farmSetupRepository.deleteCategory(category.categoryId);

          // Update local state directly
          if (mounted) {
            setState(() {
              _categories
                  .removeWhere((c) => c.categoryId == category.categoryId);
            });

            messenger.showSnackBar(
              const SnackBar(
                content: Text('Category deleted successfully'),
                backgroundColor: kPrimaryAppColor,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            messenger.showSnackBar(
              SnackBar(
                content: Text('Failed to delete category: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Income Categories',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: kPrimaryAppColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: kPrimaryAppColor))
          : _categories.isEmpty
              ? _buildEmptyState()
              : _buildCategoriesList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddEditCategoryDialog(),
        backgroundColor: kPrimaryAppColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.attach_money,
            size: 80,
            color: kPrimaryAppColor.withAlpha(204),
          ),
          const SizedBox(height: kSpacingLarge),
          const Text(
            'No Income Categories',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: kSpacingMedium),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 40),
            child: Text(
              'Add categories to organize your farm income',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.black54,
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(height: kSpacingLarge + kSpacing),
          ElevatedButton.icon(
            onPressed: () => _showAddEditCategoryDialog(),
            icon: const Icon(Icons.add, size: 24),
            label: const Text(
              'Add Category',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: kPrimaryAppColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                  horizontal: kSpacingLarge, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesList() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(kSpacingMedium, kSpacing,
          kSpacingMedium, 80), // Extra bottom padding for FAB
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return ListView.builder(
            itemCount: _categories.length,
            itemBuilder: (context, index) {
              final category = _categories[index];

              // Calculate staggered animation delay
              final Animation<double> animation = CurvedAnimation(
                parent: _animationController,
                curve: Interval(
                  index / _categories.length * 0.5,
                  (index + 1) / _categories.length * 0.5 + 0.5,
                  curve: Curves.easeOutBack,
                ),
              );

              return Padding(
                padding: const EdgeInsets.only(bottom: kSpacingMedium),
                child: FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.2),
                      end: Offset.zero,
                    ).animate(animation),
                    child: _buildCategoryCard(category),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildCategoryCard(CategoryIsar category) {
    final theme = Theme.of(context);
    final categoryColor = category.color; // Uses the extension method

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () => _showAddEditCategoryDialog(category),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(kSpacingMedium),
          child: Row(
            children: [
              // Icon in a circle with the category's color
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: categoryColor.withAlpha(38),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  category.icon,
                  color: categoryColor,
                  size: 28,
                ),
              ),
              const SizedBox(width: kSpacingMedium),
              // Category name
              Expanded(
                child: Text(
                  category.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
              // More menu
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.grey),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.edit_outlined,
                            size: 20, color: categoryColor),
                        const SizedBox(width: kSpacing),
                        const Flexible(
                          child: Text(
                            'Edit',
                            maxLines: 2,
                            softWrap: true,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.delete_outline, size: 20, color: Colors.red),
                        SizedBox(width: kSpacing),
                        Flexible(
                          child: Text(
                            'Delete',
                            style: TextStyle(color: Colors.red),
                            maxLines: 2,
                            softWrap: true,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                onSelected: (value) {
                  if (value == 'edit') {
                    _showAddEditCategoryDialog(category);
                  } else if (value == 'delete') {
                    _deleteCategory(category);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Reusable form widget for adding and editing income categories
class _IncomeCategoryForm extends StatefulWidget {
  final CategoryIsar? initialData;
  final Function(CategoryIsar) onSave;
  final String actionButtonText;
  final List<CategoryIsar> existingCategories;

  const _IncomeCategoryForm({
    Key? key,
    this.initialData,
    required this.onSave,
    required this.actionButtonText,
    required this.existingCategories,
  }) : super(key: key);

  @override
  _IncomeCategoryFormState createState() => _IncomeCategoryFormState();
}

class _IncomeCategoryFormState extends State<_IncomeCategoryForm> {
  final formKey = GlobalKey<FormState>();
  late TextEditingController nameController;
  IconData? selectedIcon;
  late Color selectedColor;

  @override
  void initState() {
    super.initState();
    nameController =
        TextEditingController(text: widget.initialData?.name ?? '');
    selectedIcon = widget.initialData?.icon;
    // Initialize with the category's color or a default color if it's a new category
    selectedColor = widget.initialData?.color ?? kPrimaryAppColor;
  }

  @override
  void dispose() {
    nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Form(
      key: formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                widget.initialData == null ? Icons.add_circle : Icons.edit_note,
                color: kPrimaryAppColor,
                size: 28,
              ),
              const SizedBox(width: kSpacingMedium - 4),
              Flexible(
                child: Text(
                  widget.initialData == null
                      ? 'Add Income Category'
                      : 'Edit Category',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                  maxLines: 2,
                  softWrap: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: kSpacingLarge),

          // Icon Preview and Picker Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Icon preview
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(kSpacingMedium),
                    decoration: BoxDecoration(
                      color: selectedColor.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      selectedIcon ?? Icons.category_outlined,
                      color: selectedColor,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: kSpacing),
                  Text(
                    'Preview',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(153),
                    ),
                  ),
                ],
              ),

              // Buttons column
              Column(
                children: [
                  // Icon picker button
                  OutlinedButton.icon(
                    onPressed: () async {
                      final pickedIcon = await showDialog<IconData>(
                        context: context,
                        builder: (context) => IconPicker(
                          selectedIcon: selectedIcon,
                          onIconSelected: (_) {}, // No debug printing
                        ),
                      );

                      if (pickedIcon != null) {
                        setState(() {
                          selectedIcon = pickedIcon;
                        });
                      }
                    },
                    icon: const Icon(Icons.image),
                    label: const Text('Select Icon'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: kSpacingMedium, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),

                  const SizedBox(height: kSpacing),

                  // Color picker button
                  OutlinedButton.icon(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: const Text('Pick a color'),
                            content: SingleChildScrollView(
                              child: BlockPicker(
                                pickerColor: selectedColor,
                                onColorChanged: (Color color) {
                                  setState(() {
                                    selectedColor = color;
                                  });
                                  Navigator.of(context).pop();
                                },
                              ),
                            ),
                          );
                        },
                      );
                    },
                    icon: Icon(Icons.color_lens, color: selectedColor),
                    label: const Text('Select Color'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: kSpacingMedium, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: kSpacingLarge),
          const Divider(),
          const SizedBox(height: kSpacingMedium),

          // Category Name Field
          TextFormField(
            controller: nameController,
            decoration: InputDecoration(
              labelText: 'Category Name',
              hintText: 'Enter category name',
              prefixIcon:
                  const Icon(Icons.label_outline, color: kPrimaryAppColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: kSpacingMedium, vertical: kSpacingMedium),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a category name';
              }

              // Check for duplicate category names (case-insensitive)
              final nameExists = widget.existingCategories.any((cat) =>
                  cat.name.toLowerCase() == value.trim().toLowerCase() &&
                  cat.categoryId != widget.initialData?.categoryId);

              if (nameExists) {
                return 'A category with this name already exists';
              }
              return null;
            },
          ),
          const SizedBox(height: kSpacingLarge),

          // Action Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Cancel',
                  style: TextStyle(color: theme.colorScheme.error),
                ),
              ),
              const SizedBox(width: kSpacing),
              ElevatedButton(
                onPressed: () {
                  if (selectedIcon == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please select an icon'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  if (formKey.currentState!.validate()) {
                    // Create or update CategoryIsar object
                    final CategoryIsar resultCategory;

                    if (widget.initialData == null) {
                      // Create new category
                      resultCategory = CategoryIsar.create(
                        categoryId: const Uuid().v4(),
                        name: nameController.text.trim(),
                        description: '', // Will be set by the color setter
                        type: 'Income',
                        icon: selectedIcon!,
                      );
                      // Set the selected color
                      resultCategory.color = selectedColor;
                    } else {
                      // Update existing category
                      final originalName = widget.initialData!.name;
                      final newName = nameController.text.trim();

                      // If name hasn't changed, we can avoid the duplicate name check in the backend
                      if (originalName.toLowerCase() == newName.toLowerCase()) {
                        // Just update the icon and color, but keep the same name
                        resultCategory = widget.initialData!.copyWith(
                          icon: selectedIcon,
                          updatedAt: DateTime.now(),
                        );
                        // Set the selected color
                        resultCategory.color = selectedColor;
                      } else {
                        // Name has changed
                        resultCategory = widget.initialData!.copyWith(
                          name: newName,
                          icon: selectedIcon,
                          updatedAt: DateTime.now(),
                        );
                        // Set the selected color
                        resultCategory.color = selectedColor;
                      }
                    }

                    // Pass the result back
                    widget.onSave(resultCategory);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: kPrimaryAppColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(widget.actionButtonText),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
