import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../controllers/events_controller.dart';
import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../screens/event_details_screen.dart';

import '../../widgets/universal_record_card.dart';
import '../../widgets/filters/filter_layout.dart';
import '../../widgets/filters/filters.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../utils/message_utils.dart';
// For ControllerState enum

/// Event List Tab with advanced filtering, search, and grouping capabilities
/// Follows the established patterns from BreedingRecordsTab and HealthRecordsTab
class EventListTab extends StatefulWidget {
  final EventsController controller;

  const EventListTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<EventListTab> createState() => _EventListTabState();
}

class _EventListTabState extends State<EventListTab> {
  late FilterController _filterController;
  String _groupBy = 'date'; // Default grouping by date
  
  // Pagination state
  int _currentPage = 0;
  final int _pageSize = 50;
  bool _isLoadingMore = false;
  final ScrollController _scrollController = ScrollController();
  List<EventIsar> _paginatedEvents = [];
  bool _hasMorePages = true;

  /// Get controller reference
  EventsController get _controller => widget.controller;

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
    
    // Setup scroll controller for infinite scroll
    _scrollController.addListener(_onScroll);
    
    // Load initial page
    _loadInitialPage();
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // Apply filters at database level for ultimate scalability
    _controller.applyFilters(_filterController.toEventFilterState());
    
    // Reset pagination when filters change
    _resetPagination();
  }

  /// Handle scroll events for infinite scroll
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadNextPage();
    }
  }

  /// Load initial page of events
  void _loadInitialPage() async {
    if (!mounted) return;

    try {
      final events = _controller.events;
      final hasMore = events.length > _pageSize;
      final paginatedEvents = events.take(_pageSize).toList();

      if (mounted) {
        setState(() {
          _paginatedEvents = paginatedEvents;
          _currentPage = 0;
          _hasMorePages = hasMore;
        });
      }
    } catch (e) {
      debugPrint('Error loading initial page: $e');
    }
  }

  /// Load next page of events
  void _loadNextPage() async {
    if (_isLoadingMore || !_hasMorePages || !mounted) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final events = _controller.events;
      final startIndex = nextPage * _pageSize;
      final endIndex = (startIndex + _pageSize > events.length) ? events.length : (startIndex + _pageSize);

      if (startIndex >= events.length) {
        setState(() {
          _hasMorePages = false;
          _isLoadingMore = false;
        });
        return;
      }

      final newEvents = events.getRange(startIndex, endIndex).toList();

      if (mounted) {
        setState(() {
          _paginatedEvents.addAll(newEvents);
          _currentPage = nextPage;
          _hasMorePages = _paginatedEvents.length < events.length;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading next page: $e');
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  /// Reset pagination when filters change
  void _resetPagination() {
    setState(() {
      _currentPage = 0;
      _paginatedEvents.clear();
      _hasMorePages = true;
    });
    _loadInitialPage();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    debugPrint('📱 EVENT LIST TAB: Building content');
    debugPrint('   Total events: ${_controller.unfilteredEvents.length}');
    debugPrint('   Filtered events: ${_controller.events.length}');
    debugPrint('   Controller state: ${_controller.state}');

    // Check if we have data to display
    if (_controller.unfilteredEvents.isEmpty) {
      debugPrint('📱 EVENT LIST TAB: No data - showing empty state');
      return _buildEmptyState(true);
    }

    // Use paginated events for better performance
    final events = _paginatedEvents.isNotEmpty ? _paginatedEvents : _controller.events;
    final allEventsCount = _controller.unfilteredEvents.length;

    return Column(
      children: [
        // Universal Filter Layout with events-specific configuration
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.events,
          moduleName: 'events',
          sortFields: const [...SortField.commonFields, ...SortField.eventFields],
          searchHint: 'Search events by title, description, cattle, or notes...',
          totalCount: allEventsCount,
          filteredCount: events.length,
        ),

        // Grouping Controls
        if (events.isNotEmpty) _buildGroupingControls(),

        // Events List - data is already filtered at database level
        Expanded(
          child: events.isEmpty
              ? _buildEmptyState(allEventsCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: _buildEventsList(events),
                ),
        ),
      ],
    );
  }

  /// Build grouping controls for organizing events
  Widget _buildGroupingControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          const Icon(Icons.group_work, size: 20, color: AppColors.eventsHeader),
          const SizedBox(width: 8),
          const Text(
            'Group by:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: AppColors.eventsHeader,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildGroupingChip('date', 'Date', Icons.date_range),
                  const SizedBox(width: 8),
                  _buildGroupingChip('category', 'Category', Icons.category),
                  const SizedBox(width: 8),
                  _buildGroupingChip('status', 'Status', Icons.pending_actions),
                  const SizedBox(width: 8),
                  _buildGroupingChip('priority', 'Priority', Icons.priority_high),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual grouping chip
  Widget _buildGroupingChip(String value, String label, IconData icon) {
    final isSelected = _groupBy == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          _groupBy = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.eventsHeader : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.eventsHeader,
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : AppColors.eventsHeader,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : AppColors.eventsHeader,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the events list with grouping and infinite scroll
  Widget _buildEventsList(List<EventIsar> events) {
    // Group events based on selected grouping
    final groupedEvents = _groupEvents(events);
    
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: groupedEvents.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        // Show loading indicator at the end
        if (index == groupedEvents.length) {
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        final group = groupedEvents[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Group header
            _buildGroupHeader(group['title'] as String, group['events'].length),
            const SizedBox(height: 8),
            // Group events
            ...((group['events'] as List<EventIsar>).map((event) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: _buildEventCard(event),
              ),
            )),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  /// Group events based on the selected grouping method
  List<Map<String, dynamic>> _groupEvents(List<EventIsar> events) {
    final Map<String, List<EventIsar>> groups = {};

    for (final event in events) {
      String groupKey = 'Other'; // Initialize with default value
      switch (_groupBy) {
        case 'category':
          groupKey = event.category.displayName;
          break;
        case 'status':
          groupKey = event.status.displayName;
          break;
        case 'priority':
          groupKey = event.priority.displayName;
          break;
        case 'date':
          groupKey = _getDateGroupKey(event.scheduledDate);
          break;
        default:
          groupKey = 'Other';
          break;
      }

      groups.putIfAbsent(groupKey, () => []).add(event);
    }

    // Sort groups and convert to list format
    final sortedGroups = groups.entries.toList();
    
    // Sort groups based on grouping type
    switch (_groupBy) {
      case 'date':
        sortedGroups.sort((a, b) => _compareDateGroups(a.key, b.key));
        break;
      case 'priority':
        sortedGroups.sort((a, b) => _comparePriorityGroups(a.key, b.key));
        break;
      default:
        sortedGroups.sort((a, b) => a.key.compareTo(b.key));
        break;
    }

    return sortedGroups.map((entry) => {
      'title': entry.key,
      'events': entry.value..sort((a, b) => 
        (b.scheduledDate ?? DateTime.now()).compareTo(a.scheduledDate ?? DateTime.now())),
    }).toList();
  }

  /// Get date group key for grouping events by date
  String _getDateGroupKey(DateTime? date) {
    if (date == null) return 'No Date';
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate = DateTime(date.year, date.month, date.day);
    
    final difference = eventDate.difference(today).inDays;
    
    if (difference == 0) return 'Today';
    if (difference == 1) return 'Tomorrow';
    if (difference == -1) return 'Yesterday';
    if (difference > 1 && difference <= 7) return 'This Week';
    if (difference < -1 && difference >= -7) return 'Last Week';
    if (difference > 7 && difference <= 30) return 'This Month';
    if (difference < -7 && difference >= -30) return 'Last Month';
    if (difference > 30) return 'Future';
    return 'Past';
  }

  /// Compare date groups for sorting
  int _compareDateGroups(String a, String b) {
    const order = [
      'Past', 'Last Month', 'Last Week', 'Yesterday', 'Today', 
      'Tomorrow', 'This Week', 'This Month', 'Future', 'No Date'
    ];
    return order.indexOf(a).compareTo(order.indexOf(b));
  }

  /// Compare priority groups for sorting
  int _comparePriorityGroups(String a, String b) {
    const order = ['Critical', 'High', 'Medium', 'Low'];
    return order.indexOf(a).compareTo(order.indexOf(b));
  }

  /// Build group header
  Widget _buildGroupHeader(String title, int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.eventsHeader.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.eventsHeader.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getGroupIcon(),
            size: 20,
            color: AppColors.eventsHeader,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.eventsHeader,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.eventsHeader,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get icon for current grouping method
  IconData _getGroupIcon() {
    switch (_groupBy) {
      case 'category':
        return Icons.category;
      case 'status':
        return Icons.pending_actions;
      case 'priority':
        return Icons.priority_high;
      case 'date':
        return Icons.date_range;
      default:
        return Icons.list;
    }
  }

  /// Build individual event card with swipe actions
  Widget _buildEventCard(EventIsar event) {
    return Dismissible(
      key: Key(event.businessId ?? event.id.toString()),
      background: _buildSwipeBackground(Colors.blue, Icons.edit, 'Edit', true),
      secondaryBackground: _buildSwipeBackground(Colors.red, Icons.delete, 'Delete', false),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          // Edit action
          _showEditEventDialog(event);
          return false; // Don't dismiss
        } else {
          // Delete action
          return await _showDeleteConfirmation(event);
        }
      },
      child: UniversalRecordCard(
        row1Left: _formatDate(event.scheduledDate),
        row1Right: event.title ?? 'Untitled Event',
        row1LeftIcon: Icons.calendar_today,
        row1RightIcon: _getCategoryIcon(event.category),
        row2Left: _getCattleName(event.cattleTagId),
        row2Right: event.status.displayName,
        row2LeftIcon: Icons.pets,
        row2RightIcon: _getStatusIcon(event.status),
        row3Left: event.priority != EventPriority.medium ? event.priority.displayName : null,
        row3Right: event.location?.isNotEmpty == true ? event.location : null,
        row3LeftIcon: event.priority != EventPriority.medium ? Icons.priority_high : null,
        row3RightIcon: event.location?.isNotEmpty == true ? Icons.location_on : null,
        notes: event.description?.isNotEmpty == true ? event.description : event.notes,
        primaryColor: AppColors.eventsHeader,
        row1RightColor: _getPriorityColor(event.priority),
        onTap: () => _navigateToEventDetail(event),
        onEdit: () => _showEditEventDialog(event),
        onDelete: () => _showDeleteConfirmation(event),
      ),
    );
  }

  /// Build swipe action background
  Widget _buildSwipeBackground(Color color, IconData icon, String label, bool isLeft) {
    return Container(
      color: color,
      alignment: isLeft ? Alignment.centerLeft : Alignment.centerRight,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: Colors.white, size: 28),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// Get cattle name from tag ID
  String _getCattleName(String? cattleTagId) {
    if (cattleTagId == null || cattleTagId.isEmpty) return 'No Cattle';
    
    // Find cattle by tag ID from controller's cattle list
    try {
      final cattle = _controller.unfilteredCattle.firstWhere(
        (c) => c.tagId == cattleTagId,
      );
      return cattle.name ?? 'Unknown Cattle';
    } catch (e) {
      // Cattle not found
      return 'Unknown Cattle ($cattleTagId)';
    }
  }

  /// Get category icon
  IconData _getCategoryIcon(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return Icons.medical_services;
      case EventCategory.breeding:
        return Icons.favorite;
      case EventCategory.feeding:
        return Icons.restaurant;
      case EventCategory.management:
        return Icons.business;
      case EventCategory.maintenance:
        return Icons.build;
      case EventCategory.financial:
        return Icons.attach_money;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return Icons.event;
    }
  }

  /// Get status icon
  IconData _getStatusIcon(EventStatus status) {
    switch (status) {
      case EventStatus.scheduled:
        return Icons.schedule;
      case EventStatus.inProgress:
        return Icons.play_circle;
      case EventStatus.completed:
        return Icons.check_circle;
      case EventStatus.cancelled:
        return Icons.cancel;
      case EventStatus.overdue:
        return Icons.warning;
      case EventStatus.missed:
        return Icons.error;
    }
  }

  /// Get priority color
  Color _getPriorityColor(EventPriority priority) {
    switch (priority) {
      case EventPriority.critical:
        return Colors.red;
      case EventPriority.high:
        return Colors.orange;
      case EventPriority.medium:
        return AppColors.eventsHeader;
      case EventPriority.low:
        return Colors.green;
    }
  }

  /// Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'No Date';
    return DateFormat('MMM dd, yyyy').format(date);
  }

  /// Navigate to event detail screen
  void _navigateToEventDetail(EventIsar event) {
    if (event.businessId == null) return;
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EventDetailsScreen(
          eventBusinessId: event.businessId!,
        ),
      ),
    );
  }

  /// Show edit event dialog (placeholder for now)
  void _showEditEventDialog(EventIsar event) {
    // TODO: Implement edit event dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit event "${event.title}" - Coming soon!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Show delete confirmation dialog
  Future<bool> _showDeleteConfirmation(EventIsar event) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _controller.deleteEvent(event.businessId!);
        if (mounted) {
          MessageUtils.showSuccess(context, 'Event deleted successfully');
        }
        return true;
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Failed to delete event: $e');
        }
        return false;
      }
    }
    return false;
  }

  /// Consolidated empty state builder
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    const tabColor = AppColors.eventsHeader;

    if (isCompletelyEmpty) {
      // No events exist - show call-to-action to add first event
      return UniversalTabEmptyState.forTab(
        title: 'No Events',
        message: 'Add your first event to start tracking farm activities and schedules.',
        tabColor: tabColor,
        tabIndex: 1, // List tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddEventDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Events exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Events',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 1, // List tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  /// Show add event dialog (placeholder for now)
  void _showAddEventDialog() {
    // TODO: Implement add event dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add event dialog - Coming soon!'),
        backgroundColor: AppColors.eventsHeader,
      ),
    );
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }
}
