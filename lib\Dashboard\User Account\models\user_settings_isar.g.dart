// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetUserSettingsIsarCollection on Isar {
  IsarCollection<UserSettingsIsar> get userSettingsIsars => this.collection();
}

const UserSettingsIsarSchema = CollectionSchema(
  name: r'UserSettingsIsar',
  id: -897199500488509727,
  properties: {
    r'allowLocationTracking': PropertySchema(
      id: 0,
      name: r'allowLocationTracking',
      type: IsarType.bool,
    ),
    r'autoBackupEnabled': PropertySchema(
      id: 1,
      name: r'autoBackupEnabled',
      type: IsarType.bool,
    ),
    r'autoLockEnabled': PropertySchema(
      id: 2,
      name: r'autoLockEnabled',
      type: IsarType.bool,
    ),
    r'autoLockTimeoutMinutes': PropertySchema(
      id: 3,
      name: r'autoLockTimeoutMinutes',
      type: IsarType.long,
    ),
    r'backupFrequency': PropertySchema(
      id: 4,
      name: r'backupFrequency',
      type: IsarType.string,
    ),
    r'breedingReminders': PropertySchema(
      id: 5,
      name: r'breedingReminders',
      type: IsarType.bool,
    ),
    r'businessId': PropertySchema(
      id: 6,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'compactView': PropertySchema(
      id: 7,
      name: r'compactView',
      type: IsarType.bool,
    ),
    r'createdAt': PropertySchema(
      id: 8,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'currency': PropertySchema(
      id: 9,
      name: r'currency',
      type: IsarType.string,
    ),
    r'dateFormat': PropertySchema(
      id: 10,
      name: r'dateFormat',
      type: IsarType.string,
    ),
    r'emailNotifications': PropertySchema(
      id: 11,
      name: r'emailNotifications',
      type: IsarType.bool,
    ),
    r'eventReminders': PropertySchema(
      id: 12,
      name: r'eventReminders',
      type: IsarType.bool,
    ),
    r'healthReminders': PropertySchema(
      id: 13,
      name: r'healthReminders',
      type: IsarType.bool,
    ),
    r'itemsPerPage': PropertySchema(
      id: 14,
      name: r'itemsPerPage',
      type: IsarType.long,
    ),
    r'language': PropertySchema(
      id: 15,
      name: r'language',
      type: IsarType.string,
    ),
    r'lastBackupDate': PropertySchema(
      id: 16,
      name: r'lastBackupDate',
      type: IsarType.string,
    ),
    r'logSecurityEvents': PropertySchema(
      id: 17,
      name: r'logSecurityEvents',
      type: IsarType.bool,
    ),
    r'milkingReminders': PropertySchema(
      id: 18,
      name: r'milkingReminders',
      type: IsarType.bool,
    ),
    r'pushNotifications': PropertySchema(
      id: 19,
      name: r'pushNotifications',
      type: IsarType.bool,
    ),
    r'requireBiometricAuth': PropertySchema(
      id: 20,
      name: r'requireBiometricAuth',
      type: IsarType.bool,
    ),
    r'shareDataForAnalytics': PropertySchema(
      id: 21,
      name: r'shareDataForAnalytics',
      type: IsarType.bool,
    ),
    r'showCattleImages': PropertySchema(
      id: 22,
      name: r'showCattleImages',
      type: IsarType.bool,
    ),
    r'showProfileToOthers': PropertySchema(
      id: 23,
      name: r'showProfileToOthers',
      type: IsarType.bool,
    ),
    r'smsNotifications': PropertySchema(
      id: 24,
      name: r'smsNotifications',
      type: IsarType.bool,
    ),
    r'syncWithCloud': PropertySchema(
      id: 25,
      name: r'syncWithCloud',
      type: IsarType.bool,
    ),
    r'theme': PropertySchema(
      id: 26,
      name: r'theme',
      type: IsarType.string,
    ),
    r'timeFormat': PropertySchema(
      id: 27,
      name: r'timeFormat',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 28,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'userBusinessId': PropertySchema(
      id: 29,
      name: r'userBusinessId',
      type: IsarType.string,
    ),
    r'vaccinationReminders': PropertySchema(
      id: 30,
      name: r'vaccinationReminders',
      type: IsarType.bool,
    )
  },
  estimateSize: _userSettingsIsarEstimateSize,
  serialize: _userSettingsIsarSerialize,
  deserialize: _userSettingsIsarDeserialize,
  deserializeProp: _userSettingsIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'userBusinessId': IndexSchema(
      id: -598832101615522968,
      name: r'userBusinessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'userBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {
    r'user': LinkSchema(
      id: -8043947260231021809,
      name: r'user',
      target: r'UserIsar',
      single: true,
      linkName: r'settings',
    )
  },
  embeddedSchemas: {},
  getId: _userSettingsIsarGetId,
  getLinks: _userSettingsIsarGetLinks,
  attach: _userSettingsIsarAttach,
  version: '3.1.0+1',
);

int _userSettingsIsarEstimateSize(
  UserSettingsIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.backupFrequency.length * 3;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.currency.length * 3;
  bytesCount += 3 + object.dateFormat.length * 3;
  bytesCount += 3 + object.language.length * 3;
  {
    final value = object.lastBackupDate;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.theme.length * 3;
  bytesCount += 3 + object.timeFormat.length * 3;
  {
    final value = object.userBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _userSettingsIsarSerialize(
  UserSettingsIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.allowLocationTracking);
  writer.writeBool(offsets[1], object.autoBackupEnabled);
  writer.writeBool(offsets[2], object.autoLockEnabled);
  writer.writeLong(offsets[3], object.autoLockTimeoutMinutes);
  writer.writeString(offsets[4], object.backupFrequency);
  writer.writeBool(offsets[5], object.breedingReminders);
  writer.writeString(offsets[6], object.businessId);
  writer.writeBool(offsets[7], object.compactView);
  writer.writeDateTime(offsets[8], object.createdAt);
  writer.writeString(offsets[9], object.currency);
  writer.writeString(offsets[10], object.dateFormat);
  writer.writeBool(offsets[11], object.emailNotifications);
  writer.writeBool(offsets[12], object.eventReminders);
  writer.writeBool(offsets[13], object.healthReminders);
  writer.writeLong(offsets[14], object.itemsPerPage);
  writer.writeString(offsets[15], object.language);
  writer.writeString(offsets[16], object.lastBackupDate);
  writer.writeBool(offsets[17], object.logSecurityEvents);
  writer.writeBool(offsets[18], object.milkingReminders);
  writer.writeBool(offsets[19], object.pushNotifications);
  writer.writeBool(offsets[20], object.requireBiometricAuth);
  writer.writeBool(offsets[21], object.shareDataForAnalytics);
  writer.writeBool(offsets[22], object.showCattleImages);
  writer.writeBool(offsets[23], object.showProfileToOthers);
  writer.writeBool(offsets[24], object.smsNotifications);
  writer.writeBool(offsets[25], object.syncWithCloud);
  writer.writeString(offsets[26], object.theme);
  writer.writeString(offsets[27], object.timeFormat);
  writer.writeDateTime(offsets[28], object.updatedAt);
  writer.writeString(offsets[29], object.userBusinessId);
  writer.writeBool(offsets[30], object.vaccinationReminders);
}

UserSettingsIsar _userSettingsIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = UserSettingsIsar();
  object.allowLocationTracking = reader.readBool(offsets[0]);
  object.autoBackupEnabled = reader.readBool(offsets[1]);
  object.autoLockEnabled = reader.readBool(offsets[2]);
  object.autoLockTimeoutMinutes = reader.readLong(offsets[3]);
  object.backupFrequency = reader.readString(offsets[4]);
  object.breedingReminders = reader.readBool(offsets[5]);
  object.businessId = reader.readStringOrNull(offsets[6]);
  object.compactView = reader.readBool(offsets[7]);
  object.createdAt = reader.readDateTimeOrNull(offsets[8]);
  object.currency = reader.readString(offsets[9]);
  object.dateFormat = reader.readString(offsets[10]);
  object.emailNotifications = reader.readBool(offsets[11]);
  object.eventReminders = reader.readBool(offsets[12]);
  object.healthReminders = reader.readBool(offsets[13]);
  object.id = id;
  object.itemsPerPage = reader.readLong(offsets[14]);
  object.language = reader.readString(offsets[15]);
  object.lastBackupDate = reader.readStringOrNull(offsets[16]);
  object.logSecurityEvents = reader.readBool(offsets[17]);
  object.milkingReminders = reader.readBool(offsets[18]);
  object.pushNotifications = reader.readBool(offsets[19]);
  object.requireBiometricAuth = reader.readBool(offsets[20]);
  object.shareDataForAnalytics = reader.readBool(offsets[21]);
  object.showCattleImages = reader.readBool(offsets[22]);
  object.showProfileToOthers = reader.readBool(offsets[23]);
  object.smsNotifications = reader.readBool(offsets[24]);
  object.syncWithCloud = reader.readBool(offsets[25]);
  object.theme = reader.readString(offsets[26]);
  object.timeFormat = reader.readString(offsets[27]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[28]);
  object.userBusinessId = reader.readStringOrNull(offsets[29]);
  object.vaccinationReminders = reader.readBool(offsets[30]);
  return object;
}

P _userSettingsIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBool(offset)) as P;
    case 1:
      return (reader.readBool(offset)) as P;
    case 2:
      return (reader.readBool(offset)) as P;
    case 3:
      return (reader.readLong(offset)) as P;
    case 4:
      return (reader.readString(offset)) as P;
    case 5:
      return (reader.readBool(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readBool(offset)) as P;
    case 8:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 9:
      return (reader.readString(offset)) as P;
    case 10:
      return (reader.readString(offset)) as P;
    case 11:
      return (reader.readBool(offset)) as P;
    case 12:
      return (reader.readBool(offset)) as P;
    case 13:
      return (reader.readBool(offset)) as P;
    case 14:
      return (reader.readLong(offset)) as P;
    case 15:
      return (reader.readString(offset)) as P;
    case 16:
      return (reader.readStringOrNull(offset)) as P;
    case 17:
      return (reader.readBool(offset)) as P;
    case 18:
      return (reader.readBool(offset)) as P;
    case 19:
      return (reader.readBool(offset)) as P;
    case 20:
      return (reader.readBool(offset)) as P;
    case 21:
      return (reader.readBool(offset)) as P;
    case 22:
      return (reader.readBool(offset)) as P;
    case 23:
      return (reader.readBool(offset)) as P;
    case 24:
      return (reader.readBool(offset)) as P;
    case 25:
      return (reader.readBool(offset)) as P;
    case 26:
      return (reader.readString(offset)) as P;
    case 27:
      return (reader.readString(offset)) as P;
    case 28:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 29:
      return (reader.readStringOrNull(offset)) as P;
    case 30:
      return (reader.readBool(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _userSettingsIsarGetId(UserSettingsIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _userSettingsIsarGetLinks(UserSettingsIsar object) {
  return [object.user];
}

void _userSettingsIsarAttach(
    IsarCollection<dynamic> col, Id id, UserSettingsIsar object) {
  object.id = id;
  object.user.attach(col, col.isar.collection<UserIsar>(), r'user', id);
}

extension UserSettingsIsarByIndex on IsarCollection<UserSettingsIsar> {
  Future<UserSettingsIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  UserSettingsIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<UserSettingsIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<UserSettingsIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(UserSettingsIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(UserSettingsIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<UserSettingsIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<UserSettingsIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }

  Future<UserSettingsIsar?> getByUserBusinessId(String? userBusinessId) {
    return getByIndex(r'userBusinessId', [userBusinessId]);
  }

  UserSettingsIsar? getByUserBusinessIdSync(String? userBusinessId) {
    return getByIndexSync(r'userBusinessId', [userBusinessId]);
  }

  Future<bool> deleteByUserBusinessId(String? userBusinessId) {
    return deleteByIndex(r'userBusinessId', [userBusinessId]);
  }

  bool deleteByUserBusinessIdSync(String? userBusinessId) {
    return deleteByIndexSync(r'userBusinessId', [userBusinessId]);
  }

  Future<List<UserSettingsIsar?>> getAllByUserBusinessId(
      List<String?> userBusinessIdValues) {
    final values = userBusinessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'userBusinessId', values);
  }

  List<UserSettingsIsar?> getAllByUserBusinessIdSync(
      List<String?> userBusinessIdValues) {
    final values = userBusinessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'userBusinessId', values);
  }

  Future<int> deleteAllByUserBusinessId(List<String?> userBusinessIdValues) {
    final values = userBusinessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'userBusinessId', values);
  }

  int deleteAllByUserBusinessIdSync(List<String?> userBusinessIdValues) {
    final values = userBusinessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'userBusinessId', values);
  }

  Future<Id> putByUserBusinessId(UserSettingsIsar object) {
    return putByIndex(r'userBusinessId', object);
  }

  Id putByUserBusinessIdSync(UserSettingsIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'userBusinessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByUserBusinessId(List<UserSettingsIsar> objects) {
    return putAllByIndex(r'userBusinessId', objects);
  }

  List<Id> putAllByUserBusinessIdSync(List<UserSettingsIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'userBusinessId', objects, saveLinks: saveLinks);
  }
}

extension UserSettingsIsarQueryWhereSort
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QWhere> {
  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension UserSettingsIsarQueryWhere
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QWhereClause> {
  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      userBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'userBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      userBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'userBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      userBusinessIdEqualTo(String? userBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'userBusinessId',
        value: [userBusinessId],
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterWhereClause>
      userBusinessIdNotEqualTo(String? userBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [],
              upper: [userBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [userBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [userBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [],
              upper: [userBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension UserSettingsIsarQueryFilter
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QFilterCondition> {
  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      allowLocationTrackingEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'allowLocationTracking',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      autoBackupEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoBackupEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      autoLockEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoLockEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      autoLockTimeoutMinutesEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoLockTimeoutMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      autoLockTimeoutMinutesGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'autoLockTimeoutMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      autoLockTimeoutMinutesLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'autoLockTimeoutMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      autoLockTimeoutMinutesBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'autoLockTimeoutMinutes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'backupFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'backupFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'backupFrequency',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'backupFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'backupFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'backupFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'backupFrequency',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupFrequency',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      backupFrequencyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'backupFrequency',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      breedingRemindersEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedingReminders',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      compactViewEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'compactView',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'currency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'currency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'currency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'currency',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'currency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'currency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'currency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'currency',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'currency',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      currencyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'currency',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dateFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dateFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dateFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dateFormat',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dateFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dateFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dateFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dateFormat',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dateFormat',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      dateFormatIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dateFormat',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      emailNotificationsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emailNotifications',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      eventRemindersEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventReminders',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      healthRemindersEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthReminders',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      itemsPerPageEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'itemsPerPage',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      itemsPerPageGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'itemsPerPage',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      itemsPerPageLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'itemsPerPage',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      itemsPerPageBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'itemsPerPage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'language',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'language',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'language',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'language',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'language',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'language',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'language',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'language',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'language',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      languageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'language',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastBackupDate',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastBackupDate',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastBackupDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastBackupDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastBackupDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastBackupDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'lastBackupDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'lastBackupDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'lastBackupDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'lastBackupDate',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastBackupDate',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'lastBackupDate',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      logSecurityEventsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'logSecurityEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      milkingRemindersEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkingReminders',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      pushNotificationsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pushNotifications',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      requireBiometricAuthEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'requireBiometricAuth',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      shareDataForAnalyticsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'shareDataForAnalytics',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      showCattleImagesEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'showCattleImages',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      showProfileToOthersEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'showProfileToOthers',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      smsNotificationsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'smsNotifications',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      syncWithCloudEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'syncWithCloud',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'theme',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'theme',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'theme',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'theme',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'theme',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'theme',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'theme',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'theme',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'theme',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      themeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'theme',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'timeFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'timeFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'timeFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'timeFormat',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'timeFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'timeFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'timeFormat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'timeFormat',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'timeFormat',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      timeFormatIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'timeFormat',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userBusinessId',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userBusinessId',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'userBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'userBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      vaccinationRemindersEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'vaccinationReminders',
        value: value,
      ));
    });
  }
}

extension UserSettingsIsarQueryObject
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QFilterCondition> {}

extension UserSettingsIsarQueryLinks
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QFilterCondition> {
  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition> user(
      FilterQuery<UserIsar> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'user');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterFilterCondition>
      userIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'user', 0, true, 0, true);
    });
  }
}

extension UserSettingsIsarQuerySortBy
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QSortBy> {
  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAllowLocationTracking() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allowLocationTracking', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAllowLocationTrackingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allowLocationTracking', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAutoBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAutoLockEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockEnabled', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAutoLockEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockEnabled', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAutoLockTimeoutMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockTimeoutMinutes', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByAutoLockTimeoutMinutesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockTimeoutMinutes', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequency', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByBackupFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequency', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByBreedingReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByBreedingRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByCompactView() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'compactView', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByCompactViewDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'compactView', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByCurrency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currency', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByCurrencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currency', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByDateFormat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dateFormat', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByDateFormatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dateFormat', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByEmailNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotifications', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByEmailNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotifications', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByEventReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByEventRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByHealthReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByHealthRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByItemsPerPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByItemsPerPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByLanguage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'language', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByLanguageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'language', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByLastBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByLogSecurityEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logSecurityEvents', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByLogSecurityEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logSecurityEvents', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByMilkingReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkingReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByMilkingRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkingReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByPushNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByPushNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByRequireBiometricAuth() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requireBiometricAuth', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByRequireBiometricAuthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requireBiometricAuth', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByShareDataForAnalytics() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shareDataForAnalytics', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByShareDataForAnalyticsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shareDataForAnalytics', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByShowCattleImages() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showCattleImages', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByShowCattleImagesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showCattleImages', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByShowProfileToOthers() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showProfileToOthers', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByShowProfileToOthersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showProfileToOthers', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortBySmsNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotifications', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortBySmsNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotifications', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortBySyncWithCloud() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncWithCloud', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortBySyncWithCloudDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncWithCloud', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy> sortByTheme() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'theme', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByThemeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'theme', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByTimeFormat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timeFormat', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByTimeFormatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timeFormat', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByUserBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByUserBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByVaccinationReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      sortByVaccinationRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationReminders', Sort.desc);
    });
  }
}

extension UserSettingsIsarQuerySortThenBy
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QSortThenBy> {
  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAllowLocationTracking() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allowLocationTracking', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAllowLocationTrackingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allowLocationTracking', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAutoBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAutoLockEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockEnabled', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAutoLockEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockEnabled', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAutoLockTimeoutMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockTimeoutMinutes', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByAutoLockTimeoutMinutesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoLockTimeoutMinutes', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequency', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByBackupFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequency', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByBreedingReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByBreedingRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByCompactView() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'compactView', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByCompactViewDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'compactView', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByCurrency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currency', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByCurrencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currency', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByDateFormat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dateFormat', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByDateFormatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dateFormat', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByEmailNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotifications', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByEmailNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotifications', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByEventReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByEventRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByHealthReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByHealthRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByItemsPerPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByItemsPerPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByLanguage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'language', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByLanguageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'language', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByLastBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByLogSecurityEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logSecurityEvents', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByLogSecurityEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'logSecurityEvents', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByMilkingReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkingReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByMilkingRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkingReminders', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByPushNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByPushNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByRequireBiometricAuth() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requireBiometricAuth', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByRequireBiometricAuthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requireBiometricAuth', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByShareDataForAnalytics() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shareDataForAnalytics', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByShareDataForAnalyticsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shareDataForAnalytics', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByShowCattleImages() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showCattleImages', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByShowCattleImagesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showCattleImages', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByShowProfileToOthers() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showProfileToOthers', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByShowProfileToOthersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showProfileToOthers', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenBySmsNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotifications', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenBySmsNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotifications', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenBySyncWithCloud() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncWithCloud', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenBySyncWithCloudDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncWithCloud', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy> thenByTheme() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'theme', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByThemeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'theme', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByTimeFormat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timeFormat', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByTimeFormatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timeFormat', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByUserBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByUserBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.desc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByVaccinationReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationReminders', Sort.asc);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QAfterSortBy>
      thenByVaccinationRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationReminders', Sort.desc);
    });
  }
}

extension UserSettingsIsarQueryWhereDistinct
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct> {
  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByAllowLocationTracking() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'allowLocationTracking');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoBackupEnabled');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByAutoLockEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoLockEnabled');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByAutoLockTimeoutMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoLockTimeoutMinutes');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByBackupFrequency({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'backupFrequency',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByBreedingReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'breedingReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByCompactView() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'compactView');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByCurrency({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'currency', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByDateFormat({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dateFormat', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByEmailNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emailNotifications');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByEventReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByHealthReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'healthReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByItemsPerPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'itemsPerPage');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByLanguage({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'language', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByLastBackupDate({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastBackupDate',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByLogSecurityEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'logSecurityEvents');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByMilkingReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkingReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByPushNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pushNotifications');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByRequireBiometricAuth() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'requireBiometricAuth');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByShareDataForAnalytics() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'shareDataForAnalytics');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByShowCattleImages() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'showCattleImages');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByShowProfileToOthers() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'showProfileToOthers');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctBySmsNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'smsNotifications');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctBySyncWithCloud() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'syncWithCloud');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct> distinctByTheme(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'theme', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByTimeFormat({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'timeFormat', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByUserBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserSettingsIsar, UserSettingsIsar, QDistinct>
      distinctByVaccinationReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'vaccinationReminders');
    });
  }
}

extension UserSettingsIsarQueryProperty
    on QueryBuilder<UserSettingsIsar, UserSettingsIsar, QQueryProperty> {
  QueryBuilder<UserSettingsIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      allowLocationTrackingProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'allowLocationTracking');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      autoBackupEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoBackupEnabled');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      autoLockEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoLockEnabled');
    });
  }

  QueryBuilder<UserSettingsIsar, int, QQueryOperations>
      autoLockTimeoutMinutesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoLockTimeoutMinutes');
    });
  }

  QueryBuilder<UserSettingsIsar, String, QQueryOperations>
      backupFrequencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'backupFrequency');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      breedingRemindersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'breedingReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations> compactViewProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'compactView');
    });
  }

  QueryBuilder<UserSettingsIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<UserSettingsIsar, String, QQueryOperations> currencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'currency');
    });
  }

  QueryBuilder<UserSettingsIsar, String, QQueryOperations>
      dateFormatProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dateFormat');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      emailNotificationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emailNotifications');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      eventRemindersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      healthRemindersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'healthReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, int, QQueryOperations> itemsPerPageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'itemsPerPage');
    });
  }

  QueryBuilder<UserSettingsIsar, String, QQueryOperations> languageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'language');
    });
  }

  QueryBuilder<UserSettingsIsar, String?, QQueryOperations>
      lastBackupDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastBackupDate');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      logSecurityEventsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'logSecurityEvents');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      milkingRemindersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkingReminders');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      pushNotificationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pushNotifications');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      requireBiometricAuthProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'requireBiometricAuth');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      shareDataForAnalyticsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'shareDataForAnalytics');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      showCattleImagesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'showCattleImages');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      showProfileToOthersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'showProfileToOthers');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      smsNotificationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'smsNotifications');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      syncWithCloudProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'syncWithCloud');
    });
  }

  QueryBuilder<UserSettingsIsar, String, QQueryOperations> themeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'theme');
    });
  }

  QueryBuilder<UserSettingsIsar, String, QQueryOperations>
      timeFormatProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'timeFormat');
    });
  }

  QueryBuilder<UserSettingsIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<UserSettingsIsar, String?, QQueryOperations>
      userBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userBusinessId');
    });
  }

  QueryBuilder<UserSettingsIsar, bool, QQueryOperations>
      vaccinationRemindersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'vaccinationReminders');
    });
  }
}
