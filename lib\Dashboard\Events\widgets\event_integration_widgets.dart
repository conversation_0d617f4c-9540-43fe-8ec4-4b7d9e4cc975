import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../screens/event_details_screen.dart';
import '../../../constants/app_colors.dart';
import '../../../routes/app_routes.dart';

/// Widget for displaying event summary cards on dashboard
class EventSummaryCard extends StatelessWidget {
  final int totalEvents;
  final int upcomingEvents;
  final int overdueEvents;
  final int completedToday;

  const EventSummaryCard({
    Key? key,
    required this.totalEvents,
    required this.upcomingEvents,
    required this.overdueEvents,
    required this.completedToday,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.event,
                  color: AppColors.eventsHeader,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Events Overview',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.eventsHeader,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.pushNamed(context, AppRoutes.events),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total',
                    totalEvents.toString(),
                    Colors.blue,
                    Icons.event_note,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Upcoming',
                    upcomingEvents.toString(),
                    Colors.orange,
                    Icons.schedule,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Overdue',
                    overdueEvents.toString(),
                    Colors.red,
                    Icons.warning,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Completed Today',
                    completedToday.toString(),
                    Colors.green,
                    Icons.check_circle,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying cattle-specific events in cattle details
class CattleEventsList extends StatelessWidget {
  final List<EventIsar> events;
  final String cattleTagId;
  final int maxEvents;

  const CattleEventsList({
    Key? key,
    required this.events,
    required this.cattleTagId,
    this.maxEvents = 5,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (events.isEmpty) {
      return _buildEmptyState(context);
    }

    final displayEvents = events.take(maxEvents).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.timeline,
                  color: AppColors.eventsHeader,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Events',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _navigateToEventsWithFilter(context),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...displayEvents.map((event) => _buildEventItem(context, event)),
            if (events.length > maxEvents) ...[
              const SizedBox(height: 8),
              Center(
                child: TextButton(
                  onPressed: () => _navigateToEventsWithFilter(context),
                  child: Text('View ${events.length - maxEvents} more events'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.event_busy,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              'No Events Yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Events for this cattle will appear here',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventItem(BuildContext context, EventIsar event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _navigateToEventDetails(context, event),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _getCategoryColor(event.category),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      event.title ?? 'Untitled Event',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Icon(
                          _getCategoryIcon(event.category),
                          size: 12,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          event.category.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const Spacer(),
                        Text(
                          _formatDate(event.scheduledDate),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              _buildStatusChip(event.status),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(EventStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: _getStatusColor(status),
          width: 1,
        ),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: _getStatusColor(status),
        ),
      ),
    );
  }

  void _navigateToEventDetails(BuildContext context, EventIsar event) {
    if (event.businessId == null) return;
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EventDetailsScreen(
          eventBusinessId: event.businessId!,
        ),
      ),
    );
  }

  void _navigateToEventsWithFilter(BuildContext context) {
    Navigator.pushNamed(
      context,
      AppRoutes.events,
      arguments: {'cattleFilter': cattleTagId},
    );
  }

  Color _getCategoryColor(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return AppColors.healthHeader;
      case EventCategory.breeding:
        return AppColors.breedingHeader;
      case EventCategory.feeding:
        return Colors.green;
      case EventCategory.management:
        return Colors.blue;
      case EventCategory.maintenance:
        return Colors.orange;
      case EventCategory.financial:
        return AppColors.transactionHeader;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return AppColors.eventsHeader;
    }
  }

  IconData _getCategoryIcon(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return Icons.medical_services;
      case EventCategory.breeding:
        return Icons.favorite;
      case EventCategory.feeding:
        return Icons.restaurant;
      case EventCategory.management:
        return Icons.business;
      case EventCategory.maintenance:
        return Icons.build;
      case EventCategory.financial:
        return Icons.attach_money;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return Icons.event;
    }
  }

  Color _getStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.scheduled:
        return Colors.blue;
      case EventStatus.inProgress:
        return Colors.orange;
      case EventStatus.completed:
        return Colors.green;
      case EventStatus.cancelled:
        return Colors.grey;
      case EventStatus.overdue:
        return Colors.red;
      case EventStatus.missed:
        return Colors.red;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'No Date';
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate = DateTime(date.year, date.month, date.day);
    
    if (eventDate == today) {
      return 'Today';
    } else if (eventDate == today.add(const Duration(days: 1))) {
      return 'Tomorrow';
    } else if (eventDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else {
      return DateFormat('MMM dd').format(date);
    }
  }
}

/// Quick action button for creating events from other modules
class QuickEventButton extends StatelessWidget {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onPressed;

  const QuickEventButton({
    Key? key,
    required this.label,
    required this.icon,
    required this.color,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: color),
        label: Text(
          label,
          style: TextStyle(color: color),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: color),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
    );
  }
}

/// Widget for displaying event metrics in analytics screens
class EventMetricsWidget extends StatelessWidget {
  final Map<String, int> eventsByCategory;
  final int totalEvents;
  final String moduleFilter;

  const EventMetricsWidget({
    Key? key,
    required this.eventsByCategory,
    required this.totalEvents,
    required this.moduleFilter,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (totalEvents == 0) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.event,
                  color: AppColors.eventsHeader,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Related Events',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _navigateToEventsWithFilter(context),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Total Events: $totalEvents',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.eventsHeader,
              ),
            ),
            const SizedBox(height: 8),
            ...eventsByCategory.entries.map((entry) => 
              _buildCategoryItem(context, entry.key, entry.value)
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(BuildContext context, String category, int count) {
    final categoryEnum = EventCategory.values.firstWhere(
      (e) => e.displayName.toLowerCase() == category.toLowerCase(),
      orElse: () => EventCategory.other,
    );
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            _getCategoryIcon(categoryEnum),
            size: 16,
            color: _getCategoryColor(categoryEnum),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              category,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: _getCategoryColor(categoryEnum).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(categoryEnum),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToEventsWithFilter(BuildContext context) {
    Navigator.pushNamed(
      context,
      AppRoutes.events,
      arguments: {'moduleFilter': moduleFilter},
    );
  }

  Color _getCategoryColor(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return AppColors.healthHeader;
      case EventCategory.breeding:
        return AppColors.breedingHeader;
      case EventCategory.feeding:
        return Colors.green;
      case EventCategory.management:
        return Colors.blue;
      case EventCategory.maintenance:
        return Colors.orange;
      case EventCategory.financial:
        return AppColors.transactionHeader;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return AppColors.eventsHeader;
    }
  }

  IconData _getCategoryIcon(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return Icons.medical_services;
      case EventCategory.breeding:
        return Icons.favorite;
      case EventCategory.feeding:
        return Icons.restaurant;
      case EventCategory.management:
        return Icons.business;
      case EventCategory.maintenance:
        return Icons.build;
      case EventCategory.financial:
        return Icons.attach_money;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return Icons.event;
    }
  }
}