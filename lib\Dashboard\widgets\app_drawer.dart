import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../Farm Setup/services/farm_setup_repository.dart';
import '../Help/screens/help_screen.dart';
import '../Farm Setup/models/farm_isar.dart';
import '../Notifications/screens/notifications_screen.dart';
import '../User Account/services/auth_service.dart';
import '../User Account/controllers/auth_controller.dart';
import '../User Account/screens/user_account_screen.dart';
import '../User Account/screens/user_settings_screen.dart';
import '../Farm Setup/screens/farm_setup_screen.dart';
import '../Reports/screens/reports_screen.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../utils/message_utils.dart';
import '../../routes/app_routes.dart';

/// Modern app drawer with improved UI/UX and material design compliance
class AppDrawer extends StatefulWidget {
  const AppDrawer({Key? key}) : super(key: key);

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

/// Constants for the app drawer
class _DrawerConstants {
  static const String manageYourFarm = 'Manage Your Farm';
  static const String dashboard = 'Dashboard';
  static const String farmSetup = 'Farm Setup';
  static const String notifications = 'Notifications';
  static const String reports = 'Reports';
  static const String settings = 'Settings';
  static const String helpSupport = 'Help & Support';
  static const String version = 'Version 1.0.0';
  static const String demoMode = 'Demo Mode';
  // Removed unused signInMessage field
  static const String signInForFullAccess = 'Sign In for Full Access';
  static const String profile = 'Profile';
  static const String logout = 'Logout';
}

/// Responsive helper for drawer layout
class _ResponsiveHelper {
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  static bool isMediumScreen(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 900;
  }

  // Removed unused getAvatarRadius method

  static double getHeaderPadding(BuildContext context) {
    if (isSmallScreen(context)) return kSpacingMedium;
    if (isMediumScreen(context)) return kSpacingLarge;
    return kSpacingLarge + 8.0;
  }

  static double getSpacing(BuildContext context) {
    if (isSmallScreen(context)) return kSpacingSmall;
    if (isMediumScreen(context)) return 12.0;
    return kSpacingMedium;
  }

  static double getDrawerWidth(BuildContext context) {
    if (isSmallScreen(context)) return 280.0;
    if (isMediumScreen(context)) return 320.0;
    return 360.0;
  }
}

class _AppDrawerState extends State<AppDrawer> with SingleTickerProviderStateMixin {
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  final AuthService _authService = AuthService();
  final AuthController _authController = AuthController();
  FarmIsar? _currentFarm;
  bool _isDemoMode = false;
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    debugPrint('🔧 [APP_DRAWER] ========== APP DRAWER INITIALIZATION STARTED ==========');
    debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');

    _initializeAnimations();
    _loadFarm();
    _initializeAuthService();
    _initializeAuthController();
    _checkAuthState();

    debugPrint('🔧 [APP_DRAWER] ========== APP DRAWER INITIALIZATION COMPLETED ==========');
  }

  void _checkAuthState() {
    debugPrint('🔧 [APP_DRAWER] ========== CHECKING AUTH STATE ==========');
    debugPrint('🔧 [APP_DRAWER] AuthService.isAuthenticated: $_authService.isAuthenticated');
    debugPrint('🔧 [APP_DRAWER] Current user: ${_authService.currentUser?.email}');
    debugPrint('🔧 [APP_DRAWER] Current session valid: ${_authService.currentSession?.isValid}');

    final wasInDemoMode = _isDemoMode;
    setState(() {
      _isDemoMode = !_authService.isAuthenticated;
    });

    debugPrint('🔧 [APP_DRAWER] Demo mode changed: $wasInDemoMode -> $_isDemoMode');
    debugPrint('🔧 [APP_DRAWER] ========== AUTH STATE CHECK COMPLETED ==========');
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: kAnimationDurationMedium,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  void _initializeAuthService() {
    debugPrint('🔧 [APP_DRAWER] ========== INITIALIZING AUTH SERVICE ==========');
    debugPrint('🔧 [APP_DRAWER] AuthService.isAuthenticated: $_authService.isAuthenticated');
    debugPrint('🔧 [APP_DRAWER] Current user: ${_authService.currentUser?.email}');

    setState(() {
      _isDemoMode = !_authService.isAuthenticated;
    });

    debugPrint('🔧 [APP_DRAWER] Demo mode set to: $_isDemoMode');
    debugPrint('🔧 [APP_DRAWER] ========== AUTH SERVICE INITIALIZATION COMPLETED ==========');
  }

  Future<void> _initializeAuthController() async {
    debugPrint('🔧 [APP_DRAWER] ========== INITIALIZING AUTH CONTROLLER ==========');
    try {
      debugPrint('🔧 [APP_DRAWER] Calling AuthController.initialize()...');
      await _authController.initialize();
      debugPrint('✅ [APP_DRAWER] AuthController initialized successfully');
      debugPrint('🔧 [APP_DRAWER] AuthController.isInitialized: $_authController.isInitialized');
      debugPrint('🔧 [APP_DRAWER] AuthController.isAuthenticated: $_authController.isAuthenticated');
      debugPrint('🔧 [APP_DRAWER] AuthController.currentUser: ${_authController.currentUser?.email}');
    } catch (e, stackTrace) {
      debugPrint('❌ [APP_DRAWER] Error initializing AuthController: $e');
      debugPrint('❌ [APP_DRAWER] Stack trace: $stackTrace');
    }
    debugPrint('🔧 [APP_DRAWER] ========== AUTH CONTROLLER INITIALIZATION COMPLETED ==========');
  }

  Future<void> _loadFarm() async {
    debugPrint('🔧 [APP_DRAWER] Loading farm data...');
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final farm = await _farmSetupRepository.getCurrentFarm();
      debugPrint('🔧 [APP_DRAWER] Current farm: ${farm?.name ?? 'No farm found'}');

      if (!mounted) return;

      setState(() {
        _currentFarm = farm;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      debugPrint('❌ [APP_DRAWER] Error loading farm: $e');
      debugPrint('$_DrawerConstants.errorLoadingFarm$e\n$stackTrace');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        MessageUtils.showError(context, 'Failed to load farm information');
      }
    }
  }

  /// Navigate to a screen with smooth animation
  void _navigateTo(Widget screen) {
    debugPrint('🔧 [APP_DRAWER] ========== NAVIGATE TO METHOD ==========');
    debugPrint('🔧 [APP_DRAWER] Target screen: $screen.runtimeType');
    debugPrint('🔧 [APP_DRAWER] Current context: $context');
    debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
    debugPrint('🔧 [APP_DRAWER] Navigator can pop: ${Navigator.canPop(context)}');

    try {
      debugPrint('🔧 [APP_DRAWER] Step 1: Closing drawer...');
      Navigator.pop(context);
      debugPrint('🔧 [APP_DRAWER] Step 2: Drawer closed successfully');

      debugPrint('🔧 [APP_DRAWER] Step 3: Pushing new route...');
      Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) {
            debugPrint('🔧 [APP_DRAWER] Step 4: PageBuilder called for $screen.runtimeType');
            return screen;
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            debugPrint('🔧 [APP_DRAWER] Step 5: TransitionsBuilder called');
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOut,
              )),
              child: child,
            );
          },
          transitionDuration: kAnimationDurationMedium,
        ),
      ).then((_) {
        debugPrint('🔧 [APP_DRAWER] Step 6: Navigation completed successfully');
      }).catchError((error) {
        debugPrint('❌ [APP_DRAWER] Navigation error in then/catchError: $error');
      });

      debugPrint('🔧 [APP_DRAWER] Navigation method completed');
    } catch (e) {
      debugPrint('❌ [APP_DRAWER] Navigation method error: $e');
      debugPrint('❌ [APP_DRAWER] Stack trace: $StackTrace.current');
    }
  }

  Future<void> _handleLogout() async {
    debugPrint('🔧 [APP_DRAWER] ========== LOGOUT PROCESS STARTED ==========');
    debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
    debugPrint('🔧 [APP_DRAWER] Current auth state: $_authService.isAuthenticated');
    debugPrint('🔧 [APP_DRAWER] Current user: ${_authService.currentUser?.email}');
    debugPrint('🔧 [APP_DRAWER] Current demo mode: $_isDemoMode');

    if (!mounted) {
      debugPrint('❌ [APP_DRAWER] Widget not mounted, aborting logout');
      return;
    }

    // CRITICAL: Capture navigator context BEFORE any dialogs or navigation
    debugPrint('🔧 [APP_DRAWER] Step 1: Capturing navigator context while widget is mounted...');
    NavigatorState? navigatorContext;
    try {
      navigatorContext = Navigator.of(context);
      debugPrint('✅ [APP_DRAWER] Navigator context captured successfully');
    } catch (e) {
      debugPrint('❌ [APP_DRAWER] Error capturing navigator context: $e');
      return; // Can't proceed without navigation capability
    }

    debugPrint('🔧 [APP_DRAWER] Step 2: Closing drawer...');
    Navigator.pop(context); // Close drawer
    debugPrint('✅ [APP_DRAWER] Drawer closed successfully');

    debugPrint('🔧 [APP_DRAWER] Step 3: Showing logout confirmation dialog...');
    // Show confirmation dialog
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?\n\nYou will be redirected to the welcome screen where you can sign in again or continue in demo mode.'),
        actions: [
          TextButton(
            onPressed: () {
              debugPrint('🔧 [APP_DRAWER] User cancelled logout');
              Navigator.of(context).pop(false);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              debugPrint('🔧 [APP_DRAWER] User confirmed logout');
              Navigator.of(context).pop(true);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    debugPrint('🔧 [APP_DRAWER] Dialog result: $shouldLogout');
    debugPrint('🔧 [APP_DRAWER] Widget mounted after dialog: $mounted');

    if (shouldLogout == true) {
      debugPrint('🔧 [APP_DRAWER] ========== EXECUTING LOGOUT ==========');
      debugPrint('🔧 [APP_DRAWER] Widget mounted status: $mounted');
      debugPrint('🔧 [APP_DRAWER] Pre-captured navigator context available: true');

      try {
        // Show loading indicator if widget is still mounted
        if (mounted) {
          debugPrint('🔧 [APP_DRAWER] Step 3: Showing loading indicator...');
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const Center(
              child: CircularProgressIndicator(),
            ),
          );
          debugPrint('✅ [APP_DRAWER] Loading dialog shown');
        }

        debugPrint('🔧 [APP_DRAWER] Step 4: Calling AuthController.logout()...');
        debugPrint('🔧 [APP_DRAWER] AuthController initialized: $_authController.isInitialized');
        debugPrint('🔧 [APP_DRAWER] AuthController loading: $_authController.isLoading');

        // Perform logout using AuthController for proper cleanup
        // This should work even if the widget is unmounted
        await _authController.signOut();

        debugPrint('✅ [APP_DRAWER] AuthController.logout() completed');
        debugPrint('🔧 [APP_DRAWER] Post-logout auth state: $_authService.isAuthenticated');
        debugPrint('🔧 [APP_DRAWER] Post-logout current user: ${_authService.currentUser?.email}');
        debugPrint('🔧 [APP_DRAWER] AuthController success message: $_authController.successMessage');
        debugPrint('🔧 [APP_DRAWER] AuthController error message: $_authController.errorMessage');
        debugPrint('🔧 [APP_DRAWER] Widget mounted after logout: $mounted');

        // Small delay to ensure logout is fully processed
        await Future.delayed(const Duration(milliseconds: 100));

        // Close loading dialog if it was shown and widget is still mounted
        if (mounted) {
          debugPrint('🔧 [APP_DRAWER] Step 5: Closing loading dialog...');
          try {
            Navigator.of(context).pop();
            debugPrint('✅ [APP_DRAWER] Loading dialog closed');
          } catch (popError) {
            debugPrint('❌ [APP_DRAWER] Error closing loading dialog: $popError');
          }
        }

        debugPrint('🔧 [APP_DRAWER] Step 6: Navigating to welcome screen...');
        debugPrint('🔧 [APP_DRAWER] Target route: $AppRoutes.welcome');
        debugPrint('🔧 [APP_DRAWER] Pre-captured navigator context available: true');
        debugPrint('🔧 [APP_DRAWER] Widget mounted for navigation: $mounted');

        bool navigationSuccessful = false;

        // Primary strategy: Use pre-captured navigator context (should always work)
        try {
          debugPrint('🔧 [APP_DRAWER] Attempting navigation with pre-captured navigator context...');
          navigatorContext.pushNamedAndRemoveUntil(
            AppRoutes.welcome,
            (route) => false,
          );
          navigationSuccessful = true;
          debugPrint('✅ [APP_DRAWER] Navigation to welcome screen initiated via pre-captured context');
        } catch (navError) {
          debugPrint('❌ [APP_DRAWER] Navigation error with pre-captured context: $navError');
          debugPrint('❌ [APP_DRAWER] This should not happen - pre-captured context failed');
        }
      
        // Fallback: Only try if pre-captured context failed (should be rare)
        if (!navigationSuccessful) {
          debugPrint('🔧 [APP_DRAWER] Pre-captured context failed, trying fallback strategies...');

          // Fallback 1: Try with current context if widget is still mounted
          if (mounted) {
            try {
              debugPrint('🔧 [APP_DRAWER] Trying fallback navigation with current context...');
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRoutes.welcome,
                (route) => false,
              );
              navigationSuccessful = true;
              debugPrint('✅ [APP_DRAWER] Fallback navigation with current context successful');
            } catch (fallbackError) {
              debugPrint('❌ [APP_DRAWER] Fallback navigation error: $fallbackError');
            }
          }

          // Fallback 2: Try with root navigator if current context failed
          if (!navigationSuccessful && mounted) {
            try {
              debugPrint('🔧 [APP_DRAWER] Trying root navigator as last resort...');
              Navigator.of(context, rootNavigator: true).pushNamedAndRemoveUntil(
                AppRoutes.welcome,
                (route) => false,
              );
              navigationSuccessful = true;
              debugPrint('✅ [APP_DRAWER] Root navigator navigation successful');
            } catch (rootError) {
              debugPrint('❌ [APP_DRAWER] Root navigator error: $rootError');
            }
          }
        }

        if (!navigationSuccessful) {
          debugPrint('❌ [APP_DRAWER] All navigation attempts failed');
          debugPrint('❌ [APP_DRAWER] Widget mounted: $mounted');
          debugPrint('❌ [APP_DRAWER] Navigator context: available');

          // Last resort: Use a delayed approach to navigate after the current frame
          debugPrint('🔧 [APP_DRAWER] Attempting delayed navigation as last resort...');
          WidgetsBinding.instance.addPostFrameCallback((_) {
            try {
              // Try to find any available context in the widget tree
              final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
              if (context != null) {
                debugPrint('🔧 [APP_DRAWER] Found focus context, attempting navigation...');
                Navigator.of(context, rootNavigator: true).pushNamedAndRemoveUntil(
                  AppRoutes.welcome,
                  (route) => false,
                );
                debugPrint('✅ [APP_DRAWER] Delayed navigation successful');
              } else {
                debugPrint('❌ [APP_DRAWER] No focus context available for delayed navigation');
              }
            } catch (delayedError) {
              debugPrint('❌ [APP_DRAWER] Delayed navigation error: $delayedError');
            }
          });
        }

        // Update local state if widget is still mounted
        if (mounted) {
          debugPrint('🔧 [APP_DRAWER] Step 7: Updating local state...');
          setState(() {
            _isDemoMode = true;
          });
          debugPrint('✅ [APP_DRAWER] Local state updated - demo mode: $_isDemoMode');
        } else {
          debugPrint('ℹ️ [APP_DRAWER] Widget unmounted, skipping local state update');
        }

        debugPrint('🎉 [APP_DRAWER] ========== LOGOUT COMPLETED SUCCESSFULLY ==========');

      } catch (e, stackTrace) {
        debugPrint('❌ [APP_DRAWER] ========== LOGOUT ERROR ==========');
        debugPrint('❌ [APP_DRAWER] Error during logout: $e');
        debugPrint('❌ [APP_DRAWER] Stack trace: $stackTrace');
        debugPrint('🔧 [APP_DRAWER] AuthController error message: $_authController.errorMessage');
        debugPrint('🔧 [APP_DRAWER] Widget mounted during error: $mounted');

        // Close loading dialog if widget is still mounted
        if (mounted) {
          debugPrint('🔧 [APP_DRAWER] Closing loading dialog due to error...');
          try {
            Navigator.of(context).pop();
            debugPrint('✅ [APP_DRAWER] Loading dialog closed after error');
          } catch (popError) {
            debugPrint('❌ [APP_DRAWER] Error closing loading dialog: $popError');
          }

          debugPrint('🔧 [APP_DRAWER] Showing error message to user...');
          MessageUtils.showError(context, 'Failed to logout. Please try again.');
          debugPrint('✅ [APP_DRAWER] Error message shown to user');
        }
        debugPrint('❌ [APP_DRAWER] ========== LOGOUT ERROR HANDLING COMPLETED ==========');
      }
    } else if (shouldLogout == false) {
      debugPrint('ℹ️ [APP_DRAWER] User cancelled logout - no action taken');
    } else {
      debugPrint('⚠️ [APP_DRAWER] Unexpected logout dialog result: $shouldLogout');
    }

    debugPrint('🔧 [APP_DRAWER] ========== LOGOUT PROCESS ENDED ==========');
  }

  /// Build a simple status indicator for demo/authenticated mode
  Widget _buildStatusIndicator() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            margin: EdgeInsets.symmetric(
              horizontal: _ResponsiveHelper.getHeaderPadding(context),
              vertical: _ResponsiveHelper.getSpacing(context),
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: kSpacingMedium,
              vertical: kSpacingSmall,
            ),
            decoration: BoxDecoration(
              color: _authService.isAuthenticated
                  ? AppColors.info.withValues(alpha: 0.1)
                  : AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(kBorderRadius),
              border: Border.all(
                color: _authService.isAuthenticated
                    ? AppColors.info.withValues(alpha: 0.3)
                    : AppColors.warning.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _authService.isAuthenticated
                      ? Icons.verified_user_rounded
                      : Icons.person_outline,
                  color: _authService.isAuthenticated
                      ? AppColors.info
                      : AppColors.warning,
                  size: 16,
                ),
                const SizedBox(width: kSpacingSmall),
                Text(
                  _authService.isAuthenticated
                      ? 'Authenticated'
                      : _DrawerConstants.demoMode,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: _authService.isAuthenticated
                        ? AppColors.info
                        : AppColors.warning,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }



  Widget _buildBottomAuthButtons() {
    return Padding(
      padding: EdgeInsets.all(_ResponsiveHelper.getHeaderPadding(context)),
      child: _authService.isAuthenticated ? _buildAuthenticatedButtons() : _buildDemoModeButtons(),
    );
  }

  Widget _buildDemoModeButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              debugPrint('🔧 [APP_DRAWER] Sign In button tapped - navigating to login');
              Navigator.pop(context);
              Navigator.pushNamed(context, AppRoutes.login);
            },
            icon: const Icon(Icons.login_rounded),
            label: const Text(
              _DrawerConstants.signInForFullAccess,
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(kBorderRadius + 4),
              ),
              elevation: 2,
            ),
          ),
        ),
        const SizedBox(height: kSpacingSmall),
      ],
    );
  }

  Widget _buildAuthenticatedButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              debugPrint('🔧 [APP_DRAWER] ========== PROFILE BUTTON TAPPED ==========');
              debugPrint('🔧 [APP_DRAWER] Attempting to navigate to UserAccountScreen');
              debugPrint('🔧 [APP_DRAWER] Current context: $context');
              debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
              debugPrint('🔧 [APP_DRAWER] Auth state: $_authService.isAuthenticated');
              try {
                _navigateTo(const UserAccountScreen());
                debugPrint('🔧 [APP_DRAWER] Profile navigation: Called _navigateTo successfully');
              } catch (e) {
                debugPrint('❌ [APP_DRAWER] Profile navigation error: $e');
                debugPrint('❌ [APP_DRAWER] Stack trace: $StackTrace.current');
              }
            },
            icon: const Icon(Icons.person_outline),
            label: const Text(_DrawerConstants.profile),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 14),
              side: const BorderSide(color: AppColors.primary),
              foregroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(kBorderRadius + 4),
              ),
            ),
          ),
        ),
        const SizedBox(height: kSpacingSmall),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _handleLogout,
            icon: const Icon(Icons.logout_rounded),
            label: const Text(_DrawerConstants.logout),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 14),
              side: const BorderSide(color: Colors.red),
              foregroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(kBorderRadius + 4),
              ),
            ),
          ),
        ),
        const SizedBox(height: kSpacingSmall),
      ],
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    bool showDivider = false,
  }) {
    final theme = Theme.of(context);
    final isSmall = _ResponsiveHelper.isSmallScreen(context);

    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(kBorderRadius),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: _ResponsiveHelper.getHeaderPadding(context),
                vertical: isSmall ? 12.0 : 16.0,
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(kBorderRadius),
                    ),
                    child: Icon(
                      icon,
                      color: iconColor ?? AppColors.primary,
                      size: isSmall ? kIconSizeMedium : kIconSizeLarge,
                    ),
                  ),
                  const SizedBox(width: kSpacingMedium),
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    Icons.chevron_right_rounded,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
                    size: kIconSizeMedium,
                  ),
                ],
              ),
            ),
          ),
        ),
        if (showDivider)
          Divider(
            height: 1,
            color: theme.dividerColor.withValues(alpha: 0.5),
            indent: _ResponsiveHelper.getHeaderPadding(context) + 48,
            endIndent: _ResponsiveHelper.getHeaderPadding(context),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Drawer(
        width: _ResponsiveHelper.getDrawerWidth(context),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final mediaQuery = MediaQuery.of(context);

    return Drawer(
      width: _ResponsiveHelper.getDrawerWidth(context),
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(2, 0),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildDrawerHeader(mediaQuery, colorScheme, textTheme),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  // Status Indicator
                  _buildStatusIndicator(),
                  const SizedBox(height: kSpacingSmall),

                  // Navigation Items
                  _buildNavigationSection(),
                ],
              ),
            ),

            // Bottom authentication buttons
            _buildBottomAuthButtons(),

            // Version info
            _buildVersionInfo(textTheme, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader(MediaQueryData mediaQuery, ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: EdgeInsets.only(
        top: mediaQuery.padding.top + _ResponsiveHelper.getHeaderPadding(context),
        bottom: _ResponsiveHelper.getHeaderPadding(context),
        left: _ResponsiveHelper.getHeaderPadding(context),
        right: _ResponsiveHelper.getHeaderPadding(context),
      ),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          _buildHeaderAvatar(),
          SizedBox(height: _ResponsiveHelper.getSpacing(context)),
          _buildHeaderUserInfo(textTheme, colorScheme),
          SizedBox(height: _ResponsiveHelper.getSpacing(context) * 0.5),
          _buildHeaderSubtitle(textTheme, colorScheme),
        ],
      ),
    );
  }

  Widget _buildHeaderAvatar() {
    final currentUser = _authService.currentUser;
    const avatarRadius = 32.0; // Fixed radius as requested

    // Check for profile image URL (Google OAuth profile image)
    if (currentUser?.profileImageUrl != null || currentUser?.profilePictureUrl != null) {
      final imageUrl = currentUser?.profileImageUrl ?? currentUser?.profilePictureUrl;
      return CircleAvatar(
        radius: avatarRadius,
        backgroundColor: AppColors.info.withValues(alpha: 0.2),
        backgroundImage: NetworkImage(imageUrl!),
        onBackgroundImageError: (exception, stackTrace) {
          // Fallback handled by child widget
        },
        child: currentUser?.fullName.isNotEmpty == true
            ? null
            : const Icon(
                Icons.person,
                size: avatarRadius * 0.8,
                color: AppColors.info,
              ),
      );
    } else if (currentUser?.fullName.isNotEmpty == true) {
      // Show initials if no profile image
      return CircleAvatar(
        radius: avatarRadius,
        backgroundColor: AppColors.info.withValues(alpha: 0.2),
        child: Text(
          _getInitials(currentUser!.fullName),
          style: const TextStyle(
            color: AppColors.info,
            fontWeight: FontWeight.bold,
            fontSize: avatarRadius * 0.5,
          ),
        ),
      );
    } else {
      // Default farm icon for demo mode or users without names
      return CircleAvatar(
        radius: avatarRadius,
        backgroundColor: AppColors.info.withValues(alpha: 0.2),
        child: const Icon(
          Icons.agriculture_rounded,
          size: avatarRadius * 0.8,
          color: AppColors.info,
        ),
      );
    }
  }

  Widget _buildHeaderUserInfo(TextTheme textTheme, ColorScheme colorScheme) {
    final currentUser = _authService.currentUser;

    if (_authService.isAuthenticated && currentUser != null) {
      // Show user name and farm name if authenticated
      return Column(
        children: [
          Text(
            currentUser.fullName.isNotEmpty ? currentUser.fullName : 'User',
            style: textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w500, // Reduced from bold to w500
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (currentUser.farmName?.isNotEmpty == true) ...[
            const SizedBox(height: 6), // Increased spacing slightly
            Text(
              currentUser.farmName ?? 'My Farm',
              style: textTheme.titleMedium?.copyWith( // Increased from bodyMedium to titleMedium
                color: AppColors.primary, // Different color - using primary green
                fontWeight: FontWeight.w600, // Increased font weight
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ] else if (_currentFarm?.name != null) ...[
            const SizedBox(height: 6), // Increased spacing slightly
            Text(
              _currentFarm?.name ?? 'My Farm',
              style: textTheme.titleMedium?.copyWith( // Increased from bodyMedium to titleMedium
                color: AppColors.primary, // Different color - using primary green
                fontWeight: FontWeight.w600, // Increased font weight
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      );
    } else {
      // Show farm name for demo mode users
      return Text(
        _currentFarm?.name ?? 'My Farm',
        style: textTheme.headlineSmall?.copyWith(
          color: AppColors.primary, // Different color for farm name
          fontWeight: FontWeight.w600, // Reduced from bold to w600
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  Widget _buildHeaderSubtitle(TextTheme textTheme, ColorScheme colorScheme) {
    final currentUser = _authService.currentUser;

    if (_authService.isAuthenticated && currentUser != null) {
      // Show email for authenticated users
      String subtitle = currentUser.email ?? _DrawerConstants.manageYourFarm;

      return Text(
        subtitle,
        style: textTheme.bodyMedium?.copyWith( // Increased from bodySmall to bodyMedium
          color: AppColors.info, // Different color - using info blue for email
          fontWeight: FontWeight.w500, // Added font weight for better visibility
        ),
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    } else {
      // Default subtitle for demo mode users
      return Text(
        _DrawerConstants.manageYourFarm,
        style: textTheme.bodySmall?.copyWith(
          color: colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        textAlign: TextAlign.center,
      );
    }
  }

  String _getInitials(String fullName) {
    final names = fullName.trim().split(' ');
    if (names.length >= 2) {
      return '${names.first[0]}${names.last[0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names.first[0].toUpperCase();
    }
    return 'U';
  }

  Widget _buildNavigationSection() {
    return Column(
      children: [
        _buildDrawerItem(
          icon: Icons.dashboard_rounded,
          title: _DrawerConstants.dashboard,
          onTap: () {
            debugPrint('🔧 [APP_DRAWER] ========== DASHBOARD BUTTON TAPPED ==========');
            debugPrint('🔧 [APP_DRAWER] Current route: ${ModalRoute.of(context)?.settings.name}');
            debugPrint('🔧 [APP_DRAWER] Navigator can pop: ${Navigator.canPop(context)}');
            try {
              Navigator.pop(context); // Close drawer
              debugPrint('🔧 [APP_DRAWER] Dashboard navigation: Drawer closed successfully');
              // Dashboard should already be showing, so just closing drawer is correct
            } catch (e) {
              debugPrint('❌ [APP_DRAWER] Dashboard navigation error: $e');
            }
          },
          iconColor: AppColors.cattleHeader,
          showDivider: true,
        ),
        _buildDrawerItem(
          icon: Icons.settings_applications_rounded,
          title: _DrawerConstants.farmSetup,
          onTap: () {
            debugPrint('🔧 [APP_DRAWER] ========== FARM SETUP BUTTON TAPPED ==========');
            debugPrint('🔧 [APP_DRAWER] Attempting to navigate to FarmSetupScreen');
            debugPrint('🔧 [APP_DRAWER] Current context: $context');
            debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
            try {
              _navigateTo(const FarmSetupScreen());
              debugPrint('🔧 [APP_DRAWER] Farm Setup navigation: Called _navigateTo successfully');
            } catch (e) {
              debugPrint('❌ [APP_DRAWER] Farm Setup navigation error: $e');
              debugPrint('❌ [APP_DRAWER] Stack trace: $StackTrace.current');
            }
          },
          iconColor: AppColors.weightHeader,
          showDivider: true,
        ),
        _buildDrawerItem(
          icon: Icons.notifications_rounded,
          title: _DrawerConstants.notifications,
          onTap: () {
            debugPrint('🔧 [APP_DRAWER] ========== NOTIFICATIONS BUTTON TAPPED ==========');
            debugPrint('🔧 [APP_DRAWER] Attempting to navigate to NotificationsScreen');
            debugPrint('🔧 [APP_DRAWER] Current context: $context');
            debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
            try {
              _navigateTo(const NotificationsScreen());
              debugPrint('🔧 [APP_DRAWER] Notifications navigation: Called _navigateTo successfully');
            } catch (e) {
              debugPrint('❌ [APP_DRAWER] Notifications navigation error: $e');
              debugPrint('❌ [APP_DRAWER] Stack trace: $StackTrace.current');
            }
          },
          iconColor: AppColors.milkHeader,
          showDivider: true,
        ),
        _buildDrawerItem(
          icon: Icons.assessment_rounded,
          title: _DrawerConstants.reports,
          onTap: () {
            debugPrint('🔧 [APP_DRAWER] ========== REPORTS BUTTON TAPPED ==========');
            debugPrint('🔧 [APP_DRAWER] Attempting to navigate to ReportsScreen');
            debugPrint('🔧 [APP_DRAWER] Current context: $context');
            debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
            try {
              _navigateTo(const ReportsScreen());
              debugPrint('🔧 [APP_DRAWER] Reports navigation: Called _navigateTo successfully');
            } catch (e) {
              debugPrint('❌ [APP_DRAWER] Reports navigation error: $e');
              debugPrint('❌ [APP_DRAWER] Stack trace: $StackTrace.current');
            }
          },
          iconColor: AppColors.breedingHeader,
          showDivider: true,
        ),
        _buildDrawerItem(
          icon: Icons.settings_rounded,
          title: _DrawerConstants.settings,
          onTap: () {
            debugPrint('🔧 [APP_DRAWER] ========== SETTINGS BUTTON TAPPED ==========');
            debugPrint('🔧 [APP_DRAWER] Attempting to navigate to UserSettingsScreen');
            debugPrint('🔧 [APP_DRAWER] Current context: $context');
            debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
            try {
              _navigateTo(const UserSettingsScreen());
              debugPrint('🔧 [APP_DRAWER] Settings navigation: Called _navigateTo successfully');
            } catch (e) {
              debugPrint('❌ [APP_DRAWER] Settings navigation error: $e');
              debugPrint('❌ [APP_DRAWER] Stack trace: $StackTrace.current');
            }
          },
          iconColor: AppColors.healthHeader,
          showDivider: true,
        ),
        _buildDrawerItem(
          icon: Icons.help_outline_rounded,
          title: _DrawerConstants.helpSupport,
          onTap: () {
            debugPrint('🔧 [APP_DRAWER] ========== HELP & SUPPORT BUTTON TAPPED ==========');
            debugPrint('🔧 [APP_DRAWER] Attempting to navigate to HelpScreen');
            debugPrint('🔧 [APP_DRAWER] Current context: $context');
            debugPrint('🔧 [APP_DRAWER] Widget mounted: $mounted');
            try {
              _navigateTo(const HelpScreen());
              debugPrint('🔧 [APP_DRAWER] Help & Support navigation: Called _navigateTo successfully');
            } catch (e) {
              debugPrint('❌ [APP_DRAWER] Help & Support navigation error: $e');
              debugPrint('❌ [APP_DRAWER] Stack trace: $StackTrace.current');
            }
          },
          iconColor: AppColors.eventsHeader,
          showDivider: false,
        ),
      ],
    );
  }

  Widget _buildVersionInfo(TextTheme textTheme, ColorScheme colorScheme) {
    return Container(
      padding: EdgeInsets.all(_ResponsiveHelper.getHeaderPadding(context)),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline_rounded,
            size: 16,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(width: kSpacingSmall),
          Text(
            _DrawerConstants.version,
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}
