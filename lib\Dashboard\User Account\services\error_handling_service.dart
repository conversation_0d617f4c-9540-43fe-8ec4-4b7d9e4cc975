import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Service for handling and displaying user account related errors
class ErrorHandlingService {
  static final ErrorHandlingService _instance = ErrorHandlingService._internal();
  factory ErrorHandlingService() => _instance;
  ErrorHandlingService._internal();

  final Logger _logger = Logger('ErrorHandlingService');

  /// Handle authentication errors and return user-friendly messages
  String handleAuthError(dynamic error) {
    _logger.severe('Authentication error: $error');

    if (error is ValidationException) {
      return error.message;
    }

    if (error is DatabaseException) {
      return _handleDatabaseError(error);
    }

    // Handle specific error types
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network connection error. Please check your internet connection and try again.';
    }

    if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    }

    if (errorString.contains('invalid credentials') || errorString.contains('wrong password')) {
      return 'Invalid email/username or password. Please check your credentials and try again.';
    }

    if (errorString.contains('account locked') || errorString.contains('locked')) {
      return 'Your account has been temporarily locked due to multiple failed login attempts. Please try again later.';
    }

    if (errorString.contains('email not verified')) {
      return 'Please verify your email address before logging in. Check your inbox for verification instructions.';
    }

    if (errorString.contains('account disabled') || errorString.contains('deactivated')) {
      return 'Your account has been deactivated. Please contact support for assistance.';
    }

    if (errorString.contains('email already exists') || errorString.contains('duplicate email')) {
      return 'An account with this email address already exists. Please use a different email or try logging in.';
    }

    if (errorString.contains('username already exists') || errorString.contains('duplicate username')) {
      return 'This username is already taken. Please choose a different username.';
    }

    if (errorString.contains('weak password') || errorString.contains('password strength')) {
      return 'Password does not meet security requirements. Please choose a stronger password.';
    }

    if (errorString.contains('token expired') || errorString.contains('expired token')) {
      return 'The verification link has expired. Please request a new one.';
    }

    if (errorString.contains('invalid token')) {
      return 'Invalid verification link. Please check the link or request a new one.';
    }

    // Generic fallback
    return 'An unexpected error occurred. Please try again later.';
  }

  /// Handle profile management errors
  String handleProfileError(dynamic error) {
    _logger.severe('Profile error: $error');

    if (error is ValidationException) {
      return error.message;
    }

    if (error is DatabaseException) {
      return _handleDatabaseError(error);
    }

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('permission denied') || errorString.contains('unauthorized')) {
      return 'You do not have permission to perform this action.';
    }

    if (errorString.contains('file too large') || errorString.contains('size limit')) {
      return 'The selected file is too large. Please choose a smaller file.';
    }

    if (errorString.contains('invalid file format') || errorString.contains('unsupported format')) {
      return 'Invalid file format. Please select a valid image file.';
    }

    if (errorString.contains('upload failed')) {
      return 'Failed to upload file. Please check your connection and try again.';
    }

    return 'Failed to update profile. Please try again.';
  }

  /// Handle data ownership errors
  String handleOwnershipError(dynamic error) {
    _logger.severe('Ownership error: $error');

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('access denied') || errorString.contains('unauthorized')) {
      return 'You do not have permission to access this data.';
    }

    if (errorString.contains('not owner') || errorString.contains('ownership')) {
      return 'You can only modify data that you own.';
    }

    if (errorString.contains('transfer failed')) {
      return 'Failed to transfer ownership. Please try again.';
    }

    return 'Access control error. Please contact support if this persists.';
  }

  /// Handle security-related errors
  String handleSecurityError(dynamic error) {
    _logger.severe('Security error: $error');

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('rate limit') || errorString.contains('too many requests')) {
      return 'Too many requests. Please wait a moment before trying again.';
    }

    if (errorString.contains('suspicious activity')) {
      return 'Suspicious activity detected. Your account has been temporarily restricted for security.';
    }

    if (errorString.contains('ip blocked') || errorString.contains('blocked')) {
      return 'Access blocked for security reasons. Please contact support.';
    }

    if (errorString.contains('session expired')) {
      return 'Your session has expired. Please log in again.';
    }

    if (errorString.contains('invalid session')) {
      return 'Invalid session. Please log in again.';
    }

    return 'Security error occurred. Please log in again.';
  }

  /// Show error dialog
  void showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[700]),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar
  void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red[700],
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Show success snackbar
  void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green[700],
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Show warning snackbar
  void showWarningSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning_amber_outlined, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.orange[700],
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Handle database errors
  String _handleDatabaseError(DatabaseException error) {
    final message = error.message.toLowerCase();

    if (message.contains('connection')) {
      return 'Database connection error. Please try again later.';
    }

    if (message.contains('timeout')) {
      return 'Database operation timed out. Please try again.';
    }

    if (message.contains('constraint') || message.contains('unique')) {
      return 'This data already exists. Please use different values.';
    }

    if (message.contains('not found')) {
      return 'The requested data was not found.';
    }

    if (message.contains('permission') || message.contains('access')) {
      return 'Database access error. Please contact support.';
    }

    return 'Database error occurred. Please try again later.';
  }

  /// Log error for debugging
  void logError(String context, dynamic error, [StackTrace? stackTrace]) {
    _logger.severe('Error in $context: $error', error, stackTrace);
  }

  /// Check if error is recoverable
  bool isRecoverableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Network errors are usually recoverable
    if (errorString.contains('network') || 
        errorString.contains('connection') || 
        errorString.contains('timeout')) {
      return true;
    }

    // Rate limiting is recoverable
    if (errorString.contains('rate limit') || 
        errorString.contains('too many requests')) {
      return true;
    }

    // Validation errors are recoverable (user can fix input)
    if (error is ValidationException) {
      return true;
    }

    // Most other errors are not easily recoverable
    return false;
  }

  /// Get retry delay for recoverable errors
  Duration getRetryDelay(dynamic error, int attemptNumber) {
    const baseDelay = Duration(seconds: 2);
    final exponentialDelay = Duration(seconds: (2 * attemptNumber).clamp(2, 30));
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('rate limit')) {
      return const Duration(minutes: 1); // Wait longer for rate limits
    }
    
    if (errorString.contains('network') || errorString.contains('timeout')) {
      return exponentialDelay; // Exponential backoff for network issues
    }
    
    return baseDelay;
  }
}
