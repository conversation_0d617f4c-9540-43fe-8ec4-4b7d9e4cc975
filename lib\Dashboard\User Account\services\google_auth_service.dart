import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../models/user_isar.dart';
import 'user_repository.dart';
import 'auth_service.dart';
import 'error_handling_service.dart';

/// Service for handling Google Sign-In authentication
class GoogleAuthService {
  static final GoogleAuthService _instance = GoogleAuthService._internal();
  factory GoogleAuthService() => _instance;
  GoogleAuthService._internal();

  final Logger _logger = Logger('GoogleAuthService');
  final _secureStorage = const FlutterSecureStorage();
  final _errorHandler = ErrorHandlingService();

  late final GoogleSignIn _googleSignIn;
  late final UserRepository _userRepository;

  // Get AuthService from dependency injection
  AuthService get _authService => GetIt.instance<AuthService>();

  bool _isInitialized = false;

  /// Initialize the Google Auth Service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Google Sign-In with basic profile scopes and web client ID
      _googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'profile',
        ],
        // Use web client ID for development to bypass certificate issues
        serverClientId: '424170625220-e2jegdnn3pg9r295ikule6gbf7vmn3ht.apps.googleusercontent.com',
      );

      _userRepository = GetIt.instance<UserRepository>();
      
      _isInitialized = true;
      _logger.info('GoogleAuthService initialized');
    } catch (e) {
      _logger.severe('Error initializing GoogleAuthService: $e');
      rethrow;
    }
  }

  /// Sign in with Google
  Future<GoogleSignInResult> signIn({bool rememberMe = false}) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _logger.info('Starting Google Sign-In process');
      
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn().catchError((error) {
        _logger.severe('Google Sign-In failed with detailed error: $error');
        if (error.toString().contains('ApiException: 10')) {
          _logger.severe('DEVELOPER_ERROR detected - this usually means:');
          _logger.severe('1. SHA-1 certificate fingerprint mismatch');
          _logger.severe('2. Package name mismatch');
          _logger.severe('3. Google Services configuration issue');
          _logger.severe('Current package: com.example.cattle_manager');
          _logger.severe('Please check Firebase console configuration');
        }
        throw error;
      });
      if (googleUser == null) {
        return GoogleSignInResult.cancelled('Sign-in was cancelled by user');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // Get user info from Google
      final String email = googleUser.email;
      final String displayName = googleUser.displayName ?? '';
      final String? photoUrl = googleUser.photoUrl;
      
      _logger.info('Google Sign-In successful for: $email');
      
      // Check if user already exists in local database
      UserIsar? existingUser = await _userRepository.getUserByEmail(email);
      
      if (existingUser != null) {
        // User exists, sign them in
        return await _signInExistingUser(existingUser, googleAuth, rememberMe: rememberMe);
      } else {
        // New user, create account
        return await _createNewGoogleUser(
          email: email,
          displayName: displayName,
          photoUrl: photoUrl,
          googleAuth: googleAuth,
          rememberMe: rememberMe,
        );
      }
      
    } catch (e) {
      _logger.severe('Google Sign-In error: $e');
      return GoogleSignInResult.error(_errorHandler.handleAuthError(e));
    }
  }

  /// Sign up with Google (same as sign in for Google OAuth)
  Future<GoogleSignInResult> signUp({bool rememberMe = false}) async {
    return await signIn(rememberMe: rememberMe); // Google OAuth handles both sign-in and sign-up
  }

  /// Sign out from Google
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _secureStorage.delete(key: 'google_access_token');
      await _secureStorage.delete(key: 'google_id_token');
      _logger.info('Google Sign-Out successful');
    } catch (e) {
      _logger.warning('Error during Google Sign-Out: $e');
    }
  }

  /// Check if user is currently signed in with Google
  Future<bool> isSignedIn() async {
    try {
      return await _googleSignIn.isSignedIn();
    } catch (e) {
      _logger.warning('Error checking Google Sign-In status: $e');
      return false;
    }
  }

  /// Get current Google user
  GoogleSignInAccount? get currentUser => _googleSignIn.currentUser;

  // Private helper methods

  Future<GoogleSignInResult> _signInExistingUser(
    UserIsar user,
    GoogleSignInAuthentication googleAuth,
    {bool rememberMe = false}
  ) async {
    try {
      // Store Google tokens securely
      await _storeGoogleTokens(googleAuth);
      
      // Update user's last login
      user.updateLastLogin();
      await _userRepository.updateUser(user);
      
      // Create session using existing auth service
      final loginResult = await _authService.loginWithGoogleUser(user, rememberMe: rememberMe);
      
      if (loginResult.success) {
        return GoogleSignInResult.success(
          'Welcome back!',
          user: user,
        );
      } else {
        return GoogleSignInResult.error(loginResult.message);
      }
    } catch (e) {
      _logger.severe('Error signing in existing Google user: $e');
      return GoogleSignInResult.error('Failed to sign in with Google');
    }
  }

  Future<GoogleSignInResult> _createNewGoogleUser({
    required String email,
    required String displayName,
    String? photoUrl,
    required GoogleSignInAuthentication googleAuth,
    bool rememberMe = false,
  }) async {
    try {
      // Parse display name
      final nameParts = displayName.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : '';
      final lastName = nameParts.length > 1 ? nameParts.skip(1).join(' ') : '';
      
      // Create new user with Google info
      final newUser = UserIsar.create(
        email: email,
        username: email.split('@').first, // Use email prefix as username
        password: '', // No password for Google users
        firstName: firstName,
        lastName: lastName,
        isGoogleUser: true,
        profileImageUrl: photoUrl,
      );
      
      // Save user to database
      final savedUser = await _userRepository.createUser(newUser);
      
      // Store Google tokens
      await _storeGoogleTokens(googleAuth);
      
      // Create session
      final loginResult = await _authService.loginWithGoogleUser(savedUser, rememberMe: rememberMe);
      
      if (loginResult.success) {
        return GoogleSignInResult.success(
          'Account created successfully! Welcome to Cattle Manager.',
          user: savedUser,
        );
      } else {
        return GoogleSignInResult.error(loginResult.message);
      }
    } catch (e) {
      _logger.severe('Error creating new Google user: $e');
      return GoogleSignInResult.error('Failed to create account with Google');
    }
  }

  Future<void> _storeGoogleTokens(GoogleSignInAuthentication googleAuth) async {
    if (googleAuth.accessToken != null) {
      await _secureStorage.write(
        key: 'google_access_token', 
        value: googleAuth.accessToken!
      );
    }
    if (googleAuth.idToken != null) {
      await _secureStorage.write(
        key: 'google_id_token', 
        value: googleAuth.idToken!
      );
    }
  }
}

/// Result class for Google Sign-In operations
class GoogleSignInResult {
  final bool success;
  final String message;
  final UserIsar? user;

  GoogleSignInResult._({
    required this.success,
    required this.message,
    this.user,
  });

  factory GoogleSignInResult.success(String message, {UserIsar? user}) {
    return GoogleSignInResult._(
      success: true,
      message: message,
      user: user,
    );
  }

  factory GoogleSignInResult.error(String message) {
    return GoogleSignInResult._(
      success: false,
      message: message,
    );
  }

  factory GoogleSignInResult.cancelled(String message) {
    return GoogleSignInResult._(
      success: false,
      message: message,
    );
  }
}
