import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../models/user_isar.dart';
import 'user_repository.dart';

/// Service for handling security-related operations
class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  final Logger _logger = Logger('SecurityService');
  UserRepository get _userRepository => GetIt.instance<UserRepository>();

  // Security constants
  static const int _maxFailedAttempts = 5;
  static const int _sessionTimeoutMinutes = 30;

  /// Check if password meets security requirements
  bool isPasswordSecure(String password) {
    if (password.length < 8) return false;
    if (!password.contains(RegExp(r'[A-Z]'))) return false;
    if (!password.contains(RegExp(r'[a-z]'))) return false;
    if (!password.contains(RegExp(r'[0-9]'))) return false;
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) return false;
    return true;
  }

  /// Generate a secure random password
  String generateSecurePassword({int length = 12}) {
    const String upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const String lowerCase = 'abcdefghijklmnopqrstuvwxyz';
    const String numbers = '0123456789';
    const String specialChars = '!@#\$%^&*(),.?":{}|<>';
    
    final Random random = Random.secure();
    String password = '';
    
    // Ensure at least one character from each category
    password += upperCase[random.nextInt(upperCase.length)];
    password += lowerCase[random.nextInt(lowerCase.length)];
    password += numbers[random.nextInt(numbers.length)];
    password += specialChars[random.nextInt(specialChars.length)];
    
    // Fill the rest randomly
    const String allChars = upperCase + lowerCase + numbers + specialChars;
    for (int i = 4; i < length; i++) {
      password += allChars[random.nextInt(allChars.length)];
    }
    
    // Shuffle the password
    List<String> passwordList = password.split('');
    passwordList.shuffle(random);
    return passwordList.join('');
  }

  /// Check if account should be locked based on failed attempts
  bool shouldLockAccount(UserIsar user) {
    return user.failedLoginAttempts >= _maxFailedAttempts;
  }

  /// Calculate remaining lockout time
  Duration? getRemainingLockoutTime(UserIsar user) {
    if (user.lockedUntil == null) return null;
    
    final now = DateTime.now();
    if (now.isAfter(user.lockedUntil!)) return null;
    
    return user.lockedUntil!.difference(now);
  }

  /// Check if user session has expired
  bool isSessionExpired(DateTime lastAccessed) {
    final now = DateTime.now();
    const sessionTimeout = Duration(minutes: _sessionTimeoutMinutes);
    return now.difference(lastAccessed) > sessionTimeout;
  }

  /// Generate secure token for various purposes
  String generateSecureToken({int length = 32}) {
    final Random random = Random.secure();
    final List<int> bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  /// Hash sensitive data
  String hashData(String data, String salt) {
    final saltBytes = base64.decode(salt);
    final dataBytes = utf8.encode(data);
    final combined = [...dataBytes, ...saltBytes];
    final digest = sha256.convert(combined);
    return digest.toString();
  }

  /// Generate salt for hashing
  String generateSalt({int length = 32}) {
    final Random random = Random.secure();
    final List<int> saltBytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  /// Validate email format with additional security checks
  bool isValidEmail(String email) {
    // Basic format check
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      return false;
    }
    
    // Additional security checks
    if (email.length > 254) return false; // RFC 5321 limit
    if (email.contains('..')) return false; // Consecutive dots
    if (email.startsWith('.') || email.endsWith('.')) return false;
    
    return true;
  }

  /// Check for common weak passwords
  bool isCommonPassword(String password) {
    final commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'football', 'baseball',
      'superman', 'batman', 'trustno1', 'hello', 'freedom'
    ];
    
    return commonPasswords.contains(password.toLowerCase());
  }

  /// Analyze password strength
  PasswordStrength analyzePasswordStrength(String password) {
    int score = 0;
    List<String> feedback = [];
    
    // Length check
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.add('Password should be at least 8 characters long');
    }
    
    if (password.length >= 12) score += 1;
    
    // Character variety checks
    if (password.contains(RegExp(r'[a-z]'))) {
      score += 1;
    } else {
      feedback.add('Add lowercase letters');
    }
    
    if (password.contains(RegExp(r'[A-Z]'))) {
      score += 1;
    } else {
      feedback.add('Add uppercase letters');
    }
    
    if (password.contains(RegExp(r'[0-9]'))) {
      score += 1;
    } else {
      feedback.add('Add numbers');
    }
    
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      score += 1;
    } else {
      feedback.add('Add special characters');
    }
    
    // Common password check
    if (isCommonPassword(password)) {
      score -= 2;
      feedback.add('Avoid common passwords');
    }
    
    // Determine strength level
    if (score <= 2) {
      return PasswordStrength.weak;
    } else if (score <= 4) {
      return PasswordStrength.medium;
    } else {
      return PasswordStrength.strong;
    }
  }

  /// Clean up expired sessions and tokens
  Future<void> cleanupExpiredData() async {
    try {
      _logger.info('Starting security cleanup...');
      
      // Clean up expired sessions
      await _userRepository.cleanupExpiredSessions();
      
      // Clean up expired password reset tokens
      final users = await _userRepository.getAllActiveUsers();
      final now = DateTime.now();
      
      for (final user in users) {
        bool needsUpdate = false;
        
        // Clear expired password reset tokens
        if (user.passwordResetTokenExpiry != null && 
            now.isAfter(user.passwordResetTokenExpiry!)) {
          user.clearPasswordResetToken();
          needsUpdate = true;
        }
        
        // Clear expired email verification tokens
        if (user.emailVerificationTokenExpiry != null && 
            now.isAfter(user.emailVerificationTokenExpiry!)) {
          user.emailVerificationToken = null;
          user.emailVerificationTokenExpiry = null;
          needsUpdate = true;
        }
        
        // Unlock accounts if lockout period has expired
        if (user.lockedUntil != null && now.isAfter(user.lockedUntil!)) {
          user.resetFailedLogins();
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          await _userRepository.updateUser(user);
        }
      }
      
      _logger.info('Security cleanup completed');
    } catch (e) {
      _logger.severe('Error during security cleanup: $e');
    }
  }

  /// Log security event
  Future<void> logSecurityEvent({
    required String userId,
    required SecurityEventType eventType,
    required String description,
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      final event = {
        'userId': userId,
        'eventType': eventType.toString(),
        'description': description,
        'ipAddress': ipAddress,
        'userAgent': userAgent,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      _logger.info('Security event: ${jsonEncode(event)}');
      
      // In production, you would store this in a security log database
      // or send to a security monitoring service
      
    } catch (e) {
      _logger.severe('Error logging security event: $e');
    }
  }

  /// Check if IP address should be blocked (basic implementation)
  bool isIpBlocked(String ipAddress) {
    // In production, you would check against a blacklist database
    // or use a service like Cloudflare for IP blocking
    return false;
  }

  /// Rate limiting check (basic implementation)
  bool isRateLimited(String identifier) {
    // In production, you would implement proper rate limiting
    // using Redis or similar caching solution
    return false;
  }
}

/// Password strength levels
enum PasswordStrength {
  weak,
  medium,
  strong,
}

/// Security event types for logging
enum SecurityEventType {
  login,
  loginFailed,
  logout,
  passwordChanged,
  passwordReset,
  accountLocked,
  accountUnlocked,
  emailVerified,
  suspiciousActivity,
}
