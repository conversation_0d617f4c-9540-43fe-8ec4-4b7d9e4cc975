import 'dart:developer' as developer;
import 'dart:math';
import '../models/report_models.dart';
import '../services/reports_service.dart';
import '../services/performance_monitor.dart';
import '../services/reports_cache_service.dart';

/// Performance benchmarking utility for Reports system
/// 
/// Validates that the system meets all performance targets from the refactoring plan:
/// - Dashboard loads in <2 seconds
/// - Chart rendering in <1 second for 1000+ data points  
/// - Export generation in <30 seconds
/// - Memory usage <100MB for typical operations
class PerformanceBenchmark {
  static final ReportsService _reportsService = ReportsService();
  static final ReportsCacheService _cacheService = ReportsCacheService();

  /// Run comprehensive performance benchmark
  static Future<BenchmarkResults> runFullBenchmark() async {
    developer.log('Starting comprehensive performance benchmark', name: 'Benchmark');
    
    final results = BenchmarkResults();
    
    // Test 1: Dashboard Load Performance
    results.dashboardLoadTime = await _benchmarkDashboardLoad();
    
    // Test 2: Chart Data Processing Performance
    results.chartProcessingTime = await _benchmarkChartProcessing();
    
    // Test 3: Cache Performance
    results.cachePerformance = await _benchmarkCachePerformance();
    
    // Test 4: Large Dataset Handling
    results.largeDatasetHandling = await _benchmarkLargeDatasets();
    
    // Test 5: Memory Usage Estimation
    results.memoryUsage = await _benchmarkMemoryUsage();
    
    // Generate performance report
    results.generateReport();
    
    developer.log('Performance benchmark completed', name: 'Benchmark');
    return results;
  }

  /// Benchmark dashboard loading performance
  static Future<int> _benchmarkDashboardLoad() async {
    const filter = FilterState();
    final loadTimes = <int>[];
    
    // Run multiple tests for accuracy
    for (int i = 0; i < 5; i++) {
      // Clear cache to ensure fresh load
      _cacheService.invalidateAll();
      
      final stopwatch = Stopwatch()..start();
      await _reportsService.getDashboardReport(filter);
      stopwatch.stop();
      
      loadTimes.add(stopwatch.elapsedMilliseconds);
      
      // Small delay between tests
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    final avgLoadTime = loadTimes.reduce((a, b) => a + b) ~/ loadTimes.length;
    
    developer.log(
      'Dashboard load benchmark: ${avgLoadTime}ms average (target: <2000ms)',
      name: 'Benchmark.Dashboard',
    );
    
    return avgLoadTime;
  }

  /// Benchmark chart data processing performance
  static Future<int> _benchmarkChartProcessing() async {
    // Create large dataset for testing
    final largeDataset = List.generate(2000, (i) => ChartPoint(
      label: 'Point $i',
      value: Random().nextDouble() * 100,
      date: DateTime.now().subtract(Duration(days: i)),
    ));
    
    final stopwatch = Stopwatch()..start();
    
    // Test chart data pagination
    final paginated = ChartDataPaginator.paginateForChart(largeDataset);
    
    // Simulate chart processing
    for (final point in paginated) {
      // Simulate processing each data point
      point.value * 1.1; // Simple calculation
    }
    
    stopwatch.stop();
    
    developer.log(
      'Chart processing benchmark: ${stopwatch.elapsedMilliseconds}ms for ${largeDataset.length} points',
      name: 'Benchmark.Charts',
    );
    
    return stopwatch.elapsedMilliseconds;
  }

  /// Benchmark cache performance
  static Future<Map<String, int>> _benchmarkCachePerformance() async {
    const filter = FilterState();
    final results = <String, int>{};
    
    // Test cache miss (first load)
    _cacheService.invalidateAll();
    final stopwatch1 = Stopwatch()..start();
    await _reportsService.getDashboardReport(filter);
    stopwatch1.stop();
    results['cacheMiss'] = stopwatch1.elapsedMilliseconds;
    
    // Test cache hit (second load)
    final stopwatch2 = Stopwatch()..start();
    await _reportsService.getDashboardReport(filter);
    stopwatch2.stop();
    results['cacheHit'] = stopwatch2.elapsedMilliseconds;
    
    // Calculate cache efficiency
    final efficiency = ((results['cacheMiss']! - results['cacheHit']!) / results['cacheMiss']! * 100).round();
    results['efficiency'] = efficiency;
    
    developer.log(
      'Cache benchmark: Miss=${results['cacheMiss']}ms, Hit=${results['cacheHit']}ms, Efficiency=$efficiency%',
      name: 'Benchmark.Cache',
    );
    
    return results;
  }

  /// Benchmark large dataset handling
  static Future<Map<String, dynamic>> _benchmarkLargeDatasets() async {
    final results = <String, dynamic>{};
    
    // Test with different dataset sizes
    final testSizes = [100, 500, 1000, 2000, 5000];
    
    for (final size in testSizes) {
      final dataset = List.generate(size, (i) => ChartPoint(
        label: 'Point $i',
        value: Random().nextDouble() * 100,
      ));
      
      final stopwatch = Stopwatch()..start();
      final paginated = ChartDataPaginator.paginateForChart(dataset);
      stopwatch.stop();
      
      results['size_$size'] = {
        'originalSize': size,
        'paginatedSize': paginated.length,
        'processingTime': stopwatch.elapsedMilliseconds,
        'shouldPaginate': PerformanceMonitor.shouldPaginateChartData(size),
      };
    }
    
    developer.log(
      'Large dataset benchmark completed for ${testSizes.length} different sizes',
      name: 'Benchmark.LargeData',
    );
    
    return results;
  }

  /// Estimate memory usage (simplified)
  static Future<Map<String, int>> _benchmarkMemoryUsage() async {
    final results = <String, int>{};
    
    // Estimate memory usage for different components
    // Note: This is a simplified estimation, real memory profiling would require platform-specific tools
    
    // Estimate dashboard data memory usage
    const filter = FilterState();
    final dashboardData = await _reportsService.getDashboardReport(filter);
    
    // Rough estimation based on data structures
    final metricsMemory = dashboardData.metrics.length * 200; // ~200 bytes per metric
    final chartDataMemory = dashboardData.chartData.length * 50; // ~50 bytes per chart point
    final insightsMemory = dashboardData.insights.join().length * 2; // ~2 bytes per character
    
    results['metrics'] = metricsMemory;
    results['chartData'] = chartDataMemory;
    results['insights'] = insightsMemory;
    results['total'] = metricsMemory + chartDataMemory + insightsMemory;
    
    developer.log(
      'Memory usage estimate: ${results['total']} bytes (~${(results['total']! / 1024 / 1024).toStringAsFixed(2)} MB)',
      name: 'Benchmark.Memory',
    );
    
    return results;
  }

  /// Quick performance check for development
  static Future<bool> quickPerformanceCheck() async {
    developer.log('Running quick performance check', name: 'Benchmark.Quick');
    
    // Test dashboard load time
    final stopwatch = Stopwatch()..start();
    const filter = FilterState();
    await _reportsService.getDashboardReport(filter);
    stopwatch.stop();
    
    final loadTime = stopwatch.elapsedMilliseconds;
    final meetsTarget = loadTime < 2000;
    
    developer.log(
      'Quick check: Dashboard loaded in ${loadTime}ms (target: <2000ms) - ${meetsTarget ? 'PASS' : 'FAIL'}',
      name: 'Benchmark.Quick',
      level: meetsTarget ? 800 : 900,
    );
    
    return meetsTarget;
  }

  /// Generate performance recommendations
  static List<String> generateRecommendations(BenchmarkResults results) {
    final recommendations = <String>[];
    
    if (results.dashboardLoadTime > 2000) {
      recommendations.add('Dashboard load time exceeds 2s target - consider data optimization');
    }
    
    if (results.chartProcessingTime > 1000) {
      recommendations.add('Chart processing is slow - implement data pagination');
    }
    
    if ((results.cachePerformance['efficiency'] ?? 0) < 50) {
      recommendations.add('Cache efficiency is low - review cache strategy');
    }
    
    if ((results.memoryUsage['total'] ?? 0) > 100 * 1024 * 1024) { // 100MB
      recommendations.add('Memory usage exceeds 100MB target - optimize data structures');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('All performance targets met - system is optimized');
    }
    
    return recommendations;
  }
}

/// Benchmark results data class
class BenchmarkResults {
  int dashboardLoadTime = 0;
  int chartProcessingTime = 0;
  Map<String, int> cachePerformance = {};
  Map<String, dynamic> largeDatasetHandling = {};
  Map<String, int> memoryUsage = {};
  
  DateTime timestamp = DateTime.now();
  List<String> recommendations = [];

  /// Generate comprehensive performance report
  void generateReport() {
    recommendations = PerformanceBenchmark.generateRecommendations(this);
    
    developer.log(
      '''
Performance Benchmark Report
===========================
Timestamp: ${timestamp.toIso8601String()}

Dashboard Load Time: ${dashboardLoadTime}ms (target: <2000ms) ${dashboardLoadTime < 2000 ? '✓' : '✗'}
Chart Processing: ${chartProcessingTime}ms (target: <1000ms) ${chartProcessingTime < 1000 ? '✓' : '✗'}
Cache Hit Efficiency: ${cachePerformance['efficiency'] ?? 0}% (target: >50%) ${(cachePerformance['efficiency'] ?? 0) > 50 ? '✓' : '✗'}
Memory Usage: ~${(memoryUsage['total'] ?? 0) / 1024 / 1024} MB (target: <100MB) ${(memoryUsage['total'] ?? 0) < 100 * 1024 * 1024 ? '✓' : '✗'}

Recommendations:
${recommendations.map((r) => '- $r').join('\n')}
      ''',
      name: 'Benchmark.Report',
    );
  }

  /// Convert to JSON for storage/analysis
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'dashboardLoadTime': dashboardLoadTime,
      'chartProcessingTime': chartProcessingTime,
      'cachePerformance': cachePerformance,
      'largeDatasetHandling': largeDatasetHandling,
      'memoryUsage': memoryUsage,
      'recommendations': recommendations,
      'meetsTargets': {
        'dashboardLoad': dashboardLoadTime < 2000,
        'chartProcessing': chartProcessingTime < 1000,
        'cacheEfficiency': (cachePerformance['efficiency'] ?? 0) > 50,
        'memoryUsage': (memoryUsage['total'] ?? 0) < 100 * 1024 * 1024,
      },
    };
  }
}
