import 'package:isar/isar.dart';
import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

import '../../../services/database/isar_service.dart';
import '../models/notification_settings_isar.dart';
import '../models/notification_priority.dart';

/// Repository for notification settings operations with Isar integration
class NotificationSettingsRepository {
  final Logger _logger = Logger('NotificationSettingsRepository');
  final IsarService _isarService;
  final Uuid _uuid = const Uuid();

  /// Constructor with IsarService dependency
  NotificationSettingsRepository(this._isarService);

  /// Get notification settings, creating default settings if none exist
  Future<NotificationSettingsIsar> getSettings() async {
    try {
      // Try to get existing settings
      final settings = await _isarService.notificationSettingsIsars.where().findFirst();
      
      // If settings exist, process and return them
      if (settings != null) {
        settings.processAfterLoad();
        return settings;
      }
      
      // Otherwise, create and return default settings
      return await _createDefaultSettings();
    } catch (e) {
      _logger.severe('Error getting notification settings: $e');
      rethrow;
    }
  }

  /// Get settings for a specific user
  Future<NotificationSettingsIsar> getUserSettings(String userBusinessId) async {
    try {
      // Try to get existing user settings
      final settings = await _isarService.notificationSettingsIsars
          .filter()
          .userBusinessIdEqualTo(userBusinessId)
          .findFirst();
      
      // If settings exist, process and return them
      if (settings != null) {
        settings.processAfterLoad();
        return settings;
      }
      
      // Otherwise, create and return default settings for this user
      return await _createDefaultSettings(userBusinessId: userBusinessId);
    } catch (e) {
      _logger.severe('Error getting user notification settings: $e');
      rethrow;
    }
  }

  /// Save notification settings
  Future<NotificationSettingsIsar> saveSettings(NotificationSettingsIsar settings) async {
    try {
      // Generate business ID if not provided
      if (settings.businessId == null || settings.businessId!.isEmpty) {
        settings.businessId = _uuid.v4();
      }

      // Set timestamps
      settings.updatedAt = DateTime.now();
      settings.createdAt ??= DateTime.now();

      // Prepare for saving (serialize JSON fields)
      settings.prepareForSave();

      // Save to database
      await _isarService.isar.writeTxn(() async {
        await _isarService.notificationSettingsIsars.put(settings);
      });

      _logger.info('Saved notification settings: ${settings.businessId}');
      return settings;
    } catch (e) {
      _logger.severe('Error saving notification settings: $e');
      rethrow;
    }
  }

  /// Create default notification settings
  Future<NotificationSettingsIsar> _createDefaultSettings({String? userBusinessId}) async {
    try {
      final settings = NotificationSettingsIsar(
        businessId: _uuid.v4(),
        userBusinessId: userBusinessId,
        notificationsEnabled: true,
        pushNotificationsEnabled: true,
        inAppNotificationsEnabled: true,
        emailNotificationsEnabled: false,
        smsNotificationsEnabled: false,
        categoryEnabled: {
          'health': true,
          'breeding': true,
          'milk': true,
          'weight': true,
          'events': true,
          'system': true,
        },
        categoryPriorities: {
          'health': NotificationPriority.high,
          'breeding': NotificationPriority.high,
          'milk': NotificationPriority.medium,
          'weight': NotificationPriority.medium,
          'events': NotificationPriority.medium,
          'system': NotificationPriority.low,
        },
        reminderLeadTimes: {
          'health': 24,
          'breeding': 12,
          'events': 24,
        },
        quietHoursEnabled: false,
        quietHoursStart: 22,
        quietHoursEnd: 7,
        emergencyOverrideEnabled: true,
        emergencyKeywords: ['emergency', 'critical', 'urgent'],
        digestFrequency: 'none',
        digestTime: 9,
        maxNotificationsToKeep: 1000,
        autoDeleteAfterDays: 90,
        autoDeleteReadNotifications: true,
        autoDeleteReadAfterDays: 30,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save to database
      await saveSettings(settings);

      _logger.info('Created default notification settings${userBusinessId != null ? ' for user: $userBusinessId' : ''}');
      return settings;
    } catch (e) {
      _logger.severe('Error creating default notification settings: $e');
      rethrow;
    }
  }

  /// Get default notification settings without saving them
  Future<NotificationSettingsIsar> getDefaultSettings() async {
    return NotificationSettingsIsar(
      businessId: _uuid.v4(),
      notificationsEnabled: true,
      pushNotificationsEnabled: true,
      inAppNotificationsEnabled: true,
      emailNotificationsEnabled: false,
      smsNotificationsEnabled: false,
      categoryEnabled: {
        'health': true,
        'breeding': true,
        'milk': true,
        'weight': true,
        'events': true,
        'system': true,
      },
      categoryPriorities: {
        'health': NotificationPriority.high,
        'breeding': NotificationPriority.high,
        'milk': NotificationPriority.medium,
        'weight': NotificationPriority.medium,
        'events': NotificationPriority.medium,
        'system': NotificationPriority.low,
      },
      reminderLeadTimes: {
        'health': 24,
        'breeding': 12,
        'events': 24,
      },
      quietHoursEnabled: false,
      quietHoursStart: 22,
      quietHoursEnd: 7,
      emergencyOverrideEnabled: true,
      emergencyKeywords: ['emergency', 'critical', 'urgent'],
      digestFrequency: 'none',
      digestTime: 9,
      maxNotificationsToKeep: 1000,
      autoDeleteAfterDays: 90,
      autoDeleteReadNotifications: true,
      autoDeleteReadAfterDays: 30,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Reset settings to defaults
  Future<NotificationSettingsIsar> resetToDefaults({String? userBusinessId}) async {
    try {
      // Get existing settings
      NotificationSettingsIsar? existingSettings;
      
      if (userBusinessId != null) {
        existingSettings = await _isarService.notificationSettingsIsars
            .filter()
            .userBusinessIdEqualTo(userBusinessId)
            .findFirst();
      } else {
        existingSettings = await _isarService.notificationSettingsIsars
            .where()
            .findFirst();
      }

      // Create default settings
      final defaultSettings = await getDefaultSettings();
      
      // If existing settings found, preserve the ID and user ID
      if (existingSettings != null) {
        defaultSettings.id = existingSettings.id;
        defaultSettings.businessId = existingSettings.businessId;
        defaultSettings.userBusinessId = existingSettings.userBusinessId;
      } else if (userBusinessId != null) {
        defaultSettings.userBusinessId = userBusinessId;
      }

      // Save the reset settings
      await saveSettings(defaultSettings);

      _logger.info('Reset notification settings to defaults${userBusinessId != null ? ' for user: $userBusinessId' : ''}');
      return defaultSettings;
    } catch (e) {
      _logger.severe('Error resetting notification settings to defaults: $e');
      rethrow;
    }
  }

  /// Update FCM token
  Future<void> updateFCMToken(String token) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Update token and timestamp
      settings.fcmToken = token;
      settings.fcmTokenUpdatedAt = DateTime.now();
      
      // Save settings
      await saveSettings(settings);
      
      _logger.info('Updated FCM token');
    } catch (e) {
      _logger.severe('Error updating FCM token: $e');
      rethrow;
    }
  }

  /// Enable or disable push notifications
  Future<void> setPushNotificationsEnabled(bool enabled) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Update setting
      settings.pushNotificationsEnabled = enabled;
      
      // Save settings
      await saveSettings(settings);
      
      _logger.info('Set push notifications enabled: $enabled');
    } catch (e) {
      _logger.severe('Error setting push notifications enabled: $e');
      rethrow;
    }
  }

  /// Update category-specific settings
  Future<void> updateCategorySettings(
    String category, 
    {bool? enabled, NotificationPriority? priority, int? reminderLeadTime}
  ) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Update category enabled
      if (enabled != null) {
        final categoryEnabled = settings.getCategoryEnabled();
        categoryEnabled[category] = enabled;
        settings.setCategoryEnabled(categoryEnabled);
      }
      
      // Update category priority
      if (priority != null) {
        final categoryPriorities = settings.getCategoryPriorities();
        categoryPriorities[category] = priority;
        settings.setCategoryPriorities(categoryPriorities);
      }
      
      // Update reminder lead time
      if (reminderLeadTime != null) {
        final reminderLeadTimes = settings.getReminderLeadTimes();
        reminderLeadTimes[category] = reminderLeadTime;
        settings.setReminderLeadTimes(reminderLeadTimes);
      }
      
      // Save settings
      await saveSettings(settings);
      
      _logger.info('Updated settings for category: $category');
    } catch (e) {
      _logger.severe('Error updating category settings: $e');
      rethrow;
    }
  }

  /// Subscribe to FCM topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Add topic to subscribed topics
      final topics = settings.getSubscribedTopics();
      if (!topics.contains(topic)) {
        topics.add(topic);
        settings.setSubscribedTopics(topics);
        
        // Save settings
        await saveSettings(settings);
        
        _logger.info('Subscribed to topic: $topic');
      }
    } catch (e) {
      _logger.severe('Error subscribing to topic: $e');
      rethrow;
    }
  }

  /// Unsubscribe from FCM topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Remove topic from subscribed topics
      final topics = settings.getSubscribedTopics();
      if (topics.contains(topic)) {
        topics.remove(topic);
        settings.setSubscribedTopics(topics);
        
        // Save settings
        await saveSettings(settings);
        
        _logger.info('Unsubscribed from topic: $topic');
      }
    } catch (e) {
      _logger.severe('Error unsubscribing from topic: $e');
      rethrow;
    }
  }

  /// Update quiet hours settings
  Future<void> updateQuietHours({
    bool? enabled,
    int? startHour,
    int? endHour,
  }) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Update settings
      if (enabled != null) {
        settings.quietHoursEnabled = enabled;
      }
      
      if (startHour != null) {
        settings.quietHoursStart = startHour;
      }
      
      if (endHour != null) {
        settings.quietHoursEnd = endHour;
      }
      
      // Save settings
      await saveSettings(settings);
      
      _logger.info('Updated quiet hours settings');
    } catch (e) {
      _logger.severe('Error updating quiet hours settings: $e');
      rethrow;
    }
  }

  /// Update emergency override settings
  Future<void> updateEmergencyOverride({
    bool? enabled,
    List<String>? keywords,
  }) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Update settings
      if (enabled != null) {
        settings.emergencyOverrideEnabled = enabled;
      }
      
      if (keywords != null) {
        settings.setEmergencyKeywords(keywords);
      }
      
      // Save settings
      await saveSettings(settings);
      
      _logger.info('Updated emergency override settings');
    } catch (e) {
      _logger.severe('Error updating emergency override settings: $e');
      rethrow;
    }
  }

  /// Update retention settings
  Future<void> updateRetentionSettings({
    int? maxNotificationsToKeep,
    int? autoDeleteAfterDays,
    bool? autoDeleteReadNotifications,
    int? autoDeleteReadAfterDays,
  }) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Update settings
      if (maxNotificationsToKeep != null) {
        settings.maxNotificationsToKeep = maxNotificationsToKeep;
      }
      
      if (autoDeleteAfterDays != null) {
        settings.autoDeleteAfterDays = autoDeleteAfterDays;
      }
      
      if (autoDeleteReadNotifications != null) {
        settings.autoDeleteReadNotifications = autoDeleteReadNotifications;
      }
      
      if (autoDeleteReadAfterDays != null) {
        settings.autoDeleteReadAfterDays = autoDeleteReadAfterDays;
      }
      
      // Save settings
      await saveSettings(settings);
      
      _logger.info('Updated retention settings');
    } catch (e) {
      _logger.severe('Error updating retention settings: $e');
      rethrow;
    }
  }

  /// Update digest settings
  Future<void> updateDigestSettings({
    required String frequency,
    int? time,
  }) async {
    try {
      // Get existing settings
      final settings = await getSettings();
      
      // Update settings
      settings.digestFrequency = frequency;
      
      if (time != null) {
        settings.digestTime = time;
      }
      
      // Save settings
      await saveSettings(settings);
      
      _logger.info('Updated digest settings: $frequency');
    } catch (e) {
      _logger.severe('Error updating digest settings: $e');
      rethrow;
    }
  }
}