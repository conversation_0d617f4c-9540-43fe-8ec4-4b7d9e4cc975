import 'package:flutter/material.dart';
import '../../../config/api_config.dart';
import '../../../constants/app_colors.dart';


/// Screen for configuring API sync settings
class ApiSyncSettingsScreen extends StatefulWidget {
  const ApiSyncSettingsScreen({Key? key}) : super(key: key);

  @override
  State<ApiSyncSettingsScreen> createState() => _ApiSyncSettingsScreenState();
}

class _ApiSyncSettingsScreenState extends State<ApiSyncSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final apiInfo = ApiConfig.apiConfigInfo;

    return Scaffold(
      appBar: AppBar(
        title: const Text('API Sync Settings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentStatusCard(apiInfo),
            const SizedBox(height: 24),
            _buildConfigurationSection(),
            const SizedBox(height: 24),
            _buildInstructionsSection(),
            const SizedBox(height: 24),
            _buildApiEndpointsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentStatusCard(Map<String, dynamic> apiInfo) {
    final isEnabled = apiInfo['enableApiSync'] as bool;
    final statusMessage = apiInfo['syncStatusMessage'] as String;
    final environment = apiInfo['environment'] as String;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isEnabled ? Icons.cloud_done : Icons.cloud_off,
                  color: isEnabled ? AppColors.success : AppColors.warning,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'API Sync Status',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isEnabled ? AppColors.success.withValues(alpha: 0.1) : AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isEnabled ? AppColors.success : AppColors.warning,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusMessage,
                    style: TextStyle(
                      color: isEnabled ? AppColors.success : AppColors.warning,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Environment: ${environment.toUpperCase()}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  if (apiInfo['currentApiUrl'].toString().isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      'API URL: ${apiInfo['currentApiUrl']}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Configuration Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildConfigOption(
              'Enable API Sync',
              'Connect to remote API server for real-time data synchronization',
              ApiConfig.enableApiSync,
              Icons.sync,
              onChanged: null, // Read-only for now
            ),
            const SizedBox(height: 12),
            _buildConfigOption(
              'Demo API Mode',
              'Use mock API endpoints for testing (no real server required)',
              ApiConfig.enableDemoApiMode,
              Icons.science,
              onChanged: null, // Read-only for now
            ),
            const SizedBox(height: 12),
            _buildConfigOption(
              'Production Mode',
              'Connect to production API server (requires valid credentials)',
              ApiConfig.isProduction,
              Icons.security,
              onChanged: null, // Read-only for now
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.info),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: AppColors.info),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Configuration changes require app restart and code modifications.',
                      style: TextStyle(color: AppColors.info),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigOption(
    String title,
    String description,
    bool value,
    IconData icon, {
    ValueChanged<bool>? onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Icon(icon, color: value ? AppColors.success : Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.success,
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How to Enable API Sync',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInstructionStep(
              1,
              'Set up Backend Server',
              'Deploy a backend API server with the required endpoints for cattle management data.',
            ),
            _buildInstructionStep(
              2,
              'Update Configuration',
              'Modify lib/config/api_config.dart to set enableApiSync = true and configure your API URL.',
            ),
            _buildInstructionStep(
              3,
              'Test Connection',
              'Restart the app and test the sync functionality with your API server.',
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  _showDetailedInstructions();
                },
                icon: const Icon(Icons.help_outline),
                label: const Text('View Detailed Instructions'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep(int step, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApiEndpointsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Required API Endpoints',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildEndpointItem('POST', '/cattle/sync', 'Sync cattle data'),
            _buildEndpointItem('POST', '/health/sync', 'Sync health records'),
            _buildEndpointItem('POST', '/breeding/sync', 'Sync breeding records'),
            _buildEndpointItem('POST', '/weight/sync', 'Sync weight records'),
            _buildEndpointItem('POST', '/events/sync', 'Sync event records'),
            _buildEndpointItem('POST', '/transactions/sync', 'Sync transaction records'),
            _buildEndpointItem('POST', '/milk/sync', 'Sync milk production records'),
          ],
        ),
      ),
    );
  }

  Widget _buildEndpointItem(String method, String endpoint, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              method,
              style: const TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  endpoint,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDetailedInstructions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Detailed Setup Instructions'),
        content: const SingleChildScrollView(
          child: Text(
            '1. Backend Server Setup:\n'
            '   • Deploy a REST API server (Node.js, Python, Java, etc.)\n'
            '   • Implement the required endpoints listed below\n'
            '   • Set up authentication and data validation\n\n'
            '2. Configuration Update:\n'
            '   • Open lib/config/api_config.dart\n'
            '   • Set enableApiSync = true\n'
            '   • Update productionApiUrl or developmentApiUrl\n'
            '   • Rebuild and restart the app\n\n'
            '3. Testing:\n'
            '   • Use the Sync Data button on the dashboard\n'
            '   • Check logs for connection status\n'
            '   • Verify data synchronization\n\n'
            'For demo purposes, you can set enableDemoApiMode = true to test with mock endpoints.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
