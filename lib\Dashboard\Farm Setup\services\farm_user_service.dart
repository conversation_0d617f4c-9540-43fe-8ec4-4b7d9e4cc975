import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

import '../models/farm_user_isar.dart';
import '../models/user_role_isar.dart';
import 'farm_setup_validation_service.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Farm User Service
/// 
/// Handles all user management operations for farm setup following established patterns.
/// Pure business logic service that orchestrates user operations with validation.
/// Follows single responsibility principle - only handles user management logic.
class FarmUserService {
  static final Logger _logger = Logger('FarmUserService');
  static const Uuid _uuid = Uuid();

  // Services accessed through dependency injection
  static IsarService get _isarService => GetIt.instance<IsarService>();
  static Isar get _isar => _isarService.isar;

  /// Create a new farm user with validation
  static Future<FarmUserIsar> createUser({
    required String name,
    required String email,
    required String farmBusinessId,
    required String roleId,
    String? phoneNumber,
    bool isActive = true,
  }) async {
    try {
      // Create user object
      final user = FarmUserIsar()
        ..businessId = _uuid.v4()
        ..name = name
        ..email = email
        ..farmBusinessId = farmBusinessId
        ..roleBusinessId = roleId
        ..phoneNumber = phoneNumber
        ..isActive = isActive
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      // Validate user data
      await FarmSetupValidationService.validateFarmUser(user);

      // Save to database
      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.put(user);
      });

      _logger.info('User created successfully: ${user.name}');
      return user;
    } catch (e) {
      _logger.severe('Error creating user: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to create user', e.toString());
    }
  }

  /// Update an existing farm user
  static Future<FarmUserIsar> updateUser(FarmUserIsar user) async {
    try {
      // Update timestamp
      user.updatedAt = DateTime.now();

      // Validate user data
      await FarmSetupValidationService.validateFarmUser(user);

      // Save to database
      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.put(user);
      });

      _logger.info('User updated successfully: ${user.name}');
      return user;
    } catch (e) {
      _logger.severe('Error updating user: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to update user', e.toString());
    }
  }

  /// Delete a farm user
  static Future<void> deleteUser(int userId) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.delete(userId);
      });

      _logger.info('User deleted successfully: $userId');
    } catch (e) {
      _logger.severe('Error deleting user: $e');
      throw DatabaseException('Failed to delete user', e.toString());
    }
  }

  /// Activate or deactivate a user
  static Future<void> setUserActiveStatus(String userBusinessId, bool isActive) async {
    try {
      final user = await _isar.farmUserIsars
          .filter()
          .businessIdEqualTo(userBusinessId)
          .findFirst();

      if (user == null) {
        throw DatabaseException('User not found', userBusinessId);
      }

      user.isActive = isActive;
      user.updatedAt = DateTime.now();

      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.put(user);
      });

      _logger.info('User ${isActive ? 'activated' : 'deactivated'}: ${user.name}');
    } catch (e) {
      _logger.severe('Error updating user status: $e');
      throw DatabaseException('Failed to update user status', e.toString());
    }
  }

  /// Change user role
  static Future<void> changeUserRole(String userBusinessId, String newRoleId) async {
    try {
      final user = await _isar.farmUserIsars
          .filter()
          .businessIdEqualTo(userBusinessId)
          .findFirst();

      if (user == null) {
        throw DatabaseException('User not found', userBusinessId);
      }

      // Verify role exists
      final role = await _isar.userRoleIsars
          .filter()
          .businessIdEqualTo(newRoleId)
          .findFirst();

      if (role == null) {
        throw DatabaseException('Role not found', newRoleId);
      }

      user.roleBusinessId = newRoleId;
      user.updatedAt = DateTime.now();

      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.put(user);
      });

      _logger.info('User role changed: ${user.name} -> ${role.name}');
    } catch (e) {
      _logger.severe('Error changing user role: $e');
      throw DatabaseException('Failed to change user role', e.toString());
    }
  }

  /// Ensure default user roles exist
  static Future<void> ensureDefaultUserRoles() async {
    try {
      final existingRoles = await _isar.userRoleIsars.where().findAll();
      if (existingRoles.isEmpty) {
        // Create default roles
        await _isar.writeTxn(() async {
          for (final role in UserRoleIsar.defaultRoles) {
            await _isar.userRoleIsars.put(role);
          }
        });
        _logger.info('Default user roles created');
      }
    } catch (e) {
      _logger.severe('Error ensuring default user roles: $e');
      throw DatabaseException('Failed to ensure default user roles', e.toString());
    }
  }

  /// Migrate users from SharedPreferences to Isar
  static Future<void> migrateUsersFromSharedPreferences(
    List<FarmUserIsar> users, 
    List<UserRoleIsar> roles
  ) async {
    try {
      await _isar.writeTxn(() async {
        // First save roles
        for (final role in roles) {
          await _isar.userRoleIsars.put(role);
        }
        
        // Then save users
        for (final user in users) {
          await _isar.farmUserIsars.put(user);
        }
      });
      _logger.info('Successfully migrated users from SharedPreferences');
    } catch (e) {
      _logger.severe('Error migrating users: $e');
      throw DatabaseException('Failed to migrate users', e.toString());
    }
  }

  /// Get user statistics for a farm
  static Future<Map<String, int>> getUserStatistics(String farmBusinessId) async {
    try {
      final allUsers = await _isar.farmUserIsars
          .filter()
          .farmBusinessIdEqualTo(farmBusinessId)
          .findAll();

      final activeUsers = allUsers.where((user) => user.isActive).length;
      final inactiveUsers = allUsers.length - activeUsers;

      // Count users by role
      final roleStats = <String, int>{};
      for (final user in allUsers) {
        final role = await _isar.userRoleIsars
            .filter()
            .businessIdEqualTo(user.roleBusinessId)
            .findFirst();
        
        final roleName = role?.name ?? 'Unknown';
        roleStats[roleName] = (roleStats[roleName] ?? 0) + 1;
      }

      return {
        'totalUsers': allUsers.length,
        'activeUsers': activeUsers,
        'inactiveUsers': inactiveUsers,
        ...roleStats,
      };
    } catch (e) {
      _logger.severe('Error getting user statistics: $e');
      return {
        'totalUsers': 0,
        'activeUsers': 0,
        'inactiveUsers': 0,
      };
    }
  }

  /// Validate user permissions for an action
  static Future<bool> hasPermission(String userBusinessId, String permission) async {
    try {
      final user = await _isar.farmUserIsars
          .filter()
          .businessIdEqualTo(userBusinessId)
          .findFirst();

      if (user == null || !user.isActive) {
        return false;
      }

      final role = await _isar.userRoleIsars
          .filter()
          .businessIdEqualTo(user.roleBusinessId)
          .findFirst();

      if (role == null) {
        return false;
      }

      return role.permissions.contains(permission);
    } catch (e) {
      _logger.severe('Error checking user permissions: $e');
      return false;
    }
  }
}
