import 'package:isar/isar.dart';
import '../models/user_isar.dart';
import '../models/user_session_isar.dart';
import '../models/user_settings_isar.dart';
import '../../../services/database/isar_service.dart';

/// User repository for database operations
/// Following the streamlined architecture pattern
class UserRepository {
  final IsarService _isarService;

  UserRepository(this._isarService);

  Isar get _isar => _isarService.isar;

  //=== USER CRUD ===//

  /// Save (add or update) a user record
  Future<void> saveUser(UserIsar user) async {
    await _isar.writeTxn(() async {
      await _isar.userIsars.put(user);
    });
  }

  /// Get user by email
  Future<UserIsar?> getUserByEmail(String email) async {
    return await _isar.userIsars
        .filter()
        .emailEqualTo(email, caseSensitive: false)
        .findFirst();
  }

  /// Get user by business ID
  Future<UserIsar?> getUserByBusinessId(String businessId) async {
    return await _isar.userIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get user by username
  Future<UserIsar?> getUserByUsername(String username) async {
    return await _isar.userIsars
        .filter()
        .usernameEqualTo(username, caseSensitive: false)
        .findFirst();
  }

  /// Delete user by business ID
  Future<void> deleteUser(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.userIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  /// Get all users
  Future<List<UserIsar>> getAllUsers() async {
    return await _isar.userIsars.where().findAll();
  }

  //=== USER SESSION CRUD ===//

  /// Save user session
  Future<void> saveUserSession(UserSessionIsar session) async {
    await _isar.writeTxn(() async {
      await _isar.userSessionIsars.put(session);
    });
  }

  /// Get active session for user
  Future<UserSessionIsar?> getActiveSession(String businessId) async {
    return await _isar.userSessionIsars
        .filter()
        .userBusinessIdEqualTo(businessId)
        .and()
        .isActiveEqualTo(true)
        .findFirst();
  }

  /// Get session by token
  Future<UserSessionIsar?> getSessionByToken(String token) async {
    return await _isar.userSessionIsars
        .filter()
        .sessionTokenEqualTo(token)
        .findFirst();
  }

  /// Get all sessions for user
  Future<List<UserSessionIsar>> getUserSessions(String businessId) async {
    return await _isar.userSessionIsars
        .filter()
        .userBusinessIdEqualTo(businessId)
        .findAll();
  }

  /// Delete session
  Future<void> deleteSession(String sessionId) async {
    await _isar.writeTxn(() async {
      await _isar.userSessionIsars
          .filter()
          .businessIdEqualTo(sessionId)
          .deleteAll();
    });
  }

  /// Delete all sessions for user
  Future<void> deleteAllUserSessions(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.userSessionIsars
          .filter()
          .userBusinessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  /// Invalidate all user sessions
  Future<void> invalidateAllUserSessions(String businessId) async {
    await deleteAllUserSessions(businessId);
  }

  //=== USER SETTINGS CRUD ===//

  /// Save user settings
  Future<void> saveUserSettings(UserSettingsIsar settings) async {
    await _isar.writeTxn(() async {
      await _isar.userSettingsIsars.put(settings);
    });
  }

  /// Get user settings
  Future<UserSettingsIsar?> getUserSettings(String businessId) async {
    return await _isar.userSettingsIsars
        .filter()
        .userBusinessIdEqualTo(businessId)
        .findFirst();
  }

  /// Delete user settings
  Future<void> deleteUserSettings(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.userSettingsIsars
          .filter()
          .userBusinessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== UTILITY METHODS ===//

  /// Check if email exists
  Future<bool> emailExists(String email) async {
    final user = await getUserByEmail(email);
    return user != null;
  }

  /// Update user last login
  Future<void> updateLastLogin(String businessId) async {
    final user = await getUserByBusinessId(businessId);
    if (user != null) {
      user.lastLoginAt = DateTime.now();
      await saveUser(user);
    }
  }

  /// Update user profile
  Future<void> updateUserProfile(String businessId, {
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    final user = await getUserByBusinessId(businessId);
    if (user != null) {
      if (firstName != null) user.firstName = firstName;
      if (lastName != null) user.lastName = lastName;
      if (email != null) user.email = email;
      if (phoneNumber != null) user.phoneNumber = phoneNumber;
      if (profileImageUrl != null) user.profileImageUrl = profileImageUrl;

      user.updatedAt = DateTime.now();
      await saveUser(user);
    }
  }

  /// Create user
  Future<UserIsar> createUser(UserIsar user) async {
    user.createdAt = DateTime.now();
    user.updatedAt = DateTime.now();
    await saveUser(user);
    return user;
  }

  /// Update user
  Future<void> updateUser(UserIsar user) async {
    user.updatedAt = DateTime.now();
    await saveUser(user);
  }

  /// Create user settings
  Future<void> createUserSettings(UserSettingsIsar settings) async {
    await saveUserSettings(settings);
  }

  /// Update user settings
  Future<void> updateUserSettings(UserSettingsIsar settings) async {
    await saveUserSettings(settings);
  }

  /// Create session
  Future<UserSessionIsar> createSession(UserSessionIsar session) async {
    await saveUserSession(session);
    return session;
  }

  /// Update session
  Future<void> updateSession(UserSessionIsar session) async {
    await saveUserSession(session);
  }

  /// Search users by name or email
  Future<List<UserIsar>> searchUsers(String query) async {
    final lowerQuery = query.toLowerCase();
    return await _isar.userIsars
        .filter()
        .group((q) => q
            .firstNameContains(lowerQuery, caseSensitive: false)
            .or()
            .lastNameContains(lowerQuery, caseSensitive: false)
            .or()
            .emailContains(lowerQuery, caseSensitive: false))
        .findAll();
  }

  /// Get all active users
  Future<List<UserIsar>> getAllActiveUsers() async {
    return await _isar.userIsars
        .filter()
        .isActiveEqualTo(true)
        .findAll();
  }

  /// Get user count
  Future<int> getUserCount() async {
    return await _isar.userIsars.count();
  }

  /// Get active user count (logged in within last 30 days)
  Future<int> getActiveUserCount() async {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return await _isar.userIsars
        .filter()
        .lastLoginAtGreaterThan(thirtyDaysAgo)
        .count();
  }

  /// Clean up expired sessions
  Future<void> cleanupExpiredSessions() async {
    final now = DateTime.now();
    await _isar.writeTxn(() async {
      await _isar.userSessionIsars
          .filter()
          .expiresAtLessThan(now)
          .deleteAll();
    });
  }

  //=== AUTHENTICATION HELPERS ===//

  /// Verify user credentials
  Future<UserIsar?> verifyCredentials(String email, String passwordHash) async {
    return await _isar.userIsars
        .filter()
        .emailEqualTo(email, caseSensitive: false)
        .and()
        .passwordHashEqualTo(passwordHash)
        .findFirst();
  }

  /// Update password
  Future<void> updatePassword(String businessId, String newPasswordHash) async {
    final user = await getUserByBusinessId(businessId);
    if (user != null) {
      user.passwordHash = newPasswordHash;
      user.updatedAt = DateTime.now();
      await saveUser(user);
    }
  }

  /// Mark email as verified
  Future<void> markEmailVerified(String businessId) async {
    final user = await getUserByBusinessId(businessId);
    if (user != null) {
      user.isEmailVerified = true;
      user.updatedAt = DateTime.now();
      await saveUser(user);
    }
  }
}
