import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_enums.dart';
import 'events_repository.dart';

/// Service for automatic event creation from other modules
/// 
/// This service handles:
/// - Automatic event creation when records are added in other modules
/// - Event scheduling based on automation rules
/// - Cross-module integration for seamless event tracking
/// - Recurrence scheduling for follow-up events
class EventAutomationService {
  final EventsRepository _eventsRepository = GetIt.instance<EventsRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  /// Create a health-related event automatically
  /// Called from HealthRepository when health records are saved
  Future<EventIsar?> createHealthEvent({
    required String cattleTagId,
    required String healthRecordId,
    required String eventType,
    required DateTime date,
    String? notes,
    String? diagnosis,
    String? treatment,
    double? cost,
  }) async {
    try {
      // Find the appropriate event type
      final eventTypeRecord = await _findEventTypeByName(eventType, EventCategory.health);
      if (eventTypeRecord == null) {
        // Create a default health event type if not found
        final defaultEventType = EventTypeIsar.create(
          name: eventType,
          category: EventCategory.health,
          description: 'Auto-generated health event type',
          canBeAutoGenerated: true,
        );
        await _eventsRepository.saveEventType(defaultEventType);
      }

      // Create the event
      final event = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = cattleTagId
        ..eventTypeId = eventTypeRecord?.businessId ?? eventType
        ..category = EventCategory.health
        ..title = _generateHealthEventTitle(eventType, diagnosis)
        ..description = _generateHealthEventDescription(notes, diagnosis, treatment)
        ..scheduledDate = date
        ..status = EventStatus.completed // Health events are typically completed when recorded
        ..priority = _determineHealthEventPriority(eventType, diagnosis)
        ..isAutoGenerated = true
        ..sourceModule = 'Health'
        ..sourceRecordId = healthRecordId
        ..actualCost = cost
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(event);

      // Schedule follow-up events if needed
      await _scheduleHealthFollowUpEvents(event, eventType);

      return event;
    } catch (e) {
      // Let exceptions bubble up naturally
      rethrow;
    }
  }

  /// Create a breeding-related event automatically
  /// Called from BreedingRepository when breeding records are saved
  Future<EventIsar?> createBreedingEvent({
    required String cattleTagId,
    required String breedingRecordId,
    required DateTime breedingDate,
    String? method,
    String? bullIdOrType,
    bool schedulePregnancyCheck = true,
    double? cost,
  }) async {
    try {
      // Find breeding event type
      final eventTypeRecord = await _findEventTypeByName('Breeding', EventCategory.breeding);
      
      // Create the breeding event
      final event = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = cattleTagId
        ..eventTypeId = eventTypeRecord?.businessId ?? 'breeding'
        ..category = EventCategory.breeding
        ..title = 'Breeding Event'
        ..description = _generateBreedingEventDescription(method, bullIdOrType)
        ..scheduledDate = breedingDate
        ..status = EventStatus.completed
        ..priority = EventPriority.high
        ..isAutoGenerated = true
        ..sourceModule = 'Breeding'
        ..sourceRecordId = breedingRecordId
        ..actualCost = cost
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(event);

      // Schedule pregnancy check if requested
      if (schedulePregnancyCheck) {
        await _schedulePregnancyCheckEvent(cattleTagId, breedingDate, breedingRecordId);
      }

      return event;
    } catch (e) {
      rethrow;
    }
  }

  /// Create a weight monitoring event automatically
  /// Called when weight records are added
  Future<EventIsar?> createWeightEvent({
    required String cattleTagId,
    required double weight,
    required DateTime date,
    bool scheduleNextWeighing = true,
    String? notes,
  }) async {
    try {
      // Find weight check event type
      final eventTypeRecord = await _findEventTypeByName('Weight Check', EventCategory.feeding);
      
      // Create the weight event
      final event = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = cattleTagId
        ..eventTypeId = eventTypeRecord?.businessId ?? 'weight_check'
        ..category = EventCategory.feeding
        ..title = 'Weight Check'
        ..description = 'Weight recorded: ${weight}kg${notes != null ? '\nNotes: $notes' : ''}'
        ..scheduledDate = date
        ..status = EventStatus.completed
        ..priority = EventPriority.medium
        ..isAutoGenerated = true
        ..sourceModule = 'Weight'
        ..sourceRecordId = '${cattleTagId}_${date.millisecondsSinceEpoch}'
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(event);

      // Schedule next weighing if requested
      if (scheduleNextWeighing) {
        await _scheduleNextWeighingEvent(cattleTagId, date);
      }

      return event;
    } catch (e) {
      rethrow;
    }
  }

  /// Create a transaction-related event automatically
  /// Called when financial transactions are recorded
  Future<EventIsar?> createTransactionEvent({
    required String cattleTagId,
    required String transactionId,
    required String transactionType,
    required DateTime date,
    double? amount,
    String? notes,
  }) async {
    try {
      // Determine event type based on transaction type
      final eventTypeName = transactionType == 'purchase' ? 'Purchase' : 'Sale';
      final eventTypeRecord = await _findEventTypeByName(eventTypeName, EventCategory.financial);
      
      // Create the transaction event
      final event = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = cattleTagId
        ..eventTypeId = eventTypeRecord?.businessId ?? transactionType.toLowerCase()
        ..category = EventCategory.financial
        ..title = '$eventTypeName Transaction'
        ..description = _generateTransactionEventDescription(transactionType, amount, notes)
        ..scheduledDate = date
        ..status = EventStatus.completed
        ..priority = EventPriority.high
        ..isAutoGenerated = true
        ..sourceModule = 'Financial'
        ..sourceRecordId = transactionId
        ..actualCost = transactionType == 'purchase' ? amount : null
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(event);

      return event;
    } catch (e) {
      rethrow;
    }
  }

  /// Create a milk production event automatically
  /// Called when milk records are added
  Future<EventIsar?> createMilkProductionEvent({
    required String cattleTagId,
    required String milkRecordId,
    required DateTime date,
    required double quantity,
    String? notes,
    double? fatContent,
    double? proteinContent,
  }) async {
    try {
      // Find milk production event type
      final eventTypeRecord = await _findEventTypeByName('Milk Production', EventCategory.feeding);
      
      // Create the milk production event
      final event = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = cattleTagId
        ..eventTypeId = eventTypeRecord?.businessId ?? 'milk_production'
        ..category = EventCategory.feeding
        ..title = 'Milk Production'
        ..description = _generateMilkProductionEventDescription(quantity, fatContent, proteinContent, notes)
        ..scheduledDate = date
        ..status = EventStatus.completed
        ..priority = EventPriority.medium
        ..isAutoGenerated = true
        ..sourceModule = 'Milk'
        ..sourceRecordId = milkRecordId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(event);

      return event;
    } catch (e) {
      rethrow;
    }
  }

  /// Create a milk sale event automatically
  /// Called when milk sales are recorded
  Future<EventIsar?> createMilkSaleEvent({
    required String milkSaleId,
    required DateTime date,
    required double quantity,
    required double totalAmount,
    String? buyerName,
    String? notes,
  }) async {
    try {
      // Find milk sale event type
      final eventTypeRecord = await _findEventTypeByName('Milk Sale', EventCategory.financial);
      
      // Create the milk sale event
      final event = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = '' // Milk sales might not be tied to specific cattle
        ..eventTypeId = eventTypeRecord?.businessId ?? 'milk_sale'
        ..category = EventCategory.financial
        ..title = 'Milk Sale'
        ..description = _generateMilkSaleEventDescription(quantity, totalAmount, buyerName, notes)
        ..scheduledDate = date
        ..status = EventStatus.completed
        ..priority = EventPriority.medium
        ..isAutoGenerated = true
        ..sourceModule = 'Milk'
        ..sourceRecordId = milkSaleId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(event);

      return event;
    } catch (e) {
      rethrow;
    }
  }

  /// Find event type by name and category
  Future<EventTypeIsar?> _findEventTypeByName(String name, EventCategory category) async {
    return await _isar.eventTypeIsars
        .filter()
        .nameEqualTo(name)
        .and()
        .categoryEqualTo(category)
        .findFirst();
  }

  /// Generate health event title
  String _generateHealthEventTitle(String eventType, String? diagnosis) {
    if (diagnosis != null && diagnosis.isNotEmpty) {
      return '$eventType - $diagnosis';
    }
    return eventType;
  }

  /// Generate health event description
  String _generateHealthEventDescription(String? notes, String? diagnosis, String? treatment) {
    final parts = <String>[];
    
    if (diagnosis != null && diagnosis.isNotEmpty) {
      parts.add('Diagnosis: $diagnosis');
    }
    
    if (treatment != null && treatment.isNotEmpty) {
      parts.add('Treatment: $treatment');
    }
    
    if (notes != null && notes.isNotEmpty) {
      parts.add('Notes: $notes');
    }
    
    return parts.join('\n');
  }

  /// Determine health event priority based on type and diagnosis
  EventPriority _determineHealthEventPriority(String eventType, String? diagnosis) {
    // High priority for treatments and emergencies
    if (eventType.toLowerCase().contains('treatment') || 
        eventType.toLowerCase().contains('emergency') ||
        diagnosis?.toLowerCase().contains('severe') == true) {
      return EventPriority.high;
    }
    
    // Medium priority for vaccinations and check-ups
    if (eventType.toLowerCase().contains('vaccination') || 
        eventType.toLowerCase().contains('check')) {
      return EventPriority.medium;
    }
    
    return EventPriority.medium;
  }

  /// Generate breeding event description
  String _generateBreedingEventDescription(String? method, String? bullIdOrType) {
    final parts = <String>[];
    
    if (method != null && method.isNotEmpty) {
      parts.add('Method: $method');
    }
    
    if (bullIdOrType != null && bullIdOrType.isNotEmpty) {
      parts.add('Bull/Sire: $bullIdOrType');
    }
    
    return parts.join('\n');
  }

  /// Generate transaction event description
  String _generateTransactionEventDescription(String transactionType, double? amount, String? notes) {
    final parts = <String>[];

    parts.add('Transaction Type: ${transactionType.toUpperCase()}');

    if (amount != null) {
      parts.add('Amount: \$${amount.toStringAsFixed(2)}');
    }

    if (notes != null && notes.isNotEmpty) {
      parts.add('Notes: $notes');
    }

    return parts.join('\n');
  }

  /// Schedule health follow-up events based on event type
  Future<void> _scheduleHealthFollowUpEvents(EventIsar parentEvent, String eventType) async {
    try {
      DateTime? followUpDate;
      String? followUpTitle;

      // Determine follow-up schedule based on event type
      switch (eventType.toLowerCase()) {
        case 'vaccination':
          // Schedule booster shot in 21 days
          followUpDate = parentEvent.scheduledDate?.add(const Duration(days: 21));
          followUpTitle = 'Vaccination Booster';
          break;
        case 'treatment':
          // Schedule follow-up check in 7 days
          followUpDate = parentEvent.scheduledDate?.add(const Duration(days: 7));
          followUpTitle = 'Treatment Follow-up';
          break;
        case 'deworming':
          // Schedule next deworming in 3 months
          if (parentEvent.scheduledDate != null) {
            followUpDate = DateTime(
              parentEvent.scheduledDate!.year,
              parentEvent.scheduledDate!.month + 3,
              parentEvent.scheduledDate!.day,
            );
          }
          followUpTitle = 'Deworming';
          break;
      }

      if (followUpDate != null && followUpTitle != null) {
        await _createFollowUpEvent(
          parentEvent: parentEvent,
          followUpDate: followUpDate,
          title: followUpTitle,
          category: EventCategory.health,
        );
      }
    } catch (e) {
      // Log error but don't fail the main operation
      //$1// $0 // Print statement commented out for production
    }
  }

  /// Schedule pregnancy check event after breeding
  Future<void> _schedulePregnancyCheckEvent(String cattleTagId, DateTime breedingDate, String breedingRecordId) async {
    try {
      // Schedule pregnancy check 30 days after breeding
      final checkDate = breedingDate.add(const Duration(days: 30));

      final eventTypeRecord = await _findEventTypeByName('Pregnancy Check', EventCategory.breeding);

      final pregnancyCheckEvent = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = cattleTagId
        ..eventTypeId = eventTypeRecord?.businessId ?? 'pregnancy_check'
        ..category = EventCategory.breeding
        ..title = 'Pregnancy Check'
        ..description = 'Scheduled pregnancy check following breeding'
        ..scheduledDate = checkDate
        ..status = EventStatus.scheduled
        ..priority = EventPriority.high
        ..isAutoGenerated = true
        ..sourceModule = 'Breeding'
        ..sourceRecordId = breedingRecordId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(pregnancyCheckEvent);
    } catch (e) {
      //$1// $0 // Print statement commented out for production
    }
  }

  /// Schedule next weighing event
  Future<void> _scheduleNextWeighingEvent(String cattleTagId, DateTime lastWeighingDate) async {
    try {
      // Schedule next weighing in 2 weeks
      final nextWeighingDate = lastWeighingDate.add(const Duration(days: 14));

      final eventTypeRecord = await _findEventTypeByName('Weight Check', EventCategory.feeding);

      final nextWeighingEvent = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = cattleTagId
        ..eventTypeId = eventTypeRecord?.businessId ?? 'weight_check'
        ..category = EventCategory.feeding
        ..title = 'Weight Check'
        ..description = 'Scheduled weight monitoring'
        ..scheduledDate = nextWeighingDate
        ..status = EventStatus.scheduled
        ..priority = EventPriority.medium
        ..isAutoGenerated = true
        ..sourceModule = 'Weight'
        ..isRecurring = true
        ..recurrencePattern = RecurrencePattern.weekly
        ..recurrenceInterval = 2
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(nextWeighingEvent);
    } catch (e) {
      //$1// $0 // Print statement commented out for production
    }
  }

  /// Create a generic follow-up event
  Future<void> _createFollowUpEvent({
    required EventIsar parentEvent,
    required DateTime followUpDate,
    required String title,
    required EventCategory category,
    String? description,
  }) async {
    try {
      final followUpEvent = EventIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = parentEvent.cattleTagId
        ..eventTypeId = parentEvent.eventTypeId
        ..category = category
        ..title = title
        ..description = description ?? 'Follow-up for: ${parentEvent.title}'
        ..scheduledDate = followUpDate
        ..status = EventStatus.scheduled
        ..priority = parentEvent.priority
        ..isAutoGenerated = true
        ..sourceModule = parentEvent.sourceModule
        ..sourceRecordId = parentEvent.sourceRecordId
        ..parentEventId = parentEvent.businessId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventsRepository.saveEvent(followUpEvent);
    } catch (e) {
      //$1// $0 // Print statement commented out for production
    }
  }

  /// Get automation rules for an event type
  Future<Map<String, dynamic>?> getAutomationRules(String eventTypeId) async {
    try {
      final eventType = await _isar.eventTypeIsars
          .filter()
          .businessIdEqualTo(eventTypeId)
          .findFirst();

      if (eventType?.autoGenerationRules != null) {
        // Parse JSON rules (placeholder for future implementation)
        return {'rules': eventType!.autoGenerationRules};
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Update automation rules for an event type
  Future<void> updateAutomationRules(String eventTypeId, Map<String, dynamic> rules) async {
    try {
      final eventType = await _isar.eventTypeIsars
          .filter()
          .businessIdEqualTo(eventTypeId)
          .findFirst();

      if (eventType != null) {
        eventType.autoGenerationRules = rules.toString(); // Simplified for now
        eventType.updatedAt = DateTime.now();
        await _eventsRepository.saveEventType(eventType);
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Generate milk production event description
  String _generateMilkProductionEventDescription(double quantity, double? fatContent, double? proteinContent, String? notes) {
    final parts = <String>[];

    parts.add('Quantity: ${quantity.toStringAsFixed(2)} liters');

    if (fatContent != null) {
      parts.add('Fat Content: ${fatContent.toStringAsFixed(2)}%');
    }

    if (proteinContent != null) {
      parts.add('Protein Content: ${proteinContent.toStringAsFixed(2)}%');
    }

    if (notes != null && notes.isNotEmpty) {
      parts.add('Notes: $notes');
    }

    return parts.join('\n');
  }

  /// Generate milk sale event description
  String _generateMilkSaleEventDescription(double quantity, double totalAmount, String? buyerName, String? notes) {
    final parts = <String>[];

    parts.add('Quantity Sold: ${quantity.toStringAsFixed(2)} liters');
    parts.add('Total Amount: \${totalAmount.toStringAsFixed(2)}');
    parts.add('Price per Liter: \${(totalAmount / quantity).toStringAsFixed(2)}');

    if (buyerName != null && buyerName.isNotEmpty) {
      parts.add('Buyer: $buyerName');
    }

    if (notes != null && notes.isNotEmpty) {
      parts.add('Notes: $notes');
    }

    return parts.join('\n');
  }
}
