import 'package:flutter/material.dart';
import '../../../shared/models/info_card_data.dart';

/// Unified Report Models for Advanced Reports System
/// 
/// This file contains all data models needed for the modernized reports system.
/// Models are designed to work with existing UniversalInfoCard and support
/// cross-module analytics with PDF/Excel export capabilities.

/// Main report data container
class ReportData {
  final String title;
  final String subtitle;
  final DateTime generated;
  final DateTime? startDate;
  final DateTime? endDate;
  final Map<String, ReportMetric> metrics;
  final List<ChartPoint> chartData;
  final List<Map<String, String>> tableData;
  final List<String> insights;
  final ReportType type;

  const ReportData({
    required this.title,
    required this.subtitle,
    required this.generated,
    this.startDate,
    this.endDate,
    required this.metrics,
    required this.chartData,
    required this.tableData,
    required this.insights,
    required this.type,
  });

  /// Factory for creating empty report data
  factory ReportData.empty(ReportType type) {
    return ReportData(
      title: _getDefaultTitle(type),
      subtitle: 'No data available',
      generated: DateTime.now(),
      metrics: {},
      chartData: [],
      tableData: [],
      insights: [],
      type: type,
    );
  }

  static String _getDefaultTitle(ReportType type) {
    switch (type) {
      case ReportType.dashboard:
        return 'Farm Dashboard';
      case ReportType.cattle:
        return 'Cattle Report';
      case ReportType.milk:
        return 'Milk Production Report';
      case ReportType.health:
        return 'Health Records Report';
      case ReportType.breeding:
        return 'Breeding Report';
      case ReportType.weight:
        return 'Weight Tracking Report';
      case ReportType.financial:
        return 'Financial Report';
    }
  }

  /// Check if report has data
  bool get hasData => metrics.isNotEmpty || chartData.isNotEmpty || tableData.isNotEmpty;

  /// Get date range string for display
  String get dateRangeString {
    if (startDate == null || endDate == null) return 'All time';
    return '${_formatDate(startDate!)} - ${_formatDate(endDate!)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Individual metric for UniversalInfoCard display
///
/// Extends InfoCardData with MetricType for reports system integration
class ReportMetric extends InfoCardData {
  final MetricType type;

  const ReportMetric({
    required super.title,
    required super.value,
    required super.icon,
    required super.color,
    super.subtitle = '',
    super.badge,
    super.insight = '',
    required this.type,
    super.onTap,
  });

  /// Convert to UniversalInfoCard based on type
  @override
  Widget toUniversalInfoCard() {
    switch (type) {
      case MetricType.kpi:
        return toUniversalInfoCardKPI();
      case MetricType.metric:
        return toUniversalInfoCardWithBadge();
      case MetricType.insight:
        return super.toUniversalInfoCard();
    }
  }

  /// Factory for KPI metrics (key performance indicators)
  factory ReportMetric.kpi({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    VoidCallback? onTap,
  }) {
    return ReportMetric(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle ?? '',
      insight: '',
      type: MetricType.kpi,
      onTap: onTap,
    );
  }

  /// Factory for metrics with badges
  factory ReportMetric.withBadge({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    required String badge,
    VoidCallback? onTap,
  }) {
    return ReportMetric(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle ?? '',
      badge: badge,
      insight: '',
      type: MetricType.metric,
      onTap: onTap,
    );
  }

  /// Factory for metrics with insights
  factory ReportMetric.withInsight({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    required String insight,
    VoidCallback? onTap,
  }) {
    return ReportMetric(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle ?? '',
      insight: insight,
      type: MetricType.insight,
      onTap: onTap,
    );
  }
}

/// Chart data point for visualizations
class ChartPoint {
  final String label;
  final double value;
  final DateTime? date;
  final Color? color;
  final Map<String, dynamic>? metadata;

  const ChartPoint({
    required this.label,
    required this.value,
    this.date,
    this.color,
    this.metadata,
  });

  /// Factory for time-series data points
  factory ChartPoint.timeSeries({
    required DateTime date,
    required double value,
    Color? color,
    Map<String, dynamic>? metadata,
  }) {
    return ChartPoint(
      label: _formatDateLabel(date),
      value: value,
      date: date,
      color: color,
      metadata: metadata,
    );
  }

  static String _formatDateLabel(DateTime date) {
    return '${date.day}/${date.month}';
  }

  /// Factory for categorical data points
  factory ChartPoint.category({
    required String category,
    required double value,
    Color? color,
    Map<String, dynamic>? metadata,
  }) {
    return ChartPoint(
      label: category,
      value: value,
      color: color,
      metadata: metadata,
    );
  }
}

/// Filter state for report generation
class FilterState {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String>? cattleIds;
  final List<String>? categories;
  final String? searchQuery;
  final Map<String, dynamic>? customFilters;

  const FilterState({
    this.startDate,
    this.endDate,
    this.cattleIds,
    this.categories,
    this.searchQuery,
    this.customFilters,
  });

  /// Check if any filters are active
  bool get hasFilters => 
    startDate != null || 
    endDate != null || 
    (cattleIds?.isNotEmpty ?? false) || 
    (categories?.isNotEmpty ?? false) ||
    (searchQuery?.isNotEmpty ?? false) ||
    (customFilters?.isNotEmpty ?? false);

  /// Create copy with updated filters
  FilterState copyWith({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? cattleIds,
    List<String>? categories,
    String? searchQuery,
    Map<String, dynamic>? customFilters,
  }) {
    return FilterState(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      cattleIds: cattleIds ?? this.cattleIds,
      categories: categories ?? this.categories,
      searchQuery: searchQuery ?? this.searchQuery,
      customFilters: customFilters ?? this.customFilters,
    );
  }

  /// Clear all filters
  FilterState clear() {
    return const FilterState();
  }

  /// Get active filter count
  int get activeFilterCount {
    int count = 0;
    if (startDate != null) count++;
    if (endDate != null) count++;
    if (cattleIds?.isNotEmpty ?? false) count++;
    if (categories?.isNotEmpty ?? false) count++;
    if (searchQuery?.isNotEmpty ?? false) count++;
    if (customFilters?.isNotEmpty ?? false) count++;
    return count;
  }
}

/// Export configuration
class ExportConfig {
  final ExportType type;
  final ExportFormat format;
  final bool includeCharts;
  final bool includeMetrics;
  final bool includeTableData;
  final bool includeInsights;
  final String? customTitle;
  final Map<String, dynamic>? customSettings;

  const ExportConfig({
    required this.type,
    required this.format,
    this.includeCharts = true,
    this.includeMetrics = true,
    this.includeTableData = true,
    this.includeInsights = true,
    this.customTitle,
    this.customSettings,
  });

  /// Factory for PDF export
  factory ExportConfig.pdf({
    ExportType type = ExportType.download,
    bool includeCharts = true,
    bool includeMetrics = true,
    bool includeTableData = true,
    bool includeInsights = true,
    String? customTitle,
  }) {
    return ExportConfig(
      type: type,
      format: ExportFormat.pdf,
      includeCharts: includeCharts,
      includeMetrics: includeMetrics,
      includeTableData: includeTableData,
      includeInsights: includeInsights,
      customTitle: customTitle,
    );
  }

  /// Factory for Excel export
  factory ExportConfig.excel({
    ExportType type = ExportType.download,
    bool includeCharts = false, // Excel typically uses data tables
    bool includeMetrics = true,
    bool includeTableData = true,
    bool includeInsights = true,
    String? customTitle,
  }) {
    return ExportConfig(
      type: type,
      format: ExportFormat.excel,
      includeCharts: includeCharts,
      includeMetrics: includeMetrics,
      includeTableData: includeTableData,
      includeInsights: includeInsights,
      customTitle: customTitle,
    );
  }
}

/// Enums for type safety

enum ReportType {
  dashboard,
  cattle,
  milk,
  health,
  breeding,
  weight,
  financial,
}

enum MetricType {
  kpi,
  metric,
  insight,
}

enum ChartType {
  line,
  bar,
  pie,
  area,
}

enum ExportFormat {
  pdf,
  excel,
}

enum ExportType {
  download,
  share,
  email,
  print,
}

enum ShareAction {
  download,
  share,
  email,
  print,
}

/// Extensions for convenience

extension DateRanges on DateTime {
  static DateTime get weekAgo => DateTime.now().subtract(const Duration(days: 7));
  static DateTime get monthAgo => DateTime.now().subtract(const Duration(days: 30));
  static DateTime get quarterAgo => DateTime.now().subtract(const Duration(days: 90));
  static DateTime get yearAgo => DateTime.now().subtract(const Duration(days: 365));
  
  /// Get start of day
  DateTime get startOfDay => DateTime(year, month, day);
  
  /// Get end of day
  DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59);
}

extension ReportTypeExtensions on ReportType {
  String get displayName {
    switch (this) {
      case ReportType.dashboard:
        return 'Dashboard';
      case ReportType.cattle:
        return 'Cattle';
      case ReportType.milk:
        return 'Milk Production';
      case ReportType.health:
        return 'Health Records';
      case ReportType.breeding:
        return 'Breeding';
      case ReportType.weight:
        return 'Weight Tracking';
      case ReportType.financial:
        return 'Financial';
    }
  }

  IconData get icon {
    switch (this) {
      case ReportType.dashboard:
        return Icons.dashboard;
      case ReportType.cattle:
        return Icons.pets;
      case ReportType.milk:
        return Icons.local_drink;
      case ReportType.health:
        return Icons.health_and_safety;
      case ReportType.breeding:
        return Icons.favorite;
      case ReportType.weight:
        return Icons.monitor_weight;
      case ReportType.financial:
        return Icons.attach_money;
    }
  }

  Color get color {
    switch (this) {
      case ReportType.dashboard:
        return Colors.blue;
      case ReportType.cattle:
        return Colors.brown;
      case ReportType.milk:
        return Colors.lightBlue;
      case ReportType.health:
        return Colors.red;
      case ReportType.breeding:
        return Colors.pink;
      case ReportType.weight:
        return Colors.orange;
      case ReportType.financial:
        return Colors.green;
    }
  }
}

extension ChartTypeExtensions on ChartType {
  String get displayName {
    switch (this) {
      case ChartType.line:
        return 'Line Chart';
      case ChartType.bar:
        return 'Bar Chart';
      case ChartType.pie:
        return 'Pie Chart';
      case ChartType.area:
        return 'Area Chart';
    }
  }

  IconData get icon {
    switch (this) {
      case ChartType.line:
        return Icons.show_chart;
      case ChartType.bar:
        return Icons.bar_chart;
      case ChartType.pie:
        return Icons.pie_chart;
      case ChartType.area:
        return Icons.area_chart;
    }
  }
}