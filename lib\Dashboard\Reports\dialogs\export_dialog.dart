import 'package:flutter/material.dart';
import '../models/report_models.dart';
import '../../../core/get_stub.dart';
import '../services/sharing_service.dart';

/// Export Dialog
/// 
/// Provides comprehensive export and sharing options for reports.
/// Supports PDF/Excel export, download, share, email, and print functionality.
class ExportDialog extends StatefulWidget {
  final ReportData reportData;

  const ExportDialog({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  State<ExportDialog> createState() => _ExportDialogState();
}

class _ExportDialogState extends State<ExportDialog> {
  final SharingService _sharingService = SharingService();
  
  ExportFormat _selectedFormat = ExportFormat.pdf;
  bool _includeCharts = true;
  bool _includeMetrics = true;
  bool _includeTableData = true;
  bool _includeInsights = true;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.file_download,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          const Text('Export & Share Report'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Report info
            _buildReportInfo(),
            const SizedBox(height: 16),
            
            // Format selection
            _buildFormatSelection(),
            const SizedBox(height: 16),
            
            // Content options
            _buildContentOptions(),
            const SizedBox(height: 16),
            
            // Export actions
            _buildExportActions(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  Widget _buildReportInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.reportData.title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.reportData.subtitle,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          if (widget.reportData.startDate != null) ...[
            const SizedBox(height: 4),
            Text(
              'Period: ${widget.reportData.dateRangeString}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFormatSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Export Format',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<ExportFormat>(
                title: const Row(
                  children: [
                    Icon(Icons.picture_as_pdf, color: Colors.red, size: 20),
                    SizedBox(width: 8),
                    Text('PDF'),
                  ],
                ),
                value: ExportFormat.pdf,
                groupValue: _selectedFormat,
                onChanged: (value) {
                  setState(() {
                    _selectedFormat = value!;
                  });
                },
                dense: true,
              ),
            ),
            Expanded(
              child: RadioListTile<ExportFormat>(
                title: const Row(
                  children: [
                    Icon(Icons.table_chart, color: Colors.green, size: 20),
                    SizedBox(width: 8),
                    Text('Excel'),
                  ],
                ),
                value: ExportFormat.excel,
                groupValue: _selectedFormat,
                onChanged: (value) {
                  setState(() {
                    _selectedFormat = value!;
                  });
                },
                dense: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContentOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Include in Export',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: const Text('Key Metrics'),
          subtitle: Text('${widget.reportData.metrics.length} metrics'),
          value: _includeMetrics,
          onChanged: (value) {
            setState(() {
              _includeMetrics = value ?? true;
            });
          },
          dense: true,
        ),
        CheckboxListTile(
          title: const Text('Charts & Visualizations'),
          subtitle: Text('${widget.reportData.chartData.length} data points'),
          value: _includeCharts,
          onChanged: (value) {
            setState(() {
              _includeCharts = value ?? true;
            });
          },
          dense: true,
        ),
        CheckboxListTile(
          title: const Text('Detailed Data Tables'),
          subtitle: Text('${widget.reportData.tableData.length} rows'),
          value: _includeTableData,
          onChanged: (value) {
            setState(() {
              _includeTableData = value ?? true;
            });
          },
          dense: true,
        ),
        CheckboxListTile(
          title: const Text('Insights & Recommendations'),
          subtitle: Text('${widget.reportData.insights.length} insights'),
          value: _includeInsights,
          onChanged: (value) {
            setState(() {
              _includeInsights = value ?? true;
            });
          },
          dense: true,
        ),
      ],
    );
  }

  Widget _buildExportActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Export Actions',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        
        // Download and Share row
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'Download',
                Icons.download,
                Colors.blue,
                () => _handleExport(ExportType.download),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildActionButton(
                'Share',
                Icons.share,
                Colors.green,
                () => _handleExport(ExportType.share),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Email and Print row
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'Email',
                Icons.email,
                Colors.orange,
                () => _handleExport(ExportType.email),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildActionButton(
                'Print',
                Icons.print,
                Colors.purple,
                () => _handleExport(ExportType.print),
                enabled: _selectedFormat == ExportFormat.pdf,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed, {
    bool enabled = true,
  }) {
    return ElevatedButton.icon(
      onPressed: enabled ? onPressed : null,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: enabled ? color.withValues(alpha: 0.1) : null,
        foregroundColor: enabled ? color : null,
        side: enabled ? BorderSide(color: color.withValues(alpha: 0.3)) : null,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      ),
    );
  }

  Future<void> _handleExport(ExportType exportType) async {
    // Close dialog first
    Navigator.of(context).pop();
    
    // Create export config
    final config = _selectedFormat == ExportFormat.pdf
        ? ExportConfig.pdf(
            type: exportType,
            includeCharts: _includeCharts,
            includeMetrics: _includeMetrics,
            includeTableData: _includeTableData,
            includeInsights: _includeInsights,
          )
        : ExportConfig.excel(
            type: exportType,
            includeCharts: _includeCharts,
            includeMetrics: _includeMetrics,
            includeTableData: _includeTableData,
            includeInsights: _includeInsights,
          );
    
    // Execute export
    try {
      switch (exportType) {
        case ExportType.download:
          if (_selectedFormat == ExportFormat.pdf) {
            await _sharingService.downloadPDF(widget.reportData, config: config);
          } else {
            await _sharingService.downloadExcel(widget.reportData, config: config);
          }
          break;
        case ExportType.share:
          if (_selectedFormat == ExportFormat.pdf) {
            await _sharingService.sharePDF(widget.reportData, config: config);
          } else {
            await _sharingService.shareExcel(widget.reportData, config: config);
          }
          break;
        case ExportType.email:
          if (_selectedFormat == ExportFormat.pdf) {
            await _sharingService.emailPDF(widget.reportData, config: config);
          } else {
            await _sharingService.emailExcel(widget.reportData, config: config);
          }
          break;
        case ExportType.print:
          await _sharingService.printPDF(widget.reportData, config: config);
          break;
      }
    } catch (e) {
      Get.snackbar(
        'Export Error',
        'Failed to export report: $e',
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red[800],
      );
    }
  }
}

/// Quick Export Dialog - Simplified version for quick actions
class QuickExportDialog extends StatelessWidget {
  final ReportData reportData;

  const QuickExportDialog({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final sharingService = SharingService();
    final options = sharingService.getAvailableShareOptions();

    return AlertDialog(
      title: const Text('Quick Export'),
      content: SizedBox(
        width: double.maxFinite,
        child: GridView.count(
          shrinkWrap: true,
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 2,
          children: options.map((option) => InkWell(
            onTap: () {
              Navigator.of(context).pop();
              sharingService.executeShareOption(option, reportData);
            },
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: option.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: option.color.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(option.icon, color: option.color, size: 20),
                  const SizedBox(height: 4),
                  Text(
                    option.title,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: option.color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          )).toList(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }
}