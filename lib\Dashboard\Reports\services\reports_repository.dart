import 'dart:async';
import 'package:isar/isar.dart';
import '../../../services/database/isar_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Breeding/models/breeding_record_isar.dart';
import '../../Weight/models/weight_record_isar.dart';
import '../../Transactions/models/transaction_isar.dart';
import '../../Events/models/event_isar.dart';

/// Pure reactive repository for Reports module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
/// 
/// This repository provides reactive streams to aggregated data from all modules
/// for report generation. It does not store report data itself, but provides
/// access to the underlying data that reports are generated from.
class ReportsRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  ReportsRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE DATA STREAMS FOR REPORT GENERATION ===//

  /// Watches all cattle with reactive updates for cattle reports
  Stream<List<CattleIsar>> watchAllCattle() {
    return _isar.cattleIsars.where().watch(fireImmediately: true);
  }

  /// Watches all milk records with reactive updates for milk production reports
  Stream<List<MilkRecordIsar>> watchAllMilkRecords() {
    return _isar.milkRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all health records with reactive updates for health reports
  Stream<List<HealthRecordIsar>> watchAllHealthRecords() {
    return _isar.healthRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all breeding records with reactive updates for breeding reports
  Stream<List<BreedingRecordIsar>> watchAllBreedingRecords() {
    return _isar.breedingRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all weight records with reactive updates for weight reports
  Stream<List<WeightRecordIsar>> watchAllWeightRecords() {
    return _isar.weightRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all transactions with reactive updates for financial reports
  Stream<List<TransactionIsar>> watchAllTransactions() {
    return _isar.transactionIsars.where().watch(fireImmediately: true);
  }

  /// Watches all events with reactive updates for event reports
  Stream<List<EventIsar>> watchAllEvents() {
    return _isar.eventIsars.where().watch(fireImmediately: true);
  }

  //=== QUERY METHODS FOR ANALYTICS AND REPORT GENERATION ===//

  /// Get all cattle (for analytics and report generation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<CattleIsar>> getAllCattle() async {
    return await _isar.cattleIsars.where().findAll();
  }

  /// Get all milk records (for analytics and report generation)
  Future<List<MilkRecordIsar>> getAllMilkRecords() async {
    return await _isar.milkRecordIsars.where().findAll();
  }

  /// Get all health records (for analytics and report generation)
  Future<List<HealthRecordIsar>> getAllHealthRecords() async {
    return await _isar.healthRecordIsars.where().findAll();
  }

  /// Get all breeding records (for analytics and report generation)
  Future<List<BreedingRecordIsar>> getAllBreedingRecords() async {
    return await _isar.breedingRecordIsars.where().findAll();
  }

  /// Get all weight records (for analytics and report generation)
  Future<List<WeightRecordIsar>> getAllWeightRecords() async {
    return await _isar.weightRecordIsars.where().findAll();
  }

  /// Get all transactions (for analytics and report generation)
  Future<List<TransactionIsar>> getAllTransactions() async {
    return await _isar.transactionIsars.where().findAll();
  }

  /// Get all events (for analytics and report generation)
  Future<List<EventIsar>> getAllEvents() async {
    return await _isar.eventIsars.where().findAll();
  }

  //=== FILTERED QUERY METHODS FOR REPORT FILTERING ===//

  /// Get cattle by date range for filtered reports
  Future<List<CattleIsar>> getCattleByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.cattleIsars
        .filter()
        .dateOfBirthBetween(startDate, endDate)
        .findAll();
  }

  /// Get milk records by date range for filtered reports
  Future<List<MilkRecordIsar>> getMilkRecordsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.milkRecordIsars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }

  /// Get health records by date range for filtered reports
  Future<List<HealthRecordIsar>> getHealthRecordsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.healthRecordIsars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }

  /// Get breeding records by date range for filtered reports
  Future<List<BreedingRecordIsar>> getBreedingRecordsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.breedingRecordIsars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }

  /// Get weight records by date range for filtered reports
  Future<List<WeightRecordIsar>> getWeightRecordsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.weightRecordIsars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }

  /// Get transactions by date range for filtered reports
  Future<List<TransactionIsar>> getTransactionsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.transactionIsars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }

  /// Get events by date range for filtered reports
  Future<List<EventIsar>> getEventsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.eventIsars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }

  //=== CATTLE-SPECIFIC FILTERED QUERIES ===//

  /// Get cattle by specific IDs for filtered reports
  Future<List<CattleIsar>> getCattleByIds(List<String> cattleIds) async {
    if (cattleIds.isEmpty) return await getAllCattle();
    
    return await _isar.cattleIsars
        .filter()
        .anyOf(cattleIds, (q, businessId) => q.businessIdEqualTo(businessId))
        .findAll();
  }

  /// Get milk records for specific cattle for filtered reports
  Future<List<MilkRecordIsar>> getMilkRecordsForCattle(List<String> cattleBusinessIds) async {
    if (cattleBusinessIds.isEmpty) return await getAllMilkRecords();

    return await _isar.milkRecordIsars
        .filter()
        .anyOf(cattleBusinessIds, (q, businessId) => q.cattleBusinessIdEqualTo(businessId))
        .findAll();
  }

  /// Get health records for specific cattle for filtered reports
  Future<List<HealthRecordIsar>> getHealthRecordsForCattle(List<String> cattleBusinessIds) async {
    if (cattleBusinessIds.isEmpty) return await getAllHealthRecords();

    return await _isar.healthRecordIsars
        .filter()
        .anyOf(cattleBusinessIds, (q, businessId) => q.cattleBusinessIdEqualTo(businessId))
        .findAll();
  }
}
