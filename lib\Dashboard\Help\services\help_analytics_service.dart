import '../models/help_article_isar.dart';

/// Data class to hold all help analytics results
class HelpAnalyticsResult {
  // Basic metrics
  final int totalArticles;
  final int activeArticles;
  final int inactiveArticles;
  
  // Category distribution
  final Map<String, int> categoryDistribution;
  
  // Content metrics
  final double averageContentLength;
  final int articlesWithoutCategory;
  final int articlesWithoutTags;

  const HelpAnalyticsResult({
    required this.totalArticles,
    required this.activeArticles,
    required this.inactiveArticles,
    required this.categoryDistribution,
    required this.averageContentLength,
    required this.articlesWithoutCategory,
    required this.articlesWithoutTags,
  });

  /// Empty analytics result for when no data is available
  static const HelpAnalyticsResult empty = HelpAnalyticsResult(
    totalArticles: 0,
    activeArticles: 0,
    inactiveArticles: 0,
    categoryDistribution: {},
    averageContentLength: 0.0,
    articlesWithoutCategory: 0,
    articlesWithoutTags: 0,
  );

  /// Check if analytics result is empty
  bool get isEmpty => totalArticles == 0;

  /// Get active articles percentage
  double get activePercentage => totalArticles > 0 ? (activeArticles / totalArticles) * 100 : 0.0;

  /// Get categorized articles percentage
  double get categorizedPercentage => totalArticles > 0 ? ((totalArticles - articlesWithoutCategory) / totalArticles) * 100 : 0.0;
}

/// Pure analytics service for help calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class HelpAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static HelpAnalyticsResult calculate(List<HelpArticleIsar> articles) {
    if (articles.isEmpty) {
      return HelpAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _HelpAnalyticsAccumulator();

    // Process all articles
    for (final article in articles) {
      accumulator.processArticle(article);
    }

    return accumulator.buildResult();
  }
}

/// Efficient single-pass accumulator for all help analytics calculations
class _HelpAnalyticsAccumulator {
  // Basic counts
  int totalArticles = 0;
  int activeArticles = 0;
  int inactiveArticles = 0;

  // Category tracking
  final Map<String, int> categoryDistribution = {};
  int articlesWithoutCategory = 0;
  int articlesWithoutTags = 0;

  // Content metrics
  int totalContentLength = 0;

  /// Process a single help article
  void processArticle(HelpArticleIsar article) {
    totalArticles++;

    // Count active/inactive
    if (article.isActive) {
      activeArticles++;
    } else {
      inactiveArticles++;
    }

    // Process category
    final category = article.category?.trim();
    if (category?.isNotEmpty == true) {
      categoryDistribution[category!] = (categoryDistribution[category] ?? 0) + 1;
    } else {
      articlesWithoutCategory++;
    }

    // Process tags
    final tags = article.tags?.trim();
    if (tags?.isEmpty != false) {
      articlesWithoutTags++;
    }

    // Process content length
    final contentLength = article.content?.length ?? 0;
    totalContentLength += contentLength;
  }

  /// Build the final analytics result
  HelpAnalyticsResult buildResult() {
    final averageContentLength = totalArticles > 0 ? totalContentLength / totalArticles : 0.0;

    return HelpAnalyticsResult(
      totalArticles: totalArticles,
      activeArticles: activeArticles,
      inactiveArticles: inactiveArticles,
      categoryDistribution: Map.unmodifiable(categoryDistribution),
      averageContentLength: averageContentLength,
      articlesWithoutCategory: articlesWithoutCategory,
      articlesWithoutTags: articlesWithoutTags,
    );
  }
}
