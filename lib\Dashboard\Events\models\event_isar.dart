import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'event_enums.dart';

part 'event_isar.g.dart';

/// Main Event model for Isar database
@collection
class EventIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Reference to cattle tag ID - indexed for filtering
  @Index()
  String? cattleTagId;

  /// Reference to event type business ID - indexed for filtering
  @Index()
  String? eventTypeId;

  /// Event category - indexed for filtering
  @Index()
  @enumerated
  EventCategory category = EventCategory.other;

  /// Event title - indexed for search performance
  @Index(type: IndexType.value)
  String? title;

  /// Event description - indexed for search performance
  @Index(type: IndexType.value)
  String? description;

  /// Additional notes - indexed for search performance
  @Index(type: IndexType.value)
  String? notes;

  /// Scheduled date and time - composite index for date range queries
  @Index()
  DateTime? scheduledDate;

  // Note: Composite indexes are defined at the collection level, not as fields

  /// Actual completion date and time
  DateTime? completedDate;

  /// Current status - indexed for filtering
  @Index()
  @enumerated
  EventStatus status = EventStatus.scheduled;

  // Note: Composite indexes are defined at the collection level

  /// Quick completion status flag
  bool get isCompleted => status == EventStatus.completed;
  set isCompleted(bool value) {
    status = value ? EventStatus.completed : EventStatus.scheduled;
  }

  /// Quick missed status flag
  bool get isMissed => status == EventStatus.missed;
  set isMissed(bool value) {
    status = value ? EventStatus.missed : EventStatus.scheduled;
  }

  /// Priority level
  @enumerated
  EventPriority priority = EventPriority.medium;

  /// Automation fields
  bool? isAutoGenerated;
  String? sourceModule;
  String? sourceRecordId;

  /// Recurrence fields
  bool? isRecurring;
  @enumerated
  RecurrencePattern recurrencePattern = RecurrencePattern.daily;
  int? recurrenceInterval;
  DateTime? recurrenceEndDate;
  String? parentEventId;

  /// Notification fields
  bool? notificationsEnabled;
  List<int>? reminderMinutes; // [1440, 60] for 1 day and 1 hour

  /// Cost tracking
  double? estimatedCost;
  double? actualCost;

  /// Location and weather - indexed for search performance
  @Index(type: IndexType.value)
  String? location;
  String? weatherConditions;

  /// Completion tracking - indexed for search performance
  @Index(type: IndexType.value)
  String? completedBy;
  @Index(type: IndexType.value)
  String? completionNotes;

  /// Metadata
  DateTime? createdAt;
  DateTime? updatedAt;

  /// Default constructor
  EventIsar();

  /// Generate a business ID for events
  static String generateBusinessId() {
    return const Uuid().v4();
  }

  /// Factory constructor for creating a new event
  factory EventIsar.create({
    required String title,
    required DateTime scheduledDate,
    required String cattleTagId,
    required String eventTypeId,
    required EventCategory category,
    String? description,
    String? notes,
    EventStatus? status,
    EventPriority? priority,
    bool? isAutoGenerated,
    String? sourceModule,
    String? sourceRecordId,
    bool? isRecurring,
    RecurrencePattern? recurrencePattern,
    int? recurrenceInterval,
    DateTime? recurrenceEndDate,
    String? parentEventId,
    bool? notificationsEnabled,
    List<int>? reminderMinutes,
    double? estimatedCost,
    String? location,
  }) {
    final event = EventIsar()
      ..businessId = generateBusinessId()
      ..title = title
      ..scheduledDate = scheduledDate
      ..cattleTagId = cattleTagId
      ..eventTypeId = eventTypeId
      ..category = category
      ..description = description
      ..notes = notes
      ..status = status ?? EventStatus.scheduled
      ..priority = priority ?? EventPriority.medium
      ..isAutoGenerated = isAutoGenerated ?? false
      ..sourceModule = sourceModule
      ..sourceRecordId = sourceRecordId
      ..isRecurring = isRecurring ?? false
      ..recurrencePattern = recurrencePattern ?? RecurrencePattern.daily
      ..recurrenceInterval = recurrenceInterval
      ..recurrenceEndDate = recurrenceEndDate
      ..parentEventId = parentEventId
      ..notificationsEnabled = notificationsEnabled ?? true
      ..reminderMinutes = reminderMinutes ?? [1440, 60] // 1 day and 1 hour
      ..estimatedCost = estimatedCost
      ..location = location
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    return event;
  }

  /// Mark event as completed
  void markCompleted({
    String? completedBy,
    String? completionNotes,
    double? actualCost,
  }) {
    status = EventStatus.completed;
    completedDate = DateTime.now();
    this.completedBy = completedBy;
    this.completionNotes = completionNotes;
    this.actualCost = actualCost;
    updatedAt = DateTime.now();
  }

  /// Check if event is overdue
  bool get isOverdue {
    if (status == EventStatus.completed || status == EventStatus.cancelled) {
      return false;
    }
    if (scheduledDate == null) return false;
    return DateTime.now().isAfter(scheduledDate!);
  }

  /// Check if event is upcoming (within next 7 days)
  bool get isUpcoming {
    if (status == EventStatus.completed || status == EventStatus.cancelled) {
      return false;
    }
    if (scheduledDate == null) return false;
    final now = DateTime.now();
    final sevenDaysFromNow = now.add(const Duration(days: 7));
    return scheduledDate!.isAfter(now) && scheduledDate!.isBefore(sevenDaysFromNow);
  }

  /// Get days until scheduled date (negative if overdue)
  int? get daysUntilScheduled {
    if (scheduledDate == null) return null;
    return scheduledDate!.difference(DateTime.now()).inDays;
  }

  /// Update the event's updated timestamp
  void touch() {
    updatedAt = DateTime.now();
  }
}