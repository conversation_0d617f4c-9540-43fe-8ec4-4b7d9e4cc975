# Architecture Standards and Patterns

This document defines the standardized architecture patterns that all modules in the Cattle Manager App must follow. These patterns ensure consistency, maintainability, and scalability across the entire codebase.

## Core Principles

### 1. Pure Reactive Repositories
- **Stream-only operations** with `fireImmediately: true`
- **Logic-free and simple CRUD** operations only
- **Maximum purity**: No error handling, no logging - exceptions bubble up naturally
- **Explicit dependency injection** through constructor
- **Consistent naming conventions** across all modules

### 2. Reactive Controllers with Dual-Stream Architecture
- **Individual stream subscriptions** with proper error handling
- **Separate filtered/unfiltered data streams** for analytics accuracy
- **Analytics calculated on unfiltered data only**
- **State management** with `ControllerState` enum
- **Proper disposal** of stream subscriptions

### 3. Stateless Analytics Services
- **Pure calculation services** with no state management
- **Static methods** for all calculations
- **Single-pass O(n) efficiency** algorithms
- **Immutable result objects**
- **Accumulator pattern** for complex calculations

### 4. Dependency Injection
- **GetIt service locator** for all dependencies
- **Explicit constructor injection** for repositories and services
- **Lazy getters** in controllers to avoid constructor issues
- **Consistent service registration** in dependency_injection.dart

### 5. Consistent Error Handling
- **Exceptions bubble up naturally** from repositories
- **Service layer handles** business logic errors
- **Controller layer manages** UI error states
- **No error handling** in repository layer

## Directory Structure Standard

```
lib/Dashboard/{ModuleName}/
├── controllers/
│   ├── {module}_controller.dart          # Main reactive controller
│   └── {module}_details_controller.dart  # Detail view controller (optional)
├── details/
│   ├── {module}_details_screen.dart      # Detail screen (optional)
│   └── {module}_details_*_tab.dart       # Detail tabs (optional)
├── dialogs/
│   └── {module}_form_dialog.dart         # Form dialogs (optional)
├── models/
│   ├── {module}_isar.dart                # Main data model
│   ├── {module}_isar.g.dart              # Generated code
│   └── {module}_insights_models.dart     # Analytics models (optional)
├── screens/
│   └── {module}_screen.dart              # Main screen
├── services/
│   ├── {module}_repository.dart          # Pure reactive repository
│   ├── {module}_analytics_service.dart   # Pure analytics calculations
│   ├── {module}_insights_service.dart    # Specialized insights (optional)
│   ├── {module}_sync_service.dart        # Bidirectional sync (optional)
│   └── {module}_event_integration.dart   # Cross-module events (optional)
├── tabs/
│   ├── {module}_records_tab.dart         # Records display (optional)
│   ├── {module}_analytics_tab.dart       # Analytics display (optional)
│   └── {module}_insights_tab.dart        # Insights display (optional)
└── widgets/
    └── {module}_*.dart                   # Module-specific widgets (optional)
```

## Repository Pattern

### Base Repository Interface
All repositories must extend `BaseRepository<T>` and implement:

```dart
class {Module}Repository extends BaseRepository<{Model}Isar> {
  {Module}Repository(IsarService isarService) : super(isarService);

  // Required implementations
  @override
  Stream<List<{Model}Isar>> watchAll() { ... }
  
  @override
  Future<void> save({Model}Isar record) { ... }
  
  @override
  Future<void> delete(int id) { ... }
  
  @override
  Future<List<{Model}Isar>> getAll() { ... }
}
```

### Repository Rules
- ✅ **DO**: Use pure reactive streams with `fireImmediately: true`
- ✅ **DO**: Implement simple CRUD operations only
- ✅ **DO**: Use Isar's native upsert with `put()`
- ✅ **DO**: Let exceptions bubble up naturally
- ❌ **DON'T**: Add error handling or logging
- ❌ **DON'T**: Include business logic
- ❌ **DON'T**: Extend `ChangeNotifier`

## Controller Pattern

### Base Controller Interface
All controllers must extend `BaseController` or `DualStreamController<TData, TFilter>`:

```dart
class {Module}Controller extends DualStreamController<{Model}Isar, {Module}FilterState> {
  // Lazy getters for dependencies
  {Module}Repository get _repository => GetIt.instance<{Module}Repository>();
  
  // Stream subscriptions
  StreamSubscription<List<{Model}Isar>>? _unfilteredStreamSubscription;
  
  // Analytics result
  {Module}AnalyticsResult _analyticsResult = {Module}AnalyticsResult.empty;
}
```

### Controller Rules
- ✅ **DO**: Use dual-stream architecture (unfiltered/filtered)
- ✅ **DO**: Calculate analytics on unfiltered data only
- ✅ **DO**: Implement proper state management
- ✅ **DO**: Dispose stream subscriptions properly
- ✅ **DO**: Use lazy getters for GetIt dependencies
- ❌ **DON'T**: Access GetIt services in constructor
- ❌ **DON'T**: Mix filtered and unfiltered data for analytics

## Analytics Service Pattern

### Base Analytics Service
All analytics services must extend `BaseAnalyticsService`:

```dart
class {Module}AnalyticsService extends BaseAnalyticsService {
  // Private constructor
  {Module}AnalyticsService._();
  
  // Static calculation methods
  static {Module}AnalyticsResult calculate(List<{Model}Isar> records) {
    // Single-pass O(n) calculation
    final accumulator = _{Module}AnalyticsAccumulator();
    for (final record in records) {
      accumulator.process(record);
    }
    return accumulator.toResult();
  }
}
```

### Analytics Service Rules
- ✅ **DO**: Use static methods only
- ✅ **DO**: Implement single-pass O(n) algorithms
- ✅ **DO**: Use accumulator pattern for complex calculations
- ✅ **DO**: Return immutable result objects
- ❌ **DON'T**: Store state in analytics services
- ❌ **DON'T**: Use instance methods
- ❌ **DON'T**: Include business logic beyond calculations

## Service Layer Pattern

### Business Logic Services
Business logic services should extend `BaseService`:

```dart
class {Module}Service extends BaseService {
  final {Module}Repository _repository;
  
  {Module}Service(this._repository) : super('{Module}Service');
  
  Future<ServiceResult<bool>> performBusinessOperation() async {
    return await executeWithErrorHandling(
      'business operation',
      () async {
        // Business logic here
        return true;
      },
    );
  }
}
```

### Service Rules
- ✅ **DO**: Handle business logic and validation
- ✅ **DO**: Use dependency injection for repositories
- ✅ **DO**: Implement proper error handling
- ✅ **DO**: Use consistent logging patterns
- ❌ **DON'T**: Access database directly
- ❌ **DON'T**: Mix concerns (analytics, sync, validation)

## Reference Implementations

The following modules serve as reference implementations:

1. **Cattle Module** (`lib/Dashboard/Cattle/`) - Complete dual-stream pattern
2. **Weight Module** (`lib/Dashboard/Weight/`) - Individual stream pattern
3. **Breeding Module** (`lib/Dashboard/Breeding/`) - Standard pattern
4. **Reports Module** (`lib/Dashboard/Reports/`) - Cross-module analytics
5. **Transactions Module** (`lib/Dashboard/Transactions/`) - Financial pattern

## Compliance Checking

Use the `ArchitectureComplianceChecker` to validate module compliance:

```dart
final checker = ArchitectureComplianceChecker();
final report = await checker.checkModuleCompliance('lib/Dashboard/YourModule');
print(report);
```

## Migration Guide

When refactoring existing modules:

1. **Extract business logic** from repositories to services
2. **Implement dual-stream architecture** in controllers
3. **Create dedicated analytics services** with static methods
4. **Remove error handling** from repository layer
5. **Add proper stream disposal** in controllers
6. **Update dependency injection** patterns

## Templates

Use the provided templates in `lib/core/templates/` when creating new modules:

- `repository_template.dart` - Repository implementation
- `controller_template.dart` - Controller implementation  
- `analytics_service_template.dart` - Analytics service implementation

These templates ensure consistency and compliance with established patterns.
