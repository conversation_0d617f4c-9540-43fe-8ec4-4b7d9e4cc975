# Event Notification System

This document describes the event notification and reminder system implemented for the Events module.

## Overview

The event notification system provides automated reminders and notifications for cattle management events. It consists of three main components:

1. **EventNotificationService** - Core service for managing notifications
2. **EventNotificationManager** - Higher-level coordinator for event operations with notification management
3. **Integration with existing NotificationsRepository** - Uses the existing notification infrastructure

## Components

### EventNotificationService

Located at `lib/Dashboard/Events/services/event_notification_service.dart`

**Key Features:**
- Schedule event reminders based on event priority
- Send overdue event notifications
- Send upcoming event notifications
- Cancel notifications when events are completed
- Respect user notification preferences
- Background processing for automated notifications

**Priority-based Reminders:**
- **Critical**: 1 week, 1 day, 1 hour before
- **High**: 1 day, 1 hour before
- **Medium**: 1 day before
- **Low**: 1 day before

**Methods:**
- `scheduleEventReminders(EventIsar event)` - Schedule reminders for an event
- `cancelEventReminders(String eventBusinessId)` - Cancel all reminders for an event
- `sendOverdueNotifications()` - Process and send overdue notifications
- `sendUpcomingEventNotifications()` - Process and send upcoming event notifications
- `initialize()` - Start background processing
- `dispose()` - Clean up resources

### EventNotificationManager

Located at `lib/Dashboard/Events/services/event_notification_manager.dart`

**Purpose:**
Coordinates event operations with notification management to avoid circular dependencies between EventsRepository and EventNotificationService.

**Key Methods:**
- `saveEventWithNotifications(EventIsar event)` - Save event and manage notifications
- `deleteEventWithNotifications(String businessId)` - Delete event and cancel notifications
- `completeEventWithNotifications(EventIsar event, ...)` - Complete event and cancel notifications
- `rescheduleEventWithNotifications(EventIsar event, DateTime newDate)` - Reschedule event and update notifications
- `processBackgroundNotifications()` - Process background notifications

### Integration with Existing Systems

The notification system integrates with:

1. **NotificationsRepository** - For storing and managing notifications
2. **NotificationSettingsIsar** - For user notification preferences
3. **EventsRepository** - For event data access (through EventNotificationManager)

## Notification Types

The system creates three types of notifications:

1. **event_reminder** - Scheduled reminders before events
2. **event_overdue** - Notifications for overdue events
3. **event_upcoming** - Notifications for events due within 24 hours

## Usage Examples

### Basic Usage

```dart
// Create service
final notificationService = EventNotificationService();

// Initialize background processing
notificationService.initialize();

// Schedule reminders for an event
await notificationService.scheduleEventReminders(event);

// Cancel reminders
await notificationService.cancelEventReminders(eventBusinessId);

// Clean up
notificationService.dispose();
```

### Using EventNotificationManager

```dart
// Create manager
final manager = EventNotificationManager();

// Save event with notification management
await manager.saveEventWithNotifications(event);

// Complete event and cancel notifications
await manager.completeEventWithNotifications(
  event,
  completedBy: 'John Doe',
  completionNotes: 'Completed successfully',
);

// Process background notifications
await manager.processBackgroundNotifications();
```

## Configuration

Notifications can be enabled/disabled through `NotificationSettingsIsar`:

```dart
final settings = NotificationSettingsIsar()
  ..eventsNotificationsEnabled = true; // Enable event notifications

await notificationsRepository.saveNotificationSettings(settings);
```

## Background Processing

The service includes a background timer that runs every 15 minutes to:
- Check for overdue events and send notifications
- Check for upcoming events and send notifications
- Update event statuses (scheduled → overdue)

## Error Handling

The system includes comprehensive error handling:
- Graceful handling of missing notification settings
- Logging of errors without failing operations
- Continuation of core functionality even if notifications fail
- Proper resource cleanup

## Testing

Due to the complexity of mocking Isar database operations, the notification system includes:
- Basic functionality tests that verify the service can be created and methods don't throw exceptions
- Integration with the existing notification infrastructure
- Error handling verification

## Future Enhancements

Potential improvements for the notification system:
1. Push notifications for mobile devices
2. Email notifications for critical events
3. SMS notifications for urgent events
4. Customizable notification templates
5. Notification scheduling based on user timezone
6. Batch notification processing for better performance

## Dependencies

The notification system depends on:
- `NotificationsRepository` - For notification storage
- `IsarService` - For database access
- `GetIt` - For dependency injection
- `logging` package - For error logging
- `uuid` package - For generating notification IDs

## Integration Points

To integrate the notification system:

1. Register services in dependency injection:
```dart
GetIt.instance.registerSingleton<EventNotificationService>(
  EventNotificationService()
);
```

2. Use EventNotificationManager instead of direct EventsRepository calls:
```dart
// Instead of:
await eventsRepository.saveEvent(event);

// Use:
await eventNotificationManager.saveEventWithNotifications(event);
```

3. Initialize background processing in app startup:
```dart
final notificationService = GetIt.instance<EventNotificationService>();
notificationService.initialize();
```

4. Clean up on app shutdown:
```dart
final notificationService = GetIt.instance<EventNotificationService>();
notificationService.dispose();
```