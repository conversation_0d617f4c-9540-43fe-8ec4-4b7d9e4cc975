import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import '../../../services/logging_service.dart';
import '../../../config/api_config.dart';
import 'package:get_it/get_it.dart';
import '../models/transaction_isar.dart';
import 'transactions_repository.dart';

/// Bidirectional sync service for Transactions module
/// Handles synchronization between local Isar database and external API
class TransactionsSyncService {
  final TransactionsRepository _transactionsRepository = GetIt.instance<TransactionsRepository>();
  
  // Sync-related constants
  static const String _lastSyncKey = 'last_transactions_sync';
  final LoggingService _logger = LoggingService();

  /// Get last sync time for incremental sync
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSync = prefs.getString(_lastSyncKey);
    return lastSync != null ? DateTime.parse(lastSync) : null;
  }

  /// Set last sync time
  Future<void> setLastSyncTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, time.toIso8601String());
  }

  /// Get all transactions for sync
  Future<List<TransactionIsar>> getAllTransactions() async {
    final isar = GetIt.instance<Isar>();
    return await isar.transactionIsars.where().findAll();
  }

  /// Get transactions modified since last sync
  Future<List<TransactionIsar>> getModifiedTransactionsSince(DateTime? lastSync) async {
    if (lastSync == null) {
      return await getAllTransactions();
    }
    
    final isar = GetIt.instance<Isar>();
    return await isar.transactionIsars
        .filter()
        .updatedAtGreaterThan(lastSync)
        .findAll();
  }

  /// Convert transaction to sync-friendly map
  Map<String, dynamic> _transactionToSyncMap(TransactionIsar transaction) {
    return {
      'id': transaction.transactionId,
      'title': transaction.title,
      'amount': transaction.amount,
      'description': transaction.description,
      'category': transaction.category,
      'categoryType': transaction.categoryType,
      'paymentMethod': transaction.paymentMethod,
      'date': transaction.date.toIso8601String(),
      'icon': transaction.iconCodePoint,
      'iconFontFamily': transaction.iconFontFamily,
      'createdAt': transaction.createdAt.toIso8601String(),
      'updatedAt': transaction.updatedAt.toIso8601String(),
    };
  }

  /// Convert sync map to transaction
  TransactionIsar _transactionFromSyncMap(Map<String, dynamic> map) {
    final transaction = TransactionIsar()
      ..transactionId = map['id'] as String
      ..title = map['title'] as String
      ..amount = (map['amount'] as num).toDouble()
      ..description = map['description'] as String
      ..category = map['category'] as String
      ..categoryType = map['categoryType'] as String
      ..paymentMethod = map['paymentMethod'] as String? ?? 'Cash'
      ..date = DateTime.parse(map['date'] as String)
      ..iconCodePoint = map['icon'] as int?
      ..iconFontFamily = map['iconFontFamily'] as String? ?? 'MaterialIcons'
      ..createdAt = DateTime.parse(map['createdAt'] as String)
      ..updatedAt = DateTime.parse(map['updatedAt'] as String);

    return transaction;
  }

  /// Bidirectional sync with external API
  Future<bool> syncData() async {
    try {
      // Check if API sync is enabled
      if (!ApiConfig.isApiSyncAvailable) {
        _logger.info('Transactions sync skipped - API sync disabled (local-only mode)');
        return true; // Return success for local-only mode
      }

      final lastSync = await getLastSyncTime();
      final localRecords = await getModifiedTransactionsSince(lastSync);

      // Prepare data for API
      final syncData = {
        'lastSync': lastSync?.toIso8601String(),
        'records': localRecords.map((r) => _transactionToSyncMap(r)).toList(),
      };

      final response = await http.post(
        Uri.parse(ApiConfig.transactionsSync),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode(syncData),
      ).timeout(ApiConfig.syncTimeout);

      if (response.statusCode == 200) {
        if (!response.body.startsWith('{')) {
          _logger.error(
              'Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          return false;
        }

        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (!responseData.containsKey('records')) {
          _logger.error('Invalid response format: Missing records field');
          return false;
        }

        // Process server records and handle conflicts
        final List<dynamic> serverRecords = responseData['records'];
        await _processServerTransactions(serverRecords);

        await setLastSyncTime(DateTime.now());
        _logger.info('Transactions synchronized successfully');
        return true;
      } else {
        _logger.error('Failed to sync transactions: $response.statusCode');
        return false;
      }
    } catch (e) {
      _logger.error('Error syncing transactions: $e');
      return false;
    }
  }

  /// Process server records and handle conflicts
  Future<void> _processServerTransactions(List<dynamic> serverRecords) async {
    for (final recordData in serverRecords) {
      try {
        final serverRecord = _transactionFromSyncMap(recordData);
        final isar = GetIt.instance<Isar>();
        final existingRecord = await isar.transactionIsars
            .filter()
            .transactionIdEqualTo(serverRecord.transactionId)
            .findFirst();

        if (existingRecord == null) {
          // New record from server
          await _transactionsRepository.saveTransaction(serverRecord);
        } else {
          // Handle conflict resolution - server wins for now
          final resolvedRecord = _resolveTransactionConflict(existingRecord, serverRecord);
          await _transactionsRepository.saveTransaction(resolvedRecord);
        }
      } catch (e) {
        _logger.error('Error processing server transaction: $e');
      }
    }
  }

  /// Simple conflict resolution - server wins
  /// In a more sophisticated implementation, this could use timestamps,
  /// user preferences, or merge strategies
  TransactionIsar _resolveTransactionConflict(TransactionIsar local, TransactionIsar server) {
    // For now, server record wins in conflicts
    // Keep the local Isar ID but use server data
    server.id = local.id;
    return server;
  }
}
