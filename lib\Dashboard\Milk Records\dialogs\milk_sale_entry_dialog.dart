import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/milk_sale_isar.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

// --- Constants ---
class _AppStrings {
  static const String addSaleTitle = 'Record Milk Sale';
  static const String editSaleTitle = 'Edit Milk Sale';
  static const String dateLabel = 'Sale Date';
  static const String homeUsageLabel = 'Home Usage (L)';
  static const String calfUsageLabel = 'Calf Usage (L)';
  static const String paymentStatusLabel = 'Payment Status';
  static const String notesLabel = 'Notes';

  // Validation messages
  static const String dateRequired = 'Sale date is required';
  static const String usageExceedsAvailable = 'Total usage cannot exceed available milk';
}

class MilkSaleEntryDialog extends StatefulWidget {
  final DateTime selectedDate;
  final double availableMilk;
  final MilkSaleIsar? existingSale;
  final Function(MilkSaleIsar)? onSave;

  const MilkSaleEntryDialog({
    super.key,
    required this.selectedDate,
    required this.availableMilk,
    this.existingSale,
    this.onSave,
  });

  @override
  State<MilkSaleEntryDialog> createState() => _MilkSaleEntryDialogState();
}

class _MilkSaleEntryDialogState extends State<MilkSaleEntryDialog> {
  final _formKey = GlobalKey<FormState>();

  final _farmSetupRepository = GetIt.instance<FarmSetupRepository>();

  // Form controllers - only for user input fields
  final _homeUsageController = TextEditingController();
  final _calfUsageController = TextEditingController();
  final _notesController = TextEditingController();

  // State variables
  bool _isPaid = true;
  bool _isSaving = false;
  bool _showOptionalFields = false;
  DateTime _selectedDate = DateTime.now();
  double _availableMilk = 0.0;
  double _homeUsage = 0.0;
  double _calfUsage = 0.0;
  double _quantitySold = 0.0;
  double _ratePerLiter = 0.0;
  double _totalAmount = 0.0;

  // Currency settings
  String _currencySymbol = '';
  bool _symbolBeforeAmount = true;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    _availableMilk = widget.availableMilk;

    // Load farm settings and initialize form
    _loadFarmSettings();

    // Add listeners for real-time calculation
    _homeUsageController.addListener(_calculateQuantitySold);
    _calfUsageController.addListener(_calculateQuantitySold);

    // If editing existing sale, populate form
    if (widget.existingSale != null) {
      _homeUsageController.text = widget.existingSale!.homeUsage?.toString() ?? '0';
      _calfUsageController.text = widget.existingSale!.calfUsage?.toString() ?? '0';
      _notesController.text = widget.existingSale!.notes ?? '';
      _isPaid = widget.existingSale!.isPaid ?? true;
      _selectedDate = widget.existingSale!.date ?? widget.selectedDate;
      _ratePerLiter = widget.existingSale!.ratePerLiter ?? 0.0;
    }

    // Initial calculation
    _calculateQuantitySold();
  }

  @override
  void dispose() {
    _homeUsageController.dispose();
    _calfUsageController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// Load farm settings including milk rate and currency
  Future<void> _loadFarmSettings() async {
    try {
      // Load milk settings for rate
      final milkSettings = await _farmSetupRepository.getMilkSettings();

      // Load currency settings
      final currencySettings = await _farmSetupRepository.getCurrencySettings();

      if (mounted) {
        setState(() {
          _ratePerLiter = milkSettings.regularRate ?? 1.0;
          _currencySymbol = currencySettings?.currencySymbol ?? '\$';
          _symbolBeforeAmount = currencySettings?.symbolBeforeAmount ?? true;
        });

        // Recalculate after loading settings
        _calculateQuantitySold();
      }
    } catch (e) {
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _ratePerLiter = 1.0;
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });

        // Recalculate with defaults
        _calculateQuantitySold();
      }
    }
  }

  /// Calculate quantity sold based on available milk minus usage
  void _calculateQuantitySold() {
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0.0;
    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0.0;

    setState(() {
      _homeUsage = homeUsage;
      _calfUsage = calfUsage;
      _quantitySold = _availableMilk - homeUsage - calfUsage;

      // Ensure quantity sold is not negative
      if (_quantitySold < 0) {
        _quantitySold = 0;
      }

      // Calculate total amount
      _totalAmount = _quantitySold * _ratePerLiter;
    });
  }

  String _formatCurrency(double amount) {
    final formattedAmount = amount.toStringAsFixed(2);
    return _symbolBeforeAmount
        ? '$_currencySymbol$formattedAmount'
        : '$formattedAmount$_currencySymbol';
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate usage doesn't exceed available milk
    if (_homeUsage + _calfUsage > _availableMilk) {
      MilkMessageUtils.showError(context, _AppStrings.usageExceedsAvailable);
      return;
    }

    setState(() => _isSaving = true);

    try {
      // Create or update milk sale record
      final MilkSaleIsar milkSale;

      if (widget.existingSale != null) {
        // Update existing sale
        milkSale = MilkSaleIsar()
          ..id = widget.existingSale!.id
          ..businessId = widget.existingSale!.businessId
          ..date = _selectedDate
          ..quantity = _quantitySold
          ..ratePerLiter = _ratePerLiter
          ..totalAmount = _totalAmount
          ..calfUsage = _calfUsage > 0 ? _calfUsage : null
          ..homeUsage = _homeUsage > 0 ? _homeUsage : null
          ..isPaid = _isPaid
          ..notes = _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null
          ..createdAt = widget.existingSale!.createdAt
          ..updatedAt = DateTime.now();
      } else {
        // Create new sale
        milkSale = MilkSaleIsar()
          ..date = _selectedDate
          ..quantity = _quantitySold
          ..ratePerLiter = _ratePerLiter
          ..totalAmount = _totalAmount
          ..calfUsage = _calfUsage > 0 ? _calfUsage : null
          ..homeUsage = _homeUsage > 0 ? _homeUsage : null
          ..isPaid = _isPaid
          ..notes = _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
      }

      // Call the save callback if provided
      if (widget.onSave != null) {
        widget.onSave!(milkSale);
      }

      // Show success message and close dialog
      if (mounted) {
        MilkMessageUtils.showSuccess(context,
          widget.existingSale == null
            ? 'Milk sale recorded successfully'
            : 'Milk sale updated successfully');
        Navigator.of(context).pop(milkSale);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isSaving = false);
        MilkMessageUtils.showError(context,
            'Failed to save milk sale: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return UniversalFormDialog(
      title: widget.existingSale == null ? _AppStrings.addSaleTitle : _AppStrings.editSaleTitle,
      headerIcon: Icons.monetization_on, // Sale-specific icon
      formContent: _buildFormContent(),
      actionButtons: widget.existingSale == null
          ? UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
              addText: 'Save', // User preference: Use 'Save' instead of 'Add'
              isAdding: _isSaving,
            )
          : UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
              updateText: 'Save', // User preference: Use 'Save' instead of 'Update'
              isUpdating: _isSaving,
            ),
    );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Sale Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.dateLabel,
            value: _selectedDate,
            onChanged: (date) {
              setState(() {
                _selectedDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            validator: (date) {
              if (date == null) {
                return _AppStrings.dateRequired;
              }
              return null;
            },
            lastDate: DateTime.now().add(const Duration(days: 1)),
          ),
          UniversalFormField.spacing,

          // Available Milk Info Card
          _buildAvailableMilkCard(),
          UniversalFormField.spacing,

          // Home Usage Field
          UniversalFormField.numberField(
            label: _AppStrings.homeUsageLabel,
            controller: _homeUsageController,
            allowDecimals: true,
            prefixIcon: Icons.home,
            prefixIconColor: Colors.green,
            validator: (value) {
              final usage = double.tryParse(value ?? '') ?? 0.0;
              if (usage < 0) return 'Usage cannot be negative';
              return null;
            },
          ),
          UniversalFormField.spacing,

          // Calf Usage Field
          UniversalFormField.numberField(
            label: _AppStrings.calfUsageLabel,
            controller: _calfUsageController,
            allowDecimals: true,
            prefixIcon: Icons.pets,
            prefixIconColor: Colors.orange,
            validator: (value) {
              final usage = double.tryParse(value ?? '') ?? 0.0;
              if (usage < 0) return 'Usage cannot be negative';
              return null;
            },
          ),
          UniversalFormField.spacing,

          // Calculated Fields Section
          _buildCalculatedFieldsCard(),
          UniversalFormField.spacing,

          // Optional Information Toggle Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showOptionalFields = !_showOptionalFields;
                });
              },
              icon: Icon(
                _showOptionalFields
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF2E7D32),
              ),
              label: Text(
                _showOptionalFields
                    ? 'Hide Optional Information'
                    : 'Show Optional Information',
                style: const TextStyle(
                  color: Color(0xFF2E7D32),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: Color(0xFF2E7D32),
                  width: 1.5,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Optional Fields - Conditionally Displayed
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Payment Status Toggle
            UniversalFormField.fieldWithToggle(
              field: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    const Icon(Icons.payment, color: Colors.indigo),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        _AppStrings.paymentStatusLabel,
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                    ),
                    Text(
                      _isPaid ? 'Paid' : 'Pending',
                      style: TextStyle(
                        color: _isPaid ? Colors.green : Colors.red, // Changed from orange (forbidden)
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              toggleLabel: _isPaid ? 'Paid' : 'Pending',
              toggleValue: _isPaid,
              onToggleChanged: (value) {
                setState(() {
                  _isPaid = value;
                });
              },
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.notes,
              prefixIconColor: Colors.cyan,
            ),
          ],
        ],
      ),
    );
  }

  /// Build available milk information card
  Widget _buildAvailableMilkCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue.shade700),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Available Milk: ${_availableMilk.toStringAsFixed(1)} L',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.blue.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build calculated fields display card
  Widget _buildCalculatedFieldsCard() {
    final hasError = _homeUsage + _calfUsage > _availableMilk;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: hasError ? Colors.red.shade50 : Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: hasError ? Colors.red.shade200 : Colors.green.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                hasError ? Icons.error_outline : Icons.calculate,
                color: hasError ? Colors.red.shade700 : Colors.green.shade700,
              ),
              const SizedBox(width: 8),
              Text(
                'Calculation Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: hasError ? Colors.red.shade700 : Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildCalculationRow('Quantity Sold', '${_quantitySold.toStringAsFixed(1)} L'),
          _buildCalculationRow('Rate per Liter', _formatCurrency(_ratePerLiter)),
          _buildCalculationRow('Total Amount', _formatCurrency(_totalAmount)),
          if (hasError) ...[
            const SizedBox(height: 8),
            Text(
              'Error: Total usage exceeds available milk',
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build calculation row
  Widget _buildCalculationRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }


}
