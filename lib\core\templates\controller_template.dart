// This file contains template code for creating new controllers
// Copy and modify this template when creating new module controllers

/*
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../models/{module}_isar.dart';
import '../services/{module}_repository.dart';
import '../services/{module}_analytics_service.dart';
import '../../core/base/base_controller.dart';

/// Filter state for {Module} module
class {Module}FilterState {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String>? cattleIds;
  final String? status;
  // Add other filter properties as needed

  const {Module}FilterState({
    this.startDate,
    this.endDate,
    this.cattleIds,
    this.status,
  });

  /// Empty filter state
  static const {Module}FilterState empty = {Module}FilterState();

  /// Check if filter has any active filters
  bool get hasActiveFilters {
    return startDate != null ||
           endDate != null ||
           (cattleIds?.isNotEmpty ?? false) ||
           status != null;
  }
}

/// Reactive controller for the main {module} screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class {Module}Controller extends DualStreamController<{Model}Isar, {Module}FilterState> {
  // Use lazy getters to avoid accessing GetIt services in constructor
  {Module}Repository get _repository => GetIt.instance<{Module}Repository>();
  Isar get _isar => GetIt.instance<Isar>();

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription<List<{Model}Isar>>? _unfilteredStreamSubscription;

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  {Module}AnalyticsResult _analyticsResult = {Module}AnalyticsResult.empty;

  //=== GETTERS ===//

  /// Analytics result (calculated on unfiltered data)
  {Module}AnalyticsResult get analytics => _analyticsResult;

  //=== CONSTRUCTOR ===//

  {Module}Controller() {
    _initializeStreamListeners();
  }

  //=== INITIALIZATION ===//

  /// Initialize and load data
  @override
  Future<void> loadData() async {
    try {
      setState(ControllerState.loading);
      // Data comes from Isar watch() streams automatically
      // State management is handled by stream listeners
    } catch (e, stackTrace) {
      debugPrint('Error loading {module} data: $e\n$stackTrace');
      setError('Failed to load {module} data: ${e.toString()}');
    }
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    _unfilteredStreamSubscription = _repository.watchAll()
        .listen((unfilteredList) {
      updateUnfilteredData(unfilteredList);
    });

    // Add the subscription for proper disposal
    addSubscription(_unfilteredStreamSubscription!);
  }

  //=== ANALYTICS ===//

  /// Update analytics - ALWAYS calculated on unfiltered data
  @override
  void _updateAnalytics() {
    _analyticsResult = {Module}AnalyticsService.calculate(unfilteredData);
  }

  //=== FILTERING ===//

  /// Apply current filters to unfiltered data
  @override
  void _applyCurrentFilters() {
    if (currentFilter == null || !currentFilter!.hasActiveFilters) {
      // No filters applied - show all data
      updateFilteredData(unfilteredData);
      return;
    }

    // Apply filters to unfiltered data
    final filtered = unfilteredData.where((record) {
      // Date range filter
      if (currentFilter!.startDate != null && record.date != null) {
        if (record.date!.isBefore(currentFilter!.startDate!)) return false;
      }
      if (currentFilter!.endDate != null && record.date != null) {
        if (record.date!.isAfter(currentFilter!.endDate!)) return false;
      }

      // Cattle filter
      if (currentFilter!.cattleIds?.isNotEmpty ?? false) {
        if (!currentFilter!.cattleIds!.contains(record.cattleBusinessId)) return false;
      }

      // Status filter
      if (currentFilter!.status != null) {
        if (record.status != currentFilter!.status) return false;
      }

      return true;
    }).toList();

    updateFilteredData(filtered);
  }

  /// Apply filters to {module} data - triggers database-level filtering
  @override
  Future<void> applyFilters({Module}FilterState filter) async {
    try {
      setState(ControllerState.loading);
      updateFilterState(filter);
      _applyCurrentFilters();
    } catch (e) {
      setError('Failed to apply filters: ${e.toString()}');
    }
  }

  /// Clear all filters
  @override
  Future<void> clearFilters() async {
    await applyFilters({Module}FilterState.empty);
  }

  /// Check if filter has active filters
  @override
  bool hasFilters({Module}FilterState filter) {
    return filter.hasActiveFilters;
  }

  //=== DATA OPERATIONS ===//

  /// Add a new {module} record
  Future<void> add{Model}({Model}Isar record) async {
    try {
      await _repository.save(record);
      // Success - the stream will automatically update the UI
    } catch (e) {
      setError('Failed to add {module} record: ${e.toString()}');
      rethrow;
    }
  }

  /// Update an existing {module} record
  Future<void> update{Model}({Model}Isar record) async {
    try {
      await _repository.save(record);
      // Success - the stream will automatically update the UI
    } catch (e) {
      setError('Failed to update {module} record: ${e.toString()}');
      rethrow;
    }
  }

  /// Delete a {module} record
  Future<void> delete{Model}(int recordId) async {
    try {
      await _repository.delete(recordId);
      // Success - the stream will automatically update the UI
    } catch (e) {
      setError('Failed to delete {module} record: ${e.toString()}');
      rethrow;
    }
  }

  //=== REFRESH ===//

  /// Refresh all data
  @override
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      _updateAnalytics();
      _applyCurrentFilters();
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing {module} data: $e\n$stackTrace');
      throw Exception('Failed to refresh {module} data: ${e.toString()}');
    }
  }
}

/// Analytics result for {Module} module - immutable data class
class {Module}AnalyticsResult {
  final int totalRecords;
  final int activeRecords;
  final double averageValue;
  // Add other analytics properties as needed

  const {Module}AnalyticsResult({
    required this.totalRecords,
    required this.activeRecords,
    required this.averageValue,
  });

  static const {Module}AnalyticsResult empty = {Module}AnalyticsResult(
    totalRecords: 0,
    activeRecords: 0,
    averageValue: 0.0,
  );
}
*/
