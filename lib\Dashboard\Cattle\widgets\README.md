# Cattle Manager App Reusable Widgets

This directory contains reusable widgets that are used across the Cattle Manager application, particularly in the breeding, pregnancy, and delivery views.

## Available Widgets

### Basic Components

- **InfoRow** (`info_row.dart`): A row with an icon, label, and value. Can be styled as a status, highlighted, or multiline.
- **StatItem** (`stat_item.dart`): A widget for displaying a statistic with an icon, label, and value.

### Card Components

- **StatusCard** (`status_card.dart`): A card showing the current status of a cattle (pregnant/not pregnant, due date, etc.)
- **StatsCard** (`stats_card.dart`): A card showing multiple statistics with an optional success rate progress bar.
- **EligibilityCard** (`eligibility_card.dart`): A card showing breeding eligibility status and requirements.
- **MilestoneCard** (`milestone_card.dart`): A card showing pregnancy milestones and progress.

### Note on Record Cards

For displaying individual records (breeding, pregnancy, delivery), use the **UniversalRecordCard** from `lib/Dashboard/widgets/universal_record_card.dart` instead of the legacy history cards.

## Usage Examples

### Using InfoRow

```dart
InfoRow(
  icon: Icons.calendar_today,
  label: 'Due Date',
  value: DateFormat('MMM dd, yyyy').format(dueDate),
  color: Colors.purple,
  isHighlighted: true,
)
```

### Using StatusCard

```dart
StatusCard(
  isPregnant: cattle.isPregnant!,
  dueDate: cattle.expectedCalvingDate,
  startDate: cattle.lastBreedingDate,
)
```

### Using StatsCard

```dart
final statItems = [
  {
    'label': 'Total Calves',
    'value': totalCalves.toString(),
    'icon': Icons.child_care,
    'color': Colors.blue,
  },
  {
    'label': 'Male',
    'value': maleCalves.toString(),
    'icon': Icons.male,
    'color': Colors.indigo,
  },
  // Add more items as needed
];

StatsCard(
  title: 'Calf Statistics',
  statItems: statItems,
  successRate: successfulDeliveries / totalDeliveries,
  successRateLabel: 'Success Rate',
)
```

### Using EligibilityCard

```dart
EligibilityCard.breeding(
  gender: cattle.gender.name,
  cattleId: cattle.businessId ?? '',
  animalTypeId: cattle.animalTypeId ?? '',
  isPregnant: controller.isCurrentlyPregnant,
  dateOfBirth: cattle.dateOfBirth,
  onAddPressed: () => _addBreedingRecord(context, controller),
)
```

## Benefits of Using These Widgets

- **Consistency**: Ensures a consistent look and feel across the app.
- **Maintainability**: Makes it easier to update the UI in one place.
- **Reusability**: Reduces code duplication.
- **Flexibility**: Most widgets are customizable via parameters. 