// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_settings_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetNotificationSettingsIsarCollection on Isar {
  IsarCollection<NotificationSettingsIsar> get notificationSettingsIsars =>
      this.collection();
}

const NotificationSettingsIsarSchema = CollectionSchema(
  name: r'NotificationSettingsIsar',
  id: 7435365505411647902,
  properties: {
    r'autoDeleteAfterDays': PropertySchema(
      id: 0,
      name: r'autoDeleteAfterDays',
      type: IsarType.long,
    ),
    r'autoDeleteReadAfterDays': PropertySchema(
      id: 1,
      name: r'autoDeleteReadAfterDays',
      type: IsarType.long,
    ),
    r'autoDeleteReadNotifications': PropertySchema(
      id: 2,
      name: r'autoDeleteReadNotifications',
      type: IsarType.bool,
    ),
    r'businessId': PropertySchema(
      id: 3,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'categoryEnabledJson': PropertySchema(
      id: 4,
      name: r'categoryEnabledJson',
      type: IsarType.string,
    ),
    r'categoryPrioritiesJson': PropertySchema(
      id: 5,
      name: r'categoryPrioritiesJson',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 6,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'digestFrequency': PropertySchema(
      id: 7,
      name: r'digestFrequency',
      type: IsarType.string,
    ),
    r'digestTime': PropertySchema(
      id: 8,
      name: r'digestTime',
      type: IsarType.long,
    ),
    r'emailNotificationsEnabled': PropertySchema(
      id: 9,
      name: r'emailNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'emergencyKeywordsJson': PropertySchema(
      id: 10,
      name: r'emergencyKeywordsJson',
      type: IsarType.string,
    ),
    r'emergencyOverrideEnabled': PropertySchema(
      id: 11,
      name: r'emergencyOverrideEnabled',
      type: IsarType.bool,
    ),
    r'fcmToken': PropertySchema(
      id: 12,
      name: r'fcmToken',
      type: IsarType.string,
    ),
    r'fcmTokenUpdatedAt': PropertySchema(
      id: 13,
      name: r'fcmTokenUpdatedAt',
      type: IsarType.dateTime,
    ),
    r'inAppNotificationsEnabled': PropertySchema(
      id: 14,
      name: r'inAppNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'maxNotificationsToKeep': PropertySchema(
      id: 15,
      name: r'maxNotificationsToKeep',
      type: IsarType.long,
    ),
    r'notificationsEnabled': PropertySchema(
      id: 16,
      name: r'notificationsEnabled',
      type: IsarType.bool,
    ),
    r'pushNotificationsEnabled': PropertySchema(
      id: 17,
      name: r'pushNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'quietHoursEnabled': PropertySchema(
      id: 18,
      name: r'quietHoursEnabled',
      type: IsarType.bool,
    ),
    r'quietHoursEnd': PropertySchema(
      id: 19,
      name: r'quietHoursEnd',
      type: IsarType.long,
    ),
    r'quietHoursStart': PropertySchema(
      id: 20,
      name: r'quietHoursStart',
      type: IsarType.long,
    ),
    r'reminderLeadTimesJson': PropertySchema(
      id: 21,
      name: r'reminderLeadTimesJson',
      type: IsarType.string,
    ),
    r'smsNotificationsEnabled': PropertySchema(
      id: 22,
      name: r'smsNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'subscribedTopicsJson': PropertySchema(
      id: 23,
      name: r'subscribedTopicsJson',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 24,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'userBusinessId': PropertySchema(
      id: 25,
      name: r'userBusinessId',
      type: IsarType.string,
    )
  },
  estimateSize: _notificationSettingsIsarEstimateSize,
  serialize: _notificationSettingsIsarSerialize,
  deserialize: _notificationSettingsIsarDeserialize,
  deserializeProp: _notificationSettingsIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'userBusinessId': IndexSchema(
      id: -598832101615522968,
      name: r'userBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'userBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _notificationSettingsIsarGetId,
  getLinks: _notificationSettingsIsarGetLinks,
  attach: _notificationSettingsIsarAttach,
  version: '3.1.0+1',
);

int _notificationSettingsIsarEstimateSize(
  NotificationSettingsIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.categoryEnabledJson;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.categoryPrioritiesJson;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.digestFrequency.length * 3;
  {
    final value = object.emergencyKeywordsJson;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.fcmToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.reminderLeadTimesJson;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.subscribedTopicsJson;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.userBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _notificationSettingsIsarSerialize(
  NotificationSettingsIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.autoDeleteAfterDays);
  writer.writeLong(offsets[1], object.autoDeleteReadAfterDays);
  writer.writeBool(offsets[2], object.autoDeleteReadNotifications);
  writer.writeString(offsets[3], object.businessId);
  writer.writeString(offsets[4], object.categoryEnabledJson);
  writer.writeString(offsets[5], object.categoryPrioritiesJson);
  writer.writeDateTime(offsets[6], object.createdAt);
  writer.writeString(offsets[7], object.digestFrequency);
  writer.writeLong(offsets[8], object.digestTime);
  writer.writeBool(offsets[9], object.emailNotificationsEnabled);
  writer.writeString(offsets[10], object.emergencyKeywordsJson);
  writer.writeBool(offsets[11], object.emergencyOverrideEnabled);
  writer.writeString(offsets[12], object.fcmToken);
  writer.writeDateTime(offsets[13], object.fcmTokenUpdatedAt);
  writer.writeBool(offsets[14], object.inAppNotificationsEnabled);
  writer.writeLong(offsets[15], object.maxNotificationsToKeep);
  writer.writeBool(offsets[16], object.notificationsEnabled);
  writer.writeBool(offsets[17], object.pushNotificationsEnabled);
  writer.writeBool(offsets[18], object.quietHoursEnabled);
  writer.writeLong(offsets[19], object.quietHoursEnd);
  writer.writeLong(offsets[20], object.quietHoursStart);
  writer.writeString(offsets[21], object.reminderLeadTimesJson);
  writer.writeBool(offsets[22], object.smsNotificationsEnabled);
  writer.writeString(offsets[23], object.subscribedTopicsJson);
  writer.writeDateTime(offsets[24], object.updatedAt);
  writer.writeString(offsets[25], object.userBusinessId);
}

NotificationSettingsIsar _notificationSettingsIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = NotificationSettingsIsar(
    autoDeleteAfterDays: reader.readLongOrNull(offsets[0]) ?? 90,
    autoDeleteReadAfterDays: reader.readLongOrNull(offsets[1]) ?? 30,
    autoDeleteReadNotifications: reader.readBoolOrNull(offsets[2]) ?? true,
    businessId: reader.readStringOrNull(offsets[3]),
    createdAt: reader.readDateTimeOrNull(offsets[6]),
    digestFrequency: reader.readStringOrNull(offsets[7]) ?? 'none',
    digestTime: reader.readLongOrNull(offsets[8]) ?? 9,
    emailNotificationsEnabled: reader.readBoolOrNull(offsets[9]) ?? false,
    emergencyOverrideEnabled: reader.readBoolOrNull(offsets[11]) ?? true,
    fcmToken: reader.readStringOrNull(offsets[12]),
    fcmTokenUpdatedAt: reader.readDateTimeOrNull(offsets[13]),
    inAppNotificationsEnabled: reader.readBoolOrNull(offsets[14]) ?? true,
    maxNotificationsToKeep: reader.readLongOrNull(offsets[15]) ?? 1000,
    notificationsEnabled: reader.readBoolOrNull(offsets[16]) ?? true,
    pushNotificationsEnabled: reader.readBoolOrNull(offsets[17]) ?? true,
    quietHoursEnabled: reader.readBoolOrNull(offsets[18]) ?? false,
    quietHoursEnd: reader.readLongOrNull(offsets[19]) ?? 7,
    quietHoursStart: reader.readLongOrNull(offsets[20]) ?? 22,
    smsNotificationsEnabled: reader.readBoolOrNull(offsets[22]) ?? false,
    updatedAt: reader.readDateTimeOrNull(offsets[24]),
    userBusinessId: reader.readStringOrNull(offsets[25]),
  );
  object.categoryEnabledJson = reader.readStringOrNull(offsets[4]);
  object.categoryPrioritiesJson = reader.readStringOrNull(offsets[5]);
  object.emergencyKeywordsJson = reader.readStringOrNull(offsets[10]);
  object.id = id;
  object.reminderLeadTimesJson = reader.readStringOrNull(offsets[21]);
  object.subscribedTopicsJson = reader.readStringOrNull(offsets[23]);
  return object;
}

P _notificationSettingsIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset) ?? 90) as P;
    case 1:
      return (reader.readLongOrNull(offset) ?? 30) as P;
    case 2:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset) ?? 'none') as P;
    case 8:
      return (reader.readLongOrNull(offset) ?? 9) as P;
    case 9:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 14:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 15:
      return (reader.readLongOrNull(offset) ?? 1000) as P;
    case 16:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 17:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 18:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 19:
      return (reader.readLongOrNull(offset) ?? 7) as P;
    case 20:
      return (reader.readLongOrNull(offset) ?? 22) as P;
    case 21:
      return (reader.readStringOrNull(offset)) as P;
    case 22:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 23:
      return (reader.readStringOrNull(offset)) as P;
    case 24:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 25:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _notificationSettingsIsarGetId(NotificationSettingsIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _notificationSettingsIsarGetLinks(
    NotificationSettingsIsar object) {
  return [];
}

void _notificationSettingsIsarAttach(
    IsarCollection<dynamic> col, Id id, NotificationSettingsIsar object) {
  object.id = id;
}

extension NotificationSettingsIsarByIndex
    on IsarCollection<NotificationSettingsIsar> {
  Future<NotificationSettingsIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  NotificationSettingsIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<NotificationSettingsIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<NotificationSettingsIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(NotificationSettingsIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(NotificationSettingsIsar object,
      {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<NotificationSettingsIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<NotificationSettingsIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension NotificationSettingsIsarQueryWhereSort on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QWhere> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension NotificationSettingsIsarQueryWhere on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QWhereClause> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> userBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'userBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> userBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'userBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> userBusinessIdEqualTo(String? userBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'userBusinessId',
        value: [userBusinessId],
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> userBusinessIdNotEqualTo(String? userBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [],
              upper: [userBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [userBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [userBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userBusinessId',
              lower: [],
              upper: [userBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension NotificationSettingsIsarQueryFilter on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QFilterCondition> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteAfterDaysEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoDeleteAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteAfterDaysGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'autoDeleteAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteAfterDaysLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'autoDeleteAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteAfterDaysBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'autoDeleteAfterDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoDeleteReadAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'autoDeleteReadAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'autoDeleteReadAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'autoDeleteReadAfterDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadNotificationsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoDeleteReadNotifications',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'categoryEnabledJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'categoryEnabledJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryEnabledJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'categoryEnabledJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'categoryEnabledJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'categoryEnabledJson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'categoryEnabledJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'categoryEnabledJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      categoryEnabledJsonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'categoryEnabledJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      categoryEnabledJsonMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'categoryEnabledJson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryEnabledJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryEnabledJsonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'categoryEnabledJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'categoryPrioritiesJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'categoryPrioritiesJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryPrioritiesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'categoryPrioritiesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'categoryPrioritiesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'categoryPrioritiesJson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'categoryPrioritiesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'categoryPrioritiesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      categoryPrioritiesJsonContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'categoryPrioritiesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      categoryPrioritiesJsonMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'categoryPrioritiesJson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryPrioritiesJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> categoryPrioritiesJsonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'categoryPrioritiesJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'digestFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'digestFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'digestFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'digestFrequency',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'digestFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'digestFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      digestFrequencyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'digestFrequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      digestFrequencyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'digestFrequency',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'digestFrequency',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestFrequencyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'digestFrequency',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestTimeEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'digestTime',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestTimeGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'digestTime',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestTimeLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'digestTime',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> digestTimeBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'digestTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emailNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emailNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'emergencyKeywordsJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'emergencyKeywordsJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyKeywordsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'emergencyKeywordsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'emergencyKeywordsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'emergencyKeywordsJson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'emergencyKeywordsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'emergencyKeywordsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      emergencyKeywordsJsonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'emergencyKeywordsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      emergencyKeywordsJsonMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'emergencyKeywordsJson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyKeywordsJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyKeywordsJsonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'emergencyKeywordsJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emergencyOverrideEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyOverrideEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fcmToken',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fcmToken',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fcmToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fcmToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fcmToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fcmToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'fcmToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'fcmToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      fcmTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'fcmToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      fcmTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'fcmToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fcmToken',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'fcmToken',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenUpdatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fcmTokenUpdatedAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenUpdatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fcmTokenUpdatedAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenUpdatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fcmTokenUpdatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenUpdatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fcmTokenUpdatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenUpdatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fcmTokenUpdatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> fcmTokenUpdatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fcmTokenUpdatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> inAppNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'inAppNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'maxNotificationsToKeep',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'maxNotificationsToKeep',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'maxNotificationsToKeep',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'maxNotificationsToKeep',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> notificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> pushNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pushNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'quietHoursEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursEndEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'quietHoursEnd',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursEndGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'quietHoursEnd',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursEndLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'quietHoursEnd',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursEndBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'quietHoursEnd',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursStartEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'quietHoursStart',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursStartGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'quietHoursStart',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursStartLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'quietHoursStart',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> quietHoursStartBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'quietHoursStart',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reminderLeadTimesJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reminderLeadTimesJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reminderLeadTimesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reminderLeadTimesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reminderLeadTimesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reminderLeadTimesJson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reminderLeadTimesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reminderLeadTimesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      reminderLeadTimesJsonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reminderLeadTimesJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      reminderLeadTimesJsonMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reminderLeadTimesJson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reminderLeadTimesJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> reminderLeadTimesJsonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reminderLeadTimesJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> smsNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'smsNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'subscribedTopicsJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'subscribedTopicsJson',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'subscribedTopicsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'subscribedTopicsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'subscribedTopicsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'subscribedTopicsJson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'subscribedTopicsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'subscribedTopicsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      subscribedTopicsJsonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'subscribedTopicsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      subscribedTopicsJsonMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'subscribedTopicsJson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'subscribedTopicsJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> subscribedTopicsJsonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'subscribedTopicsJson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userBusinessId',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userBusinessId',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      userBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'userBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
          QAfterFilterCondition>
      userBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'userBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> userBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'userBusinessId',
        value: '',
      ));
    });
  }
}

extension NotificationSettingsIsarQueryObject on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QFilterCondition> {}

extension NotificationSettingsIsarQueryLinks on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QFilterCondition> {}

extension NotificationSettingsIsarQuerySortBy on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QSortBy> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteAfterDays', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteAfterDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteAfterDays', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteReadAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteReadAfterDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteReadNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadNotifications', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteReadNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadNotifications', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByCategoryEnabledJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryEnabledJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByCategoryEnabledJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryEnabledJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByCategoryPrioritiesJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryPrioritiesJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByCategoryPrioritiesJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryPrioritiesJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByDigestFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestFrequency', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByDigestFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestFrequency', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByDigestTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestTime', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByDigestTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestTime', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmailNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmailNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmergencyKeywordsJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyKeywordsJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmergencyKeywordsJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyKeywordsJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmergencyOverrideEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyOverrideEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmergencyOverrideEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyOverrideEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByFcmToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmToken', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByFcmTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmToken', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByFcmTokenUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmTokenUpdatedAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByFcmTokenUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmTokenUpdatedAt', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByInAppNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByInAppNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByMaxNotificationsToKeep() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByMaxNotificationsToKeepDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByPushNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByPushNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByQuietHoursEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByQuietHoursEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByQuietHoursEnd() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnd', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByQuietHoursEndDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnd', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByQuietHoursStart() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursStart', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByQuietHoursStartDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursStart', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByReminderLeadTimesJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderLeadTimesJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByReminderLeadTimesJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderLeadTimesJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortBySmsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortBySmsNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortBySubscribedTopicsJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subscribedTopicsJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortBySubscribedTopicsJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subscribedTopicsJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByUserBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByUserBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.desc);
    });
  }
}

extension NotificationSettingsIsarQuerySortThenBy on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QSortThenBy> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteAfterDays', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteAfterDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteAfterDays', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteReadAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteReadAfterDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteReadNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadNotifications', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteReadNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadNotifications', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByCategoryEnabledJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryEnabledJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByCategoryEnabledJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryEnabledJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByCategoryPrioritiesJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryPrioritiesJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByCategoryPrioritiesJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryPrioritiesJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByDigestFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestFrequency', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByDigestFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestFrequency', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByDigestTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestTime', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByDigestTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'digestTime', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmailNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmailNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmergencyKeywordsJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyKeywordsJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmergencyKeywordsJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyKeywordsJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmergencyOverrideEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyOverrideEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmergencyOverrideEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyOverrideEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByFcmToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmToken', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByFcmTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmToken', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByFcmTokenUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmTokenUpdatedAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByFcmTokenUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fcmTokenUpdatedAt', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByInAppNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByInAppNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByMaxNotificationsToKeep() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByMaxNotificationsToKeepDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByPushNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByPushNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByQuietHoursEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByQuietHoursEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByQuietHoursEnd() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnd', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByQuietHoursEndDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursEnd', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByQuietHoursStart() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursStart', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByQuietHoursStartDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quietHoursStart', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByReminderLeadTimesJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderLeadTimesJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByReminderLeadTimesJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderLeadTimesJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenBySmsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenBySmsNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenBySubscribedTopicsJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subscribedTopicsJson', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenBySubscribedTopicsJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subscribedTopicsJson', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByUserBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByUserBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userBusinessId', Sort.desc);
    });
  }
}

extension NotificationSettingsIsarQueryWhereDistinct on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QDistinct> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByAutoDeleteAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoDeleteAfterDays');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByAutoDeleteReadAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoDeleteReadAfterDays');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByAutoDeleteReadNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoDeleteReadNotifications');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByCategoryEnabledJson({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'categoryEnabledJson',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByCategoryPrioritiesJson({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'categoryPrioritiesJson',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByDigestFrequency({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'digestFrequency',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByDigestTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'digestTime');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByEmailNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emailNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByEmergencyKeywordsJson({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emergencyKeywordsJson',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByEmergencyOverrideEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emergencyOverrideEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByFcmToken({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fcmToken', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByFcmTokenUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fcmTokenUpdatedAt');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByInAppNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'inAppNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByMaxNotificationsToKeep() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'maxNotificationsToKeep');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByPushNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pushNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByQuietHoursEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'quietHoursEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByQuietHoursEnd() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'quietHoursEnd');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByQuietHoursStart() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'quietHoursStart');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByReminderLeadTimesJson({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reminderLeadTimesJson',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctBySmsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'smsNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctBySubscribedTopicsJson({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'subscribedTopicsJson',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByUserBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userBusinessId',
          caseSensitive: caseSensitive);
    });
  }
}

extension NotificationSettingsIsarQueryProperty on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QQueryProperty> {
  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      autoDeleteAfterDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoDeleteAfterDays');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      autoDeleteReadAfterDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoDeleteReadAfterDays');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      autoDeleteReadNotificationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoDeleteReadNotifications');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      categoryEnabledJsonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'categoryEnabledJson');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      categoryPrioritiesJsonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'categoryPrioritiesJson');
    });
  }

  QueryBuilder<NotificationSettingsIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String, QQueryOperations>
      digestFrequencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'digestFrequency');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      digestTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'digestTime');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      emailNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emailNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      emergencyKeywordsJsonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emergencyKeywordsJson');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      emergencyOverrideEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emergencyOverrideEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      fcmTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fcmToken');
    });
  }

  QueryBuilder<NotificationSettingsIsar, DateTime?, QQueryOperations>
      fcmTokenUpdatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fcmTokenUpdatedAt');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      inAppNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'inAppNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      maxNotificationsToKeepProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'maxNotificationsToKeep');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      notificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      pushNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pushNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      quietHoursEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'quietHoursEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      quietHoursEndProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'quietHoursEnd');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      quietHoursStartProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'quietHoursStart');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      reminderLeadTimesJsonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reminderLeadTimesJson');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      smsNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'smsNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      subscribedTopicsJsonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'subscribedTopicsJson');
    });
  }

  QueryBuilder<NotificationSettingsIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<NotificationSettingsIsar, String?, QQueryOperations>
      userBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userBusinessId');
    });
  }
}
