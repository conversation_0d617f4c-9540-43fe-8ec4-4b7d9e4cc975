import 'dart:io';
import 'package:logging/logging.dart';

/// Architecture compliance checker for ensuring modules follow established patterns
/// This tool validates that all modules adhere to the standardized architecture
class ArchitectureComplianceChecker {
  final Logger _logger = Logger('ArchitectureComplianceChecker');
  
  /// Check compliance for a specific module
  Future<ComplianceReport> checkModuleCompliance(String modulePath) async {
    final report = ComplianceReport(modulePath);
    
    try {
      // Check directory structure
      await _checkDirectoryStructure(modulePath, report);
      
      // Check repository compliance
      await _checkRepositoryCompliance(modulePath, report);
      
      // Check controller compliance
      await _checkControllerCompliance(modulePath, report);
      
      // Check service compliance
      await _checkServiceCompliance(modulePath, report);
      
      // Check model compliance
      await _checkModelCompliance(modulePath, report);
      
    } catch (e) {
      _logger.severe('Error checking compliance for $modulePath: $e');
      report.addError('Failed to complete compliance check: $e');
    }
    
    return report;
  }

  /// Check all modules in the Dashboard directory
  Future<List<ComplianceReport>> checkAllModules() async {
    final reports = <ComplianceReport>[];
    final dashboardDir = Directory('lib/Dashboard');
    
    if (!await dashboardDir.exists()) {
      _logger.warning('Dashboard directory not found');
      return reports;
    }
    
    await for (final entity in dashboardDir.list()) {
      if (entity is Directory) {
        final moduleName = entity.path.split('/').last;
        if (!_isSystemDirectory(moduleName)) {
          final report = await checkModuleCompliance(entity.path);
          reports.add(report);
        }
      }
    }
    
    return reports;
  }

  /// Check directory structure compliance
  Future<void> _checkDirectoryStructure(String modulePath, ComplianceReport report) async {
    final requiredDirs = [
      'controllers',
      'services', 
      'models',
      'screens',
    ];
    
    final optionalDirs = [
      'dialogs',
      'tabs',
      'details',
      'widgets',
    ];
    
    for (final dirName in requiredDirs) {
      final dir = Directory('$modulePath/$dirName');
      if (!await dir.exists()) {
        report.addError('Missing required directory: $dirName');
      } else {
        report.addSuccess('Found required directory: $dirName');
      }
    }
    
    for (final dirName in optionalDirs) {
      final dir = Directory('$modulePath/$dirName');
      if (await dir.exists()) {
        report.addInfo('Found optional directory: $dirName');
      }
    }
  }

  /// Check repository compliance
  Future<void> _checkRepositoryCompliance(String modulePath, ComplianceReport report) async {
    final servicesDir = Directory('$modulePath/services');
    if (!await servicesDir.exists()) return;
    
    bool foundRepository = false;
    await for (final file in servicesDir.list()) {
      if (file is File && file.path.endsWith('_repository.dart')) {
        foundRepository = true;
        await _validateRepositoryFile(file, report);
      }
    }
    
    if (!foundRepository) {
      report.addError('No repository file found in services directory');
    }
  }

  /// Check controller compliance
  Future<void> _checkControllerCompliance(String modulePath, ComplianceReport report) async {
    final controllersDir = Directory('$modulePath/controllers');
    if (!await controllersDir.exists()) return;
    
    bool foundController = false;
    await for (final file in controllersDir.list()) {
      if (file is File && file.path.endsWith('_controller.dart')) {
        foundController = true;
        await _validateControllerFile(file, report);
      }
    }
    
    if (!foundController) {
      report.addError('No controller file found in controllers directory');
    }
  }

  /// Check service compliance
  Future<void> _checkServiceCompliance(String modulePath, ComplianceReport report) async {
    final servicesDir = Directory('$modulePath/services');
    if (!await servicesDir.exists()) return;
    
    final expectedServices = [
      '_analytics_service.dart',
      '_insights_service.dart',
    ];
    
    for (final serviceSuffix in expectedServices) {
      bool found = false;
      await for (final file in servicesDir.list()) {
        if (file is File && file.path.endsWith(serviceSuffix)) {
          found = true;
          break;
        }
      }
      if (!found) {
        report.addWarning('Recommended service not found: *$serviceSuffix');
      }
    }
  }

  /// Check model compliance
  Future<void> _checkModelCompliance(String modulePath, ComplianceReport report) async {
    final modelsDir = Directory('$modulePath/models');
    if (!await modelsDir.exists()) return;
    
    bool foundIsarModel = false;
    await for (final file in modelsDir.list()) {
      if (file is File && file.path.endsWith('_isar.dart')) {
        foundIsarModel = true;
        break;
      }
    }
    
    if (!foundIsarModel) {
      report.addError('No Isar model file found in models directory');
    } else {
      report.addSuccess('Found Isar model file');
    }
  }

  /// Validate repository file content
  Future<void> _validateRepositoryFile(File file, ComplianceReport report) async {
    final content = await file.readAsString();
    final fileName = file.path.split('/').last;
    
    // Check for required patterns
    final requiredPatterns = [
      'extends BaseRepository',
      'Stream<List<',
      'watchAll',
      'Future<void> save(',
      'Future<void> delete(',
      'Future<List<',
      'getAll()',
    ];
    
    for (final pattern in requiredPatterns) {
      if (!content.contains(pattern)) {
        report.addError('Repository $fileName missing required pattern: $pattern');
      }
    }
    
    // Check for anti-patterns
    final antiPatterns = [
      'extends ChangeNotifier',
      'Logger(',
      'try {',
      'catch (',
      'debugPrint(',
    ];
    
    for (final antiPattern in antiPatterns) {
      if (content.contains(antiPattern)) {
        report.addError('Repository $fileName contains anti-pattern: $antiPattern');
      }
    }
  }

  /// Validate controller file content
  Future<void> _validateControllerFile(File file, ComplianceReport report) async {
    final content = await file.readAsString();
    final fileName = file.path.split('/').last;
    
    // Check for required patterns
    final requiredPatterns = [
      'extends ChangeNotifier',
      'ControllerState',
      'StreamSubscription',
      'notifyListeners()',
      'dispose()',
    ];
    
    for (final pattern in requiredPatterns) {
      if (!content.contains(pattern)) {
        report.addError('Controller $fileName missing required pattern: $pattern');
      }
    }
    
    // Check for recommended patterns
    final recommendedPatterns = [
      '_unfilteredStreamSubscription',
      '_updateAnalytics',
      'applyFilters',
    ];
    
    for (final pattern in recommendedPatterns) {
      if (!content.contains(pattern)) {
        report.addWarning('Controller $fileName missing recommended pattern: $pattern');
      }
    }
  }

  /// Check if directory is a system directory that should be skipped
  bool _isSystemDirectory(String dirName) {
    final systemDirs = ['widgets', '.git', '.dart_tool', 'build'];
    return systemDirs.contains(dirName) || dirName.startsWith('.');
  }
}

/// Compliance report for a module
class ComplianceReport {
  final String modulePath;
  final List<ComplianceIssue> issues = [];
  
  ComplianceReport(this.modulePath);
  
  String get moduleName => modulePath.split('/').last;
  
  void addError(String message) {
    issues.add(ComplianceIssue(ComplianceLevel.error, message));
  }
  
  void addWarning(String message) {
    issues.add(ComplianceIssue(ComplianceLevel.warning, message));
  }
  
  void addInfo(String message) {
    issues.add(ComplianceIssue(ComplianceLevel.info, message));
  }
  
  void addSuccess(String message) {
    issues.add(ComplianceIssue(ComplianceLevel.success, message));
  }
  
  bool get isCompliant => !issues.any((issue) => issue.level == ComplianceLevel.error);
  
  int get errorCount => issues.where((issue) => issue.level == ComplianceLevel.error).length;
  int get warningCount => issues.where((issue) => issue.level == ComplianceLevel.warning).length;
  int get successCount => issues.where((issue) => issue.level == ComplianceLevel.success).length;
  
  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('=== Compliance Report for $moduleName ===');
    buffer.writeln('Status: ${isCompliant ? "COMPLIANT" : "NON-COMPLIANT"}');
    buffer.writeln('Errors: $errorCount, Warnings: $warningCount, Successes: $successCount');
    buffer.writeln();
    
    for (final issue in issues) {
      buffer.writeln('${issue.level.symbol} ${issue.message}');
    }
    
    return buffer.toString();
  }
}

/// Individual compliance issue
class ComplianceIssue {
  final ComplianceLevel level;
  final String message;
  
  ComplianceIssue(this.level, this.message);
}

/// Compliance issue levels
enum ComplianceLevel {
  error('❌'),
  warning('⚠️'),
  info('ℹ️'),
  success('✅');
  
  const ComplianceLevel(this.symbol);
  final String symbol;
}
