# Events Module API Reference

## Quick Reference

### Core Models

#### EventIsar
```dart
@collection
class EventIsar {
  Id id = Isar.autoIncrement;
  @Index(unique: true) String? businessId;
  @Index() String? cattleTagId;
  @Index() String? eventTypeId;
  @Index() EventCategory? category;
  String? title;
  String? description;
  @Index() DateTime? scheduledDate;
  DateTime? completedDate;
  @Index() EventStatus? status;
  EventPriority? priority;
  // ... additional fields
}
```

#### EventTypeIsar
```dart
@collection
class EventTypeIsar {
  Id id = Isar.autoIncrement;
  @Index(unique: true) String? businessId;
  String? name;
  EventCategory? category;
  EventPriority? defaultPriority;
  bool? isActive;
  // ... additional fields
}
```

### Key Services

#### EventsRepository
```dart
class EventsRepository {
  // Reactive streams
  Stream<List<EventIsar>> watchAllEvents();
  Stream<List<EventTypeIsar>> watchAllEventTypes();
  
  // CRUD operations
  Future<void> saveEvent(EventIsar event);
  Future<void> deleteEvent(int id);
  
  // Query methods
  Future<List<EventIsar>> getAllEvents();
  Future<List<EventIsar>> getEventsByDateRange(DateTime start, DateTime end);
  Future<List<EventIsar>> getOverdueEvents();
}
```

#### EventsController
```dart
class EventsController extends ChangeNotifier {
  // State
  ControllerState get state;
  List<EventIsar> get events;
  EventAnalyticsResult get analytics;
  
  // Filtering
  void applyFilters(EventFilterState filterState);
  void applySearchFilter(String searchQuery);
  void clearFilters();
  
  // CRUD
  Future<void> addEvent(EventIsar event);
  Future<void> updateEvent(EventIsar event);
  Future<void> deleteEvent(String businessId);
  Future<void> completeEvent(String businessId, String notes);
}
```

#### EventAutomationService
```dart
class EventAutomationService {
  static Future<void> createHealthEvent({
    required String cattleTagId,
    required String healthRecordId,
    required String eventType,
    required DateTime date,
    String? notes,
    double? cost,
  });
  
  static Future<void> createBreedingEvent({
    required String cattleTagId,
    required String breedingRecordId,
    required DateTime breedingDate,
    bool schedulePregnancyCheck = true,
  });
}
```

#### EventNotificationService
```dart
class EventNotificationService {
  Future<void> scheduleEventReminders(EventIsar event);
  Future<void> cancelEventReminders(String eventBusinessId);
  Future<void> sendOverdueNotifications();
  Future<void> sendUpcomingEventNotifications();
}
```

### Enums

```dart
enum EventCategory { health, breeding, feeding, management, maintenance, financial, other }
enum EventStatus { scheduled, inProgress, completed, cancelled, overdue }
enum EventPriority { low, medium, high, critical }
enum RecurrencePattern { daily, weekly, monthly, yearly, custom }
```

### Usage Examples

#### Create Event
```dart
final event = EventIsar()
  ..businessId = 'event-001'
  ..cattleTagId = 'C001'
  ..title = 'Vaccination'
  ..scheduledDate = DateTime.now().add(Duration(days: 7))
  ..category = EventCategory.health
  ..status = EventStatus.scheduled;

await controller.addEvent(event);
```

#### Filter Events
```dart
controller.applyFilters(EventFilterState(
  category: EventCategory.health,
  status: EventStatus.scheduled,
  searchQuery: 'vaccination',
));
```

#### Get Analytics
```dart
final analytics = controller.analytics;
print('Total events: ${analytics.totalEvents}');
print('Completion rate: ${analytics.completionRate}%');
```

#### Schedule Notifications
```dart
final event = EventIsar()
  ..notificationsEnabled = true
  ..reminderMinutes = [1440, 60]; // 1 day, 1 hour

await notificationService.scheduleEventReminders(event);
```

For complete documentation, see README.md