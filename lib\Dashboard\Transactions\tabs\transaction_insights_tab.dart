import 'package:flutter/material.dart';

import '../controllers/transaction_controller.dart';
import '../services/transaction_insights_service.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';



/// Transaction Insights Tab - Displays AI-generated insights and recommendations
///
/// Architectural Pattern: Pure Dependency Injection (Ultimate Form)
/// - ALL dependencies REQUIRED via constructor for complete architectural purity
/// - ZERO knowledge of dependency creation/location within the widget
/// - Widget is a pure function of its inputs - perfect for testing and reusability
/// - Parent widgets are responsible for dependency resolution
class TransactionInsightsTab extends StatelessWidget {
  final TransactionController controller; // Required - pure dependency injection

  const TransactionInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Pure dependency injection - all dependencies provided via constructor
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        // Check if we have data to display
        if (controller.totalTransactions == 0) {
          return UniversalTabEmptyState.forTab(
            title: 'No Data for Insights',
            message: 'Add transaction records to get AI-powered financial insights and recommendations.',
            tabColor: AppColors.transactionHeader,
            tabIndex: 2, // Insights tab
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () {
                // Navigate to records tab or show add dialog
              },
              tabColor: AppColors.transactionHeader,
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(context),
              const SizedBox(height: 24),

              // Financial Insights
              _buildFinancialInsights(context),
              const SizedBox(height: 24),

              // Management Recommendations
              _buildManagementRecommendations(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.transactionHeader,
            AppColors.transactionHeader.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.transactionHeader.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Financial Insights',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Smart recommendations for optimizing your farm\'s finances',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildHeaderStat('Total Transactions', controller.totalTransactions.toString(), Icons.receipt_long),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildHeaderStat('Net Balance', '\$${controller.netBalance.toStringAsFixed(0)}', Icons.account_balance_wallet),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildHeaderStat('Savings Rate', '${controller.savingsRate.toStringAsFixed(1)}%', Icons.savings),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.8), size: 16),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16, // Reduced from 18 for better fit
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 11, // Reduced from 12 for better fit
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialInsights(BuildContext context) {
    // Pure dependency injection - use injected dependencies directly
    final insights = TransactionInsightsService.generateInsights(controller.analytics);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Financial Analysis',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (insights.isEmpty)
          _buildEmptyInsightsCard(context)
        else
          ...insights.map((insight) => _buildInsightCard(context, insight)),
      ],
    );
  }

  Widget _buildManagementRecommendations(BuildContext context) {
    final recommendations = TransactionInsightsService.generateManagementRecommendations();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Management Recommendations',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...recommendations.map((rec) => _buildRecommendationCard(context, rec)),
      ],
    );
  }

  Widget _buildInsightCard(BuildContext context, String insight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: AppColors.transactionHeader.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.lightbulb_outline,
                  color: AppColors.transactionHeader,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[700],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationCard(BuildContext context, String recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.transactionHeader.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.recommend,
                color: AppColors.transactionHeader,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  recommendation,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInsightsCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(Icons.lightbulb_outline, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Specific Insights Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add more transaction records to unlock personalized financial insights',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }


}
