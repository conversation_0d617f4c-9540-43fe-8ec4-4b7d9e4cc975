import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/report_models.dart';

/// Chart Service
/// 
/// Unified chart builder service that creates consistent charts across all reports.
/// Supports line, bar, pie, and area charts with standardized styling.
class ChartService {
  static final ChartService _instance = ChartService._internal();
  factory ChartService() => _instance;
  ChartService._internal();

  // Chart styling constants
  static const double _chartHeight = 200.0;
  static const double _chartPadding = 16.0;
  static const double _gridLineWidth = 0.5;
  static const double _borderWidth = 1.0;

  /// Generate chart widget based on type and data
  Widget buildChart({
    required ChartType type,
    required List<ChartPoint> data,
    required String title,
    Color? primaryColor,
    bool showGrid = true,
    bool showLegend = true,
    double? height,
  }) {
    if (data.isEmpty) {
      return _buildEmptyChart(title, height ?? _chartHeight);
    }

    final chartHeight = height ?? _chartHeight;
    final color = primaryColor ?? Colors.blue;

    return Container(
      height: chartHeight + 60, // Extra space for title and legend
      padding: const EdgeInsets.all(_chartPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart title
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          // Chart content
          Expanded(
            child: _buildChartByType(
              type: type,
              data: data,
              color: color,
              showGrid: showGrid,
              showLegend: showLegend,
            ),
          ),
        ],
      ),
    );
  }

  /// Build chart based on type
  Widget _buildChartByType({
    required ChartType type,
    required List<ChartPoint> data,
    required Color color,
    required bool showGrid,
    required bool showLegend,
  }) {
    switch (type) {
      case ChartType.line:
        return _buildLineChart(data, color, showGrid);
      case ChartType.bar:
        return _buildBarChart(data, color, showGrid);
      case ChartType.pie:
        return _buildPieChart(data, showLegend);
      case ChartType.area:
        return _buildAreaChart(data, color, showGrid);
    }
  }

  /// Build line chart
  Widget _buildLineChart(List<ChartPoint> data, Color color, bool showGrid) {
    final spots = data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.value);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: showGrid,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          horizontalInterval: _calculateInterval(data.map((d) => d.value).toList()),
          getDrawingHorizontalLine: (value) => FlLine(
            color: Colors.grey.withValues(alpha: 0.3),
            strokeWidth: _gridLineWidth,
          ),
          getDrawingVerticalLine: (value) => FlLine(
            color: Colors.grey.withValues(alpha: 0.3),
            strokeWidth: _gridLineWidth,
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      _truncateLabel(data[index].label),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  _formatValue(value),
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.3),
            width: _borderWidth,
          ),
        ),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: color,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: color.withValues(alpha: 0.1),
            ),
          ),
        ],
        minX: 0,
        maxX: (data.length - 1).toDouble(),
        minY: 0,
        maxY: _getMaxValue(data) * 1.1,
      ),
    );
  }

  /// Build bar chart
  Widget _buildBarChart(List<ChartPoint> data, Color color, bool showGrid) {
    final barGroups = data.asMap().entries.map((entry) {
      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: entry.value.value,
            color: entry.value.color ?? color,
            width: 20,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxValue(data) * 1.1,
        barTouchData: BarTouchData(
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (_) => Colors.blueGrey,
            tooltipHorizontalAlignment: FLHorizontalAlignment.center,
            tooltipMargin: -10,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return BarTooltipItem(
                '${data[group.x].label}\n',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                children: <TextSpan>[
                  TextSpan(
                    text: _formatValue(rod.toY),
                    style: const TextStyle(
                      color: Colors.yellow,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      _truncateLabel(data[index].label),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                }
                return const Text('');
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  _formatValue(value),
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.3),
            width: _borderWidth,
          ),
        ),
        barGroups: barGroups,
        gridData: FlGridData(
          show: showGrid,
          drawHorizontalLine: true,
          drawVerticalLine: false,
          horizontalInterval: _calculateInterval(data.map((d) => d.value).toList()),
          getDrawingHorizontalLine: (value) => FlLine(
            color: Colors.grey.withValues(alpha: 0.3),
            strokeWidth: _gridLineWidth,
          ),
        ),
      ),
    );
  }

  /// Build pie chart
  Widget _buildPieChart(List<ChartPoint> data, bool showLegend) {
    final total = data.fold<double>(0, (sum, point) => sum + point.value);
    
    final sections = data.asMap().entries.map((entry) {
      final index = entry.key;
      final point = entry.value;
      final percentage = (point.value / total * 100);
      
      return PieChartSectionData(
        color: point.color ?? _getColorForIndex(index),
        value: point.value,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: PieChart(
            PieChartData(
              sections: sections,
              borderData: FlBorderData(show: false),
              sectionsSpace: 2,
              centerSpaceRadius: 40,
            ),
          ),
        ),
        if (showLegend) ...[
          const SizedBox(width: 16),
          Expanded(
            flex: 1,
            child: _buildPieLegend(data),
          ),
        ],
      ],
    );
  }

  /// Build area chart
  Widget _buildAreaChart(List<ChartPoint> data, Color color, bool showGrid) {
    final spots = data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.value);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: showGrid,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          horizontalInterval: _calculateInterval(data.map((d) => d.value).toList()),
          getDrawingHorizontalLine: (value) => FlLine(
            color: Colors.grey.withValues(alpha: 0.3),
            strokeWidth: _gridLineWidth,
          ),
          getDrawingVerticalLine: (value) => FlLine(
            color: Colors.grey.withValues(alpha: 0.3),
            strokeWidth: _gridLineWidth,
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      _truncateLabel(data[index].label),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  _formatValue(value),
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.3),
            width: _borderWidth,
          ),
        ),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: color,
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: color.withValues(alpha: 0.3),
            ),
          ),
        ],
        minX: 0,
        maxX: (data.length - 1).toDouble(),
        minY: 0,
        maxY: _getMaxValue(data) * 1.1,
      ),
    );
  }

  /// Build pie chart legend
  Widget _buildPieLegend(List<ChartPoint> data) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: data.asMap().entries.map((entry) {
        final index = entry.key;
        final point = entry.value;
        final color = point.color ?? _getColorForIndex(index);
        
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  point.label,
                  style: const TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// Build empty chart placeholder
  Widget _buildEmptyChart(String title, double height) {
    return Container(
      height: height,
      padding: const EdgeInsets.all(_chartPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: _borderWidth,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'No data available',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods

  double _getMaxValue(List<ChartPoint> data) {
    if (data.isEmpty) return 100;
    return data.map((d) => d.value).reduce((a, b) => a > b ? a : b);
  }

  double _calculateInterval(List<double> values) {
    if (values.isEmpty) return 1.0;
    final max = values.reduce((a, b) => a > b ? a : b);
    if (max <= 0) return 1.0; // Prevent zero or negative intervals
    final interval = max / 5; // 5 grid lines
    return interval > 0 ? interval : 1.0; // Ensure interval is always positive
  }

  String _formatValue(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else if (value == value.toInt()) {
      return value.toInt().toString();
    } else {
      return value.toStringAsFixed(1);
    }
  }

  String _truncateLabel(String label) {
    if (label.length <= 8) return label;
    return '${label.substring(0, 6)}..';
  }

  Color _getColorForIndex(int index) {
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return colors[index % colors.length];
  }

  /// Generate trend data from time series
  static List<ChartPoint> generateTrendData(List<dynamic> records, String valueField, String dateField) {
    final dailyTotals = <String, double>{};
    
    for (final record in records) {
      final date = record[dateField] as DateTime?;
      final value = record[valueField] as double?;
      
      if (date == null || value == null) continue;
      
      final dateKey = '${date.day}/${date.month}';
      dailyTotals[dateKey] = (dailyTotals[dateKey] ?? 0) + value;
    }
    
    return dailyTotals.entries.map((entry) => 
      ChartPoint(
        label: entry.key,
        value: entry.value,
      )
    ).toList();
  }

  /// Generate category distribution data
  static List<ChartPoint> generateCategoryData(List<dynamic> records, String categoryField) {
    final categoryCounts = <String, int>{};
    
    for (final record in records) {
      final category = record[categoryField] as String? ?? 'Unknown';
      categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
    }
    
    return categoryCounts.entries.map((entry) => 
      ChartPoint.category(
        category: entry.key,
        value: entry.value.toDouble(),
      )
    ).toList();
  }
}