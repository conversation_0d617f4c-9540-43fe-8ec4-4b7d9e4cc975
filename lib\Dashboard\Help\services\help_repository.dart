import 'package:isar/isar.dart';
import '../models/help_article_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Help module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class HelpRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  HelpRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE HELP STREAMS ===//

  /// Watches all help articles with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<HelpArticleIsar>> watchAllHelpArticles() {
    return _isar.helpArticleIsars.where().watch(fireImmediately: true);
  }

  //=== HELP ARTICLE CRUD ===//

  /// Save (add or update) a help article using <PERSON>r's native upsert
  Future<void> saveHelpArticle(HelpArticleIsar article) async {
    await _isar.writeTxn(() async {
      await _isar.helpArticleIsars.put(article);
    });
  }

  /// Delete a help article by its Isar ID
  Future<void> deleteHelpArticle(int articleId) async {
    await _isar.writeTxn(() async {
      await _isar.helpArticleIsars.delete(articleId);
    });
  }

  /// Delete a help article by its business ID
  Future<void> deleteHelpArticleByBusinessId(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.helpArticleIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all help articles (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<HelpArticleIsar>> getAllHelpArticles() async {
    return await _isar.helpArticleIsars.where().findAll();
  }

  /// Get help article by business ID (for validation and navigation)
  /// Returns a Future<HelpArticleIsar?> for one-time data fetching
  Future<HelpArticleIsar?> getHelpArticleByBusinessId(String businessId) async {
    return await _isar.helpArticleIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get help articles by category (for categorized display)
  /// Returns a Future<List> for one-time data fetching
  Future<List<HelpArticleIsar>> getHelpArticlesByCategory(String category) async {
    return await _isar.helpArticleIsars
        .filter()
        .categoryEqualTo(category)
        .and()
        .isActiveEqualTo(true)
        .sortByPriorityDesc()
        .findAll();
  }

  //=== BACKWARD COMPATIBILITY METHODS ===//

  /// Update help article - alias for saveHelpArticle for backward compatibility
  Future<void> updateHelpArticle(HelpArticleIsar article) async {
    await saveHelpArticle(article);
  }

  /// Create help article - alias for saveHelpArticle for backward compatibility
  Future<void> createHelpArticle(HelpArticleIsar article) async {
    await saveHelpArticle(article);
  }
}
