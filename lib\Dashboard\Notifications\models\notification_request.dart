import '../models/notification_priority.dart';

/// Request model for creating notifications
class NotificationRequest {
  final String title;
  final String message;
  final String? category;
  final String? cattleId;
  final NotificationPriority priority;
  final DateTime? scheduledTime;
  final Map<String, dynamic>? additionalData;
  final List<String>? deliveryChannels;
  final bool persistent;
  final Duration? expiresAfter;
  
  const NotificationRequest({
    required this.title,
    required this.message,
    this.category,
    this.cattleId,
    this.priority = NotificationPriority.normal,
    this.scheduledTime,
    this.additionalData,
    this.deliveryChannels,
    this.persistent = false,
    this.expiresAfter,
  });
  
  /// Create a health alert notification request
  factory NotificationRequest.healthAlert({
    required String cattleName,
    required String healthIssue,
    required String severity,
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    NotificationPriority priority;
    switch (severity.toLowerCase()) {
      case 'critical':
        priority = NotificationPriority.high;
        break;
      case 'high':
        priority = NotificationPriority.high;
        break;
      default:
        priority = NotificationPriority.normal;
    }
    
    return NotificationRequest(
      title: _getHealthAlertTitle(severity),
      message: '$cattleName requires attention for $healthIssue.',
      category: 'health_alert',
      cattleId: cattleId,
      priority: priority,
      additionalData: {
        'cattleName': cattleName,
        'healthIssue': healthIssue,
        'severity': severity,
        ...?additionalData,
      },
      deliveryChannels: severity.toLowerCase() == 'critical' 
          ? ['push', 'email', 'sms'] 
          : ['push'],
      persistent: severity.toLowerCase() == 'critical',
    );
  }
  
  /// Create a breeding reminder notification request
  factory NotificationRequest.breedingReminder({
    required String cattleName,
    required String reminderType,
    required DateTime dueDate,
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    final isOverdue = dueDate.isBefore(DateTime.now());
    final priority = isOverdue ? NotificationPriority.high : NotificationPriority.normal;
    
    return NotificationRequest(
      title: isOverdue ? 'Overdue Breeding Reminder' : 'Breeding Reminder',
      message: '$cattleName is ${isOverdue ? 'overdue for' : 'due for'} $reminderType.',
      category: 'breeding_reminder',
      cattleId: cattleId,
      priority: priority,
      additionalData: {
        'cattleName': cattleName,
        'reminderType': reminderType,
        'dueDate': dueDate.toIso8601String(),
        'isOverdue': isOverdue,
        ...?additionalData,
      },
      deliveryChannels: ['push'],
    );
  }
  
  /// Create a milk production notification request
  factory NotificationRequest.milkProduction({
    required String cattleName,
    required double currentProduction,
    required double previousProduction,
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    final productionChange = currentProduction - previousProduction;
    final isSignificantChange = (productionChange.abs() / previousProduction) > 0.2; // 20% change
    
    return NotificationRequest(
      title: _getMilkProductionTitle(productionChange),
      message: '$cattleName produced ${currentProduction.toStringAsFixed(1)}L of milk.',
      category: 'milk_production',
      cattleId: cattleId,
      priority: isSignificantChange ? NotificationPriority.normal : NotificationPriority.low,
      additionalData: {
        'cattleName': cattleName,
        'currentProduction': currentProduction,
        'previousProduction': previousProduction,
        'productionChange': productionChange,
        'isSignificantChange': isSignificantChange,
        ...?additionalData,
      },
      deliveryChannels: ['push'],
    );
  }
  
  /// Create a weight update notification request
  factory NotificationRequest.weightUpdate({
    required String cattleName,
    required double currentWeight,
    required double previousWeight,
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    final weightChange = currentWeight - previousWeight;
    final isSignificantLoss = weightChange < -5.0; // 5kg loss
    
    return NotificationRequest(
      title: _getWeightUpdateTitle(weightChange),
      message: '$cattleName now weighs ${currentWeight.toStringAsFixed(1)}kg.',
      category: 'weight_update',
      cattleId: cattleId,
      priority: isSignificantLoss ? NotificationPriority.high : NotificationPriority.normal,
      additionalData: {
        'cattleName': cattleName,
        'currentWeight': currentWeight,
        'previousWeight': previousWeight,
        'weightChange': weightChange,
        'isSignificantLoss': isSignificantLoss,
        ...?additionalData,
      },
      deliveryChannels: isSignificantLoss ? ['push', 'email'] : ['push'],
    );
  }
  
  /// Create a vaccination due notification request
  factory NotificationRequest.vaccinationDue({
    required String cattleName,
    required String vaccinationType,
    required DateTime dueDate,
    String? cattleId,
    Map<String, dynamic>? additionalData,
  }) {
    final daysUntilDue = dueDate.difference(DateTime.now()).inDays;
    final isUrgent = daysUntilDue <= 3;
    
    return NotificationRequest(
      title: 'Vaccination Due',
      message: '$cattleName is due for $vaccinationType vaccination.',
      category: 'vaccination_due',
      cattleId: cattleId,
      priority: isUrgent ? NotificationPriority.high : NotificationPriority.normal,
      scheduledTime: daysUntilDue > 7 ? dueDate.subtract(const Duration(days: 7)) : null,
      additionalData: {
        'cattleName': cattleName,
        'vaccinationType': vaccinationType,
        'dueDate': dueDate.toIso8601String(),
        'daysUntilDue': daysUntilDue,
        'isUrgent': isUrgent,
        ...?additionalData,
      },
      deliveryChannels: ['push'],
    );
  }
  
  /// Create a system alert notification request
  factory NotificationRequest.systemAlert({
    required String alertType,
    required String message,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? additionalData,
  }) {
    return NotificationRequest(
      title: 'System Alert: $alertType',
      message: message,
      category: 'system_alert',
      priority: priority,
      additionalData: {
        'alertType': alertType,
        ...?additionalData,
      },
      deliveryChannels: ['push'],
      persistent: priority == NotificationPriority.high,
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'message': message,
      'category': category,
      'cattleId': cattleId,
      'priority': priority.name,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'additionalData': additionalData,
      'deliveryChannels': deliveryChannels,
      'persistent': persistent,
      'expiresAfter': expiresAfter?.inMilliseconds,
    };
  }
  
  /// Create from JSON
  factory NotificationRequest.fromJson(Map<String, dynamic> json) {
    return NotificationRequest(
      title: json['title'],
      message: json['message'],
      category: json['category'],
      cattleId: json['cattleId'],
      priority: NotificationPriority.values.firstWhere(
        (p) => p.name == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      scheduledTime: json['scheduledTime'] != null 
          ? DateTime.parse(json['scheduledTime']) 
          : null,
      additionalData: json['additionalData']?.cast<String, dynamic>(),
      deliveryChannels: json['deliveryChannels']?.cast<String>(),
      persistent: json['persistent'] ?? false,
      expiresAfter: json['expiresAfter'] != null 
          ? Duration(milliseconds: json['expiresAfter']) 
          : null,
    );
  }
  
  /// Helper methods for generating titles
  static String _getHealthAlertTitle(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return '🚨 Critical Health Alert';
      case 'high':
        return '⚠️ Health Alert';
      default:
        return '📋 Health Update';
    }
  }
  
  static String _getMilkProductionTitle(double change) {
    if (change > 0) {
      return '📈 Milk Production Increase';
    } else if (change < 0) {
      return '📉 Milk Production Decrease';
    } else {
      return '🥛 Milk Production Update';
    }
  }
  
  static String _getWeightUpdateTitle(double change) {
    if (change > 0) {
      return '📊 Weight Gain Recorded';
    } else if (change < 0) {
      return '⚠️ Weight Loss Recorded';
    } else {
      return '⚖️ Weight Update';
    }
  }
}
