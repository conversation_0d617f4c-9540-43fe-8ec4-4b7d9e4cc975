import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import 'package:logging/logging.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_enums.dart';
import '../services/events_repository.dart';
// Using streamlined architecture - validation and error handling inline
import '../widgets/event_attachment_widgets.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

// Extension for firstOrNull functionality
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull => isEmpty ? null : first;
}

// --- Constants ---
class _AppStrings {
  static const String addEventTitle = 'Add Event';
  static const String editEventTitle = 'Edit Event';
  static const String titleLabel = 'Event Title';
  static const String descriptionLabel = 'Description (Optional)';
  static const String cattleLabel = 'Select Cattle';
  static const String eventTypeLabel = 'Event Type';
  static const String categoryLabel = 'Category';
  static const String priorityLabel = 'Priority';
  static const String scheduledDateLabel = 'Scheduled Date';
  static const String scheduledTimeLabel = 'Scheduled Time';
  static const String locationLabel = 'Location (Optional)';
  static const String estimatedCostLabel = 'Estimated Cost (Optional)';
  static const String notesLabel = 'Notes (Optional)';
  static const String recurrenceLabel = 'Recurring Event';
  static const String recurrencePatternLabel = 'Recurrence Pattern';
  static const String recurrenceIntervalLabel = 'Repeat Every';
  static const String recurrenceEndDateLabel = 'End Date (Optional)';
  static const String notificationsLabel = 'Enable Notifications';

  // Validation messages
  static const String titleRequired = 'Please enter event title';
  static const String costInvalid = 'Please enter valid cost';
}

/// Event form dialog with reactive updates - following established patterns
class EventFormDialog extends StatefulWidget {
  final List<CattleIsar> cattle;
  final List<EventTypeIsar> eventTypes;
  final EventIsar? existingEvent; // For editing
  final CattleIsar? preSelectedCattle; // For pre-selecting specific cattle
  final Function(EventIsar)? onSave; // Callback for saving events

  const EventFormDialog({
    Key? key,
    required this.cattle,
    required this.eventTypes,
    this.existingEvent,
    this.preSelectedCattle,
    this.onSave,
  }) : super(key: key);

  @override
  State<EventFormDialog> createState() => _EventFormDialogState();
}

class _EventFormDialogState extends State<EventFormDialog> {
  _EventFormDialogState();

  final _formKey = GlobalKey<FormState>();
  final EventsRepository _eventsRepository = GetIt.I<EventsRepository>();
  
  // Using streamlined architecture - inline validation and error handling
  final Logger _logger = Logger('EventFormDialog');
  final Uuid _uuid = const Uuid();

  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  final _locationController = TextEditingController();
  final _estimatedCostController = TextEditingController();
  final _recurrenceIntervalController = TextEditingController(text: '1');

  // Form data
  CattleIsar? _selectedCattle;
  EventTypeIsar? _selectedEventType;
  DateTime _scheduledDate = DateTime.now();
  TimeOfDay _scheduledTime = TimeOfDay.now();
  EventCategory _selectedCategory = EventCategory.other;
  EventPriority _selectedPriority = EventPriority.medium;
  
  // Recurrence settings
  bool _isRecurring = false;
  RecurrencePattern _recurrencePattern = RecurrencePattern.daily;
  int _recurrenceInterval = 1;
  DateTime? _recurrenceEndDate;
  
  // Notification settings
  bool _notificationsEnabled = true;

  // Loading state
  bool _isSaving = false;

  // Colors - Event theme colors
  static const _eventColor = Color(0xFF7B1FA2);          // Purple for Event Details
  static const _scheduleColor = Color(0xFF1976D2);       // Blue for Schedule Details
  static const _recurrenceColor = Color(0xFFFF5722);     // Red-orange for Recurrence
  static const _notesColor = Color(0xFF795548);          // Brown for Notes

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.existingEvent != null) {
      final event = widget.existingEvent!;
      _titleController.text = event.title ?? '';
      _descriptionController.text = event.description ?? '';
      _notesController.text = event.notes ?? '';
      _locationController.text = event.location ?? '';
      _estimatedCostController.text = event.estimatedCost?.toString() ?? '';
      
      // Find cattle by tag ID
      _selectedCattle = widget.cattle.where((c) => c.tagId == event.cattleTagId).firstOrNull;
      
      // Find event type by business ID
      _selectedEventType = widget.eventTypes.where((et) => et.businessId == event.eventTypeId).firstOrNull;
      
      _selectedCategory = event.category;
      _selectedPriority = event.priority;
      _scheduledDate = event.scheduledDate ?? DateTime.now();
      _scheduledTime = TimeOfDay.fromDateTime(event.scheduledDate ?? DateTime.now());
      
      // Recurrence settings
      _isRecurring = event.isRecurring ?? false;
      _recurrencePattern = event.recurrencePattern;
      _recurrenceInterval = event.recurrenceInterval ?? 1;
      _recurrenceIntervalController.text = _recurrenceInterval.toString();
      _recurrenceEndDate = event.recurrenceEndDate;
      
      // Notification settings
      _notificationsEnabled = event.notificationsEnabled ?? true;
    } else {
      // Use pre-selected cattle if provided, otherwise use first cattle
      _selectedCattle = widget.preSelectedCattle ??
          (widget.cattle.isNotEmpty ? widget.cattle.first : null);
      
      // Use first event type if available
      _selectedEventType = widget.eventTypes.isNotEmpty ? widget.eventTypes.first : null;
      
      // Set category based on selected event type
      if (_selectedEventType != null) {
        _selectedCategory = _selectedEventType!.category;
        _selectedPriority = _selectedEventType!.defaultPriority;
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    _locationController.dispose();
    _estimatedCostController.dispose();
    _recurrenceIntervalController.dispose();
    super.dispose();
  }

  Future<void> _selectRecurrenceEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _recurrenceEndDate ?? _scheduledDate.add(const Duration(days: 30)),
      firstDate: _scheduledDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (picked != null) {
      setState(() {
        _recurrenceEndDate = picked;
      });
    }
  }

  void _onEventTypeChanged(EventTypeIsar? eventType) {
    setState(() {
      _selectedEventType = eventType;
      if (eventType != null) {
        _selectedCategory = eventType.category;
        _selectedPriority = eventType.defaultPriority;
        
        // Update recurrence settings based on event type defaults
        if (eventType.supportsRecurrence ?? false) {
          _recurrencePattern = eventType.defaultRecurrencePattern;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.existingEvent == null
        ? UniversalFormDialog(
            title: _AppStrings.addEventTitle,
            headerIcon: Icons.event, // Event-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _saveEvent,
              addText: 'Save', // User preference: Use 'Save' instead of 'Add'
              isAdding: _isSaving,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editEventTitle,
            headerIcon: Icons.edit_calendar, // Event-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _saveEvent,
              updateText: 'Save', // User preference: Use 'Save' instead of 'Update'
              isUpdating: _isSaving,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildEventDetailsSection(),
          UniversalFormField.sectionSpacing,
          _buildScheduleSection(),
          if (_selectedEventType?.supportsRecurrence ?? false) ...[
            UniversalFormField.sectionSpacing,
            _buildRecurrenceSection(),
          ],
          UniversalFormField.sectionSpacing,
          _buildNotesSection(),
          UniversalFormField.sectionSpacing,
          _buildAttachmentsSection(),
        ],
      ),
    );
  }

  Widget _buildEventDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Event Details',
          icon: Icons.event,
          color: _eventColor,
          filled: true,
        ),
        
        // Event title
        UniversalFormField.textField(
          label: _AppStrings.titleLabel,
          controller: _titleController,
          prefixIcon: Icons.title,
          prefixIconColor: _eventColor,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return _AppStrings.titleRequired;
            }
            if (value.trim().length > 100) {
              return 'Event title cannot exceed 100 characters';
            }
            return null;
          },
        ),
        UniversalFormField.spacing,

        // Cattle selection with search functionality
        UniversalFormField.dropdownField<CattleIsar>(
          label: _AppStrings.cattleLabel,
          value: _selectedCattle,
          items: widget.cattle.map((cattle) {
            final cattleName = cattle.name ?? 'Unknown';
            final tagId = cattle.tagId ?? '';
            final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
            return DropdownMenuItem(
              value: cattle,
              child: Text(displayName, overflow: TextOverflow.ellipsis),
            );
          }).toList(),
          onChanged: (value) => setState(() => _selectedCattle = value),
          prefixIcon: Icons.pets,
          prefixIconColor: _eventColor,
          validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
        ),
        UniversalFormField.spacing,

        // Event type selection with category-based filtering
        UniversalFormField.dropdownField<EventTypeIsar>(
          label: _AppStrings.eventTypeLabel,
          value: _selectedEventType,
          items: widget.eventTypes.map((eventType) {
            return DropdownMenuItem(
              value: eventType,
              child: Text(eventType.name ?? 'Unknown', overflow: TextOverflow.ellipsis),
            );
          }).toList(),
          onChanged: _onEventTypeChanged,
          prefixIcon: Icons.category,
          prefixIconColor: _eventColor,
          validator: (value) => UniversalFormField.dropdownValidator(value, 'event type'),
        ),
        UniversalFormField.spacing,

        // Category and Priority row
        UniversalFormField.fieldRow(
          leftField: UniversalFormField.dropdownField<EventCategory>(
            label: _AppStrings.categoryLabel,
            value: _selectedCategory,
            items: EventCategory.values.map((category) {
              return DropdownMenuItem(
                value: category,
                child: Text(category.displayName),
              );
            }).toList(),
            onChanged: (value) => setState(() => _selectedCategory = value!),
            prefixIcon: Icons.label,
            prefixIconColor: _eventColor,
          ),
          rightField: UniversalFormField.dropdownField<EventPriority>(
            label: _AppStrings.priorityLabel,
            value: _selectedPriority,
            items: EventPriority.values.map((priority) {
              return DropdownMenuItem(
                value: priority,
                child: Text(priority.displayName),
              );
            }).toList(),
            onChanged: (value) => setState(() => _selectedPriority = value!),
            prefixIcon: Icons.priority_high,
            prefixIconColor: _eventColor,
          ),
        ),
        UniversalFormField.spacing,

        // Description
        UniversalFormField.multilineField(
          label: _AppStrings.descriptionLabel,
          controller: _descriptionController,
          maxLines: 3,
          prefixIcon: Icons.description,
          prefixIconColor: _eventColor,
          validator: (value) {
            if (value != null && value.trim().length > 500) {
              return 'Description cannot exceed 500 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildScheduleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Schedule Details',
          icon: Icons.schedule,
          color: _scheduleColor,
          filled: true,
        ),
        
        // Date and Time row
        UniversalFormField.fieldRow(
          leftField: UniversalFormField.dateField(
            context: context,
            label: _AppStrings.scheduledDateLabel,
            value: _scheduledDate,
            onChanged: (date) {
              setState(() {
                _scheduledDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: _scheduleColor,
            firstDate: DateTime.now().subtract(const Duration(days: 30)),
            lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
          ),
          rightField: UniversalFormField.textField(
            label: _AppStrings.scheduledTimeLabel,
            controller: TextEditingController(text: _scheduledTime.format(context)),
            prefixIcon: Icons.access_time,
            prefixIconColor: _scheduleColor,
            readOnly: true,
            onTap: () async {
              final TimeOfDay? picked = await showTimePicker(
                context: context,
                initialTime: _scheduledTime,
              );
              if (picked != null) {
                setState(() {
                  _scheduledTime = picked;
                });
              }
            },
          ),
        ),
        UniversalFormField.spacing,

        // Location and Estimated Cost row
        UniversalFormField.fieldRow(
          leftField: UniversalFormField.textField(
            label: _AppStrings.locationLabel,
            controller: _locationController,
            prefixIcon: Icons.location_on,
            prefixIconColor: _scheduleColor,
            validator: (value) {
              if (value != null && value.trim().length > 100) {
                return 'Location cannot exceed 100 characters';
              }
              return null;
            },
          ),
          rightField: UniversalFormField.numberField(
            label: _AppStrings.estimatedCostLabel,
            controller: _estimatedCostController,
            allowDecimals: true,
            prefixIcon: Icons.attach_money,
            prefixIconColor: _scheduleColor,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final cost = double.tryParse(value);
                if (cost == null || cost < 0) return _AppStrings.costInvalid;
              }
              return null;
            },
          ),
        ),
        UniversalFormField.spacing,

        // Notifications toggle
        UniversalFormField.fieldWithToggle(
          field: Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: const Text(
              _AppStrings.notificationsLabel,
              style: TextStyle(
                fontSize: 16,
                color: _scheduleColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          toggleLabel: '',
          toggleValue: _notificationsEnabled,
          onToggleChanged: (value) => setState(() => _notificationsEnabled = value),
        ),
      ],
    );
  }

  Widget _buildRecurrenceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Recurrence Settings',
          icon: Icons.repeat,
          color: _recurrenceColor,
          filled: true,
        ),
        
        // Recurring toggle
        UniversalFormField.fieldWithToggle(
          field: Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: const Text(
              _AppStrings.recurrenceLabel,
              style: TextStyle(
                fontSize: 16,
                color: _recurrenceColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          toggleLabel: '',
          toggleValue: _isRecurring,
          onToggleChanged: (value) => setState(() => _isRecurring = value),
        ),
        
        if (_isRecurring) ...[
          UniversalFormField.spacing,
          
          // Recurrence pattern and interval row
          UniversalFormField.fieldRow(
            leftField: UniversalFormField.dropdownField<RecurrencePattern>(
              label: _AppStrings.recurrencePatternLabel,
              value: _recurrencePattern,
              items: RecurrencePattern.values.map((pattern) {
                return DropdownMenuItem(
                  value: pattern,
                  child: Text(pattern.displayName),
                );
              }).toList(),
              onChanged: (value) => setState(() => _recurrencePattern = value!),
              prefixIcon: Icons.repeat,
              prefixIconColor: _recurrenceColor,
            ),
            rightField: UniversalFormField.numberField(
              label: _AppStrings.recurrenceIntervalLabel,
              controller: _recurrenceIntervalController,
              prefixIcon: Icons.numbers,
              prefixIconColor: _recurrenceColor,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final interval = int.tryParse(value);
                  if (interval == null || interval < 1) {
                    return 'Interval must be at least 1';
                  }
                  if (interval > 365) {
                    return 'Interval cannot exceed 365 days';
                  }
                }
                return null;
              },
              onChanged: (value) {
                final interval = int.tryParse(value);
                if (interval != null && interval > 0) {
                  _recurrenceInterval = interval;
                }
              },
            ),
          ),
          UniversalFormField.spacing,
          
          // Recurrence end date
          UniversalFormField.textField(
            label: _AppStrings.recurrenceEndDateLabel,
            controller: TextEditingController(
              text: _recurrenceEndDate != null 
                  ? '${_recurrenceEndDate!.day}/${_recurrenceEndDate!.month}/${_recurrenceEndDate!.year}'
                  : '',
            ),
            prefixIcon: Icons.event_busy,
            prefixIconColor: _recurrenceColor,
            readOnly: true,
            onTap: () => _selectRecurrenceEndDate(context),
            suffixIcon: _recurrenceEndDate != null
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () => setState(() => _recurrenceEndDate = null),
                  )
                : null,
          ),
        ],
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Additional Information',
          icon: Icons.note,
          color: _notesColor,
          filled: true,
        ),
        
        // Notes
        UniversalFormField.multilineField(
          label: _AppStrings.notesLabel,
          controller: _notesController,
          maxLines: 4,
          prefixIcon: Icons.note,
          prefixIconColor: _notesColor,
          validator: (value) {
            if (value != null && value.trim().length > 1000) {
              return 'Notes cannot exceed 1000 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildAttachmentsSection() {
    // Only show attachments section if we have an existing event with a business ID
    // For new events, attachments can be added after the event is created
    if (widget.existingEvent?.businessId == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Attachments',
          icon: Icons.attach_file,
          color: _notesColor,
          filled: true,
        ),
        
        // Attachments widget
        EventAttachmentsWidget(
          eventBusinessId: widget.existingEvent!.businessId!,
          isEditable: true,
          onAttachmentsChanged: () {
            // Refresh the dialog if needed
            setState(() {});
          },
        ),
      ],
    );
  }

  Future<void> _saveEvent() async {
    // Basic form validation
    if (!_formKey.currentState!.validate()) {
      _showValidationError('Please correct the highlighted fields');
      return;
    }

    // Required field validation
    if (_selectedCattle == null) {
      _showValidationError('Please select a cattle');
      return;
    }

    if (_selectedEventType == null) {
      _showValidationError('Please select an event type');
      return;
    }

    setState(() => _isSaving = true);

    final operationId = 'save_event_${widget.existingEvent?.businessId ?? 'new'}';

    try {
      final scheduledDateTime = DateTime(
        _scheduledDate.year,
        _scheduledDate.month,
        _scheduledDate.day,
        _scheduledTime.hour,
        _scheduledTime.minute,
      );

      final event = EventIsar()
        ..title = _titleController.text.trim()
        ..description = _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim()
        ..cattleTagId = _selectedCattle!.tagId
        ..eventTypeId = _selectedEventType!.businessId
        ..category = _selectedCategory
        ..priority = _selectedPriority
        ..scheduledDate = scheduledDateTime
        ..status = EventStatus.scheduled
        ..location = _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim()
        ..estimatedCost = _estimatedCostController.text.trim().isEmpty
            ? null
            : double.tryParse(_estimatedCostController.text)
        ..notes = _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim()
        ..isRecurring = _isRecurring
        ..recurrencePattern = _recurrencePattern
        ..recurrenceInterval = _isRecurring ? _recurrenceInterval : null
        ..recurrenceEndDate = _isRecurring ? _recurrenceEndDate : null
        ..notificationsEnabled = _notificationsEnabled
        ..reminderMinutes = _notificationsEnabled ? [1440, 60] : null; // 1 day and 1 hour

      if (widget.existingEvent != null) {
        // Editing existing event
        event.id = widget.existingEvent!.id;
        event.businessId = widget.existingEvent!.businessId;
        event.createdAt = widget.existingEvent!.createdAt;
        event.updatedAt = DateTime.now();
      } else {
        // Adding new event
        event.businessId = _uuid.v4();
        event.createdAt = DateTime.now();
        event.updatedAt = DateTime.now();
      }

      // Simplified validation - inline
      if (event.title == null || event.title!.trim().isEmpty) {
        _showValidationError('Event title is required');
        return;
      }

      if (event.cattleTagId == null || event.cattleTagId!.trim().isEmpty) {
        _showValidationError('Cattle selection is required');
        return;
      }

      // Save event directly
      if (widget.onSave != null) {
        await widget.onSave!(event);
      } else {
        await _eventsRepository.saveEvent(event);
      }

      if (mounted) {
        _showSuccess(
          widget.existingEvent != null
              ? 'Event updated successfully'
              : 'Event created successfully',
        );

        Navigator.of(context).pop();
      }
    } catch (e, stackTrace) {
      _logger.severe('Failed to save event: $e', e, stackTrace);
      
      if (mounted) {
        await _handleSaveError(e, operationId);
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  /// Handle save errors with user-friendly messages and retry options
  Future<void> _handleSaveError(dynamic error, String operationId) async {
    final friendlyMessage = _formatError(error);

    // Simplified error handling - just show the error
    _showError(friendlyMessage);
  }

  /// Format error message for user display
  String _formatError(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    }
    return error.toString();
  }

  // Inline message helpers to replace removed services
  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
