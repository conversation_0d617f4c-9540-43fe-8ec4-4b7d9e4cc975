import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import '../controllers/event_details_controller.dart';
import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../widgets/event_attachment_widgets.dart';
import '../widgets/event_timeline_widget.dart';
import '../dialogs/event_form_dialog.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum
import '../../../constants/app_colors.dart';
import '../../../utils/message_utils.dart';

/// Comprehensive event details screen for viewing and editing events
/// Follows the established screen pattern with Provider state management
class EventDetailsScreen extends StatelessWidget {
  final String eventBusinessId;

  const EventDetailsScreen({
    Key? key,
    required this.eventBusinessId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => EventDetailsController()..initialize(eventBusinessId),
      child: const _EventDetailsScreenContent(),
    );
  }
}

class _EventDetailsScreenContent extends StatefulWidget {
  const _EventDetailsScreenContent();

  @override
  State<_EventDetailsScreenContent> createState() => _EventDetailsScreenContentState();
}

class _EventDetailsScreenContentState extends State<_EventDetailsScreenContent> {
  @override
  Widget build(BuildContext context) {
    return Consumer<EventDetailsController>(
      builder: (context, controller, child) {
        return Scaffold(
          appBar: _buildAppBar(context, controller),
          body: _buildBody(context, controller),
          floatingActionButton: _buildFloatingActionButton(context, controller),
        );
      },
    );
  }

  /// Build app bar with actions
  PreferredSizeWidget _buildAppBar(BuildContext context, EventDetailsController controller) {
    final event = controller.event;
    
    return AppBar(
      title: Text(event?.title ?? 'Event Details'),
      backgroundColor: AppColors.eventsHeader,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // Share button
        if (event != null)
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareEvent(event),
            tooltip: 'Share Event',
          ),
        // Edit button
        if (event != null)
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editEvent(context, controller, event),
            tooltip: 'Edit Event',
          ),
        // More actions menu
        if (event != null)
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, controller, value),
            itemBuilder: (context) => [
              if (event.status != EventStatus.completed)
                const PopupMenuItem(
                  value: 'complete',
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green),
                      SizedBox(width: 8),
                      Text('Mark Complete'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('Duplicate Event'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete Event', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }

  /// Build main body content
  Widget _buildBody(BuildContext context, EventDetailsController controller) {
    switch (controller.state) {
      case ControllerState.initial:
      case ControllerState.empty:
      case ControllerState.loading:
        return const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.eventsHeader),
          ),
        );
      case ControllerState.error:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                controller.errorMessage ?? 'Failed to load event',
                style: const TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        );
      case ControllerState.loaded:
        final event = controller.event;
        if (event == null) {
          return const Center(child: Text('Event not found'));
        }
        return _buildEventDetails(context, controller, event);
    }
  }

  /// Build event details content
  Widget _buildEventDetails(BuildContext context, EventDetailsController controller, EventIsar event) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Event header card
          _buildEventHeaderCard(event, controller),
          const SizedBox(height: 16),
          
          // Event details card
          _buildEventDetailsCard(event, controller),
          const SizedBox(height: 16),
          
          // Attachments section
          _buildAttachmentsSection(controller),
          const SizedBox(height: 16),
          
          // Event timeline (related events)
          _buildEventTimelineSection(controller),
          const SizedBox(height: 16),
          
          // Completion workflow (if not completed)
          if (event.status != EventStatus.completed)
            _buildCompletionWorkflowSection(context, controller, event),
        ],
      ),
    );
  }

  /// Build event header card with status and key info
  Widget _buildEventHeaderCard(EventIsar event, EventDetailsController controller) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              _getCategoryColor(event.category),
              _getCategoryColor(event.category).withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status and priority row
            Row(
              children: [
                _buildStatusChip(event.status),
                const Spacer(),
                if (event.priority != EventPriority.medium)
                  _buildPriorityChip(event.priority),
              ],
            ),
            const SizedBox(height: 12),
            
            // Title and category
            Row(
              children: [
                Icon(
                  _getCategoryIcon(event.category),
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title ?? 'Untitled Event',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        event.category.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Date and cattle info
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.schedule,
                    'Scheduled',
                    _formatDateTime(event.scheduledDate),
                  ),
                ),
                if (controller.cattle != null)
                  Expanded(
                    child: _buildInfoItem(
                      Icons.pets,
                      'Cattle',
                      controller.cattle!.name ?? 'Unknown',
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build event details card with comprehensive information
  Widget _buildEventDetailsCard(EventIsar event, EventDetailsController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Event Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.eventsHeader,
              ),
            ),
            const SizedBox(height: 16),
            
            // Description
            if (event.description?.isNotEmpty == true) ...[
              _buildDetailRow('Description', event.description!, Icons.description),
              const SizedBox(height: 12),
            ],
            
            // Notes
            if (event.notes?.isNotEmpty == true) ...[
              _buildDetailRow('Notes', event.notes!, Icons.notes),
              const SizedBox(height: 12),
            ],
            
            // Location
            if (event.location?.isNotEmpty == true) ...[
              _buildDetailRow('Location', event.location!, Icons.location_on),
              const SizedBox(height: 12),
            ],
            
            // Cost information
            if (event.estimatedCost != null || event.actualCost != null) ...[
              _buildCostSection(event),
              const SizedBox(height: 12),
            ],
            
            // Completion information
            if (event.status == EventStatus.completed) ...[
              _buildCompletionSection(event),
              const SizedBox(height: 12),
            ],
            
            // Automation information
            if (event.isAutoGenerated == true) ...[
              _buildAutomationSection(event),
              const SizedBox(height: 12),
            ],
            
            // Recurrence information
            if (event.isRecurring == true) ...[
              _buildRecurrenceSection(event),
            ],
          ],
        ),
      ),
    );
  }

  /// Build attachments section
  Widget _buildAttachmentsSection(EventDetailsController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Attachments',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.eventsHeader,
                  ),
                ),
                const Spacer(),
                Text(
                  '${controller.attachments.length}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.eventsHeader,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            EventAttachmentsWidget(
              eventBusinessId: controller.event!.businessId!,
              isEditable: true,
              onAttachmentsChanged: () {
                // Attachments are updated via stream, no need for manual refresh
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build event timeline section showing related events
  Widget _buildEventTimelineSection(EventDetailsController controller) {
    final relatedEvents = controller.relatedEvents;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Related Events',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.eventsHeader,
                  ),
                ),
                const Spacer(),
                Text(
                  '${relatedEvents.length}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.eventsHeader,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            EventTimelineWidget(
              events: relatedEvents,
              currentEventId: controller.event?.businessId,
            ),
          ],
        ),
      ),
    );
  }



  /// Build completion workflow section
  Widget _buildCompletionWorkflowSection(BuildContext context, EventDetailsController controller, EventIsar event) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Complete Event',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.eventsHeader,
              ),
            ),
            const SizedBox(height: 16),
            
            Text(
              'Mark this event as completed and add completion notes or photos.',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showCompletionDialog(context, controller, event),
                icon: const Icon(Icons.check_circle),
                label: const Text('Mark as Complete'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build floating action button
  Widget? _buildFloatingActionButton(BuildContext context, EventDetailsController controller) {
    final event = controller.event;
    if (event == null || event.status == EventStatus.completed) return null;
    
    return FloatingActionButton.extended(
      onPressed: () => _showCompletionDialog(context, controller, event),
      backgroundColor: Colors.green,
      icon: const Icon(Icons.check_circle),
      label: const Text('Complete'),
    );
  }

  /// Build status chip
  Widget _buildStatusChip(EventStatus status, {bool compact = false}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: compact ? 6 : 8,
        vertical: compact ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(compact ? 8 : 12),
        border: Border.all(
          color: _getStatusColor(status),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(status),
            size: compact ? 10 : 14,
            color: _getStatusColor(status),
          ),
          SizedBox(width: compact ? 2 : 4),
          Text(
            status.displayName,
            style: TextStyle(
              fontSize: compact ? 10 : 12,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(status),
            ),
          ),
        ],
      ),
    );
  }

  /// Build priority chip
  Widget _buildPriorityChip(EventPriority priority) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.priority_high,
            size: 12,
            color: Colors.white,
          ),
          const SizedBox(width: 2),
          Text(
            priority.displayName,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build info item for header card
  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: Colors.white, size: 16),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white.withValues(alpha: 0.8),
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: AppColors.eventsHeader),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build cost section
  Widget _buildCostSection(EventIsar event) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.attach_money, size: 20, color: AppColors.eventsHeader),
            SizedBox(width: 12),
            Text(
              'Cost Information',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.only(left: 32),
          child: Column(
            children: [
              if (event.estimatedCost != null)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Estimated Cost:'),
                    Text(
                      '\$${event.estimatedCost!.toStringAsFixed(2)}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              if (event.actualCost != null)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Actual Cost:'),
                    Text(
                      '\$${event.actualCost!.toStringAsFixed(2)}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build completion section
  Widget _buildCompletionSection(EventIsar event) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.check_circle, size: 20, color: Colors.green),
            SizedBox(width: 12),
            Text(
              'Completion Details',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.only(left: 32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (event.completedDate != null)
                Text('Completed: ${_formatDateTime(event.completedDate)}'),
              if (event.completedBy?.isNotEmpty == true)
                Text('Completed by: ${event.completedBy}'),
              if (event.completionNotes?.isNotEmpty == true) ...[
                const SizedBox(height: 4),
                Text(
                  'Notes: ${event.completionNotes}',
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Build automation section
  Widget _buildAutomationSection(EventIsar event) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Icon(Icons.auto_awesome, size: 20, color: Colors.blue),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Auto-Generated Event',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                'Created automatically from ${event.sourceModule ?? 'system'}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build recurrence section
  Widget _buildRecurrenceSection(EventIsar event) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Icon(Icons.repeat, size: 20, color: Colors.purple),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Recurring Event',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                'Repeats ${event.recurrencePattern.displayName}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Handle menu actions
  void _handleMenuAction(BuildContext context, EventDetailsController controller, String action) {
    switch (action) {
      case 'complete':
        _showCompletionDialog(context, controller, controller.event!);
        break;
      case 'duplicate':
        _duplicateEvent(context, controller.event!);
        break;
      case 'delete':
        _deleteEvent(context, controller);
        break;
    }
  }

  /// Show completion dialog
  void _showCompletionDialog(BuildContext context, EventDetailsController controller, EventIsar event) {
    final completionNotesController = TextEditingController();
    final completedByController = TextEditingController();
    final actualCostController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Event'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: completedByController,
                decoration: const InputDecoration(
                  labelText: 'Completed By',
                  hintText: 'Who completed this event?',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: actualCostController,
                decoration: const InputDecoration(
                  labelText: 'Actual Cost',
                  hintText: 'Enter actual cost (optional)',
                  prefixText: '\$',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: completionNotesController,
                decoration: const InputDecoration(
                  labelText: 'Completion Notes',
                  hintText: 'Add any completion notes...',
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await controller.completeEvent(
                  completedBy: completedByController.text.trim().isEmpty 
                      ? null : completedByController.text.trim(),
                  completionNotes: completionNotesController.text.trim().isEmpty 
                      ? null : completionNotesController.text.trim(),
                  actualCost: actualCostController.text.trim().isEmpty 
                      ? null : double.tryParse(actualCostController.text.trim()),
                );
                if (context.mounted) {
                  Navigator.of(context).pop();
                  MessageUtils.showSuccess(context, 'Event completed successfully');
                }
              } catch (e) {
                if (context.mounted) {
                  MessageUtils.showError(context, 'Failed to complete event: $e');
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Complete Event'),
          ),
        ],
      ),
    );
  }

  /// Edit event
  void _editEvent(BuildContext context, EventDetailsController controller, EventIsar event) {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattle: controller.unfilteredCattle,
        eventTypes: controller.unfilteredEventTypes,
        existingEvent: event,
        onSave: (updatedEvent) async {
          try {
            await controller.updateEvent(updatedEvent);
            if (context.mounted) {
              MessageUtils.showSuccess(context, 'Event updated successfully');
            }
          } catch (e) {
            if (context.mounted) {
              MessageUtils.showError(context, 'Failed to update event: $e');
            }
          }
        },
      ),
    );
  }

  /// Duplicate event
  void _duplicateEvent(BuildContext context, EventIsar event) {
    // Create a copy of the event with new business ID and scheduled for tomorrow
    final duplicatedEvent = EventIsar.create(
      title: '${event.title} (Copy)',
      scheduledDate: DateTime.now().add(const Duration(days: 1)),
      cattleTagId: event.cattleTagId!,
      eventTypeId: event.eventTypeId!,
      category: event.category,
      description: event.description,
      notes: event.notes,
      priority: event.priority,
      estimatedCost: event.estimatedCost,
      location: event.location,
    );

    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattle: Provider.of<EventDetailsController>(context, listen: false).unfilteredCattle,
        eventTypes: Provider.of<EventDetailsController>(context, listen: false).unfilteredEventTypes,
        existingEvent: duplicatedEvent,
        onSave: (newEvent) async {
          try {
            final controller = Provider.of<EventDetailsController>(context, listen: false);
            await controller.updateEvent(newEvent);
            if (context.mounted) {
              MessageUtils.showSuccess(context, 'Event duplicated successfully');
            }
          } catch (e) {
            if (context.mounted) {
              MessageUtils.showError(context, 'Failed to duplicate event: $e');
            }
          }
        },
      ),
    );
  }

  /// Delete event
  void _deleteEvent(BuildContext context, EventDetailsController controller) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${controller.event?.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await controller.deleteEvent();
        if (context.mounted) {
          Navigator.of(context).pop(); // Go back to previous screen
          MessageUtils.showSuccess(context, 'Event deleted successfully');
        }
      } catch (e) {
        if (context.mounted) {
          MessageUtils.showError(context, 'Failed to delete event: $e');
        }
      }
    }
  }

  /// Share event
  void _shareEvent(EventIsar event) {
    final text = '''
Event: ${event.title}
Category: ${event.category.displayName}
Status: ${event.status.displayName}
Scheduled: ${_formatDateTime(event.scheduledDate)}
${event.description?.isNotEmpty == true ? '\nDescription: ${event.description}' : ''}
${event.notes?.isNotEmpty == true ? '\nNotes: ${event.notes}' : ''}
${event.location?.isNotEmpty == true ? '\nLocation: ${event.location}' : ''}
''';

    Share.share(text, subject: 'Event: ${event.title}');
  }

  /// Get category color
  Color _getCategoryColor(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return AppColors.healthHeader;
      case EventCategory.breeding:
        return AppColors.breedingHeader;
      case EventCategory.feeding:
        return Colors.green;
      case EventCategory.management:
        return Colors.blue;
      case EventCategory.maintenance:
        return Colors.orange;
      case EventCategory.financial:
        return AppColors.transactionHeader;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return AppColors.eventsHeader;
    }
  }

  /// Get category icon
  IconData _getCategoryIcon(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return Icons.medical_services;
      case EventCategory.breeding:
        return Icons.favorite;
      case EventCategory.feeding:
        return Icons.restaurant;
      case EventCategory.management:
        return Icons.business;
      case EventCategory.maintenance:
        return Icons.build;
      case EventCategory.financial:
        return Icons.attach_money;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return Icons.event;
    }
  }

  /// Get status color
  Color _getStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.scheduled:
        return Colors.blue;
      case EventStatus.inProgress:
        return Colors.orange;
      case EventStatus.completed:
        return Colors.green;
      case EventStatus.cancelled:
        return Colors.grey;
      case EventStatus.overdue:
        return Colors.red;
      case EventStatus.missed:
        return Colors.red;
    }
  }

  /// Get status icon
  IconData _getStatusIcon(EventStatus status) {
    switch (status) {
      case EventStatus.scheduled:
        return Icons.schedule;
      case EventStatus.inProgress:
        return Icons.play_circle;
      case EventStatus.completed:
        return Icons.check_circle;
      case EventStatus.cancelled:
        return Icons.cancel;
      case EventStatus.overdue:
        return Icons.warning;
      case EventStatus.missed:
        return Icons.error;
    }
  }

  /// Format date and time for display
  String _formatDateTime(DateTime? date) {
    if (date == null) return 'No Date';
    return DateFormat('MMM dd, yyyy HH:mm').format(date);
  }
}