import '../models/weight_record_isar.dart';
import '../models/weight_insights_models.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Pure insights service for module-wide weight analytics
/// Following the cattle module pattern with static methods and comprehensive insights
class WeightInsightsService {
  
  /// Calculate comprehensive insights for the main weight screen
  /// This replaces the complex logic from the old WeightController
  static WeightInsightsData calculateInsights(
    List<WeightRecordIsar> allRecords, 
    List<CattleIsar> allCattle
  ) {
    if (allRecords.isEmpty) {
      return WeightInsightsData.empty;
    }

    final performance = _calculatePerformanceInsights(allRecords, allCattle);
    final health = _calculateHealthInsights(allRecords, allCattle);
    final trends = _calculateTrendInsights(allRecords);
    final recommendations = _generateRecommendations(performance, health, trends);

    return WeightInsightsData(
      performance: performance,
      health: health,
      trends: trends,
      recommendations: recommendations,
      lastUpdated: DateTime.now(),
    );
  }

  /// Calculate performance insights across all cattle
  static PerformanceInsights _calculatePerformanceInsights(
    List<WeightRecordIsar> records,
    List<CattleIsar> cattle
  ) {
    if (records.isEmpty || cattle.isEmpty) {
      return PerformanceInsights.empty;
    }

    // Group records by cattle for performance analysis
    final Map<int, List<WeightRecordIsar>> recordsByCattle = {};
    for (final record in records) {
      final cattleId = record.cattle.value?.id;
      if (cattleId != null) {
        recordsByCattle.putIfAbsent(cattleId, () => []).add(record);
      }
    }

    // Calculate daily gains for each cattle
    final Map<int, double> dailyGainsByCattle = {};
    double totalDailyGain = 0.0;
    int gainingCattle = 0;
    int losingCattle = 0;

    for (final entry in recordsByCattle.entries) {
      final cattleRecords = entry.value;
      if (cattleRecords.length >= 2) {
        final dailyGain = _calculateDailyGainForCattle(cattleRecords);
        dailyGainsByCattle[entry.key] = dailyGain;
        totalDailyGain += dailyGain;

        if (dailyGain > 0.1) gainingCattle++;
        if (dailyGain < -0.1) losingCattle++;
      }
    }

    // Find top performer
    String topPerformerName = 'N/A';
    String topPerformerId = '';
    double topPerformerGainRate = 0.0;

    if (dailyGainsByCattle.isNotEmpty) {
      final topEntry = dailyGainsByCattle.entries
          .reduce((a, b) => a.value > b.value ? a : b);
      
      final topCattle = cattle.firstWhere(
        (c) => c.id == topEntry.key,
        orElse: () => CattleIsar(),
      );
      
      topPerformerName = topCattle.name ?? 'Unknown';
      topPerformerId = topCattle.businessId ?? '';
      topPerformerGainRate = topEntry.value;
    }

    final averageDailyGain = dailyGainsByCattle.isNotEmpty 
        ? totalDailyGain / dailyGainsByCattle.length 
        : 0.0;

    // Determine overall growth trend
    String growthTrend;
    String growthTrendDescription;
    
    if (averageDailyGain > 0.5) {
      growthTrend = 'Excellent';
      growthTrendDescription = 'Herd showing strong weight gains';
    } else if (averageDailyGain > 0.2) {
      growthTrend = 'Good';
      growthTrendDescription = 'Herd showing steady growth';
    } else if (averageDailyGain > -0.1) {
      growthTrend = 'Stable';
      growthTrendDescription = 'Herd maintaining weight';
    } else {
      growthTrend = 'Concerning';
      growthTrendDescription = 'Herd showing weight loss';
    }

    return PerformanceInsights(
      topPerformerName: topPerformerName,
      topPerformerId: topPerformerId,
      topPerformerGainRate: topPerformerGainRate,
      averageDailyGain: averageDailyGain,
      growthTrend: growthTrend,
      growthTrendDescription: growthTrendDescription,
      totalGainingCattle: gainingCattle,
      totalLosingCattle: losingCattle,
    );
  }

  /// Calculate health insights based on weight patterns
  static HealthInsights _calculateHealthInsights(
    List<WeightRecordIsar> records,
    List<CattleIsar> cattle
  ) {
    if (records.isEmpty) {
      return HealthInsights.empty;
    }

    // Calculate average body condition score
    final bodyConditionScores = records
        .where((r) => r.bodyConditionScore != null)
        .map((r) => r.bodyConditionScore!)
        .toList();

    final averageBodyCondition = bodyConditionScores.isNotEmpty
        ? bodyConditionScores.reduce((a, b) => a + b) / bodyConditionScores.length
        : 0.0;

    // Analyze weight consistency and health alerts
    int healthAlerts = 0;
    List<String> alertReasons = [];
    int underweightCattle = 0;
    int overweightCattle = 0;
    int normalWeightCattle = 0;

    // Group by cattle and analyze patterns
    final Map<int, List<WeightRecordIsar>> recordsByCattle = {};
    for (final record in records) {
      final cattleId = record.cattle.value?.id;
      if (cattleId != null) {
        recordsByCattle.putIfAbsent(cattleId, () => []).add(record);
      }
    }

    for (final cattleRecords in recordsByCattle.values) {
      if (cattleRecords.isNotEmpty) {
        final latestRecord = cattleRecords.first; // Assuming sorted by date desc
        
        // Simple weight classification (this could be enhanced with breed standards)
        if (latestRecord.weight < 200) {
          underweightCattle++;
        } else if (latestRecord.weight > 800) {
          overweightCattle++;
        } else {
          normalWeightCattle++;
        }

        // Check for rapid weight loss
        if (cattleRecords.length >= 2) {
          final recent = cattleRecords[0];
          final previous = cattleRecords[1];
          final weightChange = recent.weight - previous.weight;
          
          if (weightChange < -20) { // Lost more than 20kg
            healthAlerts++;
            alertReasons.add('Rapid weight loss detected');
          }
        }
      }
    }

    // Determine consistency rating
    String consistencyRating;
    String consistencyDescription;
    
    if (healthAlerts == 0) {
      consistencyRating = 'Excellent';
      consistencyDescription = 'No health concerns detected';
    } else if (healthAlerts <= 2) {
      consistencyRating = 'Good';
      consistencyDescription = 'Minor concerns detected';
    } else {
      consistencyRating = 'Needs Attention';
      consistencyDescription = 'Multiple health alerts detected';
    }

    return HealthInsights(
      averageBodyCondition: averageBodyCondition,
      consistencyRating: consistencyRating,
      consistencyDescription: consistencyDescription,
      healthAlerts: healthAlerts,
      alertReasons: alertReasons,
      underweightCattle: underweightCattle,
      overweightCattle: overweightCattle,
      normalWeightCattle: normalWeightCattle,
    );
  }

  /// Calculate trend insights over time
  static TrendInsights _calculateTrendInsights(List<WeightRecordIsar> records) {
    if (records.length < 10) {
      return TrendInsights.empty;
    }

    // Sort records by date
    final sortedRecords = List<WeightRecordIsar>.from(records)
      ..sort((a, b) => (a.measurementDate ?? DateTime.now())
          .compareTo(b.measurementDate ?? DateTime.now()));

    // Calculate monthly trends (simplified)
    final recentRecords = sortedRecords.take(30).toList(); // Last 30 records
    final olderRecords = sortedRecords.skip(30).take(30).toList(); // Previous 30

    final recentAverage = recentRecords.isNotEmpty
        ? recentRecords.map((r) => r.weight).reduce((a, b) => a + b) / recentRecords.length
        : 0.0;

    final olderAverage = olderRecords.isNotEmpty
        ? olderRecords.map((r) => r.weight).reduce((a, b) => a + b) / olderRecords.length
        : 0.0;

    final monthlyChange = recentAverage - olderAverage;
    
    String trendDirection;
    if (monthlyChange > 5) {
      trendDirection = 'Increasing';
    } else if (monthlyChange < -5) {
      trendDirection = 'Decreasing';
    } else {
      trendDirection = 'Stable';
    }

    return TrendInsights(
      monthlyChange: monthlyChange,
      trendDirection: trendDirection,
      seasonalPattern: 'Analysis pending', // Could be enhanced
      predictedNextMonth: recentAverage + (monthlyChange * 0.5), // Simple prediction
    );
  }

  /// Generate actionable recommendations based on insights
  static List<WeightRecommendation> _generateRecommendations(
    PerformanceInsights performance,
    HealthInsights health,
    TrendInsights trends,
  ) {
    final recommendations = <WeightRecommendation>[];

    // Performance-based recommendations
    if (performance.averageDailyGain < 0.2) {
      recommendations.add(const WeightRecommendation(
        title: 'Improve Feed Quality',
        description: 'Consider upgrading feed quality to improve daily weight gains',
        priority: 'High',
        category: 'Nutrition',
        actionItems: [
          'Evaluate current feed composition',
          'Consider higher protein feed',
          'Consult with nutritionist',
        ],
      ));
    }

    // Health-based recommendations
    if (health.healthAlerts > 0) {
      recommendations.add(const WeightRecommendation(
        title: 'Health Check Required',
        description: 'Schedule veterinary examination for cattle showing weight loss',
        priority: 'High',
        category: 'Health',
        actionItems: [
          'Schedule veterinary examination',
          'Check for parasites',
          'Review vaccination status',
        ],
      ));
    }

    // Trend-based recommendations
    if (trends.trendDirection == 'Decreasing') {
      recommendations.add(const WeightRecommendation(
        title: 'Review Feeding Program',
        description: 'Declining weight trends suggest need for feeding program review',
        priority: 'Medium',
        category: 'Management',
        actionItems: [
          'Analyze feeding schedule',
          'Check feed quality',
          'Monitor water intake',
        ],
      ));
    }

    return recommendations;
  }

  /// Helper method to calculate daily gain for a single cattle
  static double _calculateDailyGainForCattle(List<WeightRecordIsar> records) {
    if (records.length < 2) return 0.0;

    final sortedRecords = List<WeightRecordIsar>.from(records)
      ..sort((a, b) => (a.measurementDate ?? DateTime.now())
          .compareTo(b.measurementDate ?? DateTime.now()));

    final latest = sortedRecords.last;
    final earliest = sortedRecords.first;

    if (latest.measurementDate != null && earliest.measurementDate != null) {
      final days = latest.measurementDate!
          .difference(earliest.measurementDate!)
          .inDays;
      
      if (days > 0) {
        return (latest.weight - earliest.weight) / days;
      }
    }

    return 0.0;
  }
}
