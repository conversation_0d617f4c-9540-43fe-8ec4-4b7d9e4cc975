import 'package:isar/isar.dart';
import 'notification_priority.dart';
import 'dart:convert';

part 'notification_settings_isar.g.dart';

/// Enhanced notification settings model with granular category controls and FCM integration
@collection
class NotificationSettingsIsar {
  /// Auto-incremented ID for Isar
  Id id = Isar.autoIncrement;
  
  /// Unique business identifier for the settings
  @Index(unique: true)
  String? businessId;
  
  /// User business ID for multi-user environments
  @Index()
  String? userBusinessId;
  
  // Global settings
  /// Master toggle for all notifications
  bool notificationsEnabled = true;
  
  /// Toggle for push notifications
  bool pushNotificationsEnabled = true;
  
  /// Toggle for in-app notifications
  bool inAppNotificationsEnabled = true;
  
  /// Toggle for email notifications
  bool emailNotificationsEnabled = false;
  
  /// Toggle for SMS notifications
  bool smsNotificationsEnabled = false;
  
  // Category-specific settings
  /// Serialized version of categoryEnabled for Isar storage
  String? categoryEnabledJson;
  
  /// Serialized version of categoryPriorities for Isar storage
  String? categoryPrioritiesJson;
  
  // Timing settings
  /// Serialized version of reminderLeadTimes for Isar storage
  String? reminderLeadTimesJson;
  
  // Quiet hours
  /// Toggle for quiet hours
  bool quietHoursEnabled = false;
  
  /// Start hour for quiet hours (24-hour format)
  int quietHoursStart = 22; // 10 PM
  
  /// End hour for quiet hours (24-hour format)
  int quietHoursEnd = 7; // 7 AM
  
  // Emergency overrides
  /// Toggle for emergency override
  bool emergencyOverrideEnabled = true;
  
  /// Serialized version of emergencyKeywords for Isar storage
  String? emergencyKeywordsJson;
  
  // Delivery preferences
  /// Frequency for notification digests (none, daily, weekly)
  String digestFrequency = 'none';
  
  /// Hour of the day for digest delivery (24-hour format)
  int digestTime = 9; // 9 AM
  
  // Retention settings
  /// Maximum number of notifications to keep
  int maxNotificationsToKeep = 1000;
  
  /// Number of days after which notifications are auto-deleted
  int autoDeleteAfterDays = 90;
  
  /// Toggle for auto-deleting read notifications
  bool autoDeleteReadNotifications = true;
  
  /// Number of days after which read notifications are auto-deleted
  int autoDeleteReadAfterDays = 30;
  
  // FCM settings
  /// Firebase Cloud Messaging token
  String? fcmToken;
  
  /// When the FCM token was last updated
  DateTime? fcmTokenUpdatedAt;
  
  /// Serialized version of subscribedTopics for Isar storage
  String? subscribedTopicsJson;
  
  /// When the settings were created
  DateTime? createdAt;
  
  /// When the settings were last updated
  DateTime? updatedAt;
  
  /// Constructor
  NotificationSettingsIsar({
    this.businessId,
    this.userBusinessId,
    this.notificationsEnabled = true,
    this.pushNotificationsEnabled = true,
    this.inAppNotificationsEnabled = true,
    this.emailNotificationsEnabled = false,
    this.smsNotificationsEnabled = false,
    Map<String, bool>? categoryEnabled,
    Map<String, NotificationPriority>? categoryPriorities,
    Map<String, int>? reminderLeadTimes,
    this.quietHoursEnabled = false,
    this.quietHoursStart = 22,
    this.quietHoursEnd = 7,
    this.emergencyOverrideEnabled = true,
    List<String>? emergencyKeywords,
    this.digestFrequency = 'none',
    this.digestTime = 9,
    this.maxNotificationsToKeep = 1000,
    this.autoDeleteAfterDays = 90,
    this.autoDeleteReadNotifications = true,
    this.autoDeleteReadAfterDays = 30,
    this.fcmToken,
    this.fcmTokenUpdatedAt,
    List<String>? subscribedTopics,
    this.createdAt,
    this.updatedAt,
  }) {
    // Set default values and serialize JSON fields
    if (categoryEnabled != null) {
      setCategoryEnabled(categoryEnabled);
    }
    
    if (categoryPriorities != null) {
      setCategoryPriorities(categoryPriorities);
    }
    
    if (reminderLeadTimes != null) {
      setReminderLeadTimes(reminderLeadTimes);
    }
    
    if (emergencyKeywords != null) {
      setEmergencyKeywords(emergencyKeywords);
    }
    
    if (subscribedTopics != null) {
      setSubscribedTopics(subscribedTopics);
    }
  }
  
  // Getters and setters for JSON fields
  
  /// Get category enabled map
  Map<String, bool> getCategoryEnabled() {
    if (categoryEnabledJson == null || categoryEnabledJson!.isEmpty) {
      return {
        'health': true,
        'breeding': true,
        'milk': true,
        'weight': true,
        'events': true,
        'system': true,
      };
    }
    
    try {
      final Map<String, dynamic> decoded = jsonDecode(categoryEnabledJson!);
      return decoded.map((key, value) => MapEntry(key, value as bool));
    } catch (e) {
      return {
        'health': true,
        'breeding': true,
        'milk': true,
        'weight': true,
        'events': true,
        'system': true,
      };
    }
  }
  
  /// Set category enabled map
  void setCategoryEnabled(Map<String, bool> value) {
    categoryEnabledJson = jsonEncode(value);
  }
  
  /// Get category priorities map
  Map<String, NotificationPriority> getCategoryPriorities() {
    if (categoryPrioritiesJson == null || categoryPrioritiesJson!.isEmpty) {
      return {
        'health': NotificationPriority.high,
        'breeding': NotificationPriority.high,
        'milk': NotificationPriority.medium,
        'weight': NotificationPriority.medium,
        'events': NotificationPriority.medium,
        'system': NotificationPriority.low,
      };
    }
    
    try {
      final Map<String, dynamic> decoded = jsonDecode(categoryPrioritiesJson!);
      return decoded.map((key, value) => 
          MapEntry(key, NotificationPriority.values[value as int]));
    } catch (e) {
      return {
        'health': NotificationPriority.high,
        'breeding': NotificationPriority.high,
        'milk': NotificationPriority.medium,
        'weight': NotificationPriority.medium,
        'events': NotificationPriority.medium,
        'system': NotificationPriority.low,
      };
    }
  }
  
  /// Set category priorities map
  void setCategoryPriorities(Map<String, NotificationPriority> value) {
    final encoded = value.map((key, value) => MapEntry(key, value.index));
    categoryPrioritiesJson = jsonEncode(encoded);
  }
  
  /// Get reminder lead times map
  Map<String, int> getReminderLeadTimes() {
    if (reminderLeadTimesJson == null || reminderLeadTimesJson!.isEmpty) {
      return {
        'health': 24,
        'breeding': 12,
        'events': 24,
      };
    }
    
    try {
      final Map<String, dynamic> decoded = jsonDecode(reminderLeadTimesJson!);
      return decoded.map((key, value) => MapEntry(key, value as int));
    } catch (e) {
      return {
        'health': 24,
        'breeding': 12,
        'events': 24,
      };
    }
  }
  
  /// Set reminder lead times map
  void setReminderLeadTimes(Map<String, int> value) {
    reminderLeadTimesJson = jsonEncode(value);
  }
  
  /// Get emergency keywords list
  List<String> getEmergencyKeywords() {
    if (emergencyKeywordsJson == null || emergencyKeywordsJson!.isEmpty) {
      return ['emergency', 'critical', 'urgent'];
    }
    
    try {
      final List<dynamic> decoded = jsonDecode(emergencyKeywordsJson!);
      return decoded.map((item) => item as String).toList();
    } catch (e) {
      return ['emergency', 'critical', 'urgent'];
    }
  }
  
  /// Set emergency keywords list
  void setEmergencyKeywords(List<String> value) {
    emergencyKeywordsJson = jsonEncode(value);
  }
  
  /// Get subscribed topics list
  List<String> getSubscribedTopics() {
    if (subscribedTopicsJson == null || subscribedTopicsJson!.isEmpty) {
      return [];
    }
    
    try {
      final List<dynamic> decoded = jsonDecode(subscribedTopicsJson!);
      return decoded.map((item) => item as String).toList();
    } catch (e) {
      return [];
    }
  }
  
  /// Set subscribed topics list
  void setSubscribedTopics(List<String> value) {
    subscribedTopicsJson = jsonEncode(value);
  }
  
  /// Convert to a map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'businessId': businessId,
      'userBusinessId': userBusinessId,
      'notificationsEnabled': notificationsEnabled,
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'inAppNotificationsEnabled': inAppNotificationsEnabled,
      'emailNotificationsEnabled': emailNotificationsEnabled,
      'smsNotificationsEnabled': smsNotificationsEnabled,
      'categoryEnabledJson': categoryEnabledJson,
      'categoryPrioritiesJson': categoryPrioritiesJson,
      'reminderLeadTimesJson': reminderLeadTimesJson,
      'quietHoursEnabled': quietHoursEnabled,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
      'emergencyOverrideEnabled': emergencyOverrideEnabled,
      'emergencyKeywordsJson': emergencyKeywordsJson,
      'digestFrequency': digestFrequency,
      'digestTime': digestTime,
      'maxNotificationsToKeep': maxNotificationsToKeep,
      'autoDeleteAfterDays': autoDeleteAfterDays,
      'autoDeleteReadNotifications': autoDeleteReadNotifications,
      'autoDeleteReadAfterDays': autoDeleteReadAfterDays,
      'fcmToken': fcmToken,
      'fcmTokenUpdatedAt': fcmTokenUpdatedAt?.toIso8601String(),
      'subscribedTopicsJson': subscribedTopicsJson,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  /// Create from a map (JSON deserialization)
  factory NotificationSettingsIsar.fromMap(Map<String, dynamic> map) {
    return NotificationSettingsIsar(
      businessId: map['businessId'],
      userBusinessId: map['userBusinessId'],
      notificationsEnabled: map['notificationsEnabled'] ?? true,
      pushNotificationsEnabled: map['pushNotificationsEnabled'] ?? true,
      inAppNotificationsEnabled: map['inAppNotificationsEnabled'] ?? true,
      emailNotificationsEnabled: map['emailNotificationsEnabled'] ?? false,
      smsNotificationsEnabled: map['smsNotificationsEnabled'] ?? false,
      quietHoursEnabled: map['quietHoursEnabled'] ?? false,
      quietHoursStart: map['quietHoursStart'] ?? 22,
      quietHoursEnd: map['quietHoursEnd'] ?? 7,
      emergencyOverrideEnabled: map['emergencyOverrideEnabled'] ?? true,
      digestFrequency: map['digestFrequency'] ?? 'none',
      digestTime: map['digestTime'] ?? 9,
      maxNotificationsToKeep: map['maxNotificationsToKeep'] ?? 1000,
      autoDeleteAfterDays: map['autoDeleteAfterDays'] ?? 90,
      autoDeleteReadNotifications: map['autoDeleteReadNotifications'] ?? true,
      autoDeleteReadAfterDays: map['autoDeleteReadAfterDays'] ?? 30,
      fcmToken: map['fcmToken'],
      fcmTokenUpdatedAt: map['fcmTokenUpdatedAt'] != null 
          ? DateTime.parse(map['fcmTokenUpdatedAt']) 
          : null,
      createdAt: map['createdAt'] != null 
          ? DateTime.parse(map['createdAt']) 
          : null,
      updatedAt: map['updatedAt'] != null 
          ? DateTime.parse(map['updatedAt']) 
          : null,
    )
      ..categoryEnabledJson = map['categoryEnabledJson']
      ..categoryPrioritiesJson = map['categoryPrioritiesJson']
      ..reminderLeadTimesJson = map['reminderLeadTimesJson']
      ..emergencyKeywordsJson = map['emergencyKeywordsJson']
      ..subscribedTopicsJson = map['subscribedTopicsJson'];
  }

  /// Prepare object for saving to Isar (placeholder for consistency)
  void prepareForSave() {
    // JSON fields are already serialized through setters
  }
  
  /// Process object after loading from Isar (placeholder for consistency)
  void processAfterLoad() {
    // JSON fields are deserialized through getters
  }
}