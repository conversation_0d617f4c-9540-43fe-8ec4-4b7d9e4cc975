import 'package:isar/isar.dart';
import 'dart:convert';
import 'notification_priority.dart';
import 'notification_status.dart';

part 'notification_isar.g.dart';

/// Enhanced notification model with rich metadata, associations, and offline sync support
@collection
class NotificationIsar {
  /// Auto-incremented ID for Isar
  Id id = Isar.autoIncrement;
  
  /// Unique business identifier for the notification
  @Index(unique: true)
  String? businessId;
  
  // Core properties
  /// Title of the notification
  String? title;
  
  /// Main content/message of the notification
  String? message;
  
  /// Category of the notification (health, breeding, milk, weight, events, system)
  @Index()
  String? category;
  
  /// Type of notification (reminder, alert, info, warning, error)
  @Index()
  String? type;
  
  /// Priority level of the notification
  @enumerated
  NotificationPriority priority = NotificationPriority.medium;
  
  /// Current status of the notification
  @enumerated
  NotificationStatus status = NotificationStatus.unread;
  
  // Associations
  /// Related cattle ID if applicable
  @Index()
  String? cattleId;
  
  /// Related record ID if applicable (health record, breeding record, etc.)
  String? relatedRecordId;
  
  /// Type of the related record
  String? relatedRecordType;
  
  /// Link to event system
  String? eventId;
  
  // Rich content
  /// URL to an image associated with the notification
  String? imageUrl;
  
  /// Deep link for navigation within the app
  String? actionUrl;
  
  /// Flexible metadata for additional information
  @ignore
  Map<String, String>? customData;
  
  /// Serialized version of customData for Isar storage
  String? customDataJson;
  
  // Scheduling
  /// When the notification is scheduled to be shown
  DateTime? scheduledFor;
  
  /// When the notification expires
  DateTime? expiresAt;
  
  /// Whether the notification recurs
  bool isRecurring = false;
  
  /// Pattern for recurring notifications (daily, weekly, monthly, custom)
  String? recurringPattern;
  
  // Timestamps
  /// When the notification was created
  @Index()
  DateTime? createdAt;
  
  /// When the notification was read
  DateTime? readAt;

  /// Whether the notification has been read
  bool get isRead => status == NotificationStatus.read || readAt != null;

  /// Set read status
  set isRead(bool value) {
    if (value) {
      status = NotificationStatus.read;
      readAt ??= DateTime.now();
    } else {
      status = NotificationStatus.unread;
      readAt = null;
    }
  }

  /// When action was taken on the notification
  DateTime? actionedAt;
  
  /// When the notification was last updated
  DateTime? updatedAt;
  
  // Push notification tracking
  /// ID of the push notification if sent
  String? pushNotificationId;
  
  /// Whether a push notification was sent
  bool pushNotificationSent = false;
  
  /// When the push notification was sent
  DateTime? pushNotificationSentAt;
  
  // Offline sync
  /// Whether the notification is synced with the server
  bool isSynced = true;
  
  /// When the notification was last synced
  DateTime? lastSyncAt;
  
  /// Data for resolving sync conflicts
  String? syncConflictData;
  
  /// Constructor
  NotificationIsar({
    this.businessId,
    this.title,
    this.message,
    this.category,
    this.type,
    this.priority = NotificationPriority.medium,
    this.status = NotificationStatus.unread,
    this.cattleId,
    this.relatedRecordId,
    this.relatedRecordType,
    this.eventId,
    this.imageUrl,
    this.actionUrl,
    Map<String, String>? customData,
    this.scheduledFor,
    this.expiresAt,
    this.isRecurring = false,
    this.recurringPattern,
    this.createdAt,
    this.readAt,
    this.actionedAt,
    this.updatedAt,
    this.pushNotificationId,
    this.pushNotificationSent = false,
    this.pushNotificationSentAt,
    this.isSynced = true,
    this.lastSyncAt,
    this.syncConflictData,
  }) {
    if (customData != null) {
      this.customData = customData;
      _serializeCustomData();
    }
  }
  
  /// Serialize customData to JSON
  void _serializeCustomData() {
    if (customData != null) {
      try {
        customDataJson = jsonEncode(customData);
      } catch (e) {
        customDataJson = null;
      }
    } else {
      customDataJson = null;
    }
  }
  
  /// Deserialize customData from JSON
  void _deserializeCustomData() {
    if (customDataJson != null && customDataJson!.isNotEmpty) {
      try {
        final Map<String, dynamic> decoded = jsonDecode(customDataJson!);
        customData = decoded.map((key, value) => MapEntry(key, value.toString()));
      } catch (e) {
        customData = null;
      }
    } else {
      customData = null;
    }
  }
  
  /// Prepare object for saving to Isar
  void prepareForSave() {
    _serializeCustomData();
  }
  
  /// Process object after loading from Isar
  void processAfterLoad() {
    _deserializeCustomData();
  }
  
  /// Convert to a map for JSON serialization
  Map<String, dynamic> toMap() {
    // Ensure customData is serialized
    _serializeCustomData();
    
    return {
      'businessId': businessId,
      'title': title,
      'message': message,
      'category': category,
      'type': type,
      'priority': priority.index,
      'status': status.index,
      'cattleId': cattleId,
      'relatedRecordId': relatedRecordId,
      'relatedRecordType': relatedRecordType,
      'eventId': eventId,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'customDataJson': customDataJson,
      'scheduledFor': scheduledFor?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'isRecurring': isRecurring,
      'recurringPattern': recurringPattern,
      'createdAt': createdAt?.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'actionedAt': actionedAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'pushNotificationId': pushNotificationId,
      'pushNotificationSent': pushNotificationSent,
      'pushNotificationSentAt': pushNotificationSentAt?.toIso8601String(),
      'isSynced': isSynced,
      'lastSyncAt': lastSyncAt?.toIso8601String(),
      'syncConflictData': syncConflictData,
    };
  }
  
  /// Create from JSON string
  factory NotificationIsar.fromJson(String jsonString) {
    final Map<String, dynamic> map = jsonDecode(jsonString);
    return NotificationIsar.fromMap(map);
  }

  /// Convert to JSON string
  String toJson() {
    return jsonEncode(toMap());
  }

  /// Create from a map (JSON deserialization)
  factory NotificationIsar.fromMap(Map<String, dynamic> map) {
    final notification = NotificationIsar(
      businessId: map['businessId'],
      title: map['title'],
      message: map['message'],
      category: map['category'],
      type: map['type'],
      priority: map['priority'] != null 
          ? NotificationPriority.values[map['priority']] 
          : NotificationPriority.medium,
      status: map['status'] != null 
          ? NotificationStatus.values[map['status']] 
          : NotificationStatus.unread,
      cattleId: map['cattleId'],
      relatedRecordId: map['relatedRecordId'],
      relatedRecordType: map['relatedRecordType'],
      eventId: map['eventId'],
      imageUrl: map['imageUrl'],
      actionUrl: map['actionUrl'],
      scheduledFor: map['scheduledFor'] != null 
          ? DateTime.parse(map['scheduledFor']) 
          : null,
      expiresAt: map['expiresAt'] != null 
          ? DateTime.parse(map['expiresAt']) 
          : null,
      isRecurring: map['isRecurring'] ?? false,
      recurringPattern: map['recurringPattern'],
      createdAt: map['createdAt'] != null 
          ? DateTime.parse(map['createdAt']) 
          : null,
      readAt: map['readAt'] != null 
          ? DateTime.parse(map['readAt']) 
          : null,
      actionedAt: map['actionedAt'] != null 
          ? DateTime.parse(map['actionedAt']) 
          : null,
      updatedAt: map['updatedAt'] != null 
          ? DateTime.parse(map['updatedAt']) 
          : null,
      pushNotificationId: map['pushNotificationId'],
      pushNotificationSent: map['pushNotificationSent'] ?? false,
      pushNotificationSentAt: map['pushNotificationSentAt'] != null 
          ? DateTime.parse(map['pushNotificationSentAt']) 
          : null,
      isSynced: map['isSynced'] ?? true,
      lastSyncAt: map['lastSyncAt'] != null 
          ? DateTime.parse(map['lastSyncAt']) 
          : null,
      syncConflictData: map['syncConflictData'],
    );
    
    // Set JSON fields directly
    notification.customDataJson = map['customDataJson'];
    
    // Deserialize JSON fields
    notification.processAfterLoad();
    
    return notification;
  }
  
  /// Create a copy with modified fields
  NotificationIsar copyWith({
    String? businessId,
    String? title,
    String? message,
    String? category,
    String? type,
    NotificationPriority? priority,
    NotificationStatus? status,
    String? cattleId,
    String? relatedRecordId,
    String? relatedRecordType,
    String? eventId,
    String? imageUrl,
    String? actionUrl,
    Map<String, String>? customData,
    DateTime? scheduledFor,
    DateTime? expiresAt,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? actionedAt,
    DateTime? updatedAt,
    String? pushNotificationId,
    bool? pushNotificationSent,
    DateTime? pushNotificationSentAt,
    bool? isSynced,
    DateTime? lastSyncAt,
    String? syncConflictData,
  }) {
    final notification = NotificationIsar(
      businessId: businessId ?? this.businessId,
      title: title ?? this.title,
      message: message ?? this.message,
      category: category ?? this.category,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      cattleId: cattleId ?? this.cattleId,
      relatedRecordId: relatedRecordId ?? this.relatedRecordId,
      relatedRecordType: relatedRecordType ?? this.relatedRecordType,
      eventId: eventId ?? this.eventId,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      customData: customData ?? this.customData,
      scheduledFor: scheduledFor ?? this.scheduledFor,
      expiresAt: expiresAt ?? this.expiresAt,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      actionedAt: actionedAt ?? this.actionedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      pushNotificationId: pushNotificationId ?? this.pushNotificationId,
      pushNotificationSent: pushNotificationSent ?? this.pushNotificationSent,
      pushNotificationSentAt: pushNotificationSentAt ?? this.pushNotificationSentAt,
      isSynced: isSynced ?? this.isSynced,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      syncConflictData: syncConflictData ?? this.syncConflictData,
    );
    
    return notification;
  }
}