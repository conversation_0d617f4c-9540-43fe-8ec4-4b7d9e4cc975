import '../models/breeding_record_isar.dart';
// import '../../Events/services/event_service.dart'; // File removed

import 'package:flutter/foundation.dart';

/// BreedingEventIntegration - DISABLED
/// This class is disabled because EventService and related enums have been removed
class BreedingEventIntegration {
  BreedingEventIntegration();

  /// Create breeding-related events - DISABLED
  Future<void> createBreedingEvents(BreedingRecordIsar breedingRecord) async {
    debugPrint('BreedingEventIntegration: createBreedingEvents disabled - EventService removed');
    // Method disabled - EventService and related enums removed
    return;
  }

  /// Update breeding events - DISABLED
  Future<void> updateBreedingEvents(BreedingRecordIsar breedingRecord) async {
    debugPrint('BreedingEventIntegration: updateBreedingEvents disabled - EventService removed');
    return;
  }

  /// Delete breeding events - DISABLED
  Future<void> deleteBreedingEvents(String cattleId, String businessId) async {
    debugPrint('BreedingEventIntegration: deleteBreedingEvents disabled - EventService removed');
    return;
  }

  /*
  // All methods below are commented out due to missing EventService and related enums

  /// Create heat detection event
  Future<void> _createBreedingEvent(BreedingRecordIsar breedingRecord) async {
    final event = EventIsar()
      ..title = 'Breeding - ${breedingRecord.cattleId}'
      ..description = 'Breeding event for cattle ${breedingRecord.cattleId} using ${breedingRecord.method ?? "unknown method"}'
      ..eventType = EventType.breeding
      ..priority = EventPriority.high
      ..status = EventStatus.pending
      ..startDateTime = breedingRecord.date!
      ..endDateTime = breedingRecord.date!.add(const Duration(hours: 2))
      ..cattleIds = [breedingRecord.cattleId!]
      ..businessId = breedingRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create calving event
  Future<void> _createCalvingEvent(BreedingRecordIsar breedingRecord) async {
    final event = EventIsar()
      ..title = 'Expected Calving - $breedingRecord.cattleId'
      ..description = 'Expected calving for cattle $breedingRecord.cattleId'
      ..eventType = EventType.breeding
      ..priority = EventPriority.high
      ..status = EventStatus.pending
      ..startDateTime = breedingRecord.expectedDate!
      ..endDateTime = breedingRecord.expectedDate!.add(const Duration(hours: 24))
      ..cattleIds = [breedingRecord.cattleId!]
      ..businessId = breedingRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create dry off event
  Future<void> _createDryOffEvent(BreedingRecordIsar breedingRecord) async {
    final dryOffDate = breedingRecord.expectedDate!.subtract(const Duration(days: 60));

    final event = EventIsar()
      ..title = 'Dry Off - $breedingRecord.cattleId'
      ..description = 'Dry off period for cattle $breedingRecord.cattleId'
      ..eventType = EventType.breeding
      ..priority = EventPriority.medium
      ..status = EventStatus.pending
      ..startDateTime = dryOffDate
      ..endDateTime = dryOffDate.add(const Duration(hours: 2))
      ..cattleIds = [breedingRecord.cattleId!]
      ..businessId = breedingRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Update event from breeding record
  Future<void> _updateEventFromBreedingRecord(
    EventIsar event,
    BreedingRecordIsar breedingRecord,
  ) async {
    if (event.title?.contains('Heat Detection') == true && breedingRecord.date != null) {
      event.startDateTime = breedingRecord.date!;
      event.endDateTime = breedingRecord.date!.add(const Duration(hours: 2));
    } else if (event.title?.contains('Insemination') == true && breedingRecord.date != null) {
      event.startDateTime = breedingRecord.date!;
      event.endDateTime = breedingRecord.date!.add(const Duration(hours: 1));
    } else if (event.title?.contains('Pregnancy Check') == true && breedingRecord.date != null) {
      final checkDate = breedingRecord.date!.add(const Duration(days: 21));
      event.startDateTime = checkDate;
      event.endDateTime = checkDate.add(const Duration(hours: 1));
    } else if (event.title?.contains('Expected Calving') == true && breedingRecord.expectedDate != null) {
      event.startDateTime = breedingRecord.expectedDate!;
      event.endDateTime = breedingRecord.expectedDate!.add(const Duration(hours: 24));
    } else if (event.title?.contains('Dry Off') == true && breedingRecord.expectedDate != null) {
      final dryOffDate = breedingRecord.expectedDate!.subtract(const Duration(days: 60));
      event.startDateTime = dryOffDate;
      event.endDateTime = dryOffDate.add(const Duration(hours: 2));
    }

    event.updatedAt = DateTime.now();
    await _eventService.updateEvent(event);
  }
  */
}