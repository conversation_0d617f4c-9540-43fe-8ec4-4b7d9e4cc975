import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'backup_settings_isar.g.dart';

/// Enum for backup storage providers
enum BackupStorageProvider {
  local,
  googleDrive,
}

@collection
class BackupSettingsIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? farmBusinessId; // Reference to the farm these settings belong to

  bool autoBackupEnabled = true;
  int autoBackupFrequency = 7; // Days
  String backupLocation = 'local';
  int? backupFrequencyDays; // Changed from String? to int? to match generated code

  // Cloud storage settings
  @Enumerated(EnumType.name)
  BackupStorageProvider storageProvider = BackupStorageProvider.local;

  // Google Drive settings
  bool googleDriveEnabled = false;
  String? googleDriveAccountEmail;
  DateTime? googleDriveLastSync;



  // Cloud backup retention settings
  int cloudBackupRetentionDays = 30; // Keep cloud backups for 30 days
  int maxCloudBackups = 10; // Maximum number of cloud backups to keep

  DateTime? lastBackupDate;
  DateTime? lastCloudBackupDate;

  DateTime? createdAt;
  DateTime? updatedAt;

  BackupSettingsIsar() {
    createdAt = DateTime.now();
    updatedAt = DateTime.now();
    backupFrequencyDays = autoBackupFrequency; // Update this to use the int directly instead of string conversion
  }

  factory BackupSettingsIsar.create({
    required String farmBusinessId,
    bool autoBackupEnabled = true,
    int autoBackupFrequency = 7,
    String backupLocation = 'local',
    BackupStorageProvider storageProvider = BackupStorageProvider.local,
    DateTime? lastBackupDate,
  }) {
    return BackupSettingsIsar()
      ..businessId = const Uuid().v4()
      ..farmBusinessId = farmBusinessId
      ..autoBackupEnabled = autoBackupEnabled
      ..autoBackupFrequency = autoBackupFrequency
      ..backupFrequencyDays = autoBackupFrequency // Update this to use the int directly
      ..backupLocation = backupLocation
      ..storageProvider = storageProvider
      ..lastBackupDate = lastBackupDate
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'farmBusinessId': farmBusinessId,
      'autoBackupEnabled': autoBackupEnabled,
      'autoBackupFrequency': autoBackupFrequency,
      'backupFrequencyDays': backupFrequencyDays,
      'backupLocation': backupLocation,
      'storageProvider': storageProvider.name,
      'googleDriveEnabled': googleDriveEnabled,
      'googleDriveAccountEmail': googleDriveAccountEmail,
      'googleDriveLastSync': googleDriveLastSync?.toIso8601String(),
      'cloudBackupRetentionDays': cloudBackupRetentionDays,
      'maxCloudBackups': maxCloudBackups,
      'lastBackupDate': lastBackupDate?.toIso8601String(),
      'lastCloudBackupDate': lastCloudBackupDate?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory BackupSettingsIsar.fromMap(Map<String, dynamic> map) {
    return BackupSettingsIsar()
      ..id = map['id'] ?? Isar.autoIncrement
      ..farmBusinessId = map['farmBusinessId'] as String?
      ..autoBackupEnabled = map['autoBackupEnabled'] ?? true
      ..autoBackupFrequency = map['autoBackupFrequency'] ?? 7
      ..backupFrequencyDays = map['backupFrequencyDays'] as int? ?? 7 // Updated to int instead of string
      ..backupLocation = map['backupLocation'] ?? 'local'
      ..storageProvider = _parseStorageProvider(map['storageProvider'])
      ..googleDriveEnabled = map['googleDriveEnabled'] ?? false
      ..googleDriveAccountEmail = map['googleDriveAccountEmail'] as String?
      ..googleDriveLastSync = map['googleDriveLastSync'] != null ? DateTime.parse(map['googleDriveLastSync'] as String) : null
      ..cloudBackupRetentionDays = map['cloudBackupRetentionDays'] ?? 30
      ..maxCloudBackups = map['maxCloudBackups'] ?? 10
      ..lastBackupDate = map['lastBackupDate'] != null ? DateTime.parse(map['lastBackupDate'] as String) : null
      ..lastCloudBackupDate = map['lastCloudBackupDate'] != null ? DateTime.parse(map['lastCloudBackupDate'] as String) : null
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
  }

  static BackupStorageProvider _parseStorageProvider(dynamic value) {
    if (value == null) return BackupStorageProvider.local;
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'googledrive':
        case 'google_drive':
          return BackupStorageProvider.googleDrive;
        default:
          return BackupStorageProvider.local;
      }
    }
    return BackupStorageProvider.local;
  }

  BackupSettingsIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    bool? autoBackupEnabled,
    int? autoBackupFrequency,
    int? backupFrequencyDays, // Changed from String? to int?
    String? backupLocation,
    BackupStorageProvider? storageProvider,
    bool? googleDriveEnabled,
    String? googleDriveAccountEmail,
    DateTime? googleDriveLastSync,
    int? cloudBackupRetentionDays,
    int? maxCloudBackups,
    DateTime? lastBackupDate,
    DateTime? lastCloudBackupDate,
  }) {
    final settings = BackupSettingsIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..autoBackupEnabled = autoBackupEnabled ?? this.autoBackupEnabled
      ..autoBackupFrequency = autoBackupFrequency ?? this.autoBackupFrequency
      ..backupFrequencyDays = backupFrequencyDays ?? this.backupFrequencyDays
      ..backupLocation = backupLocation ?? this.backupLocation
      ..storageProvider = storageProvider ?? this.storageProvider
      ..googleDriveEnabled = googleDriveEnabled ?? this.googleDriveEnabled
      ..googleDriveAccountEmail = googleDriveAccountEmail ?? this.googleDriveAccountEmail
      ..googleDriveLastSync = googleDriveLastSync ?? this.googleDriveLastSync
      ..cloudBackupRetentionDays = cloudBackupRetentionDays ?? this.cloudBackupRetentionDays
      ..maxCloudBackups = maxCloudBackups ?? this.maxCloudBackups
      ..lastBackupDate = lastBackupDate ?? this.lastBackupDate
      ..lastCloudBackupDate = lastCloudBackupDate ?? this.lastCloudBackupDate
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();

    // Ensure backupFrequencyDays is synchronized with autoBackupFrequency
    if (autoBackupFrequency != null && backupFrequencyDays == null) {
      settings.backupFrequencyDays = autoBackupFrequency; // Use the int directly
    }

    return settings;
  }

  factory BackupSettingsIsar.createDefault(String farmId) {
    return BackupSettingsIsar()
      ..businessId = farmId
      ..autoBackupEnabled = true  // Enable auto backup by default
      ..autoBackupFrequency = 7
      ..backupFrequencyDays = 7 // Updated to int instead of string
      ..backupLocation = 'local'
      ..storageProvider = BackupStorageProvider.local  // Start with local, user can upgrade to cloud
      ..googleDriveEnabled = false  // Will be enabled when user authenticates
      ..cloudBackupRetentionDays = 30
      ..maxCloudBackups = 10
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  BackupSettingsIsar clone() {
    return BackupSettingsIsar()
      ..id = id
      ..businessId = businessId
      ..farmBusinessId = farmBusinessId
      ..autoBackupEnabled = autoBackupEnabled
      ..autoBackupFrequency = autoBackupFrequency
      ..backupFrequencyDays = backupFrequencyDays
      ..backupLocation = backupLocation
      ..storageProvider = storageProvider
      ..googleDriveEnabled = googleDriveEnabled
      ..googleDriveAccountEmail = googleDriveAccountEmail
      ..googleDriveLastSync = googleDriveLastSync
      ..cloudBackupRetentionDays = cloudBackupRetentionDays
      ..maxCloudBackups = maxCloudBackups
      ..lastBackupDate = lastBackupDate
      ..lastCloudBackupDate = lastCloudBackupDate
      ..createdAt = createdAt
      ..updatedAt = updatedAt;
  }

  // Helper methods for cloud storage providers
  bool get isCloudStorageEnabled => storageProvider != BackupStorageProvider.local;

  bool get isGoogleDriveSelected => storageProvider == BackupStorageProvider.googleDrive;

  String get storageProviderDisplayName {
    switch (storageProvider) {
      case BackupStorageProvider.local:
        return 'Local Storage';
      case BackupStorageProvider.googleDrive:
        return 'Google Drive';
    }
  }
}