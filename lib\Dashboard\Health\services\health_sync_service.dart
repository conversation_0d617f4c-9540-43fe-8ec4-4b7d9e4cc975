import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import '../../../services/logging_service.dart';
import '../../../config/api_config.dart';
import 'package:get_it/get_it.dart';
import '../models/health_record_isar.dart';
import 'health_repository.dart';

/// Dedicated bidirectional sync service for Health module
/// Separates sync logic from repository CRUD operations
/// Follows consistent pattern across all modules
class HealthSyncService {
  final HealthRepository _healthRepository = GetIt.instance<HealthRepository>();
  
  // Sync-related constants
  static const String _lastSyncKey = 'last_health_sync';
  final LoggingService _logger = LoggingService();

  /// Get last sync time for incremental sync
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSync = prefs.getString(_lastSyncKey);
    return lastSync != null ? DateTime.parse(lastSync) : null;
  }

  /// Set last sync time
  Future<void> setLastSyncTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, time.toIso8601String());
  }

  /// Get all health records for sync
  Future<List<HealthRecordIsar>> getAllHealthRecords() async {
    final isar = GetIt.instance<Isar>();
    return await isar.healthRecordIsars.where().findAll();
  }

  /// Get health records modified since last sync
  Future<List<HealthRecordIsar>> getModifiedHealthRecordsSince(DateTime? lastSync) async {
    if (lastSync == null) {
      return await getAllHealthRecords();
    }
    
    final isar = GetIt.instance<Isar>();
    return await isar.healthRecordIsars
        .filter()
        .updatedAtGreaterThan(lastSync)
        .findAll();
  }

  /// Convert health record to sync-friendly map
  Map<String, dynamic> _healthRecordToSyncMap(HealthRecordIsar record) {
    return {
      'id': record.businessId,
      'cattleTagId': record.cattleTagId,
      'recordType': record.recordType,
      'date': record.date?.toIso8601String(),
      'description': record.description,
      'diagnosis': record.diagnosis,
      'treatment': record.treatment,
      'medicine': record.medicine,
      'dose': record.dose,
      'dosageUnit': record.dosageUnit,
      'condition': record.condition,
      'veterinarian': record.veterinarian,
      'cost': record.cost,
      'notes': record.notes,
      'followUpRequired': record.followUpRequired,
      'followUpDate': record.followUpDate?.toIso8601String(),
      'isFollowUp': record.isFollowUp,
      'isResolved': record.isResolved,
      'status': record.status,
      'createdAt': record.createdAt?.toIso8601String(),
      'updatedAt': record.updatedAt?.toIso8601String(),
    };
  }

  /// Convert sync map to health record
  HealthRecordIsar _healthRecordFromSyncMap(Map<String, dynamic> map) {
    final record = HealthRecordIsar()
      ..businessId = map['id'] as String?
      ..cattleTagId = map['cattleTagId'] as String?
      ..recordType = map['recordType'] as String?
      ..description = map['description'] as String?
      ..diagnosis = map['diagnosis'] as String?
      ..treatment = map['treatment'] as String?
      ..medicine = map['medicine'] as String?
      ..dose = map['dose'] != null ? (map['dose'] as num).toDouble() : null
      ..dosageUnit = map['dosageUnit'] as String?
      ..condition = map['condition'] as String?
      ..veterinarian = map['veterinarian'] as String?
      ..cost = map['cost'] != null ? (map['cost'] as num).toDouble() : null
      ..notes = map['notes'] as String?
      ..followUpRequired = map['followUpRequired'] as bool? ?? false
      ..isFollowUp = map['isFollowUp'] as bool? ?? false
      ..isResolved = map['isResolved'] as bool? ?? false
      ..status = map['status'] as String?;

    // Handle dates
    if (map['date'] != null) {
      record.date = DateTime.parse(map['date']);
    }
    if (map['followUpDate'] != null) {
      record.followUpDate = DateTime.parse(map['followUpDate']);
    }
    if (map['createdAt'] != null) {
      record.createdAt = DateTime.parse(map['createdAt']);
    }
    if (map['updatedAt'] != null) {
      record.updatedAt = DateTime.parse(map['updatedAt']);
    }

    return record;
  }

  /// Bidirectional sync with external API
  Future<bool> syncData() async {
    try {
      // Check if API sync is enabled
      if (!ApiConfig.isApiSyncAvailable) {
        _logger.info('Health sync skipped - API sync disabled (local-only mode)');
        return true; // Return success for local-only mode
      }

      final lastSync = await getLastSyncTime();
      final localRecords = await getModifiedHealthRecordsSince(lastSync);

      // Prepare data for API
      final syncData = {
        'lastSync': lastSync?.toIso8601String(),
        'records': localRecords.map((r) => _healthRecordToSyncMap(r)).toList(),
      };

      final response = await http.post(
        Uri.parse(ApiConfig.healthSync),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode(syncData),
      ).timeout(ApiConfig.syncTimeout);

      if (response.statusCode == 200) {
        if (!response.body.startsWith('{')) {
          _logger.error(
              'Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          return false;
        }

        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (!responseData.containsKey('records')) {
          _logger.error('Invalid response format: Missing records field');
          return false;
        }

        // Process server records and handle conflicts
        final List<dynamic> serverRecords = responseData['records'];
        await _processServerHealthRecords(serverRecords);

        await setLastSyncTime(DateTime.now());
        _logger.info('Health records synchronized successfully');
        return true;
      } else {
        _logger.error('Failed to sync health records: $response.statusCode');
        return false;
      }
    } catch (e) {
      _logger.error('Error syncing health records: $e');
      return false;
    }
  }

  /// Process server records and handle conflicts
  Future<void> _processServerHealthRecords(List<dynamic> serverRecords) async {
    for (final recordData in serverRecords) {
      try {
        final serverRecord = _healthRecordFromSyncMap(recordData);
        final isar = GetIt.instance<Isar>();
        final existingRecord = await isar.healthRecordIsars
            .filter()
            .businessIdEqualTo(serverRecord.businessId)
            .findFirst();

        if (existingRecord == null) {
          // New record from server
          await _healthRepository.saveHealthRecord(serverRecord);
        } else {
          // Handle conflict resolution - server wins for now
          final resolvedRecord = _resolveHealthConflict(existingRecord, serverRecord);
          await _healthRepository.saveHealthRecord(resolvedRecord);
        }
      } catch (e) {
        _logger.error('Error processing server health record: $e');
      }
    }
  }

  /// Simple conflict resolution - server wins
  /// In a more sophisticated implementation, this could use timestamps,
  /// user preferences, or merge strategies
  HealthRecordIsar _resolveHealthConflict(HealthRecordIsar local, HealthRecordIsar server) {
    // For now, server record wins in conflicts
    // Keep the local Isar ID but use server data
    server.id = local.id;
    return server;
  }
}
