/// Universal Widget System Components
///
/// This package provides complete, universal widget systems for consistent
/// UI patterns across all modules in the Cattle Manager App.

// Core widgets
export 'app_drawer.dart';

// Universal Filter System
export 'filters/filters.dart';
export 'filters/filter_widgets.dart';
export 'filters/filter_layout.dart';

// Universal Record Card System
export 'universal_record_card.dart';

// Universal Empty State System
// export 'empty_state.dart'; // Removed - use UniversalTabEmptyState from app_tabs.dart

// Universal Navigation Card System
export 'universal_navigation_card.dart';

// Universal Info Card System
export 'universal_info_card.dart';

// Universal Form System - Use UniversalFormDialog from constants/app_layout.dart
// and UniversalFormFieldBuilder from form_fields/universal_form_field_builder.dart
