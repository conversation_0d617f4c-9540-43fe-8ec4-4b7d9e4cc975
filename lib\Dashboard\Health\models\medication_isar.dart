import 'package:isar/isar.dart';
import 'treatment_isar.dart';

part 'medication_isar.g.dart';

@collection
class MedicationIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? cattleTagId;

  String? name;

  String? dosage;

  DateTime? startDate;

  DateTime? endDate;

  String? frequency;

  String? notes;

  double? cost;

  DateTime? createdAt;

  DateTime? updatedAt;

  // Backward compatibility getters and setters
  String? get recordId => businessId;
  set recordId(String? value) => businessId = value;

  String? get cattleId => cattleTagId;
  set cattleId(String? value) => cattleTagId = value;

  String? get cattleBusinessId => cattleTagId;
  set cattleBusinessId(String? value) => cattleTagId = value;

  // Add treatmentId getter and setter for backward compatibility
  String? get treatmentId => businessId;
  set treatmentId(String? value) => businessId = value;

  MedicationIsar();

  /// Generate a formatted ID for medication records in the format 'cattleTagId-Medication-sequenceNumber'
  static String generateFormattedId(String cattleTagId, int sequenceNumber) {
    return '$cattleTagId-Medication-$sequenceNumber';
  }

  /// Generate a deterministic business ID for medication records to ensure consistency
  /// across app reinstallations. Based on cattle tag ID, start date, and medication name.
  static String generateBusinessId(
      String cattleTagId, DateTime startDate, String medicationName) {
    // Format the date in a consistent way (YYYYMMDD)
    final dateStr =
        "$startDate.year${startDate.month.toString().padLeft(2, '0')}${startDate.day.toString().padLeft(2, '0')}";

    // Normalize medication name (lowercase, remove spaces)
    final normalizedName = medicationName.toLowerCase().replaceAll(' ', '_');

    // Create a unique ID combining cattle tag ID, date and medication name
    final uniqueKey = "$cattleTagId-$dateStr-$normalizedName";

    // Return with a prefix to distinguish medication records
    return "med_$uniqueKey";
  }

  factory MedicationIsar.create({
    required String cattleTagId,
    required String name,
    required String dosage,
    required DateTime startDate,
    DateTime? endDate,
    required String frequency,
    String notes = '',
    double cost = 0.0,
    String? businessId,
  }) {
    return MedicationIsar()
      ..businessId = businessId ?? generateBusinessId(cattleTagId, startDate, name)
      ..cattleTagId = cattleTagId
      ..name = name
      ..dosage = dosage
      ..startDate = startDate
      ..endDate = endDate
      ..frequency = frequency
      ..notes = notes
      ..cost = cost
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleTagId': cattleTagId,
      'name': name,
      'dosage': dosage,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'frequency': frequency,
      'notes': notes,
      'cost': cost,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory MedicationIsar.fromMap(Map<String, dynamic> map) {
    return MedicationIsar()
      ..businessId = map['id'] as String? ?? map['businessId'] as String? ?? map['recordId'] as String?
      ..cattleTagId =
          map['cattleTagId'] as String? ?? map['cattleId'] as String? ?? map['cattleBusinessId'] as String?
      ..name = map['name'] as String?
      ..dosage = map['dosage'] as String?
      ..startDate = map['startDate'] != null
          ? DateTime.parse(map['startDate'].toString())
          : null
      ..endDate = map['endDate'] != null
          ? DateTime.parse(map['endDate'].toString())
          : null
      ..frequency = map['frequency'] as String?
      ..notes = map['notes'] as String?
      ..cost = map['cost'] != null ? double.parse(map['cost'].toString()) : null
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'].toString())
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'].toString())
          : DateTime.now();
  }

  Map<String, dynamic> toJson() => toMap();

  MedicationIsar copyWith({
    String? businessId,
    String? cattleTagId,
    String? name,
    String? dosage,
    DateTime? startDate,
    DateTime? endDate,
    String? frequency,
    String? notes,
    double? cost,
  }) {
    return MedicationIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..cattleTagId = cattleTagId ?? this.cattleTagId
      ..name = name ?? this.name
      ..dosage = dosage ?? this.dosage
      ..startDate = startDate ?? this.startDate
      ..endDate = endDate ?? this.endDate
      ..frequency = frequency ?? this.frequency
      ..notes = notes ?? this.notes
      ..cost = cost ?? this.cost
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }

  // Conversion method to TreatmentIsar
  TreatmentIsar toTreatmentIsar() {
    return TreatmentIsar()
      ..cattleId = cattleId
      ..treatment = name
      ..condition = frequency // Using frequency as condition (best approximation)
      ..date = startDate
      ..dosage = dosage
      ..veterinarian = '' // No direct mapping
      ..cost = cost?.toString()
      ..notes = notes
      ..status = endDate != null && endDate!.isBefore(DateTime.now()) ? 'Completed' : 'Active'
      ..businessId = businessId
      ..createdAt = createdAt
      ..updatedAt = updatedAt;
  }
}
