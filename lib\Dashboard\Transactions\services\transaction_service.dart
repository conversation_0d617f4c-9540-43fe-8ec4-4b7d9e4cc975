import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import 'package:get_it/get_it.dart';
import 'transactions_repository.dart';
import 'transaction_analytics_service.dart';
import '../models/transaction_analytics.dart';

/// Transaction Service - Business logic layer
///
/// Handles complex business operations, validation, and orchestration.
/// Uses repository for data access and analytics service for calculations.
/// No direct database access - all data operations go through repository.
class TransactionService {
  // Use lazy getter to avoid accessing GetIt services in constructor
  TransactionsRepository get _transactionsRepository => GetIt.instance<TransactionsRepository>();

  //=== BUSINESS LOGIC METHODS ===//

  /// Get all transactions - delegates to repository
  Future<List<TransactionIsar>> getTransactions() async {
    return await _transactionsRepository.getAllTransactions();
  }

  /// Add new transaction with business validation
  Future<void> addTransaction(TransactionIsar transaction) async {
    // Business validation
    _validateTransaction(transaction);

    // Set timestamps
    transaction.createdAt = DateTime.now();
    transaction.updatedAt = DateTime.now();

    // Generate business ID if not provided
    if (transaction.transactionId.isEmpty) {
      transaction.transactionId = TransactionIsar.generateBusinessId();
    }

    // Save through repository
    await _transactionsRepository.saveTransaction(transaction);
  }

  /// Update existing transaction with business validation
  Future<void> updateTransaction(TransactionIsar transaction) async {
    // Business validation
    _validateTransaction(transaction);

    // Update timestamp
    transaction.updatedAt = DateTime.now();

    // Save through repository
    await _transactionsRepository.saveTransaction(transaction);
  }

  /// Delete transaction by business ID
  Future<void> deleteTransaction(String transactionId) async {
    if (transactionId.isEmpty) {
      throw ArgumentError('Transaction ID cannot be empty');
    }

    // Get all transactions and find by business ID
    final transactions = await _transactionsRepository.getAllTransactions();
    final transaction = transactions
        .where((t) => t.transactionId == transactionId)
        .firstOrNull;

    if (transaction != null) {
      await _transactionsRepository.deleteTransaction(transaction.id);
    } else {
      throw Exception('Transaction not found: $transactionId');
    }
  }

  /// Get transactions by category type using repository filters
  Future<List<TransactionIsar>> getTransactionsByType(String categoryType) async {
    return await _transactionsRepository.getTransactionsByCategoryType(categoryType);
  }

  /// Get transactions by category using repository filters
  Future<List<TransactionIsar>> getTransactionsByCategory(String category) async {
    final transactions = await _transactionsRepository.getAllTransactions();
    return transactions.where((t) => t.category == category).toList();
  }

  /// Get transactions by date range using repository filters
  Future<List<TransactionIsar>> getTransactionsByDateRange(
      DateTime startDate, DateTime endDate) async {
    return await _transactionsRepository.getTransactionsByDateRange(startDate, endDate);
  }

  /// Calculate analytics for given transactions
  Future<TransactionAnalyticsResult> calculateAnalytics(List<TransactionIsar> transactions) async {
    final categories = await _transactionsRepository.getAllCategories();
    return TransactionAnalyticsService.calculate(transactions, categories);
  }

  /// Get financial summary for a date range
  Future<Map<String, double>> getFinancialSummary(DateTime startDate, DateTime endDate) async {
    final transactions = await getTransactionsByDateRange(startDate, endDate);
    final analytics = await calculateAnalytics(transactions);

    return {
      'totalIncome': analytics.totalIncome,
      'totalExpenses': analytics.totalExpenses,
      'netBalance': analytics.netBalance,
      'savingsRate': analytics.savingsRate,
    };
  }

  /// Validate transaction business rules
  void _validateTransaction(TransactionIsar transaction) {
    if (transaction.title.trim().isEmpty) {
      throw ArgumentError('Transaction title cannot be empty');
    }

    if (transaction.amount <= 0) {
      throw ArgumentError('Transaction amount must be greater than zero');
    }

    if (transaction.category.trim().isEmpty) {
      throw ArgumentError('Transaction category cannot be empty');
    }

    if (transaction.categoryType.trim().isEmpty) {
      throw ArgumentError('Transaction category type cannot be empty');
    }

    final validCategoryTypes = ['Income', 'Expense'];
    if (!validCategoryTypes.contains(transaction.categoryType)) {
      throw ArgumentError('Category type must be either Income or Expense');
    }
  }

  /// Bulk import transactions with validation
  Future<List<String>> bulkImportTransactions(List<TransactionIsar> transactions) async {
    final errors = <String>[];

    for (int i = 0; i < transactions.length; i++) {
      try {
        await addTransaction(transactions[i]);
      } catch (e) {
        errors.add('Row ${i + 1}: ${e.toString()}');
      }
    }

    return errors;
  }

  /// Get category performance analysis
  Future<Map<String, double>> getCategoryPerformance(String categoryType) async {
    final transactions = await getTransactionsByType(categoryType);
    return TransactionAnalyticsService.calculateCategoryPerformance(transactions, categoryType);
  }
}
