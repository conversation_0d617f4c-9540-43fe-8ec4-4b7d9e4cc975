import 'dart:async';
import 'package:flutter/foundation.dart';

/// Controller state enum - standardized across all modules
enum ControllerState { initial, loading, loaded, error, empty }

/// Abstract base controller for all module controllers
/// Defines the standard interface and patterns that all controllers must follow
/// 
/// Key principles:
/// - Reactive controllers with proper stream management
/// - Dual-stream architecture: separate filtered/unfiltered data streams
/// - Analytics calculated on unfiltered data only
/// - Proper state management with ControllerState enum
/// - Consistent error handling and disposal patterns
abstract class BaseController extends ChangeNotifier {
  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for proper disposal
  final List<StreamSubscription> _subscriptions = [];

  //=== GETTERS ===//

  /// Current controller state
  ControllerState get state => _state;

  /// Current error message (if any)
  String? get errorMessage => _errorMessage;

  /// Whether the controller is in loading state
  bool get isLoading => _state == ControllerState.loading;

  /// Whether the controller has an error
  bool get hasError => _state == ControllerState.error;

  /// Whether the controller has data loaded
  bool get isLoaded => _state == ControllerState.loaded;

  /// Whether the controller has no data
  bool get isEmpty => _state == ControllerState.empty;

  //=== STATE MANAGEMENT ===//

  /// Set the controller state and notify listeners
  void setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  /// Set error state with message and notify listeners
  void setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  /// Clear error state and set to loaded
  void clearError() {
    if (_state == ControllerState.error) {
      _state = ControllerState.loaded;
      _errorMessage = null;
      notifyListeners();
    }
  }

  //=== STREAM MANAGEMENT ===//

  /// Add a stream subscription for proper disposal
  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  /// Cancel all stream subscriptions
  void cancelAllSubscriptions() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  //=== ABSTRACT METHODS ===//

  /// Initialize the controller and load data
  /// Must be implemented by concrete controllers
  Future<void> loadData();

  /// Refresh all data
  /// Must be implemented by concrete controllers
  Future<void> refresh();

  //=== LIFECYCLE ===//

  @override
  void dispose() {
    // Cancel all stream subscriptions to prevent memory leaks
    cancelAllSubscriptions();
    super.dispose();
  }
}

/// Base controller for modules with filtering capabilities
/// Extends BaseController with filter management
abstract class FilterableController<TFilter> extends BaseController {
  // Current filter state
  TFilter? _currentFilter;
  bool _hasActiveFilters = false;

  //=== FILTER GETTERS ===//

  /// Current filter state
  TFilter? get currentFilter => _currentFilter;

  /// Whether filters are currently applied
  bool get hasActiveFilters => _hasActiveFilters;

  //=== FILTER METHODS ===//

  /// Apply filters to the data
  /// Must be implemented by concrete controllers
  Future<void> applyFilters(TFilter filter);

  /// Clear all filters
  /// Must be implemented by concrete controllers
  Future<void> clearFilters();

  /// Check if filter has active filters
  /// Must be implemented by concrete controllers
  bool hasFilters(TFilter filter);

  /// Update filter state (protected method for concrete controllers)
  @protected
  void updateFilterState(TFilter filter) {
    _currentFilter = filter;
    _hasActiveFilters = hasFilters(filter);
  }
}

/// Base controller for modules with dual-stream architecture
/// Extends FilterableController with unfiltered/filtered data separation
abstract class DualStreamController<TData, TFilter> extends FilterableController<TFilter> {
  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<TData> _unfilteredData = [];
  List<TData> _filteredData = [];

  //=== DATA GETTERS ===//

  /// Complete unfiltered dataset (for analytics calculations)
  List<TData> get unfilteredData => List.unmodifiable(_unfilteredData);

  /// Filtered dataset (for UI display)
  List<TData> get filteredData => List.unmodifiable(_filteredData);

  /// Data count for UI display
  int get dataCount => _filteredData.length;

  /// Total data count (unfiltered)
  int get totalDataCount => _unfilteredData.length;

  //=== DATA MANAGEMENT ===//

  /// Update unfiltered data (protected method for concrete controllers)
  @protected
  void updateUnfilteredData(List<TData> data) {
    _unfilteredData = data;
    _updateAnalytics();
    _applyCurrentFilters();
  }

  /// Update filtered data (protected method for concrete controllers)
  @protected
  void updateFilteredData(List<TData> data) {
    _filteredData = data;
    
    // Update state based on data availability
    if (_filteredData.isEmpty && _unfilteredData.isEmpty) {
      setState(ControllerState.empty);
    } else {
      setState(ControllerState.loaded);
    }
    
    notifyListeners();
  }

  //=== ABSTRACT METHODS ===//

  /// Update analytics - ALWAYS calculated on unfiltered data
  /// Must be implemented by concrete controllers
  void _updateAnalytics();

  /// Apply current filters to unfiltered data
  /// Must be implemented by concrete controllers
  void _applyCurrentFilters();
}
