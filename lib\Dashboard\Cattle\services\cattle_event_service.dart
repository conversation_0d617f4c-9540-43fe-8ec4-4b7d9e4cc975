import '../../Events/models/event_isar.dart';
import 'package:isar/isar.dart';

class CattleEventService {
  final Isar isar;

  CattleEventService(this.isar);

  // Get all events for a specific cattle - DISABLED (cattleIdsElementEqualTo method not available)
  Future<List<EventIsar>> getEventsForCattle(String cattleBusinessId) async {
    // Method disabled - cattleIdsElementEqualTo not available in EventIsar
    return <EventIsar>[];
    /*
    return await isar.eventIsars
        .filter()
        .cattleIdsElementEqualTo(cattleBusinessId)
        .findAll();
    */
  }

  // Add a new event
  Future<void> addEvent(EventIsar event) async {
    await isar.writeTxn(() async {
      await isar.eventIsars.put(event);
    });
  }

  // Update an existing event
  Future<void> updateEvent(EventIsar event) async {
    await isar.writeTxn(() async {
      await isar.eventIsars.put(event);
    });
  }

  // Delete an event
  Future<void> deleteEvent(EventIsar event) async {
    await isar.writeTxn(() async {
      await isar.eventIsars.delete(event.id);
    });
  }

  // Mark event as completed - DISABLED (EventStatus enum and properties removed)
  Future<void> markEventAsCompleted(EventIsar event) async {
    // Method disabled - EventStatus enum and completedAt property not available
    return;
    /*
    event.status = EventStatus.completed;
    event.completedAt = DateTime.now();
    event.updatedAt = DateTime.now();
    await updateEvent(event);
    */
  }

  // Mark event as missed - DISABLED (EventStatus enum and properties removed)
  Future<void> markEventAsMissed(EventIsar event) async {
    // Method disabled - EventStatus enum and cancellationReason property not available
    return;
    /*
    event.status = EventStatus.cancelled;
    event.cancellationReason = 'Missed';
    event.updatedAt = DateTime.now();
    await updateEvent(event);
    */
  }
} 