import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/health_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_tabs.dart';
import '../../../shared/models/info_card_data.dart';



class HealthAnalyticsTab extends StatefulWidget {
  final HealthController controller;

  const HealthAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<HealthAnalyticsTab> createState() => _HealthAnalyticsTabState();
}

class _HealthAnalyticsTabState extends State<HealthAnalyticsTab> {
  int _selectedChartIndex = 0; // 0: Record Type, 1: Status

  // Use global constants instead of local ones
  static const _fallbackColor = AppColors.fallback;

  // Common chart configuration
  static const _chartRadius = 80.0;
  static const _chartCenterSpace = 50.0;
  static const _chartSectionsSpace = 2.0;
  static const _chartHeight = 240.0;

  // Common text styles
  static const _chartTitleStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  @override
  void initState() {
    super.initState();
    // Data loading is now handled by the controller
  }

  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<Map<String, dynamic>> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header using universal component
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),

        // 2. Use ResponsiveGrid.cards for consistent responsive behavior
        ResponsiveGrid.cards(
          children: cardData.map((data) {
            return UniversalInfoCard(
              title: data['title'] as String,
              value: data['value'] as String,
              subtitle: data['subtitle'] as String?,
              icon: data['icon'] as IconData,
              color: data['color'] as Color,
              badge: data['badge'] as String?,
              insight: data['insight'] as String?,
            );
          }).toList(),
        ),
      ],
    );
  }

  // Universal pie chart builder - pure UI component without business logic
  Widget _buildUniversalPieChart(Map<String, int> data, Map<String, Color> colors, {String? emptyMessage}) {
    if (data.isEmpty || widget.controller.totalHealthRecords == 0) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    // Ensure we have valid data before rendering
    final validEntries = data.entries.where((entry) => entry.value > 0).toList();
    if (validEntries.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    final sections = validEntries.map((entry) {
      final percentage = (entry.value / widget.controller.totalHealthRecords) * 100;
      final sectionColor = colors[entry.key] ?? _fallbackColor;

      return PieChartSectionData(
        color: sectionColor,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: _chartRadius,
        titleStyle: _chartTitleStyle,
      );
    }).toList();

    // Add a small delay to ensure smooth rendering
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: _chartCenterSpace,
          sectionsSpace: _chartSectionsSpace,
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Check if we have any health data to display (health records, vaccinations, treatments, medications)
        final hasAnyHealthData = widget.controller.totalHealthRecords > 0 ||
                                 widget.controller.vaccinationRecords > 0 ||
                                 widget.controller.treatmentRecords > 0 ||
                                 widget.controller.medicationRecords > 0;

        if (!hasAnyHealthData) {
          return UniversalTabEmptyState.forTab(
            title: 'No Health Data',
            message: 'Add health records, vaccinations, or treatments to view comprehensive analytics and insights.',
            tabColor: AppColors.healthHeader,
            tabIndex: 0, // Analytics tab
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () {
                // Navigate to add health record screen
                Navigator.of(context).pushNamed('/health/add');
              },
              tabColor: AppColors.healthHeader,
            ),
          );
        }

        // Define sections for clean, declarative layout
        final sections = [
          _buildEnhancedKPIDashboard(context),
          _buildHealthCompositionAnalytics(context),
          _buildMedicalAnalytics(context),
          _buildHealthOverview(context),
        ];

        // Use RefreshIndicator with SingleChildScrollView for pull-to-refresh functionality
        return RefreshIndicator(
          onRefresh: () async {
            await widget.controller.refresh();
          },
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(kPaddingMedium), // Use global constant
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (int i = 0; i < sections.length; i++) ...[
                  sections[i],
                  if (i < sections.length - 1) const SizedBox(height: kSpacingLarge), // Use global constant
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedKPIDashboard(BuildContext context) {
    final kpiColors = _getKPIColors();
    final kpiCards = _buildKPICards(kpiColors);

    return _buildGridSection(
      title: 'Key Performance Indicators',
      subtitle: 'Essential metrics for your health management',
      icon: Icons.dashboard_outlined,
      headerColor: AppColors.healthHeader,
      cardData: kpiCards.map((card) => card.toMap()..['badge'] = null).toList(),
    );
  }

  /// Build KPI cards using InfoCardData for better type safety
  List<InfoCardData> _buildKPICards(List<Color> kpiColors) {
    return [
      InfoCardData(
        title: 'Total Records',
        value: widget.controller.totalHealthRecords.toString(),
        subtitle: 'health records',
        icon: Icons.medical_services,
        color: kpiColors[0],
        insight: 'Total health entries',
      ),
      InfoCardData(
        title: 'Active Records',
        value: widget.controller.activeRecords.toString(),
        subtitle: 'ongoing treatments',
        icon: Icons.pending,
        color: kpiColors[1],
        insight: 'Currently active',
      ),
      InfoCardData(
        title: 'Completed',
        value: widget.controller.completedRecords.toString(),
        subtitle: 'finished treatments',
        icon: Icons.check_circle,
        color: kpiColors[2],
        insight: 'Successfully completed',
      ),
      InfoCardData(
        title: 'Treatments',
        value: widget.controller.treatmentRecords.toString(),
        subtitle: 'treatment records',
        icon: Icons.healing,
        color: kpiColors[3],
        insight: 'Medical treatments',
      ),
      InfoCardData(
        title: 'Vaccinations',
        value: widget.controller.vaccinationRecords.toString(),
        subtitle: 'vaccination records',
        icon: Icons.vaccines,
        color: kpiColors[4],
        insight: 'Preventive care',
      ),
      InfoCardData(
        title: 'Health Score',
        value: widget.controller.averageHealthScore.toStringAsFixed(1),
        subtitle: 'average score',
        icon: Icons.star,
        color: kpiColors[5],
        insight: 'Overall health rating',
      ),
    ];
  }

  Widget _buildHealthCompositionAnalytics(BuildContext context) {
    final typeData = widget.controller.recordsByType;
    final statusData = widget.controller.recordsByStatus;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Health Composition Analytics',
          icon: Icons.pie_chart_outline,
          color: AppColors.healthHeader,
          subtitle: 'Detailed breakdown of health records and status',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Toggle buttons for chart selection
        _buildChartToggleButtons(context),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Single chart display based on selection
        _buildSelectedChart(context, typeData, statusData),
      ],
    );
  }

  Widget _buildChartToggleButtons(BuildContext context) {
    final chartOptions = [
      {'title': 'Record Type', 'icon': Icons.category},
      {'title': 'Status', 'icon': Icons.pending_actions},
    ];

    // Use colors from AppColors instead of hardcoded values
    final toggleColors = [
      AppColors.healthKpiColors[0], // Red
      AppColors.healthKpiColors[2], // Orange
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chartOptions.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedChartIndex == index;

        return ChoiceChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected ? Colors.white : toggleColors[index],
              ),
              const SizedBox(width: 6),
              Text(
                option['title'] as String,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: isSelected ? Colors.white : toggleColors[index],
                ),
              ),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedChartIndex = index;
              });
            }
          },
          selectedColor: toggleColors[index],
          backgroundColor: Colors.white,
          side: BorderSide(
            color: toggleColors[index],
            width: 2,
          ),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        );
      }).toList(),
    );
  }

  Widget _buildSelectedChart(
    BuildContext context,
    Map<String, int> typeData,
    Map<String, int> statusData,
  ) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildEnhancedChart(
          context,
          'Record Type Distribution',
          _buildUniversalPieChart(typeData, _getTypeColors()),
          _buildEnhancedLegend(typeData, _getTypeColors()),
          Icons.category,
        );
      case 1:
        return _buildEnhancedChart(
          context,
          'Status Distribution',
          _buildUniversalPieChart(statusData, _getStatusColors()),
          _buildEnhancedLegend(statusData, _getStatusColors()),
          Icons.pending_actions,
        );
      default:
        return _buildEnhancedChart(
          context,
          'Record Type Distribution',
          _buildUniversalPieChart(typeData, _getTypeColors()),
          _buildEnhancedLegend(typeData, _getTypeColors()),
          Icons.category,
        );
    }
  }

  Widget _buildEnhancedChart(
    BuildContext context,
    String title,
    Widget chart,
    Widget? legend,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(kBorderRadius * 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.healthHeader.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.healthHeader,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingLarge),
            SizedBox(height: _chartHeight, child: Center(child: chart)),
            if (legend != null) ...[
              const SizedBox(height: kSpacingMedium),
              legend,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMedicalAnalytics(BuildContext context) {
    final colors = _getInsightColors();
    final medicalCards = _buildMedicalCards(colors);

    return _buildGridSection(
      title: 'Medical Analytics',
      subtitle: 'Treatment effectiveness and medical outcomes',
      icon: Icons.timeline,
      headerColor: Colors.orange,
      cardData: medicalCards.map((card) => card.toMap()).toList(),
    );
  }

  /// Build medical analytics cards
  List<InfoCardData> _buildMedicalCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Treatment Success',
        value: '${widget.controller.treatmentSuccessRate.toStringAsFixed(1)}%',
        subtitle: 'success rate',
        icon: Icons.check_circle,
        color: colors[0],
        insight: 'Treatment effectiveness',
      ),
      InfoCardData(
        title: 'Avg Cost',
        value: '\$${widget.controller.totalHealthCosts.toStringAsFixed(0)}',
        subtitle: 'total health costs',
        icon: Icons.attach_money,
        color: colors[1],
        insight: 'Healthcare expenses',
      ),
      InfoCardData(
        title: 'Chronic Conditions',
        value: widget.controller.chronicConditions.length.toString(),
        subtitle: 'ongoing conditions',
        icon: Icons.warning,
        color: colors[2],
        insight: 'Long-term health issues',
      ),
      InfoCardData(
        title: 'Recent Treatments',
        value: widget.controller.cattleWithRecentTreatments.toString(),
        subtitle: 'last 30 days',
        icon: Icons.schedule,
        color: colors[3],
        insight: 'Recently treated cattle',
      ),
    ];
  }

  Widget _buildHealthOverview(BuildContext context) {
    final overviewColors = _getOverviewColors();
    final overviewCards = _buildOverviewCards(overviewColors);

    return _buildGridSection(
      title: 'Health Overview',
      subtitle: 'Overall health program performance and insights',
      icon: Icons.assessment,
      headerColor: Colors.green,
      cardData: overviewCards.map((card) => card.toMap()).toList(),
    );
  }

  /// Build overview cards
  List<InfoCardData> _buildOverviewCards(List<Color> overviewColors) {
    return [
      InfoCardData(
        title: 'Health Score',
        value: widget.controller.averageHealthScore.toStringAsFixed(1),
        subtitle: 'average rating',
        icon: Icons.star,
        color: overviewColors[0],
        insight: 'Overall health rating',
      ),
      InfoCardData(
        title: 'Medication Records',
        value: widget.controller.medicationRecords.toString(),
        subtitle: 'medications given',
        icon: Icons.medication,
        color: overviewColors[1],
        insight: 'Medication tracking',
      ),
      InfoCardData(
        title: 'Overdue Vaccinations',
        value: widget.controller.overdueVaccinations.toString(),
        subtitle: 'need attention',
        icon: Icons.notification_important,
        color: overviewColors[2],
        insight: 'Vaccination alerts',
      ),
      InfoCardData(
        title: 'Condition Types',
        value: widget.controller.conditionDistribution.length.toString(),
        subtitle: 'different conditions',
        icon: Icons.category,
        color: overviewColors[3],
        insight: 'Health condition variety',
      ),
    ];
  }

  Widget _buildEnhancedLegend(Map<String, int> data, Map<String, Color> colors) {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: data.entries.map((entry) {
        final percentage = widget.controller.totalHealthRecords > 0
          ? ((entry.value / widget.controller.totalHealthRecords) * 100).toStringAsFixed(1)
          : '0';
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: colors[entry.key]?.withValues(alpha: 0.1) ?? _fallbackColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: colors[entry.key] ?? _fallbackColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '$entry.key ($entry.value) $percentage%',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // Color Management - Different colors for each KPI card (multi-color rule)
  List<Color> _getKPIColors() => AppColors.healthKpiColors;
  List<Color> _getInsightColors() => AppColors.healthKpiColors;
  List<Color> _getOverviewColors() => AppColors.healthKpiColors;

  Map<String, Color> _getTypeColors() => AppColors.healthRecordTypeColors;
  Map<String, Color> _getStatusColors() => AppColors.healthStatusColors;
}
