import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../controllers/breeding_details_controller.dart';
import '../models/pregnancy_record_isar.dart';
import '../dialogs/pregnancy_form_dialog.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../utils/message_utils.dart';
import '../../../Dashboard/widgets/universal_record_card.dart';
import '../../../Dashboard/Cattle/widgets/milestone_card.dart';
import '../../../Dashboard/Cattle/widgets/eligibility_card.dart';
import '../../../Dashboard/Cattle/widgets/stats_card.dart';



class BreedingDetailsPregnancyTab extends StatefulWidget {
  const BreedingDetailsPregnancyTab({super.key});

  @override
  State<BreedingDetailsPregnancyTab> createState() => _BreedingDetailsPregnancyTabState();
}

class _BreedingDetailsPregnancyTabState extends State<BreedingDetailsPregnancyTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Consumer<BreedingDetailsController>(
      builder: (context, controller, child) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        final pregnancyRecords = controller.pregnancyRecords;

        if (pregnancyRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Pregnancy Records',
            message: 'No pregnancies recorded for ${controller.cattle?.name ?? 'this cattle'}.',
            tabColor: AppColors.breedingKpiColors[0],
            tabIndex: 0,
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () => _addPregnancyRecord(context, controller),
              tabColor: AppColors.breedingKpiColors[0],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80), // Bottom padding for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Stats card at top
              if (pregnancyRecords.isNotEmpty) ...[
                _buildStatsCard(pregnancyRecords),
                const SizedBox(height: 16),
              ],
              // Milestone card for active pregnancies
              if (pregnancyRecords.any((record) => record.status?.toLowerCase() == 'confirmed')) ...[
                _buildMilestoneCard(pregnancyRecords),
                const SizedBox(height: 16),
              ],
              // Status/eligibility card
              if (controller.cattle != null) ...[
                _buildEligibilityCard(controller),
                const SizedBox(height: 16),
              ],
              // Records list
              _buildRecordsList(pregnancyRecords),
            ],
          ),
        );
      },
    );
  }



  Widget _buildRecordsList(List<PregnancyRecordIsar> records) {
    // Sort records by start date (most recent first)
    final sortedRecords = List<PregnancyRecordIsar>.from(records)
      ..sort((a, b) => (b.startDate ?? DateTime.now()).compareTo(a.startDate ?? DateTime.now()));

    return Column(
      children: sortedRecords.map((record) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: _buildRecordCard(record),
      )).toList(),
    );
  }

  Widget _buildRecordCard(PregnancyRecordIsar record) {
    // Format dates
    String startDateText = record.startDate != null
        ? DateFormat('MMM dd, yyyy').format(record.startDate!)
        : 'Unknown date';

    // Format status and expected date
    String statusText = record.status ?? 'Unknown status';
    String expectedDateText = record.expectedCalvingDate != null
        ? 'Due: ${DateFormat('MMM dd, yyyy').format(record.expectedCalvingDate!)}'
        : 'Due date unknown';

    // Format breeding record and days remaining for row 3
    String breedingText = record.breedingRecordId?.isNotEmpty == true
        ? 'Breeding: $record.breedingRecordId'
        : 'Breeding: Unknown';
    String daysText = record.expectedCalvingDate != null
        ? _calculateDaysRemaining(record.expectedCalvingDate!)
        : 'Unknown';

    return UniversalRecordCard(
      row1Left: startDateText,
      row1Right: statusText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: _getStatusIcon(record.status),
      row2Left: expectedDateText,
      row2Right: daysText,
      row2LeftIcon: Icons.event_available,
      row2RightIcon: Icons.timer,
      row3Left: breedingText,
      row3Right: record.endDate != null
          ? 'Ended: ${DateFormat('MMM dd, yyyy').format(record.endDate!)}'
          : 'Ongoing',
      row3LeftIcon: Icons.favorite,
      row3RightIcon: record.endDate != null ? Icons.event_busy : Icons.schedule,
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.breedingHeader,
      onTap: () => _viewRecordDetails(record),
      onEdit: () => _editRecord(record),
      onDelete: () => _deleteRecord(record),
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return Icons.check_circle;
      case 'completed':
        return Icons.child_care;
      case 'abortion':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  String _calculateDaysRemaining(DateTime expectedDate) {
    final now = DateTime.now();
    final difference = expectedDate.difference(now).inDays;

    if (difference < 0) {
      return 'Overdue by ${-difference} days';
    } else if (difference == 0) {
      return 'Due today';
    } else {
      return '$difference days';
    }
  }

  Widget _buildMilestoneCard(List<PregnancyRecordIsar> pregnancyRecords) {
    // Find the most recent active pregnancy
    final activePregnancy = pregnancyRecords
        .where((record) => record.status?.toLowerCase() == 'confirmed')
        .toList()
      ..sort((a, b) => (b.startDate ?? DateTime.now()).compareTo(a.startDate ?? DateTime.now()));

    if (activePregnancy.isEmpty) {
      return const SizedBox.shrink();
    }

    final pregnancy = activePregnancy.first;
    final milestones = _generatePregnancyMilestones(pregnancy);

    return MilestoneCard(
      milestones: milestones,
      title: 'Pregnancy Milestones',
      emptyMessage: 'No milestones available for this pregnancy',
      baseColor: AppColors.breedingHeader,
      onInfoTap: () {
        // Show pregnancy milestone information
        _showMilestoneInfo();
      },
    );
  }

  List<Map<String, dynamic>> _generatePregnancyMilestones(PregnancyRecordIsar pregnancy) {
    if (pregnancy.startDate == null) return [];

    final startDate = pregnancy.startDate!;
    final expectedDate = pregnancy.expectedCalvingDate ?? startDate.add(const Duration(days: 280));

    // Standard cattle pregnancy milestones (280 days gestation)
    return [
      {
        'title': 'Pregnancy Confirmed',
        'description': 'Pregnancy has been confirmed and recorded',
        'date': startDate,
        'icon': Icons.check_circle,
        'color': MilestoneCard.milestoneColors[0], // Blue
      },
      {
        'title': 'First Trimester',
        'description': 'Critical development period - monitor nutrition',
        'date': startDate.add(const Duration(days: 93)),
        'icon': Icons.favorite,
        'color': MilestoneCard.milestoneColors[1], // Purple
      },
      {
        'title': 'Second Trimester',
        'description': 'Rapid fetal growth - increase feed quality',
        'date': startDate.add(const Duration(days: 186)),
        'icon': Icons.trending_up,
        'color': MilestoneCard.milestoneColors[2], // Green
      },
      {
        'title': 'Pre-Calving',
        'description': 'Prepare calving area and monitor closely',
        'date': expectedDate.subtract(const Duration(days: 14)),
        'icon': Icons.warning,
        'color': MilestoneCard.milestoneColors[3], // Red
      },
      {
        'title': 'Expected Calving',
        'description': 'Expected delivery date - be ready for assistance',
        'date': expectedDate,
        'icon': Icons.child_care,
        'color': MilestoneCard.milestoneColors[4], // Cyan
      },
    ];
  }

  void _showMilestoneInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pregnancy Milestones'),
        content: const Text(
          'These milestones help track the progress of pregnancy and ensure proper care at each stage:\n\n'
          '• First Trimester (0-93 days): Critical organ development\n'
          '• Second Trimester (94-186 days): Rapid growth phase\n'
          '• Third Trimester (187-280 days): Final development and preparation for birth\n\n'
          'Monitor nutrition, health, and prepare for calving as the due date approaches.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildEligibilityCard(BreedingDetailsController controller) {
    final cattle = controller.cattle!;

    // Check if cattle is currently pregnant
    final hasActivePregnancy = controller.pregnancyRecords.any(
      (record) => record.status?.toLowerCase() == 'confirmed'
    );

    return EligibilityCard.pregnancy(
      gender: cattle.gender.name,
      cattleId: cattle.businessId ?? '',
      animalTypeId: cattle.animalTypeId ?? '',
      isPregnant: hasActivePregnancy,
      dateOfBirth: cattle.dateOfBirth,
      purchaseDate: cattle.purchaseDate,
      onAddPressed: hasActivePregnancy ? null : () => _addPregnancyRecord(context, controller),
    );
  }

  Widget _buildStatsCard(List<PregnancyRecordIsar> pregnancyRecords) {
    // Calculate pregnancy statistics
    final totalPregnancies = pregnancyRecords.length;
    final confirmedPregnancies = pregnancyRecords.where((r) => r.status?.toLowerCase() == 'confirmed').length;
    final completedPregnancies = pregnancyRecords.where((r) => r.status?.toLowerCase() == 'completed').length;
    final abortedPregnancies = pregnancyRecords.where((r) => r.status?.toLowerCase() == 'abortion').length;

    final successRate = totalPregnancies > 0 ? (completedPregnancies / totalPregnancies * 100) : 0.0;

    // Calculate average pregnancy duration for completed pregnancies
    final completedWithDates = pregnancyRecords.where((r) =>
      r.status?.toLowerCase() == 'completed' &&
      r.startDate != null &&
      r.endDate != null
    );

    final avgDuration = completedWithDates.isNotEmpty
        ? completedWithDates.map((r) => r.endDate!.difference(r.startDate!).inDays)
            .reduce((a, b) => a + b) / completedWithDates.length
        : 0.0;

    return StatsCard(
      title: 'Pregnancy Statistics',
      titleColor: AppColors.breedingHeader,
      titleIcon: Icons.pregnant_woman,
      successRate: successRate,
      successRateLabel: 'Completion Rate',
      statItems: [
        {
          'label': 'Total Pregnancies',
          'value': totalPregnancies.toString(),
          'icon': Icons.pregnant_woman,
          'color': Colors.blue,
        },
        {
          'label': 'Active',
          'value': confirmedPregnancies.toString(),
          'icon': Icons.check_circle,
          'color': Colors.green,
        },
        {
          'label': 'Completed',
          'value': completedPregnancies.toString(),
          'icon': Icons.child_care,
          'color': Colors.purple,
        },
        {
          'label': 'Aborted',
          'value': abortedPregnancies.toString(),
          'icon': Icons.cancel,
          'color': Colors.red,
        },
        {
          'label': 'Avg Duration',
          'value': '${avgDuration.toStringAsFixed(0)} days',
          'icon': Icons.timer,
          'color': Colors.orange,
        },
      ],
    );
  }

  void _addPregnancyRecord(BuildContext context, BreedingDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => PregnancyFormDialog(
        initialCattleId: controller.cattle!.businessId,
        onSave: (record) async {
          final success = await controller.addPregnancyRecord(record);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Pregnancy record added successfully');
          }
        },
      ),
    );
  }

  void _viewRecordDetails(PregnancyRecordIsar record) {
    // TODO: Implement record details view
    MessageUtils.showInfo(context, 'Record details view not yet implemented');
  }

  // Removed unused _handleMenuAction method

  void _editRecord(PregnancyRecordIsar record) {
    final controller = Provider.of<BreedingDetailsController>(context, listen: false);
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => PregnancyFormDialog(
        record: record,
        initialCattleId: controller.cattle!.businessId,
        onSave: (updatedRecord) async {
          final success = await controller.updatePregnancyRecord(updatedRecord);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Pregnancy record updated successfully');
          }
        },
      ),
    );
  }

  void _deleteRecord(PregnancyRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Pregnancy Record'),
        content: const Text('Are you sure you want to delete this pregnancy record? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final controller = Provider.of<BreedingDetailsController>(context, listen: false);
              final success = await controller.deletePregnancyRecord(record.businessId!);
              if (!mounted) return;
              if (success) {
                MessageUtils.showSuccess(context, 'Pregnancy record deleted successfully');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
