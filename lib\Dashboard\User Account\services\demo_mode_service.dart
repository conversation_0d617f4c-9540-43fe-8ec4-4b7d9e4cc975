import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Breeding/models/breeding_record_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Weight/models/weight_record_isar.dart';
import '../../Farm Setup/models/farm_isar.dart';

import 'package:flutter/foundation.dart';
/// Service for managing demo mode functionality and sample data
class DemoModeService {
  static final DemoModeService _instance = DemoModeService._internal();
  factory DemoModeService() => _instance;
  DemoModeService._internal();

  final Logger _logger = Logger('DemoModeService');
  
  bool _isDemoMode = false;
  List<CattleIsar> _demoCattle = [];
  List<HealthRecordIsar> _demoHealthRecords = [];
  List<BreedingRecordIsar> _demoBreedingRecords = [];
  List<MilkRecordIsar> _demoMilkRecords = [];
  List<WeightRecordIsar> _demoWeightRecords = [];
  FarmIsar? _demoFarm;

  // Getters
  bool get isDemoMode => _isDemoMode;
  List<CattleIsar> get demoCattle => _demoCattle;
  List<HealthRecordIsar> get demoHealthRecords => _demoHealthRecords;
  List<BreedingRecordIsar> get demoBreedingRecords => _demoBreedingRecords;
  List<MilkRecordIsar> get demoMilkRecords => _demoMilkRecords;
  List<WeightRecordIsar> get demoWeightRecords => _demoWeightRecords;
  FarmIsar? get demoFarm => _demoFarm;

  /// Initialize demo mode with sample data
  Future<void> initializeDemoMode() async {
    try {
      debugPrint('🟠 [DemoModeService] initializeDemoMode() called');
      _logger.info('Initializing demo mode...');
      _isDemoMode = true;

      debugPrint('🟠 [DemoModeService] Creating demo farm...');
      await _createDemoFarm();
      debugPrint('🟠 [DemoModeService] Demo farm created successfully');

      debugPrint('🟠 [DemoModeService] Creating demo cattle...');
      await _createDemoCattle();
      debugPrint('🟠 [DemoModeService] Demo cattle created successfully');

      debugPrint('🟠 [DemoModeService] Creating demo health records...');
      await _createDemoHealthRecords();
      debugPrint('🟠 [DemoModeService] Demo health records created successfully');

      debugPrint('🟠 [DemoModeService] Creating demo breeding records...');
      await _createDemoBreedingRecords();
      debugPrint('🟠 [DemoModeService] Demo breeding records created successfully');

      debugPrint('🟠 [DemoModeService] Creating demo milk records...');
      await _createDemoMilkRecords();
      debugPrint('🟠 [DemoModeService] Demo milk records created successfully');

      debugPrint('🟠 [DemoModeService] Creating demo weight records...');
      await _createDemoWeightRecords();
      debugPrint('🟠 [DemoModeService] Demo weight records created successfully');

      debugPrint('🟠 [DemoModeService] Demo mode initialized successfully');
      _logger.info('Demo mode initialized successfully');
    } catch (e) {
      debugPrint('🔴 [DemoModeService] Error initializing demo mode: $e');
      debugPrint('🔴 [DemoModeService] Error type: $e.runtimeType');
      debugPrint('🔴 [DemoModeService] Stack trace: $StackTrace.current');
      _logger.severe('Error initializing demo mode: $e');
      rethrow;
    }
  }

  /// Exit demo mode and clear sample data
  void exitDemoMode() {
    _logger.info('Exiting demo mode...');
    _isDemoMode = false;
    _demoCattle.clear();
    _demoHealthRecords.clear();
    _demoBreedingRecords.clear();
    _demoMilkRecords.clear();
    _demoWeightRecords.clear();
    _demoFarm = null;
    _logger.info('Demo mode exited');
  }

  /// Check if a specific feature should show demo data
  bool shouldShowDemoData(String feature) {
    return _isDemoMode;
  }

  /// Get demo notification message
  String getDemoNotificationMessage() {
    return 'You are viewing demo data. Sign up to manage your own cattle records.';
  }

  // Private methods for creating demo data

  Future<void> _createDemoFarm() async {
    final now = DateTime.now();
    _demoFarm = FarmIsar.create(
      id: const Uuid().v4(),
      name: 'Sunny Meadows Farm',
      ownerName: 'John Smith',
      ownerContact: '(555) 123-4567',
      ownerEmail: '<EMAIL>',
      farmType: FarmType.mixed,
      cattleCount: 5,
      capacity: 100,
      createdAt: now,
      updatedAt: now,
    );

    // Set additional properties
    _demoFarm!.location = 'Green Valley, TX';
    _demoFarm!.address = '123 Farm Road, Green Valley, TX 75001';
  }

  Future<void> _createDemoCattle() async {
    final now = DateTime.now();

    _demoCattle = [
      CattleIsar.create(
        tagId: 'SM001',
        name: 'Bella',
        animalTypeId: 'cattle-type-1',
        breedId: 'holstein-breed',
        gender: CattleGender.female,
        source: CattleSource.purchased,
        dateOfBirth: now.subtract(const Duration(days: 1095)), // 3 years old
        color: 'Black and White',
        weight: 650.0,
        status: CattleStatus.active,
        farmId: _demoFarm?.farmBusinessId ?? '',
      ),
      CattleIsar.create(
        tagId: 'SM002',
        name: 'Thunder',
        animalTypeId: 'cattle-type-1',
        breedId: 'angus-breed',
        gender: CattleGender.male,
        source: CattleSource.purchased,
        dateOfBirth: now.subtract(const Duration(days: 1460)), // 4 years old
        color: 'Black',
        weight: 850.0,
        status: CattleStatus.active,
        farmId: _demoFarm?.farmBusinessId ?? '',
      ),
      CattleIsar.create(
        tagId: 'SM003',
        name: 'Daisy',
        animalTypeId: 'cattle-type-1',
        breedId: 'jersey-breed',
        gender: CattleGender.female,
        source: CattleSource.bornOnFarm,
        dateOfBirth: now.subtract(const Duration(days: 730)), // 2 years old
        color: 'Light Brown',
        weight: 450.0,
        status: CattleStatus.active,
        farmId: _demoFarm?.farmBusinessId ?? '',
      ),
      CattleIsar.create(
        tagId: 'SM004',
        name: 'Rocky',
        animalTypeId: 'cattle-type-1',
        breedId: 'hereford-breed',
        gender: CattleGender.male,
        source: CattleSource.purchased,
        dateOfBirth: now.subtract(const Duration(days: 2190)), // 6 years old
        color: 'Red and White',
        weight: 900.0,
        status: CattleStatus.active,
        farmId: _demoFarm?.farmBusinessId ?? '',
      ),
      CattleIsar.create(
        tagId: 'SM005',
        name: 'Luna',
        animalTypeId: 'cattle-type-1',
        breedId: 'holstein-breed',
        gender: CattleGender.female,
        source: CattleSource.bornOnFarm,
        dateOfBirth: now.subtract(const Duration(days: 365)), // 1 year old
        color: 'Black and White',
        weight: 350.0,
        status: CattleStatus.active,
        farmId: _demoFarm?.farmBusinessId ?? '',
      ),
    ];
  }

  Future<void> _createDemoHealthRecords() async {
    final now = DateTime.now();

    _demoHealthRecords = [
      HealthRecordIsar.create(
        cattleTagId: _demoCattle[0].tagId!,
        recordType: 'Vaccination',
        treatment: 'Annual vaccination - BVDV, IBR, PI3, BRSV',
        date: now.subtract(const Duration(days: 30)),
        veterinarian: 'Dr. Sarah Johnson',
        cost: 45.00,
        notes: 'No adverse reactions observed',
        description: 'Annual vaccination - BVDV, IBR, PI3, BRSV',
      ),
      HealthRecordIsar.create(
        cattleTagId: _demoCattle[1].tagId!,
        recordType: 'Health Check',
        treatment: 'Routine health examination',
        date: now.subtract(const Duration(days: 15)),
        veterinarian: 'Dr. Mike Wilson',
        cost: 75.00,
        notes: 'Excellent health condition',
        description: 'Routine health examination',
      ),
      HealthRecordIsar.create(
        cattleTagId: _demoCattle[2].tagId!,
        recordType: 'Treatment',
        treatment: 'Antibiotic treatment for minor infection',
        date: now.subtract(const Duration(days: 7)),
        veterinarian: 'Dr. Sarah Johnson',
        cost: 120.00,
        notes: 'Full recovery expected in 5-7 days',
        description: 'Antibiotic treatment for minor infection',
      ),
    ];
  }

  Future<void> _createDemoBreedingRecords() async {
    final now = DateTime.now();

    _demoBreedingRecords = [
      BreedingRecordIsar.create(
        cattleId: _demoCattle[0].tagId!,
        bullIdOrType: _demoCattle[1].tagId!,
        date: now.subtract(const Duration(days: 120)),
        method: 'Natural',
        expectedDate: now.add(const Duration(days: 160)),
        status: 'Confirmed Pregnant',
        notes: 'Pregnancy confirmed via ultrasound',
      ),
      BreedingRecordIsar.create(
        cattleId: _demoCattle[2].tagId!,
        bullIdOrType: 'AI-Premium-Bull',
        date: now.subtract(const Duration(days: 200)),
        method: 'Artificial Insemination',
        expectedDate: now.add(const Duration(days: 80)),
        status: 'Confirmed Pregnant',
        notes: 'AI performed with premium genetics',
      ),
    ];
  }

  Future<void> _createDemoMilkRecords() async {
    final now = DateTime.now();

    _demoMilkRecords = [
      MilkRecordIsar.create(
        cattleBusinessId: _demoCattle[0].businessId!,
        cattleTagId: _demoCattle[0].tagId!,
        date: now.subtract(const Duration(days: 1)),
        morningAmount: 15.5,
        eveningAmount: 14.2,
        notes: 'Excellent milk quality',
      ),
      MilkRecordIsar.create(
        cattleBusinessId: _demoCattle[0].businessId!,
        cattleTagId: _demoCattle[0].tagId!,
        date: now.subtract(const Duration(days: 2)),
        morningAmount: 16.0,
        eveningAmount: 13.8,
        notes: 'Consistent production',
      ),
      MilkRecordIsar.create(
        cattleBusinessId: _demoCattle[2].businessId!,
        cattleTagId: _demoCattle[2].tagId!,
        date: now.subtract(const Duration(days: 1)),
        morningAmount: 12.3,
        eveningAmount: 11.7,
        notes: 'Good quality milk',
      ),
    ];
  }

  Future<void> _createDemoWeightRecords() async {
    final now = DateTime.now();

    _demoWeightRecords = [
      WeightRecordIsar()
        ..businessId = const Uuid().v4()
        ..weight = 630.0
        ..measurementDate = now.subtract(const Duration(days: 30))
        ..notes = 'Monthly weight check'
        ..createdAt = now.subtract(const Duration(days: 30))
        ..updatedAt = now.subtract(const Duration(days: 30)),
      WeightRecordIsar()
        ..businessId = const Uuid().v4()
        ..weight = 650.0
        ..measurementDate = now
        ..notes = 'Good weight gain this month'
        ..createdAt = now
        ..updatedAt = now,
      WeightRecordIsar()
        ..businessId = const Uuid().v4()
        ..weight = 840.0
        ..measurementDate = now.subtract(const Duration(days: 30))
        ..notes = 'Maintaining healthy weight'
        ..createdAt = now.subtract(const Duration(days: 30))
        ..updatedAt = now.subtract(const Duration(days: 30)),
      WeightRecordIsar()
        ..businessId = const Uuid().v4()
        ..weight = 850.0
        ..measurementDate = now
        ..notes = 'Slight weight increase'
        ..createdAt = now
        ..updatedAt = now,
    ];
  }

  /// Get demo statistics for dashboard
  Map<String, dynamic> getDemoStatistics() {
    return {
      'totalCattle': _demoCattle.length,
      'activeCattle': _demoCattle.where((c) => c.status == CattleStatus.active).length,
      'pregnantCows': _demoBreedingRecords.where((b) => b.status == 'Confirmed Pregnant').length,
      'recentHealthRecords': _demoHealthRecords.where((h) =>
        h.date?.isAfter(DateTime.now().subtract(const Duration(days: 30))) ?? false
      ).length,
      'avgMilkProduction': _demoMilkRecords.isNotEmpty
        ? _demoMilkRecords.map((m) => m.totalYield).reduce((a, b) => a + b) / _demoMilkRecords.length
        : 0.0,
    };
  }
}
