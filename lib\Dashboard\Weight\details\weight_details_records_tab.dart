import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../controllers/weight_details_controller.dart';
import '../models/weight_record_isar.dart';
import '../dialogs/weight_form_dialog.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../widgets/universal_record_card.dart';

class WeightDetailsRecordsTab extends StatefulWidget {
  final WeightDetailsController controller;

  const WeightDetailsRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<WeightDetailsRecordsTab> createState() => _WeightDetailsRecordsTabState();
}

class _WeightDetailsRecordsTabState extends State<WeightDetailsRecordsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;



  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Handle loading state
        if (widget.controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // Handle error state
        if (widget.controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => widget.controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        final weightRecords = widget.controller.weightRecords;

        if (weightRecords.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.monitor_weight,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No Weight Records',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'No weight records found for ${widget.controller.cattle?.name ?? 'this cattle'}. Add weight measurements to track growth and health.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80), // Bottom padding for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section header
              _buildSectionHeader(weightRecords.length),
              const SizedBox(height: 16),
              // Records list
              _buildRecordsList(weightRecords),
            ],
          ),
        );
      },
    );
  }

  /// Build section header with record count
  Widget _buildSectionHeader(int recordCount) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.weightHeader.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.weightHeader.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.weightHeader,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.monitor_weight,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Weight Records',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.weightHeader,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$recordCount ${recordCount == 1 ? 'record' : 'records'} • Sorted by most recent',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.weightHeader.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.weightHeader.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              '$recordCount',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.weightHeader,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsList(List<WeightRecordIsar> records) {
    // Sort records by date (most recent first)
    final sortedRecords = List<WeightRecordIsar>.from(records)
      ..sort((a, b) => (b.measurementDate ?? DateTime.now()).compareTo(a.measurementDate ?? DateTime.now()));

    return Column(
      children: sortedRecords.map((record) => Padding(
        padding: const EdgeInsets.only(bottom: kSpacingSmall),
        child: UniversalRecordCard(
          row1Left: record.measurementDate != null
              ? DateFormat('MMM dd, yyyy').format(record.measurementDate!)
              : 'No date',
          row1Right: '${record.weight.toStringAsFixed(1)} kg',
          row1LeftIcon: Icons.calendar_today,
          row1RightIcon: Icons.monitor_weight,
          row2Left: 'Method: ${record.measurementMethod.toString().split('.').last}',
          row2Right: 'Quality: ${record.measurementQuality.toString().split('.').last}',
          row2RightIcon: Icons.star,
          row3Left: record.bodyConditionScore != null
              ? 'Body Condition: ${record.bodyConditionScore!.toStringAsFixed(1)}'
              : null,
          notes: record.notes?.isNotEmpty == true ? record.notes : null,
          primaryColor: AppColors.weightHeader,
          onTap: () => _editWeightRecord(record),
          onEdit: () => _editWeightRecord(record),
          onDelete: () => _deleteWeightRecord(record),
        ),
      )).toList(),
    );
  }

  // --- Action Handlers ---

  void _editWeightRecord(WeightRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => WeightFormDialog(
        cattle: widget.controller.cattle != null ? [widget.controller.cattle!] : [],
        existingRecord: record,
      ),
    );
  }

  Future<void> _deleteWeightRecord(WeightRecordIsar record) async {
    final confirmed = await WeightMessageUtils.showWeightRecordDeleteConfirmation(
      context,
      cattleName: widget.controller.cattle?.name ?? 'Unknown cattle',
      recordId: record.businessId,
      weight: '${record.weight.toStringAsFixed(1)} kg',
      date: record.measurementDate != null
          ? DateFormat('MMM dd, yyyy').format(record.measurementDate!)
          : 'Unknown date',
    );

    if (confirmed == true) {
      try {
        await widget.controller.deleteWeightRecord(record.id);
        if (mounted) {
          MessageUtils.showSuccess(context, 'Weight record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Error deleting record: $e');
        }
      }
    }
  }

  // --- Helper Methods ---




}