import 'package:logging/logging.dart';
import '../models/cattle_isar.dart';
import '../services/cattle_repository.dart';
import '../../User Account/services/data_ownership_service.dart';
import '../../User Account/services/auth_service.dart';

/// Service that wraps CattleRepository with data ownership and access control
class CattleOwnershipService {
  final Logger _logger = Logger('CattleOwnershipService');
  final CattleRepository _cattleRepository;
  final DataOwnershipService _ownershipService;
  final AuthService _authService;

  CattleOwnershipService(
    this._cattleRepository,
    this._ownershipService,
    this._authService,
  );

  /// Watch all cattle that the current user can access
  Stream<List<CattleIsar>> watchUserCattle() {
    return _cattleRepository.watchAllCattle().map((allCattle) {
      if (!_authService.isAuthenticated) return <CattleIsar>[];
      
      final accessibleCattle = _ownershipService.filterAccessibleCattle(allCattle);
      _ownershipService.logDataAccess(
        operation: 'watch',
        resourceType: 'cattle',
        resourceId: 'user_cattle_list',
      );
      
      return accessibleCattle;
    });
  }

  /// Save cattle with ownership validation
  Future<bool> saveCattle(CattleIsar cattle) async {
    try {
      if (!_authService.isAuthenticated) {
        _logger.warning('Attempted to save cattle without authentication');
        return false;
      }

      // For new cattle (no ID), set ownership
      if (cattle.id == 0) {
        _ownershipService.setOwnership(cattle);
        _logger.info('Creating new cattle $cattle.tagId for user ${_authService.currentUser?.email}');
      } else {
        // For existing cattle, check modify permission
        if (!_ownershipService.canModifyCattle(cattle)) {
          _logger.warning('User ${_authService.currentUser?.email} attempted to modify cattle $cattle.tagId without permission');
          return false;
        }
        _logger.info('Updating cattle $cattle.tagId for user ${_authService.currentUser?.email}');
      }

      cattle.updatedAt = DateTime.now();
      await _cattleRepository.saveCattle(cattle);

      _ownershipService.logDataAccess(
        operation: cattle.id == 0 ? 'create' : 'update',
        resourceType: 'cattle',
        resourceId: cattle.businessId ?? cattle.tagId ?? 'unknown',
      );

      return true;
    } catch (e) {
      _logger.severe('Error saving cattle: $e');
      _ownershipService.logDataAccess(
        operation: 'save',
        resourceType: 'cattle',
        resourceId: cattle.businessId ?? cattle.tagId ?? 'unknown',
        success: false,
      );
      return false;
    }
  }

  /// Delete cattle with ownership validation
  Future<bool> deleteCattle(CattleIsar cattle) async {
    try {
      if (!_authService.isAuthenticated) {
        _logger.warning('Attempted to delete cattle without authentication');
        return false;
      }

      if (!_ownershipService.canDeleteCattle(cattle)) {
        _logger.warning('User ${_authService.currentUser?.email} attempted to delete cattle $cattle.tagId without permission');
        return false;
      }

      await _cattleRepository.deleteCattle(cattle.id);
      
      _ownershipService.logDataAccess(
        operation: 'delete',
        resourceType: 'cattle',
        resourceId: cattle.businessId ?? cattle.tagId ?? 'unknown',
      );

      _logger.info('Deleted cattle $cattle.tagId for user ${_authService.currentUser?.email}');
      return true;
    } catch (e) {
      _logger.severe('Error deleting cattle: $e');
      _ownershipService.logDataAccess(
        operation: 'delete',
        resourceType: 'cattle',
        resourceId: cattle.businessId ?? cattle.tagId ?? 'unknown',
        success: false,
      );
      return false;
    }
  }

  /// Get all user's cattle (one-time fetch)
  Future<List<CattleIsar>> getUserCattle() async {
    try {
      if (!_authService.isAuthenticated) return <CattleIsar>[];

      final allCattle = await _cattleRepository.getAllCattle();
      final userCattle = _ownershipService.filterAccessibleCattle(allCattle);

      _ownershipService.logDataAccess(
        operation: 'read',
        resourceType: 'cattle',
        resourceId: 'user_cattle_list',
      );

      return userCattle;
    } catch (e) {
      _logger.severe('Error getting user cattle: $e');
      return <CattleIsar>[];
    }
  }

  /// Get cattle by tag ID with ownership validation
  Future<CattleIsar?> getCattleByTagId(String tagId) async {
    try {
      if (!_authService.isAuthenticated) return null;

      final cattle = await _cattleRepository.getCattleByTagId(tagId);
      if (cattle == null) return null;

      if (!_ownershipService.canAccessCattle(cattle)) {
        _logger.warning('User ${_authService.currentUser?.email} attempted to access cattle $cattle.tagId without permission');
        return null;
      }

      _ownershipService.logDataAccess(
        operation: 'read',
        resourceType: 'cattle',
        resourceId: cattle.businessId ?? tagId,
      );

      return cattle;
    } catch (e) {
      _logger.severe('Error getting cattle by tag ID: $e');
      return null;
    }
  }

  /// Transfer cattle ownership
  Future<bool> transferOwnership({
    required CattleIsar cattle,
    required String newOwnerUserId,
    String? newFarmId,
  }) async {
    try {
      if (!_authService.isAuthenticated) return false;

      final success = await _ownershipService.transferCattleOwnership(
        cattle: cattle,
        newOwnerUserId: newOwnerUserId,
        newFarmId: newFarmId,
      );

      if (success) {
        await _cattleRepository.saveCattle(cattle);
        _ownershipService.logDataAccess(
          operation: 'transfer',
          resourceType: 'cattle',
          resourceId: cattle.businessId ?? cattle.tagId ?? 'unknown',
        );
      }

      return success;
    } catch (e) {
      _logger.severe('Error transferring cattle ownership: $e');
      return false;
    }
  }

  /// Get user's cattle statistics
  Future<OwnershipStats> getUserCattleStats() async {
    try {
      if (!_authService.isAuthenticated) {
        return OwnershipStats(
          totalCattle: 0,
          activeCattle: 0,
          soldCattle: 0,
          deceasedCattle: 0,
          transferredCattle: 0,
        );
      }

      final allCattle = await _cattleRepository.getAllCattle();
      return _ownershipService.getOwnershipStats(allCattle);
    } catch (e) {
      _logger.severe('Error getting user cattle stats: $e');
      return OwnershipStats(
        totalCattle: 0,
        activeCattle: 0,
        soldCattle: 0,
        deceasedCattle: 0,
        transferredCattle: 0,
      );
    }
  }

  /// Validate cattle access
  bool canAccessCattle(CattleIsar cattle) {
    return _ownershipService.canAccessCattle(cattle);
  }

  /// Validate cattle modification
  bool canModifyCattle(CattleIsar cattle) {
    return _ownershipService.canModifyCattle(cattle);
  }

  /// Validate cattle deletion
  bool canDeleteCattle(CattleIsar cattle) {
    return _ownershipService.canDeleteCattle(cattle);
  }

  /// Create new cattle with proper ownership
  CattleIsar createCattleWithOwnership({
    required String tagId,
    required String name,
    required String animalTypeId,
    required String breedId,
    required CattleGender gender,
    required CattleSource source,
    String? farmId,
    String? motherTagId,
    String? motherBusinessId,
    DateTime? dateOfBirth,
    DateTime? purchaseDate,
    double? purchasePrice,
    double? weight,
    String? color,
    String? notes,
    String? photoPath,
    CattleCategory? category,
    CattleStatus? status,
    CattleStage? stage,
  }) {
    if (!_authService.isAuthenticated) {
      throw Exception('User not authenticated');
    }

    final cattle = CattleIsar.create(
      tagId: tagId,
      name: name,
      animalTypeId: animalTypeId,
      breedId: breedId,
      gender: gender,
      source: source,
      ownerUserId: _authService.currentUser!.businessId,
      farmId: farmId,
      motherTagId: motherTagId,
      motherBusinessId: motherBusinessId,
      dateOfBirth: dateOfBirth,
      purchaseDate: purchaseDate,
      purchasePrice: purchasePrice,
      weight: weight,
      color: color,
      notes: notes,
      photoPath: photoPath,
      category: category,
      status: status,
      stage: stage,
    );

    _logger.info('Created cattle $cattle.tagId with ownership for user ${_authService.currentUser?.email}');
    return cattle;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _authService.isAuthenticated;

  /// Get current user ID
  String? get currentUserId => _authService.currentUser?.businessId;
}
