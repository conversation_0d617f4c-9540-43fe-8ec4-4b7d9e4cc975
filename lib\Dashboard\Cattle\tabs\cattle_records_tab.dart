import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/cattle_controller.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';
import '../../widgets/filters/filter_widgets.dart';
import '../details/cattle_details_screen.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../models/cattle_isar.dart';
import '../utils/cattle_age_calculator.dart';
import '../../User Account/guards/demo_guard.dart';

class CattleRecordsTab extends StatefulWidget {
  final CattleController? controller; // Made optional to support Provider pattern

  const CattleRecordsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<CattleRecordsTab> createState() => _CattleRecordsTabState();
}

class _CattleRecordsTabState extends State<CattleRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  CattleController get _controller => widget.controller ?? context.read<CattleController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // Apply filters at database level for ultimate scalability
    _controller.applyFilters(_filterController);
  }

  /// Age group filtering still needs to be applied client-side
  /// due to complex age calculation logic that can't be easily translated to database queries
  List<CattleIsar> _applyAgeGroupFilter(List<CattleIsar> cattle) {
    final ageGroupFilter = _filterController.globalFilters[AppFilterWidget.ageGroupKey];
    if (ageGroupFilter == null || ageGroupFilter == 'All') {
      return cattle;
    }

    return cattle.where((cattle) {
      return CattleAgeCalculator.matchesAgeGroup(cattle, ageGroupFilter);
    }).toList();
  }















  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    // Check if we have data to display
    if (_controller.totalCattle == 0) {
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    // Get cattle data - now pre-filtered at database level
    // Apply age group filter client-side (complex logic that can't be done in database)
    final databaseFilteredCattle = _controller.cattle;
    final cattle = _applyAgeGroupFilter(databaseFilteredCattle);
    final allCattleCount = _controller.totalCattle; // This represents total before filtering

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.cattle,
          moduleName: 'cattle',
          sortFields: const [...SortField.commonFields, ...SortField.cattleFields],
          searchHint: 'Search cattle by name or tag ID...',
          totalCount: allCattleCount,
          filteredCount: cattle.length,
        ),

        // Cattle List - data is already filtered at database level
        Expanded(
          child: cattle.isEmpty
              ? _buildEmptyState(allCattleCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    // UX Decision: Pull-to-refresh acts as a complete reset, clearing filters
                    // to provide users with a "fresh start" experience. This is intuitive
                    // behavior when users want to see all data without current filter constraints.
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: cattle.length,
                    itemBuilder: (context, index) {
                      final cattleItem = cattle[index];
                      return _buildCattleCard(cattleItem);
                    },
                  ),
                ),
        ),
      ],
    );
  }



  /// Consolidated empty state builder - single source of truth for all empty states
  ///
  /// [isCompletelyEmpty] - true when no cattle exist at all, false when cattle exist but filters hide them
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    // Get the tab color for Records tab (index 1) from cattle module
    const tabColor = Color(0xFF2E7D32); // Green color for Records tab

    if (isCompletelyEmpty) {
      // No cattle records exist - show call-to-action to add first cattle
      return UniversalTabEmptyState.forTab(
        title: 'No Cattle Records',
        message: 'Add your first cattle to start managing your herd.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddCattleDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Cattle exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Cattle',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildCattleCard(CattleIsar cattle) {
    final animalType = _controller.getAnimalType(cattle.animalTypeId);
    final breed = _controller.getBreed(cattle.breedId);

    // Calculate age with unified logic
    String age = CattleAgeCalculator.calculateAgeDisplay(cattle);

    // Format row 1: Name (TagID) + age
    String nameWithTag = cattle.name ?? 'Unnamed Cattle';
    if (cattle.tagId != null && cattle.tagId!.isNotEmpty) {
      nameWithTag += ' (${cattle.tagId!.toUpperCase()})';
    }

    // Format row 2: Animal type and breed
    String animalTypeText = animalType?.name ?? 'Unknown type';
    String breedText = breed?.name ?? 'Unknown breed';

    return UniversalRecordCard(
      row1Left: nameWithTag,
      row1Right: age,
      row1LeftIcon: Icons.pets,
      row1RightIcon: Icons.cake,
      row2Left: animalTypeText,
      row2Right: breedText,
      row2LeftIcon: Icons.category,
      row2RightIcon: Icons.pets,
      notes: cattle.notes?.isNotEmpty == true ? cattle.notes : null,
      primaryColor: AppColors.cattleHeader,
      onTap: () => _navigateToCattleDetail(cattle),
      onEdit: () => _showEditCattleDialog(cattle),
      onDelete: () => _showDeleteConfirmation(cattle),
    );
  }



  void _navigateToCattleDetail(CattleIsar cattle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CattleDetailsScreen(
          existingCattle: cattle,
          businessId: _controller.businessId,
          onCattleUpdated: (updatedCattle) {
            _controller.updateCattle(updatedCattle);
          },
        ),
      ),
    );
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Consolidated dialog helper for both add and edit operations
  ///
  /// [cattle] - null for add operation, existing cattle for edit operation
  void _showCattleFormDialog([CattleIsar? cattle]) {
    final isEditing = cattle != null;

    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        cattle: cattle, // null for add, existing cattle for edit
        businessId: _controller.businessId,
        animalTypes: _controller.animalTypes,
        onSave: (cattleData) async {
          if (isEditing) {
            await _controller.updateCattle(cattleData);
          } else {
            await _controller.addCattle(cattleData);
          }
        },
      ),
    );
  }

  /// Show dialog to add new cattle
  void _showAddCattleDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_cattle')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_cattle');
      return;
    }
    _showCattleFormDialog();
  }

  /// Show dialog to edit existing cattle
  void _showEditCattleDialog(CattleIsar cattle) {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('edit_cattle')) {
      DemoGuard.showDemoRestrictionMessage(context, 'edit_cattle');
      return;
    }
    _showCattleFormDialog(cattle);
  }

  void _showDeleteConfirmation(CattleIsar cattle) {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('delete_cattle')) {
      DemoGuard.showDemoRestrictionMessage(context, 'delete_cattle');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Cattle'),
        content: Text('Are you sure you want to delete ${cattle.name ?? 'this cattle'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _controller.deleteCattle(cattle.businessId!);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}