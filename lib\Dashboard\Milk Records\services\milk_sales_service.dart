import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';
import '../models/milk_sale_isar.dart';
import '../services/milk_service.dart';
import '../../../services/database/isar_service.dart';

class MilkSalesService {
  static final Logger _logger = Logger('MilkSalesService');
  static Database? _database;
  final IsarService _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final path = await getDatabasesPath();
    return openDatabase(
      join(path, 'cattle_manager.db'),
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS milk_sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            buyer_name TEXT NOT NULL,
            quantity_sold REAL NOT NULL,
            rate_per_liter REAL NOT NULL,
            total_amount REAL NOT NULL,
            is_paid INTEGER NOT NULL,
            calf_usage REAL NOT NULL,
            home_usage REAL NOT NULL,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        ''');

        await db.execute('''
          CREATE TABLE IF NOT EXISTS milk_buyers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            created_at TEXT NOT NULL
          )
        ''');
      },
      version: 1,
    );
  }

  Future<int> addMilkSale(MilkSaleIsar sale) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.milkSaleIsars.put(sale);
      });
      
      _logger.info('Adding milk sale for date: $sale.date, buyer: $sale.buyer, quantity: $sale.quantity');
      return sale.id;
    } catch (e, stack) {
      _logger.severe('Failed to add milk sale: $e', e, stack);
      rethrow;
    }
  }

  Future<List<MilkSaleIsar>> getMilkSales() async {
    try {
      return await _isar.milkSaleIsars.where().findAll();
    } catch (e, stack) {
      _logger.severe('Failed to fetch milk sales', e, stack);
      rethrow;
    }
  }

  Future<List<MilkSaleIsar>> getMilkSalesForDate(DateTime date) async {
    try {
      // Normalize date to start and end of day
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      
      // Log the query for debugging
      _logger.info('Querying milk sales for date: $startOfDay');
      
      final sales = await _isar.milkSaleIsars
          .filter()
          .dateBetween(startOfDay, endOfDay)
          .findAll();
      
      _logger.info('Found $sales.length sales records for date: $startOfDay');
      return sales;
    } catch (e, stack) {
      _logger.severe('Failed to fetch milk sales for date', e, stack);
      rethrow;
    }
  }

  Future<double> getAvailableMilkForDate(DateTime date) async {
    try {
      // Get milk production records for the date
      final milkService = MilkService();
      final milkRecords = await milkService.getMilkRecordsForDate(date);
      
      // Calculate total milk production for the date
      double totalProduction = 0.0;
      for (var record in milkRecords) {
        totalProduction += (record.morningAmount ?? 0.0) + (record.eveningAmount ?? 0.0);
      }
      
      // Get all milk sales for this date to calculate remaining milk
      final sales = await getMilkSalesForDate(date);
      
      // Calculate total used milk for this date
      double totalUsed = 0.0;
      for (var sale in sales) {
        totalUsed += sale.quantity ?? 0.0;
      }
      
      // Return available milk
      return totalProduction - totalUsed;
    } catch (e, stack) {
      _logger.severe('Failed to get available milk for date', e, stack);
      rethrow;
    }
  }

  Future<void> addBuyer(String name) async {
    try {
      final db = await database;
      await db.insert('milk_buyers', {
        'name': name,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e, stack) {
      _logger.severe('Failed to add buyer', e, stack);
      rethrow;
    }
  }

  Future<List<String>> getBuyers() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query('milk_buyers');
      return List.generate(maps.length, (i) => maps[i]['name'] as String);
    } catch (e, stack) {
      _logger.severe('Failed to get buyers', e, stack);
      rethrow;
    }
  }

  Future<void> updateMilkSale(MilkSaleIsar sale) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.milkSaleIsars.put(sale);
      });
      _logger.info('Updated milk sale: $sale.saleId');
    } catch (e, stack) {
      _logger.severe('Failed to update milk sale', e, stack);
      rethrow;
    }
  }

  Future<void> deleteMilkSale(Id id) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.milkSaleIsars.delete(id);
      });
      _logger.info('Deleted milk sale: $id');
    } catch (e, stack) {
      _logger.severe('Failed to delete milk sale', e, stack);
      rethrow;
    }
  }

  Future<List<MilkSaleIsar>> getAllMilkSales() async {
    try {
      return await _isar.milkSaleIsars.where().findAll();
    } catch (e, stack) {
      _logger.severe('Failed to get all milk sales', e, stack);
      rethrow;
    }
  }
}
