import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import '../../../services/logging_service.dart';
import '../../../config/api_config.dart';
import 'package:get_it/get_it.dart';
import '../models/cattle_isar.dart';
import 'cattle_repository.dart';

import 'package:flutter/foundation.dart';
/// Dedicated bidirectional sync service for Cattle module
/// Separates sync logic from repository CRUD operations
/// Follows consistent pattern across all modules
class CattleSyncService {
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  
  // Sync-related constants
  static const String _lastSyncKey = 'last_cattle_sync';
  final LoggingService _logger = LoggingService();

  /// Get last sync time for incremental sync
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSync = prefs.getString(_lastSyncKey);
    return lastSync != null ? DateTime.parse(lastSync) : null;
  }

  /// Set last sync time
  Future<void> setLastSyncTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, time.toIso8601String());
  }

  /// Get all cattle records for sync
  Future<List<CattleIsar>> getAllCattle() async {
    return await _cattleRepository.getAllCattle();
  }

  /// Get cattle records modified since last sync
  Future<List<CattleIsar>> getModifiedCattleSince(DateTime? lastSync) async {
    if (lastSync == null) {
      return await getAllCattle();
    }
    
    final isar = GetIt.instance<Isar>();
    return await isar.cattleIsars
        .filter()
        .updatedAtGreaterThan(lastSync)
        .findAll();
  }

  /// Convert cattle record to sync-friendly map
  Map<String, dynamic> _cattleToSyncMap(CattleIsar cattle) {
    return {
      'id': cattle.businessId,
      'tagId': cattle.tagId,
      'name': cattle.name,
      'animalTypeId': cattle.animalTypeId,
      'breedId': cattle.breedId,
      'gender': cattle.gender.toString().split('.').last,
      'source': cattle.source.toString().split('.').last,
      'dateOfBirth': cattle.dateOfBirth?.toIso8601String(),
      'purchaseDate': cattle.purchaseDate?.toIso8601String(),
      'purchasePrice': cattle.purchasePrice,
      'weight': cattle.weight,
      'color': cattle.color,
      'notes': cattle.notes,
      'photoPath': cattle.photoPath,
      'status': cattle.status.toString().split('.').last,
      'createdAt': cattle.createdAt?.toIso8601String(),
      'updatedAt': cattle.updatedAt?.toIso8601String(),
    };
  }

  /// Convert sync map to cattle record
  CattleIsar _cattleFromSyncMap(Map<String, dynamic> map) {
    final cattle = CattleIsar()
      ..businessId = map['id'] as String?
      ..tagId = map['tagId'] as String?
      ..name = map['name'] as String?
      ..animalTypeId = map['animalTypeId'] as String?
      ..breedId = map['breedId'] as String?
      ..color = map['color'] as String?
      ..notes = map['notes'] as String?
      ..photoPath = map['photoPath'] as String?
      ..purchasePrice = map['purchasePrice'] != null ? (map['purchasePrice'] as num).toDouble() : null
      ..weight = map['weight'] != null ? (map['weight'] as num).toDouble() : null;

    // Handle enums
    if (map['gender'] != null) {
      cattle.gender = CattleGender.values.firstWhere(
        (e) => e.toString().split('.').last == map['gender'],
        orElse: () => CattleGender.unknown,
      );
    }

    if (map['source'] != null) {
      cattle.source = CattleSource.values.firstWhere(
        (e) => e.toString().split('.').last == map['source'],
        orElse: () => CattleSource.unknown,
      );
    }

    if (map['status'] != null) {
      cattle.status = CattleStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => CattleStatus.active,
      );
    }

    // Handle dates
    if (map['dateOfBirth'] != null) {
      cattle.dateOfBirth = DateTime.parse(map['dateOfBirth']);
    }
    if (map['purchaseDate'] != null) {
      cattle.purchaseDate = DateTime.parse(map['purchaseDate']);
    }
    if (map['createdAt'] != null) {
      cattle.createdAt = DateTime.parse(map['createdAt']);
    }
    if (map['updatedAt'] != null) {
      cattle.updatedAt = DateTime.parse(map['updatedAt']);
    }

    return cattle;
  }

  /// Bidirectional sync with external API
  Future<bool> syncData() async {
    try {
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] syncData() called at ${DateTime.now()}');

      // Check if API sync is enabled - ALWAYS check this first to prevent HTTP requests
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Checking API sync configuration...');
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] ApiConfig.enableApiSync: $ApiConfig.enableApiSync');
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] ApiConfig.isApiSyncAvailable: $ApiConfig.isApiSyncAvailable');

      if (!ApiConfig.enableApiSync) {
        debugPrint('⚠️ [CATTLE_SYNC_DEBUG] API sync disabled in configuration, returning success');
        _logger.info('Cattle sync skipped - API sync disabled in configuration (local-only mode)');
        return true; // Return success for local-only mode
      }

      if (!ApiConfig.isApiSyncAvailable) {
        debugPrint('⚠️ [CATTLE_SYNC_DEBUG] API sync not available, returning success');
        _logger.info('Cattle sync skipped - API sync not available (local-only mode)');
        return true; // Return success for local-only mode
      }

      debugPrint('🐄 [CATTLE_SYNC_DEBUG] API sync is enabled and available, proceeding...');

      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Getting last sync time...');
      final lastSync = await getLastSyncTime();
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Last sync time: ${lastSync?.toIso8601String() ?? 'never'}');

      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Getting modified cattle records since last sync...');
      final localRecords = await getModifiedCattleSince(lastSync);
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Found $localRecords.length modified cattle records');

      // Prepare data for API
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Preparing sync data for API...');
      final syncData = {
        'lastSync': lastSync?.toIso8601String(),
        'records': localRecords.map((r) => _cattleToSyncMap(r)).toList(),
      };
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Sync data prepared - lastSync: ${syncData['lastSync']}, records count: ${(syncData['records'] as List).length}');

      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Making HTTP POST request to: $ApiConfig.cattleSync');
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Request headers: $ApiConfig.defaultHeaders');
      debugPrint('🐄 [CATTLE_SYNC_DEBUG] Request timeout: $ApiConfig.syncTimeout');

      final response = await http.post(
        Uri.parse(ApiConfig.cattleSync),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode(syncData),
      ).timeout(ApiConfig.syncTimeout);

      debugPrint('🐄 [CATTLE_SYNC_DEBUG] HTTP response received - status code: $response.statusCode');

      if (response.statusCode == 200) {
        debugPrint('✅ [CATTLE_SYNC_DEBUG] HTTP request successful (200)');

        debugPrint('🐄 [CATTLE_SYNC_DEBUG] Validating response format...');
        if (!response.body.startsWith('{')) {
          debugPrint('❌ [CATTLE_SYNC_DEBUG] Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          _logger.error(
              'Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          return false;
        }

        debugPrint('🐄 [CATTLE_SYNC_DEBUG] Parsing response body...');
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        debugPrint('🐄 [CATTLE_SYNC_DEBUG] Response data keys: ${responseData.keys.toList()}');

        if (!responseData.containsKey('records')) {
          debugPrint('❌ [CATTLE_SYNC_DEBUG] Invalid response format: Missing records field');
          _logger.error('Invalid response format: Missing records field');
          return false;
        }

        // Process server records and handle conflicts
        final List<dynamic> serverRecords = responseData['records'];
        debugPrint('🐄 [CATTLE_SYNC_DEBUG] Processing $serverRecords.length server records...');
        await _processServerRecords(serverRecords);
        debugPrint('✅ [CATTLE_SYNC_DEBUG] Server records processed successfully');

        debugPrint('🐄 [CATTLE_SYNC_DEBUG] Setting last sync time to now...');
        await setLastSyncTime(DateTime.now());

        debugPrint('✅ [CATTLE_SYNC_DEBUG] Cattle records synchronized successfully');
        _logger.info('Cattle records synchronized successfully');
        return true;
      } else {
        debugPrint('❌ [CATTLE_SYNC_DEBUG] HTTP request failed with status code: $response.statusCode');
        debugPrint('❌ [CATTLE_SYNC_DEBUG] Response body: $response.body');
        _logger.error('Failed to sync cattle records: $response.statusCode');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [CATTLE_SYNC_DEBUG] Exception during cattle sync: $e');
      debugPrint('❌ [CATTLE_SYNC_DEBUG] Error type: $e.runtimeType');
      debugPrint('❌ [CATTLE_SYNC_DEBUG] Stack trace: $StackTrace.current');

      _logger.error('Error syncing cattle records: $e');
      return false;
    }
  }

  /// Process server records and handle conflicts
  Future<void> _processServerRecords(List<dynamic> serverRecords) async {
    debugPrint('🐄 [CATTLE_SYNC_DEBUG] _processServerRecords() called with $serverRecords.length records');

    for (int i = 0; i < serverRecords.length; i++) {
      final recordData = serverRecords[i];
      try {
        debugPrint('🐄 [CATTLE_SYNC_DEBUG] Processing server record ${i + 1}/$serverRecords.length...');

        final serverRecord = _cattleFromSyncMap(recordData);
        debugPrint('🐄 [CATTLE_SYNC_DEBUG] Server record parsed - ID: $serverRecord.businessId, Tag: $serverRecord.tagId');

        final isar = GetIt.instance<Isar>();
        final existingRecord = await isar.cattleIsars
            .filter()
            .businessIdEqualTo(serverRecord.businessId)
            .findFirst();

        if (existingRecord == null) {
          debugPrint('🐄 [CATTLE_SYNC_DEBUG] New record from server, saving...');
          // New record from server
          await _cattleRepository.saveCattle(serverRecord);
          debugPrint('✅ [CATTLE_SYNC_DEBUG] New server record saved successfully');
        } else {
          debugPrint('🐄 [CATTLE_SYNC_DEBUG] Existing record found, resolving conflict...');
          // Handle conflict resolution - server wins for now
          final resolvedRecord = _resolveConflict(existingRecord, serverRecord);
          await _cattleRepository.saveCattle(resolvedRecord);
          debugPrint('✅ [CATTLE_SYNC_DEBUG] Conflict resolved and record updated');
        }
      } catch (e) {
        debugPrint('❌ [CATTLE_SYNC_DEBUG] Error processing server record ${i + 1}: $e');
        debugPrint('❌ [CATTLE_SYNC_DEBUG] Error type: $e.runtimeType');
        _logger.error('Error processing server record: $e');
      }
    }

    debugPrint('✅ [CATTLE_SYNC_DEBUG] All server records processed');
  }

  /// Simple conflict resolution - server wins
  /// In a more sophisticated implementation, this could use timestamps,
  /// user preferences, or merge strategies
  CattleIsar _resolveConflict(CattleIsar local, CattleIsar server) {
    // For now, server record wins in conflicts
    // Keep the local Isar ID but use server data
    server.id = local.id;
    return server;
  }
}
