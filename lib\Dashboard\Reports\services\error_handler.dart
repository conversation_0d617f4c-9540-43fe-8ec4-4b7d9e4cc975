import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import '../models/report_models.dart';

/// Comprehensive error handling service for Reports system
/// 
/// Provides user-friendly error messages, recovery options, and detailed logging
/// for all report generation and export operations.
class ReportsErrorHandler {
  /// Handle report generation errors with user-friendly fallbacks
  static ReportData handleReportError(
    dynamic error, 
    ReportType type, {
    String? context,
    VoidCallback? retryCallback,
  }) {
    // Log detailed error for debugging
    developer.log(
      'Report generation failed: $error',
      name: 'Reports.Error',
      error: error,
      level: 1000, // Error level
    );

    // Create user-friendly error report
    final errorMetrics = <String, ReportMetric>{};
    final insights = <String>[];

    // Add error metric with retry option
    errorMetrics['error'] = ReportMetric.withInsight(
      title: 'Error Loading ${type.displayName}',
      value: 'Failed to load',
      icon: Icons.error_outline,
      color: Colors.red,
      insight: retryCallback != null ? 'Tap to retry' : 'Please try again later',
      onTap: retryCallback,
    );

    // Add context-specific insights
    if (error.toString().contains('network') || error.toString().contains('connection')) {
      insights.add('Check your internet connection and try again');
      errorMetrics['network'] = ReportMetric.withInsight(
        title: 'Network Issue',
        value: 'Offline',
        icon: Icons.wifi_off,
        color: Colors.orange,
        insight: 'Connection required for data sync',
      );
    } else if (error.toString().contains('permission')) {
      insights.add('Storage permission required for data access');
      errorMetrics['permission'] = ReportMetric.withInsight(
        title: 'Permission Required',
        value: 'Denied',
        icon: Icons.lock,
        color: Colors.red,
        insight: 'Grant storage permission in settings',
      );
    } else if (error.toString().contains('memory') || error.toString().contains('OutOfMemory')) {
      insights.add('Dataset too large - try filtering data');
      errorMetrics['memory'] = ReportMetric.withInsight(
        title: 'Memory Limit',
        value: 'Exceeded',
        icon: Icons.memory,
        color: Colors.red,
        insight: 'Reduce data range or apply filters',
      );
    } else {
      insights.add('An unexpected error occurred. Please contact support if this persists.');
    }

    return ReportData(
      title: 'Error: ${type.displayName}',
      subtitle: context ?? 'Report generation failed',
      generated: DateTime.now(),
      metrics: errorMetrics,
      chartData: [],
      tableData: [],
      insights: insights,
      type: type,
    );
  }

  /// Handle export errors with specific recovery options
  static Map<String, dynamic> handleExportError(
    dynamic error,
    String exportType, {
    String? filePath,
    VoidCallback? retryCallback,
  }) {
    developer.log(
      'Export failed: $exportType - $error',
      name: 'Reports.Export.Error',
      error: error,
      level: 1000,
    );

    String userMessage;
    String actionText = 'Try Again';
    IconData icon = Icons.error;
    Color color = Colors.red;

    if (error.toString().contains('storage') || error.toString().contains('space')) {
      userMessage = 'Insufficient storage space for export';
      actionText = 'Free Space';
      icon = Icons.storage;
      color = Colors.orange;
    } else if (error.toString().contains('permission')) {
      userMessage = 'Permission required to save file';
      actionText = 'Grant Permission';
      icon = Icons.lock;
    } else if (error.toString().contains('network')) {
      userMessage = 'Network error during export';
      actionText = 'Retry';
      icon = Icons.wifi_off;
      color = Colors.orange;
    } else if (error.toString().contains('timeout')) {
      userMessage = 'Export timed out - dataset may be too large';
      actionText = 'Reduce Data';
      icon = Icons.timer_off;
      color = Colors.orange;
    } else {
      userMessage = 'Export failed unexpectedly';
    }

    return {
      'success': false,
      'error': error.toString(),
      'userMessage': userMessage,
      'actionText': actionText,
      'icon': icon,
      'color': color,
      'canRetry': retryCallback != null,
      'retryCallback': retryCallback,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Validate report data before processing
  static ValidationResult validateReportData(ReportData reportData) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check for empty data
    if (reportData.metrics.isEmpty) {
      errors.add('No metrics data available');
    }

    // Check for excessive data that might cause performance issues
    if (reportData.chartData.length > 1000) {
      warnings.add('Large dataset detected - consider filtering data');
    }

    // Check for missing required fields
    if (reportData.title.isEmpty) {
      errors.add('Report title is required');
    }

    // Check date range validity
    if (reportData.startDate != null && reportData.endDate != null) {
      if (reportData.startDate!.isAfter(reportData.endDate!)) {
        errors.add('Start date cannot be after end date');
      }
      
      final daysDifference = reportData.endDate!.difference(reportData.startDate!).inDays;
      if (daysDifference > 365) {
        warnings.add('Large date range may affect performance');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate filter state before applying
  static ValidationResult validateFilterState(FilterState filter) {
    final errors = <String>[];
    final warnings = <String>[];

    // Check date range
    if (filter.startDate != null && filter.endDate != null) {
      if (filter.startDate!.isAfter(filter.endDate!)) {
        errors.add('Start date cannot be after end date');
      }
      
      if (filter.startDate!.isAfter(DateTime.now())) {
        errors.add('Start date cannot be in the future');
      }
    }

    // Check cattle IDs
    if (filter.cattleIds != null && filter.cattleIds!.length > 100) {
      warnings.add('Large number of cattle selected may affect performance');
    }

    // Check search query
    if (filter.searchQuery != null && filter.searchQuery!.length < 2) {
      warnings.add('Search query too short - use at least 2 characters');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Create error dialog content for UI
  static Map<String, dynamic> createErrorDialog({
    required String title,
    required String message,
    String? actionText,
    VoidCallback? actionCallback,
    bool canDismiss = true,
  }) {
    return {
      'title': title,
      'message': message,
      'actionText': actionText ?? 'OK',
      'actionCallback': actionCallback,
      'canDismiss': canDismiss,
      'icon': Icons.error_outline,
      'color': Colors.red,
    };
  }

  /// Log performance issues as warnings
  static void logPerformanceWarning(String operation, int durationMs, int targetMs) {
    if (durationMs > targetMs) {
      developer.log(
        'Performance warning: $operation took ${durationMs}ms (target: ${targetMs}ms)',
        name: 'Reports.Performance',
        level: 900, // Warning level
      );
    }
  }

  /// Handle cache errors gracefully
  static T handleCacheError<T>(
    String operation,
    T Function() cacheOperation,
    T Function() fallbackOperation,
  ) {
    try {
      return cacheOperation();
    } catch (error) {
      developer.log(
        'Cache error in $operation: $error - falling back to direct operation',
        name: 'Reports.Cache.Error',
        level: 900,
      );
      return fallbackOperation();
    }
  }

  /// Get user-friendly error message from exception
  static String getUserFriendlyMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network connection issue. Please check your internet connection.';
    } else if (errorString.contains('permission')) {
      return 'Permission required. Please grant necessary permissions in settings.';
    } else if (errorString.contains('storage') || errorString.contains('space')) {
      return 'Insufficient storage space. Please free up some space and try again.';
    } else if (errorString.contains('timeout')) {
      return 'Operation timed out. Please try again or reduce the data range.';
    } else if (errorString.contains('memory')) {
      return 'Not enough memory. Please try filtering the data or closing other apps.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
}

/// Validation result data class
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasErrors => errors.isNotEmpty;

  String get firstError => errors.isNotEmpty ? errors.first : '';
  String get firstWarning => warnings.isNotEmpty ? warnings.first : '';

  Map<String, dynamic> toJson() {
    return {
      'isValid': isValid,
      'errors': errors,
      'warnings': warnings,
      'hasWarnings': hasWarnings,
      'hasErrors': hasErrors,
    };
  }
}

/// Error recovery strategies
enum ErrorRecoveryStrategy {
  retry,
  fallback,
  cache,
  filter,
  permission,
  storage,
}

/// Error context for better debugging
class ErrorContext {
  final String operation;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;
  final String? userId;

  const ErrorContext({
    required this.operation,
    required this.parameters,
    required this.timestamp,
    this.userId,
  });

  Map<String, dynamic> toJson() {
    return {
      'operation': operation,
      'parameters': parameters,
      'timestamp': timestamp.toIso8601String(),
      'userId': userId,
    };
  }
}
