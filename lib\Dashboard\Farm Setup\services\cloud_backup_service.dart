import 'dart:io';
import 'dart:convert';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../../../services/database/isar_service.dart';
import '../models/backup_settings_isar.dart';
import '../models/farm_isar.dart';
import 'google_drive_service.dart';
import 'google_drive_auth_service.dart';

/// Unified cloud backup service that abstracts Google Drive and Firebase Storage operations
/// Integrates with the existing IsarService backup functionality
class CloudBackupService {
  static final CloudBackupService _instance = CloudBackupService._internal();
  final Logger _logger = Logger('CloudBackupService');

  late final IsarService _isarService;
  late final GoogleDriveService _googleDriveService;

  late final GoogleDriveAuthService _authService;

  bool _isInitialized = false;

  factory CloudBackupService() {
    return _instance;
  }

  CloudBackupService._internal();

  /// Initialize the cloud backup service with dependencies
  /// Note: Google Drive services are initialized lazily when first needed
  Future<bool> initialize({
    required IsarService isarService,
    GoogleDriveService? googleDriveService,
    GoogleDriveAuthService? authService,
  }) async {
    try {
      _isarService = isarService;
      _googleDriveService = googleDriveService ?? GoogleDriveService();
      _authService = authService ?? GoogleDriveAuthService();

      // DO NOT initialize Google Drive service here to avoid triggering authentication
      // Google Drive services will be initialized lazily when first needed

      _isInitialized = true;
      _logger.info('Cloud backup service initialized successfully (Google Drive services will be initialized when needed)');
      return true;
    } catch (e) {
      _logger.severe('Error initializing cloud backup service: $e');
      return false;
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Ensure the service is initialized (called lazily when first needed)
  Future<bool> _ensureInitialized() async {
    if (_isInitialized) return true;

    try {
      // Get IsarService from dependency injection
      final getIt = GetIt.instance;
      if (!getIt.isRegistered<IsarService>()) {
        _logger.warning('IsarService not available for CloudBackupService initialization');
        return false;
      }

      return await initialize(isarService: getIt<IsarService>());
    } catch (e) {
      _logger.severe('Error ensuring CloudBackupService initialization: $e');
      return false;
    }
  }

  /// Ensure Google Drive services are initialized when needed
  /// This is called lazily only when Google Drive functionality is actually used
  Future<bool> _ensureGoogleDriveInitialized() async {
    try {
      // Only initialize if not already done
      if (!_googleDriveService.isInitialized) {
        _logger.info('Initializing Google Drive service on demand...');
        final initialized = await _googleDriveService.initialize();
        if (!initialized) {
          _logger.warning('Google Drive service initialization failed');
          return false;
        }
      }
      return true;
    } catch (e) {
      _logger.severe('Error ensuring Google Drive initialization: $e');
      return false;
    }
  }

  /// Create a backup and upload to the specified cloud storage provider
  Future<CloudBackupResult> createCloudBackup(
    String farmId,
    BackupStorageProvider provider,
  ) async {
    // Ensure service is initialized
    final initialized = await _ensureInitialized();
    if (!initialized) {
      return CloudBackupResult.error('Cloud backup service could not be initialized');
    }

    try {
      _logger.info('Creating cloud backup for farm: $farmId using provider: $provider.name');

      // Create local backup first using IsarService
      final localBackupFile = await _isarService.backup();
      
      // Convert backup file to data format for cloud storage
      final backupData = await _convertBackupToCloudFormat(localBackupFile, farmId);

      String? cloudBackupId;
      switch (provider) {
        case BackupStorageProvider.googleDrive:
          cloudBackupId = await _uploadToGoogleDrive(farmId, backupData);
          break;
        case BackupStorageProvider.local:
          return CloudBackupResult.success(
            localBackupFile.path,
            'Local backup created successfully',
          );
      }

      // Clean up local backup file if cloud upload was successful
      if (cloudBackupId != null) {
        try {
          await localBackupFile.delete();
        } catch (e) {
          _logger.warning('Failed to delete local backup file: $e');
        }
        
        // Clean up old backups to maintain only 3 most recent
        await _cleanupOldBackups(farmId, provider);

        return CloudBackupResult.success(
          cloudBackupId,
          'Cloud backup created successfully',
        );
      } else {
        return CloudBackupResult.error('Failed to upload backup to cloud storage');
      }
    } catch (e) {
      _logger.severe('Error creating cloud backup: $e');
      return CloudBackupResult.error('Error creating cloud backup: $e');
    }
  }

  /// Restore from a cloud backup
  Future<CloudBackupResult> restoreFromCloudBackup(
    String backupId,
    BackupStorageProvider provider,
  ) async {
    // Ensure service is initialized
    final initialized = await _ensureInitialized();
    if (!initialized) {
      return CloudBackupResult.error('Cloud backup service could not be initialized');
    }

    try {
      _logger.info('Restoring from cloud backup: $backupId using provider: $provider.name');

      Map<String, dynamic>? backupData;
      switch (provider) {
        case BackupStorageProvider.googleDrive:
          backupData = await _downloadFromGoogleDrive(backupId);
          break;
        case BackupStorageProvider.local:
          return CloudBackupResult.error('Local restore not supported through cloud service');
      }

      if (backupData == null) {
        return CloudBackupResult.error('Failed to download backup from cloud storage');
      }

      // Convert cloud backup data back to local file format
      final localBackupFile = await _convertCloudFormatToBackup(backupData);

      // Restore using IsarService
      await _isarService.restoreFromBackup(localBackupFile);

      // Clean up temporary file
      try {
        await localBackupFile.delete();
      } catch (e) {
        _logger.warning('Failed to delete temporary backup file: $e');
      }

      return CloudBackupResult.success(
        backupId,
        'Cloud backup restored successfully',
      );
    } catch (e) {
      _logger.severe('Error restoring from cloud backup: $e');
      return CloudBackupResult.error('Error restoring from cloud backup: $e');
    }
  }

  /// List cloud backups for a specific farm
  Future<List<CloudBackupInfo>> listCloudBackups(
    String farmId,
    BackupStorageProvider provider,
  ) async {
    if (!_isInitialized) {
      return [];
    }

    try {
      switch (provider) {
        case BackupStorageProvider.googleDrive:
          return await _listGoogleDriveBackups();
        case BackupStorageProvider.local:
          return [];
      }
    } catch (e) {
      _logger.severe('Error listing cloud backups: $e');
      return [];
    }
  }

  /// Delete a cloud backup
  Future<bool> deleteCloudBackup(
    String backupId,
    BackupStorageProvider provider,
  ) async {
    if (!_isInitialized) {
      return false;
    }

    try {
      switch (provider) {
        case BackupStorageProvider.googleDrive:
          // Ensure Google Drive services are initialized
          final initialized = await _ensureGoogleDriveInitialized();
          if (!initialized) {
            _logger.warning('Google Drive service could not be initialized');
            return false;
          }
          return await _googleDriveService.deleteBackup(backupId);
        case BackupStorageProvider.local:
          return false;
      }
    } catch (e) {
      _logger.severe('Error deleting cloud backup: $e');
      return false;
    }
  }

  /// Clean up old backups to maintain only the 3 most recent
  Future<void> _cleanupOldBackups(String farmId, BackupStorageProvider provider) async {
    try {
      _logger.info('Cleaning up old backups for farm: $farmId using provider: $provider.name');

      // Get all backups sorted by creation time (newest first)
      final allBackups = await listCloudBackups(farmId, provider);

      if (allBackups.length <= 3) {
        _logger.info('Only $allBackups.length backups found, no cleanup needed');
        return;
      }

      // Sort by creation time (newest first)
      allBackups.sort((a, b) => b.createdTime.compareTo(a.createdTime));

      // Keep only the 3 most recent, delete the rest
      final backupsToDelete = allBackups.skip(3).toList();

      _logger.info('Deleting $backupsToDelete.length old backups');

      for (final backup in backupsToDelete) {
        try {
          final deleted = await deleteCloudBackup(backup.id, provider);
          if (deleted) {
            _logger.info('Deleted old backup: $backup.name ($backup.id)');
          } else {
            _logger.warning('Failed to delete old backup: $backup.name ($backup.id)');
          }
        } catch (e) {
          _logger.warning('Error deleting old backup $backup.name: $e');
        }
      }

      _logger.info('Backup cleanup completed. Kept ${allBackups.length - backupsToDelete.length} most recent backups');
    } catch (e) {
      _logger.severe('Error during backup cleanup: $e');
      // Don't throw - cleanup failure shouldn't prevent backup creation
    }
  }

  /// Check authentication status for a provider
  Future<bool> isAuthenticated(BackupStorageProvider provider) async {
    switch (provider) {
      case BackupStorageProvider.googleDrive:
        return _authService.isSignedIn();
      case BackupStorageProvider.local:
        return true;
    }
  }

  /// Sign in to a cloud provider
  Future<bool> signIn(BackupStorageProvider provider) async {
    switch (provider) {
      case BackupStorageProvider.googleDrive:
        return await _authService.signIn();
      case BackupStorageProvider.local:
        return true;
    }
  }

  /// Sign out from a cloud provider
  Future<void> signOut(BackupStorageProvider provider) async {
    switch (provider) {
      case BackupStorageProvider.googleDrive:
        await _authService.signOut();
        break;
      case BackupStorageProvider.local:
        break;
    }
  }

  // Private helper methods
  Future<String?> _uploadToGoogleDrive(String farmId, Map<String, dynamic> data) async {
    try {
      // Ensure Google Drive services are initialized
      final initialized = await _ensureGoogleDriveInitialized();
      if (!initialized) {
        _logger.warning('Google Drive service could not be initialized');
        return null;
      }

      if (!_authService.isSignedIn()) {
        _logger.warning('Not signed in to Google Drive');
        return null;
      }

      // Get farm name for the backup file name
      String? farmName;
      try {
        final farm = await _isarService.isar.farmIsars.getByFarmBusinessId(farmId);
        farmName = farm?.name;
      } catch (e) {
        _logger.warning('Could not get farm name for backup: $e');
      }

      return await _googleDriveService.uploadBackup(farmId, data, farmName: farmName);
    } catch (e) {
      _logger.severe('Error uploading to Google Drive: $e');
      return null;
    }
  }



  Future<Map<String, dynamic>?> _downloadFromGoogleDrive(String fileId) async {
    try {
      // Ensure Google Drive services are initialized
      final initialized = await _ensureGoogleDriveInitialized();
      if (!initialized) {
        _logger.warning('Google Drive service could not be initialized');
        return null;
      }

      return await _googleDriveService.downloadBackup(fileId);
    } catch (e) {
      _logger.severe('Error downloading from Google Drive: $e');
      return null;
    }
  }



  Future<List<CloudBackupInfo>> _listGoogleDriveBackups() async {
    try {
      // Ensure Google Drive services are initialized
      final initialized = await _ensureGoogleDriveInitialized();
      if (!initialized) {
        _logger.warning('Google Drive service could not be initialized');
        return [];
      }

      final files = await _googleDriveService.listBackups();
      return files.map((file) => CloudBackupInfo(
        id: file.id ?? '',
        name: file.name ?? '',
        size: 0, // Google Drive API doesn't provide size in list
        createdTime: file.createdTime ?? DateTime.now(),
        provider: BackupStorageProvider.googleDrive,
      )).toList();
    } catch (e) {
      _logger.severe('Error listing Google Drive backups: $e');
      return [];
    }
  }



  Future<Map<String, dynamic>> _convertBackupToCloudFormat(File backupFile, String farmId) async {
    // For now, we'll create a simple metadata wrapper
    // In a real implementation, you might want to extract and structure the data
    final bytes = await backupFile.readAsBytes();
    final base64Data = base64Encode(bytes);
    
    return {
      'farmId': farmId,
      'timestamp': DateTime.now().toIso8601String(),
      'version': '1.0',
      'type': 'isar_backup',
      'data': base64Data,
    };
  }

  Future<File> _convertCloudFormatToBackup(Map<String, dynamic> cloudData) async {
    final base64Data = cloudData['data'] as String;
    final bytes = base64Decode(base64Data);
    
    // Create temporary file
    final tempDir = Directory.systemTemp;
    final tempFile = File('$tempDir.path/temp_restore_${DateTime.now().millisecondsSinceEpoch}.isar');
    
    await tempFile.writeAsBytes(bytes);
    return tempFile;
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _googleDriveService.dispose();
    _logger.info('Cloud backup service disposed');
  }
}

/// Result class for cloud backup operations
class CloudBackupResult {
  final bool success;
  final String? backupId;
  final String message;

  CloudBackupResult._(this.success, this.backupId, this.message);

  factory CloudBackupResult.success(String backupId, String message) {
    return CloudBackupResult._(true, backupId, message);
  }

  factory CloudBackupResult.error(String message) {
    return CloudBackupResult._(false, null, message);
  }
}

/// Cloud backup information class
class CloudBackupInfo {
  final String id;
  final String name;
  final int size;
  final DateTime createdTime;
  final BackupStorageProvider provider;

  CloudBackupInfo({
    required this.id,
    required this.name,
    required this.size,
    required this.createdTime,
    required this.provider,
  });

  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String get formattedCreatedTime {
    return '$createdTime.day/$createdTime.month/$createdTime.year $createdTime.hour:${createdTime.minute.toString().padLeft(2, '0')}';
  }
}
