# Design Document

## Overview

This design document outlines the technical approach for refactoring the Cattle Manager App to ensure all modules follow established coding standards and architectural patterns. Based on analysis of the codebase, we have identified the Cattle, Weight, and Breeding modules as reference implementations that demonstrate the desired patterns. This refactoring will bring all Dashboard modules into compliance with these established standards.

## Architecture

### Established Pattern Analysis

Through comprehensive code analysis, we have identified the following established patterns in the reference modules (Cattle, Weight, Breeding):

#### 1. Pure Reactive Repository Pattern
- **Stream-only operations** with `watch()` methods for real-time updates
- **No business logic** in repositories - pure CRUD operations
- **No error handling** in repositories - exceptions bubble up naturally
- **Explicit dependency injection** through constructor parameters
- **Maximum purity** - repositories only handle data access

#### 2. Dual-Stream Controller Architecture
- **Unfiltered stream** for analytics calculations (complete dataset)
- **Filtered stream** for UI display (user-filtered dataset)
- **Separate stream subscriptions** for filtered and unfiltered data
- **Analytics always calculated on unfiltered data** for accuracy
- **State management** with proper ControllerState enum

#### 3. Service Layer Organization
- **Analytics services** - pure calculation functions, no state
- **Sync services** - dedicated bidirectional synchronization
- **Insights services** - specialized analytics for specific domains
- **Event integration services** - cross-module communication

### Non-Compliant Modules Identified

Based on the analysis, the following modules deviate from established patterns:

#### High Priority (Major Refactoring Required)
1. **Reports Module** - Uses non-reactive patterns, direct database access
2. **Transactions Module** - Missing proper repository pattern, mixed concerns
3. **Notifications Module** - Complex service with mixed responsibilities
4. **Farm Setup Module** - Oversized repository with business logic

#### Medium Priority (Moderate Refactoring Required)
5. **Milk Module** - Incomplete structure, missing controller
6. **Help Module** - Minimal structure, needs standardization
7. **User Account Module** - Mixed patterns, needs stream consistency

#### Low Priority (Minor Adjustments Required)
8. **Events Module** - Mostly compliant but overly complex
9. **Health Module** - Good structure but minor pattern deviations

## Components and Interfaces

### Standard Module Structure

Each compliant module should follow this exact structure:

```
lib/Dashboard/{ModuleName}/
├── controllers/
│   ├── {module}_controller.dart          # Main reactive controller
│   └── {module}_details_controller.dart  # Detail view controller
├── details/
│   ├── {module}_details_screen.dart      # Detail screen
│   └── {module}_details_*_tab.dart       # Detail tabs
├── dialogs/
│   └── {module}_form_dialog.dart         # Form dialogs
├── models/
│   ├── {module}_isar.dart                # Main data model
│   ├── {module}_isar.g.dart              # Generated code
│   └── {module}_insights_models.dart     # Analytics models
├── screens/
│   └── {module}_screen.dart              # Main screen
├── services/
│   ├── {module}_repository.dart          # Pure reactive repository
│   ├── {module}_analytics_service.dart   # Pure analytics calculations
│   ├── {module}_insights_service.dart    # Specialized insights
│   ├── {module}_sync_service.dart        # Bidirectional sync
│   └── {module}_event_integration.dart   # Cross-module events
└── tabs/
    ├── {module}_records_tab.dart         # Records display
    ├── {module}_analytics_tab.dart       # Analytics display
    └── {module}_insights_tab.dart        # Insights display
```

### Repository Interface Standard

All repositories must implement this pattern:

```dart
/// Pure reactive repository for {Module} database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class {Module}Repository {
  final IsarService _isarService;
  
  // Public constructor with explicit dependency injection
  {Module}Repository(this._isarService);
  
  // Getter for Isar instance
  Isar get _isar => _isarService.isar;
  
  //=== REACTIVE STREAMS ===//
  
  /// Watches all records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<{Model}Isar>> watchAll{Models}() {
    return _isar.{model}Isars.where().watch(fireImmediately: true);
  }
  
  //=== CRUD OPERATIONS ===//
  
  /// Save (add or update) using Isar's native upsert
  Future<void> save{Model}({Model}Isar record) async {
    await _isar.writeTxn(() async {
      await _isar.{model}Isars.put(record);
    });
  }
  
  /// Delete by Isar ID
  Future<void> delete{Model}(int id) async {
    await _isar.writeTxn(() async {
      await _isar.{model}Isars.delete(id);
    });
  }
  
  //=== QUERY METHODS FOR ANALYTICS ===//
  
  /// Get all records (for analytics and validation)
  /// Returns Future<List> for one-time data fetching
  Future<List<{Model}Isar>> getAll{Models}() async {
    return await _isar.{model}Isars.where().findAll();
  }
}
```

### Controller Interface Standard

All controllers must implement this dual-stream pattern:

```dart
class {Module}Controller extends ChangeNotifier {
  // Repositories with lazy getters
  {Module}Repository get _{module}Repository => GetIt.instance<{Module}Repository>();
  Isar get _isar => GetIt.instance<Isar>();
  
  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;
  
  // Stream subscriptions - Separated for filtered/unfiltered data
  StreamSubscription<List<{Model}Isar>>? _unfilteredStreamSubscription;
  StreamSubscription<List<{Model}Isar>>? _filteredStreamSubscription;
  
  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<{Model}Isar> _unfiltered{Models} = []; // Complete dataset for analytics
  List<{Model}Isar> _filtered{Models} = [];   // Filtered dataset for UI display
  bool _hasActiveFilters = false;
  
  // Analytics result - ALWAYS calculated on unfiltered data
  {Module}AnalyticsResult _analyticsResult = {Module}AnalyticsResult.empty;
  
  // Getters
  List<{Model}Isar> get {models} => List.unmodifiable(_filtered{Models});
  List<{Model}Isar> get unfiltered{Models} => List.unmodifiable(_unfiltered{Models});
  {Module}AnalyticsResult get analytics => _analyticsResult;
  
  // Stream initialization and filter management
  void _initializeStreamListener() { /* Implementation */ }
  void applyFilters(FilterController? filters) async { /* Implementation */ }
  void _calculateAnalytics() { /* Implementation */ }
}
```

### Analytics Service Interface Standard

All analytics services must be pure calculation functions:

```dart
/// Pure analytics service - no state, just calculations
class {Module}AnalyticsService {
  /// Main calculation method - single entry point with O(n) efficiency
  static {Module}AnalyticsResult calculate(
    List<{Model}Isar> records,
    Map<String, RelatedModel> relatedData,
  ) {
    if (records.isEmpty) {
      return {Module}AnalyticsResult.empty;
    }
    
    // Single pass through data for maximum efficiency
    final accumulator = _{Module}AnalyticsAccumulator();
    for (final record in records) {
      accumulator.process(record, relatedData);
    }
    
    return accumulator.toResult();
  }
}
```

## Data Models

### Consistent Model Patterns

All Isar models must follow these patterns:

1. **Business ID field** for external references
2. **Audit fields** (createdAt, updatedAt)
3. **Proper indexing** for performance
4. **Enum usage** for type safety
5. **Nullable fields** with proper defaults

### Model Validation Standards

- Use Isar's built-in validation where possible
- Implement custom validation in dedicated validation services
- Never put validation logic in repositories
- Use enum types for constrained values

## Error Handling

### Repository Layer
- **No error handling** - exceptions bubble up naturally
- **No logging** - pure data access only
- **Maximum purity** - let higher layers handle errors

### Controller Layer
- **Comprehensive error handling** with try-catch blocks
- **User-friendly error messages**
- **State management** with proper error states
- **Logging** for debugging purposes

### Service Layer
- **Specific error handling** based on service type
- **Validation services** handle validation errors
- **Sync services** handle network errors
- **Analytics services** are pure functions (no errors)

## Testing Strategy

### Repository Testing
- Test CRUD operations
- Test stream functionality
- Test query methods
- Mock Isar database for unit tests

### Controller Testing
- Test state management
- Test filter application
- Test analytics calculation
- Test stream handling
- Mock repositories and services

### Service Testing
- Test analytics calculations
- Test sync operations
- Test validation logic
- Use test data sets for comprehensive coverage

### Integration Testing
- Test complete data flow
- Test cross-module interactions
- Test real database operations
- Performance testing for large datasets

## Implementation Phases

### Phase 1: High Priority Modules (Weeks 1-3)
1. **Reports Module Refactoring**
   - Create proper repository with reactive streams
   - Implement dual-stream controller
   - Extract analytics to dedicated service
   - Remove direct database access

2. **Transactions Module Refactoring**
   - Implement proper repository pattern
   - Create reactive controller
   - Separate analytics service
   - Add sync service

3. **Notifications Module Refactoring**
   - Split oversized service into focused services
   - Implement repository pattern
   - Create reactive controller
   - Separate concerns properly

### Phase 2: Medium Priority Modules (Weeks 4-5)
4. **Farm Setup Module Refactoring**
   - Extract business logic from repository
   - Create focused services
   - Implement proper error handling
   - Reduce repository complexity

5. **Milk Module Completion**
   - Create missing controller
   - Implement proper structure
   - Add analytics service
   - Complete module standardization

### Phase 3: Low Priority Modules (Week 6)
6. **Minor Adjustments**
   - Events module simplification
   - Health module pattern alignment
   - User Account module stream consistency
   - Help module standardization

### Phase 4: Validation and Cleanup (Week 7)
7. **File Cleanup**
   - Remove unused files
   - Clean up imports
   - Remove duplicate code
   - Preserve documentation

8. **Testing and Validation**
   - Comprehensive testing
   - Performance validation
   - Pattern compliance verification
   - Documentation updates

## Performance Considerations

### Stream Management
- Use `fireImmediately: true` for initial data loading
- Proper stream disposal to prevent memory leaks
- Efficient filter queries at database level
- Pagination for large datasets

### Analytics Optimization
- Single-pass algorithms for O(n) efficiency
- Caching for expensive calculations
- Lazy loading for detailed analytics
- Background calculation for complex metrics

### Memory Management
- Proper stream subscription disposal
- Efficient data structures
- Minimal object creation in hot paths
- Garbage collection friendly patterns

## Migration Strategy

### Backward Compatibility
- Maintain existing API contracts during transition
- Use feature flags for gradual rollout
- Provide migration utilities for data
- Preserve existing functionality

### Rollback Plan
- Version control checkpoints at each phase
- Ability to revert individual modules
- Database migration rollback procedures
- Comprehensive testing before deployment

### Risk Mitigation
- Thorough testing at each phase
- Gradual deployment strategy
- Monitoring and alerting
- Quick rollback procedures

This design ensures all modules will follow the established patterns while maintaining functionality and improving code quality, performance, and maintainability.