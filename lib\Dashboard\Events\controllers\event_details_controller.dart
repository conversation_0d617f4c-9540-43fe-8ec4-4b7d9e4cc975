import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/events_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum

/// Controller for managing event details screen state and operations
/// Follows the established controller pattern with reactive streams
class EventDetailsController extends ChangeNotifier {
  // Repositories
  final EventsRepository _eventsRepository = GetIt.instance<EventsRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates
  StreamSubscription<List<EventIsar>>? _eventStreamSubscription;
  StreamSubscription<List<EventAttachmentIsar>>? _attachmentsStreamSubscription;
  StreamSubscription<List<EventIsar>>? _relatedEventsStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription<List<EventTypeIsar>>? _eventTypesStreamSubscription;

  // Data
  EventIsar? _event;
  List<EventAttachmentIsar> _attachments = [];
  List<EventIsar> _relatedEvents = [];
  List<CattleIsar> _unfilteredCattle = [];
  List<EventTypeIsar> _unfilteredEventTypes = [];
  CattleIsar? _cattle;
  EventTypeIsar? _eventType;

  // Event business ID for tracking
  String? _eventBusinessId;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  EventIsar? get event => _event;
  List<EventAttachmentIsar> get attachments => List.unmodifiable(_attachments);
  List<EventIsar> get relatedEvents => List.unmodifiable(_relatedEvents);
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);
  List<EventTypeIsar> get unfilteredEventTypes => List.unmodifiable(_unfilteredEventTypes);
  CattleIsar? get cattle => _cattle;
  EventTypeIsar? get eventType => _eventType;

  /// Initialize controller with event business ID
  void initialize(String eventBusinessId) {
    _eventBusinessId = eventBusinessId;
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates
  void _initializeStreamListeners() {
    if (_eventBusinessId == null) return;

    debugPrint('🔧 EVENT DETAILS CONTROLLER: Initializing stream listeners for event: $_eventBusinessId');

    // Event stream - watch for changes to this specific event
    _eventStreamSubscription = _isar.eventIsars
        .where()
        .businessIdEqualTo(_eventBusinessId!)
        .watch(fireImmediately: true)
        .listen((events) {
      debugPrint('🔄 EVENT STREAM: Received ${events.length} events');
      if (events.isNotEmpty) {
        _event = events.first;
        _loadCattleAndEventType();
        _loadRelatedEvents();
        _setState(ControllerState.loaded);
      } else {
        _setState(ControllerState.error);
        _errorMessage = 'Event not found';
      }
    }, onError: (error) {
      debugPrint('❌ Event stream error: $error');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to load event: $error';
    });

    // Attachments stream - watch for changes to event attachments
    _attachmentsStreamSubscription = _isar.eventAttachmentIsars
        .where()
        .eventBusinessIdEqualTo(_eventBusinessId!)
        .watch(fireImmediately: true)
        .listen((attachments) {
      debugPrint('🔄 ATTACHMENTS STREAM: Received ${attachments.length} attachments');
      _attachments = attachments;
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Attachments stream error: $error');
    });

    // Cattle stream - watch for all cattle
    _cattleStreamSubscription = _isar.cattleIsars
        .where()
        .watch(fireImmediately: true)
        .listen((cattle) {
      debugPrint('🔄 CATTLE STREAM: Received ${cattle.length} cattle');
      _unfilteredCattle = cattle;
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Cattle stream error: $error');
    });

    // Event types stream - watch for all event types
    _eventTypesStreamSubscription = _isar.eventTypeIsars
        .where()
        .watch(fireImmediately: true)
        .listen((eventTypes) {
      debugPrint('🔄 EVENT TYPES STREAM: Received ${eventTypes.length} event types');
      _unfilteredEventTypes = eventTypes;
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Event types stream error: $error');
    });

    debugPrint('✅ EVENT DETAILS CONTROLLER: Stream listeners initialized');
  }

  /// Load cattle and event type information
  Future<void> _loadCattleAndEventType() async {
    if (_event == null) return;

    try {
      // Load cattle information
      if (_event!.cattleTagId != null) {
        final cattleList = await _isar.cattleIsars
            .where()
            .tagIdEqualTo(_event!.cattleTagId!)
            .findAll();
        if (cattleList.isNotEmpty) {
          _cattle = cattleList.first;
        }
      }

      // Load event type information
      if (_event!.eventTypeId != null) {
        final eventTypeList = await _isar.eventTypeIsars
            .where()
            .businessIdEqualTo(_event!.eventTypeId!)
            .findAll();
        if (eventTypeList.isNotEmpty) {
          _eventType = eventTypeList.first;
        }
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading cattle and event type: $e');
    }
  }

  /// Load related events for the same cattle
  Future<void> _loadRelatedEvents() async {
    if (_event?.cattleTagId == null) return;

    try {
      // Get related events for the same cattle (excluding current event)
      _relatedEventsStreamSubscription?.cancel();
      _relatedEventsStreamSubscription = _isar.eventIsars
          .where()
          .cattleTagIdEqualTo(_event!.cattleTagId!)
          .watch(fireImmediately: true)
          .listen((events) {
        // Filter out current event and sort by date
        _relatedEvents = events
            .where((e) => e.businessId != _eventBusinessId)
            .toList()
          ..sort((a, b) => (b.scheduledDate ?? DateTime.now())
              .compareTo(a.scheduledDate ?? DateTime.now()));
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Error loading related events: $e');
    }
  }

  /// Update event
  Future<void> updateEvent(EventIsar updatedEvent) async {
    try {
      await _eventsRepository.saveEvent(updatedEvent);
      // Stream will handle the UI update automatically
    } catch (e) {
      debugPrint('Error updating event: $e');
      rethrow;
    }
  }

  /// Complete event
  Future<void> completeEvent({
    String? completedBy,
    String? completionNotes,
    double? actualCost,
  }) async {
    if (_event == null) return;

    try {
      _event!.markCompleted(
        completedBy: completedBy,
        completionNotes: completionNotes,
        actualCost: actualCost,
      );
      await _eventsRepository.saveEvent(_event!);
      // Stream will handle the UI update automatically
    } catch (e) {
      debugPrint('Error completing event: $e');
      rethrow;
    }
  }

  /// Delete event
  Future<void> deleteEvent() async {
    if (_event == null) return;

    try {
      await _eventsRepository.deleteEvent(_event!.id);
      // Navigation should be handled by the calling screen
    } catch (e) {
      debugPrint('Error deleting event: $e');
      rethrow;
    }
  }

  /// Add attachment
  Future<void> addAttachment(EventAttachmentIsar attachment) async {
    try {
      await _eventsRepository.saveEventAttachment(attachment);
      // Stream will handle the UI update automatically
    } catch (e) {
      debugPrint('Error adding attachment: $e');
      rethrow;
    }
  }

  /// Delete attachment
  Future<void> deleteAttachment(EventAttachmentIsar attachment) async {
    try {
      await _eventsRepository.deleteEventAttachment(attachment.id);
      // Stream will handle the UI update automatically
    } catch (e) {
      debugPrint('Error deleting attachment: $e');
      rethrow;
    }
  }

  /// Set controller state
  void _setState(ControllerState newState) {
    _state = newState;
    notifyListeners();
  }

  @override
  void dispose() {
    _eventStreamSubscription?.cancel();
    _attachmentsStreamSubscription?.cancel();
    _relatedEventsStreamSubscription?.cancel();
    _cattleStreamSubscription?.cancel();
    _eventTypesStreamSubscription?.cancel();
    super.dispose();
  }
}