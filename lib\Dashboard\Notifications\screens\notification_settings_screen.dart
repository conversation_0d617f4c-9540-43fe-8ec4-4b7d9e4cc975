import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/notification_settings_controller.dart';
import '../models/notification_priority.dart';
import '../../../constants/app_colors.dart';

/// Screen for managing notification settings
class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationSettingsController>().loadSettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<NotificationSettingsController>().loadSettings(),
          ),
        ],
      ),
      body: Consumer<NotificationSettingsController>(
        builder: (context, controller, child) {
          if (controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: controller.loadSettings,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final settings = controller.settings;
          if (settings == null) {
            return const Center(child: Text('No settings available'));
          }

          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Global Settings
              _buildSectionHeader('Global Settings'),
              _buildSwitchTile(
                title: 'Enable Notifications',
                subtitle: 'Turn on/off all notifications',
                value: settings.notificationsEnabled,
                onChanged: (value) => controller.updateGlobalSetting('notificationsEnabled', value),
              ),
              _buildSwitchTile(
                title: 'Push Notifications',
                subtitle: 'Receive notifications when app is closed',
                value: settings.pushNotificationsEnabled,
                onChanged: (value) => controller.updateGlobalSetting('pushNotificationsEnabled', value),
              ),
              _buildSwitchTile(
                title: 'In-App Notifications',
                subtitle: 'Show notifications within the app',
                value: settings.inAppNotificationsEnabled,
                onChanged: (value) => controller.updateGlobalSetting('inAppNotificationsEnabled', value),
              ),

              const SizedBox(height: 24),

              // Category Settings
              _buildSectionHeader('Category Settings'),
              ...settings.getCategoryEnabled().entries.map((entry) =>
                _buildCategoryTile(
                  controller,
                  entry.key,
                  entry.value,
                  settings.getCategoryPriorities()[entry.key] ?? NotificationPriority.medium,
                ),
              ),

              const SizedBox(height: 24),

              // Quiet Hours
              _buildSectionHeader('Quiet Hours'),
              _buildSwitchTile(
                title: 'Enable Quiet Hours',
                subtitle: 'Disable notifications during specified hours',
                value: settings.quietHoursEnabled,
                onChanged: (value) => controller.updateQuietHours(enabled: value),
              ),
              if (settings.quietHoursEnabled) ...[
                ListTile(
                  title: const Text('Start Time'),
                  subtitle: Text('${settings.quietHoursStart}:00'),
                  trailing: const Icon(Icons.access_time),
                  onTap: () => _selectTime(context, controller, true, settings.quietHoursStart),
                ),
                ListTile(
                  title: const Text('End Time'),
                  subtitle: Text('${settings.quietHoursEnd}:00'),
                  trailing: const Icon(Icons.access_time),
                  onTap: () => _selectTime(context, controller, false, settings.quietHoursEnd),
                ),
              ],

              const SizedBox(height: 24),

              // Emergency Override
              _buildSectionHeader('Emergency Override'),
              _buildSwitchTile(
                title: 'Emergency Override',
                subtitle: 'Allow critical notifications during quiet hours',
                value: settings.emergencyOverrideEnabled,
                onChanged: (value) => controller.updateEmergencyOverride(enabled: value),
              ),

              const SizedBox(height: 24),

              // Actions
              _buildSectionHeader('Actions'),
              ListTile(
                title: const Text('Reset to Defaults'),
                subtitle: const Text('Reset all settings to default values'),
                leading: const Icon(Icons.restore),
                onTap: () => _showResetConfirmation(context, controller),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.notificationHeader,
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
    );
  }

  Widget _buildCategoryTile(
    NotificationSettingsController controller,
    String category,
    bool enabled,
    NotificationPriority priority,
  ) {
    return ExpansionTile(
      title: Text(category.toUpperCase()),
      subtitle: Text(enabled ? 'Enabled' : 'Disabled'),
      leading: Switch(
        value: enabled,
        onChanged: (value) => controller.updateCategorySetting(category, 'enabled', value),
      ),
      children: [
        if (enabled)
          ListTile(
            title: const Text('Priority'),
            subtitle: Text(priority.name.toUpperCase()),
            trailing: DropdownButton<NotificationPriority>(
              value: priority,
              items: NotificationPriority.values.map((p) => DropdownMenuItem(
                value: p,
                child: Text(p.name.toUpperCase()),
              )).toList(),
              onChanged: (value) {
                if (value != null) {
                  controller.updateCategorySetting(category, 'priority', value);
                }
              },
            ),
          ),
      ],
    );
  }

  void _selectTime(BuildContext context, NotificationSettingsController controller, bool isStart, int currentHour) {
    showTimePicker(
      context: context,
      initialTime: TimeOfDay(hour: currentHour, minute: 0),
    ).then((time) {
      if (time != null) {
        if (isStart) {
          controller.updateQuietHours(startHour: time.hour);
        } else {
          controller.updateQuietHours(endHour: time.hour);
        }
      }
    });
  }

  void _showResetConfirmation(BuildContext context, NotificationSettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all notification settings to their default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.resetToDefaults();
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}