# Firebase Configuration Setup

This document explains how to properly configure Firebase for the Cattle Manager App.

## Current Status

The app has placeholder Firebase configuration that needs to be replaced with actual Firebase project credentials.

## Files That Need Configuration

### 1. `lib/firebase_options.dart`
This file contains Firebase configuration for all platforms. Replace the placeholder values:

```dart
// Replace these placeholder values with your actual Firebase project values:
- YOUR_WEB_API_KEY
- YOUR_PROJECT_NUMBER  
- YOUR_WEB_APP_ID
- YOUR_SENDER_ID
- YOUR_ANDROID_API_KEY
- YOUR_ANDROID_APP_ID
- YOUR_IOS_API_KEY
- YOUR_IOS_APP_ID
- YOUR_MACOS_API_KEY
- YOUR_MACOS_APP_ID
- YOUR_WINDOWS_API_KEY
- YOUR_WINDOWS_APP_ID
- YOUR_MEASUREMENT_ID
```

### 2. `android/app/google-services.json`
Replace the placeholder values with your actual Android configuration:

```json
{
  "project_info": {
    "project_number": "YOUR_ACTUAL_PROJECT_NUMBER",
    "project_id": "cattle-manager-app",
    "storage_bucket": "cattle-manager-app.appspot.com"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "YOUR_ACTUAL_ANDROID_APP_ID",
        "android_client_info": {
          "package_name": "com.example.cattle_manager"
        }
      },
      "api_key": [
        {
          "current_key": "YOUR_ACTUAL_API_KEY"
        }
      ]
    }
  ]
}
```

### 3. `lib/Dashboard/Notifications/services/firebase_config.dart`
Update the configuration constants:

```dart
class FirebaseConfig {
  static const String projectId = 'cattle-manager-app';
  static const String apiKey = 'YOUR_ACTUAL_API_KEY';
  static const String messagingSenderId = 'YOUR_ACTUAL_SENDER_ID';
  static const String androidAppId = 'YOUR_ACTUAL_ANDROID_APP_ID';
  static const String iosAppId = 'YOUR_ACTUAL_IOS_APP_ID';
  static const String serverKey = 'YOUR_ACTUAL_SERVER_KEY';
}
```

## Setup Instructions

### Option 1: Using FlutterFire CLI (Recommended)

1. Install FlutterFire CLI:
   ```bash
   dart pub global activate flutterfire_cli
   ```

2. Login to Firebase:
   ```bash
   firebase login
   ```

3. Configure your project:
   ```bash
   flutterfire configure
   ```

4. This will automatically generate the correct `firebase_options.dart` file and update platform-specific configuration files.

### Option 2: Manual Configuration

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing project
3. Add Android/iOS/Web apps to your project
4. Download configuration files and update the placeholder values

## Required Firebase Services

The app uses the following Firebase services:

- **Firebase Core**: Basic Firebase initialization
- **Firebase Cloud Messaging (FCM)**: Push notifications
- **Firebase Authentication**: User authentication (if enabled)

## Testing Configuration

After configuration, test that Firebase is working:

1. Run the app
2. Check logs for Firebase initialization messages
3. Test push notifications (if configured)

## Troubleshooting

### Common Issues

1. **Firebase App ID Warning**: Make sure all placeholder values are replaced with actual values
2. **Platform Channel Errors**: These are handled gracefully in the app and won't crash it
3. **Missing google-services.json**: Make sure the file is in `android/app/` directory

### Debug Steps

1. Check that `firebase_options.dart` has no placeholder values
2. Verify `google-services.json` is properly configured
3. Ensure Firebase project has the correct package name/bundle ID
4. Check that required Firebase services are enabled in console

## Security Notes

- Never commit actual Firebase credentials to version control
- Use environment variables or secure configuration management for production
- Regularly rotate API keys and tokens
- Enable Firebase security rules for production use
