// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetEventIsarCollection on Isar {
  IsarCollection<EventIsar> get eventIsars => this.collection();
}

const EventIsarSchema = CollectionSchema(
  name: r'EventIsar',
  id: 4386082428336480023,
  properties: {
    r'actualCost': PropertySchema(
      id: 0,
      name: r'actualCost',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 1,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'category': PropertySchema(
      id: 2,
      name: r'category',
      type: IsarType.byte,
      enumMap: _EventIsarcategoryEnumValueMap,
    ),
    r'cattleTagId': PropertySchema(
      id: 3,
      name: r'cattleTagId',
      type: IsarType.string,
    ),
    r'completedBy': PropertySchema(
      id: 4,
      name: r'completedBy',
      type: IsarType.string,
    ),
    r'completedDate': PropertySchema(
      id: 5,
      name: r'completedDate',
      type: IsarType.dateTime,
    ),
    r'completionNotes': PropertySchema(
      id: 6,
      name: r'completionNotes',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 7,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'daysUntilScheduled': PropertySchema(
      id: 8,
      name: r'daysUntilScheduled',
      type: IsarType.long,
    ),
    r'description': PropertySchema(
      id: 9,
      name: r'description',
      type: IsarType.string,
    ),
    r'estimatedCost': PropertySchema(
      id: 10,
      name: r'estimatedCost',
      type: IsarType.double,
    ),
    r'eventTypeId': PropertySchema(
      id: 11,
      name: r'eventTypeId',
      type: IsarType.string,
    ),
    r'isAutoGenerated': PropertySchema(
      id: 12,
      name: r'isAutoGenerated',
      type: IsarType.bool,
    ),
    r'isCompleted': PropertySchema(
      id: 13,
      name: r'isCompleted',
      type: IsarType.bool,
    ),
    r'isMissed': PropertySchema(
      id: 14,
      name: r'isMissed',
      type: IsarType.bool,
    ),
    r'isOverdue': PropertySchema(
      id: 15,
      name: r'isOverdue',
      type: IsarType.bool,
    ),
    r'isRecurring': PropertySchema(
      id: 16,
      name: r'isRecurring',
      type: IsarType.bool,
    ),
    r'isUpcoming': PropertySchema(
      id: 17,
      name: r'isUpcoming',
      type: IsarType.bool,
    ),
    r'location': PropertySchema(
      id: 18,
      name: r'location',
      type: IsarType.string,
    ),
    r'notes': PropertySchema(
      id: 19,
      name: r'notes',
      type: IsarType.string,
    ),
    r'notificationsEnabled': PropertySchema(
      id: 20,
      name: r'notificationsEnabled',
      type: IsarType.bool,
    ),
    r'parentEventId': PropertySchema(
      id: 21,
      name: r'parentEventId',
      type: IsarType.string,
    ),
    r'priority': PropertySchema(
      id: 22,
      name: r'priority',
      type: IsarType.byte,
      enumMap: _EventIsarpriorityEnumValueMap,
    ),
    r'recurrenceEndDate': PropertySchema(
      id: 23,
      name: r'recurrenceEndDate',
      type: IsarType.dateTime,
    ),
    r'recurrenceInterval': PropertySchema(
      id: 24,
      name: r'recurrenceInterval',
      type: IsarType.long,
    ),
    r'recurrencePattern': PropertySchema(
      id: 25,
      name: r'recurrencePattern',
      type: IsarType.byte,
      enumMap: _EventIsarrecurrencePatternEnumValueMap,
    ),
    r'reminderMinutes': PropertySchema(
      id: 26,
      name: r'reminderMinutes',
      type: IsarType.longList,
    ),
    r'scheduledDate': PropertySchema(
      id: 27,
      name: r'scheduledDate',
      type: IsarType.dateTime,
    ),
    r'sourceModule': PropertySchema(
      id: 28,
      name: r'sourceModule',
      type: IsarType.string,
    ),
    r'sourceRecordId': PropertySchema(
      id: 29,
      name: r'sourceRecordId',
      type: IsarType.string,
    ),
    r'status': PropertySchema(
      id: 30,
      name: r'status',
      type: IsarType.byte,
      enumMap: _EventIsarstatusEnumValueMap,
    ),
    r'title': PropertySchema(
      id: 31,
      name: r'title',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 32,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'weatherConditions': PropertySchema(
      id: 33,
      name: r'weatherConditions',
      type: IsarType.string,
    )
  },
  estimateSize: _eventIsarEstimateSize,
  serialize: _eventIsarSerialize,
  deserialize: _eventIsarDeserialize,
  deserializeProp: _eventIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleTagId': IndexSchema(
      id: -2283963072638323009,
      name: r'cattleTagId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleTagId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'eventTypeId': IndexSchema(
      id: -3593908424487392989,
      name: r'eventTypeId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'eventTypeId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'category': IndexSchema(
      id: -7560358558326323820,
      name: r'category',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'category',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'title': IndexSchema(
      id: -7636685945352118059,
      name: r'title',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'title',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    ),
    r'description': IndexSchema(
      id: -6307138540013950700,
      name: r'description',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'description',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    ),
    r'notes': IndexSchema(
      id: 8092016287011465773,
      name: r'notes',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'notes',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    ),
    r'scheduledDate': IndexSchema(
      id: -6773496565145745994,
      name: r'scheduledDate',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'scheduledDate',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'status': IndexSchema(
      id: -107785170620420283,
      name: r'status',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'status',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'location': IndexSchema(
      id: -2052452620202604545,
      name: r'location',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'location',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    ),
    r'completedBy': IndexSchema(
      id: 4936996281928765776,
      name: r'completedBy',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'completedBy',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    ),
    r'completionNotes': IndexSchema(
      id: -3234258750856143583,
      name: r'completionNotes',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'completionNotes',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _eventIsarGetId,
  getLinks: _eventIsarGetLinks,
  attach: _eventIsarAttach,
  version: '3.1.0+1',
);

int _eventIsarEstimateSize(
  EventIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleTagId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.completedBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.completionNotes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.description;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.eventTypeId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.location;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.parentEventId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.reminderMinutes;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.sourceModule;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sourceRecordId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.weatherConditions;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _eventIsarSerialize(
  EventIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.actualCost);
  writer.writeString(offsets[1], object.businessId);
  writer.writeByte(offsets[2], object.category.index);
  writer.writeString(offsets[3], object.cattleTagId);
  writer.writeString(offsets[4], object.completedBy);
  writer.writeDateTime(offsets[5], object.completedDate);
  writer.writeString(offsets[6], object.completionNotes);
  writer.writeDateTime(offsets[7], object.createdAt);
  writer.writeLong(offsets[8], object.daysUntilScheduled);
  writer.writeString(offsets[9], object.description);
  writer.writeDouble(offsets[10], object.estimatedCost);
  writer.writeString(offsets[11], object.eventTypeId);
  writer.writeBool(offsets[12], object.isAutoGenerated);
  writer.writeBool(offsets[13], object.isCompleted);
  writer.writeBool(offsets[14], object.isMissed);
  writer.writeBool(offsets[15], object.isOverdue);
  writer.writeBool(offsets[16], object.isRecurring);
  writer.writeBool(offsets[17], object.isUpcoming);
  writer.writeString(offsets[18], object.location);
  writer.writeString(offsets[19], object.notes);
  writer.writeBool(offsets[20], object.notificationsEnabled);
  writer.writeString(offsets[21], object.parentEventId);
  writer.writeByte(offsets[22], object.priority.index);
  writer.writeDateTime(offsets[23], object.recurrenceEndDate);
  writer.writeLong(offsets[24], object.recurrenceInterval);
  writer.writeByte(offsets[25], object.recurrencePattern.index);
  writer.writeLongList(offsets[26], object.reminderMinutes);
  writer.writeDateTime(offsets[27], object.scheduledDate);
  writer.writeString(offsets[28], object.sourceModule);
  writer.writeString(offsets[29], object.sourceRecordId);
  writer.writeByte(offsets[30], object.status.index);
  writer.writeString(offsets[31], object.title);
  writer.writeDateTime(offsets[32], object.updatedAt);
  writer.writeString(offsets[33], object.weatherConditions);
}

EventIsar _eventIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = EventIsar();
  object.actualCost = reader.readDoubleOrNull(offsets[0]);
  object.businessId = reader.readStringOrNull(offsets[1]);
  object.category =
      _EventIsarcategoryValueEnumMap[reader.readByteOrNull(offsets[2])] ??
          EventCategory.health;
  object.cattleTagId = reader.readStringOrNull(offsets[3]);
  object.completedBy = reader.readStringOrNull(offsets[4]);
  object.completedDate = reader.readDateTimeOrNull(offsets[5]);
  object.completionNotes = reader.readStringOrNull(offsets[6]);
  object.createdAt = reader.readDateTimeOrNull(offsets[7]);
  object.description = reader.readStringOrNull(offsets[9]);
  object.estimatedCost = reader.readDoubleOrNull(offsets[10]);
  object.eventTypeId = reader.readStringOrNull(offsets[11]);
  object.id = id;
  object.isAutoGenerated = reader.readBoolOrNull(offsets[12]);
  object.isCompleted = reader.readBool(offsets[13]);
  object.isMissed = reader.readBool(offsets[14]);
  object.isRecurring = reader.readBoolOrNull(offsets[16]);
  object.location = reader.readStringOrNull(offsets[18]);
  object.notes = reader.readStringOrNull(offsets[19]);
  object.notificationsEnabled = reader.readBoolOrNull(offsets[20]);
  object.parentEventId = reader.readStringOrNull(offsets[21]);
  object.priority =
      _EventIsarpriorityValueEnumMap[reader.readByteOrNull(offsets[22])] ??
          EventPriority.low;
  object.recurrenceEndDate = reader.readDateTimeOrNull(offsets[23]);
  object.recurrenceInterval = reader.readLongOrNull(offsets[24]);
  object.recurrencePattern = _EventIsarrecurrencePatternValueEnumMap[
          reader.readByteOrNull(offsets[25])] ??
      RecurrencePattern.daily;
  object.reminderMinutes = reader.readLongList(offsets[26]);
  object.scheduledDate = reader.readDateTimeOrNull(offsets[27]);
  object.sourceModule = reader.readStringOrNull(offsets[28]);
  object.sourceRecordId = reader.readStringOrNull(offsets[29]);
  object.status =
      _EventIsarstatusValueEnumMap[reader.readByteOrNull(offsets[30])] ??
          EventStatus.scheduled;
  object.title = reader.readStringOrNull(offsets[31]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[32]);
  object.weatherConditions = reader.readStringOrNull(offsets[33]);
  return object;
}

P _eventIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDoubleOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (_EventIsarcategoryValueEnumMap[reader.readByteOrNull(offset)] ??
          EventCategory.health) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readLongOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readDoubleOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readBoolOrNull(offset)) as P;
    case 13:
      return (reader.readBool(offset)) as P;
    case 14:
      return (reader.readBool(offset)) as P;
    case 15:
      return (reader.readBool(offset)) as P;
    case 16:
      return (reader.readBoolOrNull(offset)) as P;
    case 17:
      return (reader.readBool(offset)) as P;
    case 18:
      return (reader.readStringOrNull(offset)) as P;
    case 19:
      return (reader.readStringOrNull(offset)) as P;
    case 20:
      return (reader.readBoolOrNull(offset)) as P;
    case 21:
      return (reader.readStringOrNull(offset)) as P;
    case 22:
      return (_EventIsarpriorityValueEnumMap[reader.readByteOrNull(offset)] ??
          EventPriority.low) as P;
    case 23:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 24:
      return (reader.readLongOrNull(offset)) as P;
    case 25:
      return (_EventIsarrecurrencePatternValueEnumMap[
              reader.readByteOrNull(offset)] ??
          RecurrencePattern.daily) as P;
    case 26:
      return (reader.readLongList(offset)) as P;
    case 27:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 28:
      return (reader.readStringOrNull(offset)) as P;
    case 29:
      return (reader.readStringOrNull(offset)) as P;
    case 30:
      return (_EventIsarstatusValueEnumMap[reader.readByteOrNull(offset)] ??
          EventStatus.scheduled) as P;
    case 31:
      return (reader.readStringOrNull(offset)) as P;
    case 32:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 33:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _EventIsarcategoryEnumValueMap = {
  'health': 0,
  'breeding': 1,
  'feeding': 2,
  'management': 3,
  'maintenance': 4,
  'financial': 5,
  'other': 6,
};
const _EventIsarcategoryValueEnumMap = {
  0: EventCategory.health,
  1: EventCategory.breeding,
  2: EventCategory.feeding,
  3: EventCategory.management,
  4: EventCategory.maintenance,
  5: EventCategory.financial,
  6: EventCategory.other,
};
const _EventIsarpriorityEnumValueMap = {
  'low': 0,
  'medium': 1,
  'high': 2,
  'critical': 3,
};
const _EventIsarpriorityValueEnumMap = {
  0: EventPriority.low,
  1: EventPriority.medium,
  2: EventPriority.high,
  3: EventPriority.critical,
};
const _EventIsarrecurrencePatternEnumValueMap = {
  'daily': 0,
  'weekly': 1,
  'monthly': 2,
  'yearly': 3,
  'custom': 4,
};
const _EventIsarrecurrencePatternValueEnumMap = {
  0: RecurrencePattern.daily,
  1: RecurrencePattern.weekly,
  2: RecurrencePattern.monthly,
  3: RecurrencePattern.yearly,
  4: RecurrencePattern.custom,
};
const _EventIsarstatusEnumValueMap = {
  'scheduled': 0,
  'inProgress': 1,
  'completed': 2,
  'cancelled': 3,
  'overdue': 4,
  'missed': 5,
};
const _EventIsarstatusValueEnumMap = {
  0: EventStatus.scheduled,
  1: EventStatus.inProgress,
  2: EventStatus.completed,
  3: EventStatus.cancelled,
  4: EventStatus.overdue,
  5: EventStatus.missed,
};

Id _eventIsarGetId(EventIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _eventIsarGetLinks(EventIsar object) {
  return [];
}

void _eventIsarAttach(IsarCollection<dynamic> col, Id id, EventIsar object) {
  object.id = id;
}

extension EventIsarByIndex on IsarCollection<EventIsar> {
  Future<EventIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  EventIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<EventIsar?>> getAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<EventIsar?> getAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(EventIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(EventIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<EventIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<EventIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension EventIsarQueryWhereSort
    on QueryBuilder<EventIsar, EventIsar, QWhere> {
  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'category'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'title'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'description'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'notes'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyScheduledDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'scheduledDate'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'status'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'location'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyCompletedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'completedBy'),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhere> anyCompletionNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'completionNotes'),
      );
    });
  }
}

extension EventIsarQueryWhere
    on QueryBuilder<EventIsar, EventIsar, QWhereClause> {
  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> businessIdEqualTo(
      String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> businessIdNotEqualTo(
      String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleTagId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> cattleTagIdEqualTo(
      String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [cattleTagId],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> cattleTagIdNotEqualTo(
      String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> eventTypeIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'eventTypeId',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> eventTypeIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'eventTypeId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> eventTypeIdEqualTo(
      String? eventTypeId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'eventTypeId',
        value: [eventTypeId],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> eventTypeIdNotEqualTo(
      String? eventTypeId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'eventTypeId',
              lower: [],
              upper: [eventTypeId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'eventTypeId',
              lower: [eventTypeId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'eventTypeId',
              lower: [eventTypeId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'eventTypeId',
              lower: [],
              upper: [eventTypeId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> categoryEqualTo(
      EventCategory category) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'category',
        value: [category],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> categoryNotEqualTo(
      EventCategory category) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'category',
              lower: [],
              upper: [category],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'category',
              lower: [category],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'category',
              lower: [category],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'category',
              lower: [],
              upper: [category],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> categoryGreaterThan(
    EventCategory category, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'category',
        lower: [category],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> categoryLessThan(
    EventCategory category, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'category',
        lower: [],
        upper: [category],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> categoryBetween(
    EventCategory lowerCategory,
    EventCategory upperCategory, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'category',
        lower: [lowerCategory],
        includeLower: includeLower,
        upper: [upperCategory],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'title',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'title',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleEqualTo(
      String? title) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'title',
        value: [title],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleNotEqualTo(
      String? title) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'title',
              lower: [],
              upper: [title],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'title',
              lower: [title],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'title',
              lower: [title],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'title',
              lower: [],
              upper: [title],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleGreaterThan(
    String? title, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'title',
        lower: [title],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleLessThan(
    String? title, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'title',
        lower: [],
        upper: [title],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleBetween(
    String? lowerTitle,
    String? upperTitle, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'title',
        lower: [lowerTitle],
        includeLower: includeLower,
        upper: [upperTitle],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleStartsWith(
      String TitlePrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'title',
        lower: [TitlePrefix],
        upper: ['$TitlePrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'title',
        value: [''],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'title',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'title',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'title',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'title',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'description',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'description',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionEqualTo(
      String? description) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'description',
        value: [description],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionNotEqualTo(
      String? description) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'description',
              lower: [],
              upper: [description],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'description',
              lower: [description],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'description',
              lower: [description],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'description',
              lower: [],
              upper: [description],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionGreaterThan(
    String? description, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'description',
        lower: [description],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionLessThan(
    String? description, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'description',
        lower: [],
        upper: [description],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionBetween(
    String? lowerDescription,
    String? upperDescription, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'description',
        lower: [lowerDescription],
        includeLower: includeLower,
        upper: [upperDescription],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionStartsWith(
      String DescriptionPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'description',
        lower: [DescriptionPrefix],
        upper: ['$DescriptionPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> descriptionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'description',
        value: [''],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      descriptionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'description',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'description',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'description',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'description',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'notes',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'notes',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesEqualTo(
      String? notes) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'notes',
        value: [notes],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesNotEqualTo(
      String? notes) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'notes',
              lower: [],
              upper: [notes],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'notes',
              lower: [notes],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'notes',
              lower: [notes],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'notes',
              lower: [],
              upper: [notes],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesGreaterThan(
    String? notes, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'notes',
        lower: [notes],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesLessThan(
    String? notes, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'notes',
        lower: [],
        upper: [notes],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesBetween(
    String? lowerNotes,
    String? upperNotes, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'notes',
        lower: [lowerNotes],
        includeLower: includeLower,
        upper: [upperNotes],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesStartsWith(
      String NotesPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'notes',
        lower: [NotesPrefix],
        upper: ['$NotesPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'notes',
        value: [''],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'notes',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'notes',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'notes',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'notes',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> scheduledDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'scheduledDate',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      scheduledDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'scheduledDate',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> scheduledDateEqualTo(
      DateTime? scheduledDate) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'scheduledDate',
        value: [scheduledDate],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> scheduledDateNotEqualTo(
      DateTime? scheduledDate) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'scheduledDate',
              lower: [],
              upper: [scheduledDate],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'scheduledDate',
              lower: [scheduledDate],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'scheduledDate',
              lower: [scheduledDate],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'scheduledDate',
              lower: [],
              upper: [scheduledDate],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      scheduledDateGreaterThan(
    DateTime? scheduledDate, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'scheduledDate',
        lower: [scheduledDate],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> scheduledDateLessThan(
    DateTime? scheduledDate, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'scheduledDate',
        lower: [],
        upper: [scheduledDate],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> scheduledDateBetween(
    DateTime? lowerScheduledDate,
    DateTime? upperScheduledDate, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'scheduledDate',
        lower: [lowerScheduledDate],
        includeLower: includeLower,
        upper: [upperScheduledDate],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> statusEqualTo(
      EventStatus status) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'status',
        value: [status],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> statusNotEqualTo(
      EventStatus status) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [],
              upper: [status],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [status],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [status],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [],
              upper: [status],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> statusGreaterThan(
    EventStatus status, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'status',
        lower: [status],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> statusLessThan(
    EventStatus status, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'status',
        lower: [],
        upper: [status],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> statusBetween(
    EventStatus lowerStatus,
    EventStatus upperStatus, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'status',
        lower: [lowerStatus],
        includeLower: includeLower,
        upper: [upperStatus],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'location',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'location',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationEqualTo(
      String? location) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'location',
        value: [location],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationNotEqualTo(
      String? location) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'location',
              lower: [],
              upper: [location],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'location',
              lower: [location],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'location',
              lower: [location],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'location',
              lower: [],
              upper: [location],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationGreaterThan(
    String? location, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'location',
        lower: [location],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationLessThan(
    String? location, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'location',
        lower: [],
        upper: [location],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationBetween(
    String? lowerLocation,
    String? upperLocation, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'location',
        lower: [lowerLocation],
        includeLower: includeLower,
        upper: [upperLocation],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationStartsWith(
      String LocationPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'location',
        lower: [LocationPrefix],
        upper: ['$LocationPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'location',
        value: [''],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> locationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'location',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'location',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'location',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'location',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'completedBy',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completedBy',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByEqualTo(
      String? completedBy) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'completedBy',
        value: [completedBy],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByNotEqualTo(
      String? completedBy) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completedBy',
              lower: [],
              upper: [completedBy],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completedBy',
              lower: [completedBy],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completedBy',
              lower: [completedBy],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completedBy',
              lower: [],
              upper: [completedBy],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByGreaterThan(
    String? completedBy, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completedBy',
        lower: [completedBy],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByLessThan(
    String? completedBy, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completedBy',
        lower: [],
        upper: [completedBy],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByBetween(
    String? lowerCompletedBy,
    String? upperCompletedBy, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completedBy',
        lower: [lowerCompletedBy],
        includeLower: includeLower,
        upper: [upperCompletedBy],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByStartsWith(
      String CompletedByPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completedBy',
        lower: [CompletedByPrefix],
        upper: ['$CompletedByPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'completedBy',
        value: [''],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'completedBy',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'completedBy',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'completedBy',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'completedBy',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completionNotesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'completionNotes',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completionNotesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completionNotes',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completionNotesEqualTo(
      String? completionNotes) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'completionNotes',
        value: [completionNotes],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completionNotesNotEqualTo(String? completionNotes) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completionNotes',
              lower: [],
              upper: [completionNotes],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completionNotes',
              lower: [completionNotes],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completionNotes',
              lower: [completionNotes],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'completionNotes',
              lower: [],
              upper: [completionNotes],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completionNotesGreaterThan(
    String? completionNotes, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completionNotes',
        lower: [completionNotes],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completionNotesLessThan(
    String? completionNotes, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completionNotes',
        lower: [],
        upper: [completionNotes],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> completionNotesBetween(
    String? lowerCompletionNotes,
    String? upperCompletionNotes, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completionNotes',
        lower: [lowerCompletionNotes],
        includeLower: includeLower,
        upper: [upperCompletionNotes],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completionNotesStartsWith(String CompletionNotesPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'completionNotes',
        lower: [CompletionNotesPrefix],
        upper: ['$CompletionNotesPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completionNotesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'completionNotes',
        value: [''],
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause>
      completionNotesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'completionNotes',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'completionNotes',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'completionNotes',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'completionNotes',
              upper: [''],
            ));
      }
    });
  }
}

extension EventIsarQueryFilter
    on QueryBuilder<EventIsar, EventIsar, QFilterCondition> {
  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> actualCostIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'actualCost',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      actualCostIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'actualCost',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> actualCostEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'actualCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      actualCostGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'actualCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> actualCostLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'actualCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> actualCostBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'actualCost',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> businessIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> businessIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> categoryEqualTo(
      EventCategory value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'category',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> categoryGreaterThan(
    EventCategory value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'category',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> categoryLessThan(
    EventCategory value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'category',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> categoryBetween(
    EventCategory lower,
    EventCategory upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'category',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> cattleTagIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      cattleTagIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> cattleTagIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> cattleTagIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleTagId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      cattleTagIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> cattleTagIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> cattleTagIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> cattleTagIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleTagId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      cattleTagIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      cattleTagIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completedBy',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completedBy',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> completedByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> completedByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> completedByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completedBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'completedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> completedByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'completedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> completedByContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'completedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> completedByMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'completedBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'completedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completedDate',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completedDate',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completedDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completedDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completedDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completionNotes',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completionNotes',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completionNotes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'completionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'completionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'completionNotes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'completionNotes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completionNotes',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      completionNotesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'completionNotes',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> createdAtEqualTo(
      DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      daysUntilScheduledIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'daysUntilScheduled',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      daysUntilScheduledIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'daysUntilScheduled',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      daysUntilScheduledEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'daysUntilScheduled',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      daysUntilScheduledGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'daysUntilScheduled',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      daysUntilScheduledLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'daysUntilScheduled',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      daysUntilScheduledBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'daysUntilScheduled',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      descriptionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      descriptionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> descriptionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      descriptionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> descriptionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> descriptionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'description',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      descriptionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> descriptionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> descriptionContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> descriptionMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'description',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      descriptionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      descriptionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      estimatedCostIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'estimatedCost',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      estimatedCostIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'estimatedCost',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      estimatedCostEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'estimatedCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      estimatedCostGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'estimatedCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      estimatedCostLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'estimatedCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      estimatedCostBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'estimatedCost',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      eventTypeIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eventTypeId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      eventTypeIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eventTypeId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> eventTypeIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventTypeId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      eventTypeIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eventTypeId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> eventTypeIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eventTypeId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> eventTypeIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eventTypeId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      eventTypeIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'eventTypeId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> eventTypeIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'eventTypeId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> eventTypeIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'eventTypeId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> eventTypeIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'eventTypeId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      eventTypeIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventTypeId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      eventTypeIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'eventTypeId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      isAutoGeneratedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isAutoGenerated',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      isAutoGeneratedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isAutoGenerated',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      isAutoGeneratedEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isAutoGenerated',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> isCompletedEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isCompleted',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> isMissedEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isMissed',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> isOverdueEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isOverdue',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      isRecurringIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isRecurring',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      isRecurringIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isRecurring',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> isRecurringEqualTo(
      bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isRecurring',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> isUpcomingEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isUpcoming',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'location',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      locationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'location',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'location',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'location',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'location',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'location',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'location',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'location',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'location',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'location',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> locationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'location',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      locationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'location',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      notificationsEnabledIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notificationsEnabled',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      notificationsEnabledIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notificationsEnabled',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      notificationsEnabledEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'parentEventId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'parentEventId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'parentEventId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'parentEventId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'parentEventId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'parentEventId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'parentEventId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'parentEventId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'parentEventId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'parentEventId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'parentEventId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      parentEventIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'parentEventId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> priorityEqualTo(
      EventPriority value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'priority',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> priorityGreaterThan(
    EventPriority value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'priority',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> priorityLessThan(
    EventPriority value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'priority',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> priorityBetween(
    EventPriority lower,
    EventPriority upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'priority',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceEndDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recurrenceEndDate',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceEndDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recurrenceEndDate',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceEndDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recurrenceEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceEndDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recurrenceEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceEndDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recurrenceEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceEndDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recurrenceEndDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceIntervalIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recurrenceInterval',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceIntervalIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recurrenceInterval',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceIntervalEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recurrenceInterval',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceIntervalGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recurrenceInterval',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceIntervalLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recurrenceInterval',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrenceIntervalBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recurrenceInterval',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrencePatternEqualTo(RecurrencePattern value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recurrencePattern',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrencePatternGreaterThan(
    RecurrencePattern value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recurrencePattern',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrencePatternLessThan(
    RecurrencePattern value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recurrencePattern',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      recurrencePatternBetween(
    RecurrencePattern lower,
    RecurrencePattern upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recurrencePattern',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reminderMinutes',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reminderMinutes',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reminderMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reminderMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reminderMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reminderMinutes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reminderMinutes',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reminderMinutes',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reminderMinutes',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reminderMinutes',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reminderMinutes',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      reminderMinutesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reminderMinutes',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      scheduledDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'scheduledDate',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      scheduledDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'scheduledDate',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      scheduledDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'scheduledDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      scheduledDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'scheduledDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      scheduledDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'scheduledDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      scheduledDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'scheduledDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sourceModule',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sourceModule',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> sourceModuleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceModule',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sourceModule',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sourceModule',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> sourceModuleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sourceModule',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sourceModule',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sourceModule',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sourceModule',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> sourceModuleMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sourceModule',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceModule',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceModuleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sourceModule',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sourceRecordId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sourceRecordId',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sourceRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sourceRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sourceRecordId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sourceRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sourceRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sourceRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sourceRecordId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceRecordId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      sourceRecordIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sourceRecordId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> statusEqualTo(
      EventStatus value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> statusGreaterThan(
    EventStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> statusLessThan(
    EventStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> statusBetween(
    EventStatus lower,
    EventStatus upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> updatedAtEqualTo(
      DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weatherConditions',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weatherConditions',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weatherConditions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weatherConditions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weatherConditions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weatherConditions',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'weatherConditions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'weatherConditions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'weatherConditions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'weatherConditions',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weatherConditions',
        value: '',
      ));
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterFilterCondition>
      weatherConditionsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'weatherConditions',
        value: '',
      ));
    });
  }
}

extension EventIsarQueryObject
    on QueryBuilder<EventIsar, EventIsar, QFilterCondition> {}

extension EventIsarQueryLinks
    on QueryBuilder<EventIsar, EventIsar, QFilterCondition> {}

extension EventIsarQuerySortBy on QueryBuilder<EventIsar, EventIsar, QSortBy> {
  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByActualCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'actualCost', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByActualCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'actualCost', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCompletedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedBy', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCompletedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedBy', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCompletedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedDate', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCompletedDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedDate', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCompletionNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionNotes', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCompletionNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionNotes', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByDaysUntilScheduled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysUntilScheduled', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      sortByDaysUntilScheduledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysUntilScheduled', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByEstimatedCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'estimatedCost', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByEstimatedCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'estimatedCost', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByEventTypeId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventTypeId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByEventTypeIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventTypeId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsAutoGenerated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAutoGenerated', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsAutoGeneratedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAutoGenerated', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsMissed() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMissed', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsMissedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMissed', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsOverdue() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isOverdue', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsOverdueDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isOverdue', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsRecurring() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isRecurring', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsRecurringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isRecurring', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsUpcoming() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUpcoming', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByIsUpcomingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUpcoming', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'location', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'location', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      sortByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      sortByNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByParentEventId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'parentEventId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByParentEventIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'parentEventId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByPriority() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'priority', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByPriorityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'priority', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByRecurrenceEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceEndDate', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      sortByRecurrenceEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceEndDate', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByRecurrenceInterval() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceInterval', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      sortByRecurrenceIntervalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceInterval', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByRecurrencePattern() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrencePattern', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      sortByRecurrencePatternDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrencePattern', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByScheduledDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scheduledDate', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByScheduledDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scheduledDate', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortBySourceModule() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceModule', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortBySourceModuleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceModule', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortBySourceRecordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceRecordId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortBySourceRecordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceRecordId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> sortByWeatherConditions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      sortByWeatherConditionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.desc);
    });
  }
}

extension EventIsarQuerySortThenBy
    on QueryBuilder<EventIsar, EventIsar, QSortThenBy> {
  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByActualCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'actualCost', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByActualCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'actualCost', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCompletedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedBy', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCompletedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedBy', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCompletedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedDate', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCompletedDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedDate', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCompletionNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionNotes', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCompletionNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completionNotes', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByDaysUntilScheduled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysUntilScheduled', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      thenByDaysUntilScheduledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'daysUntilScheduled', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByEstimatedCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'estimatedCost', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByEstimatedCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'estimatedCost', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByEventTypeId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventTypeId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByEventTypeIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventTypeId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsAutoGenerated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAutoGenerated', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsAutoGeneratedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAutoGenerated', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsMissed() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMissed', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsMissedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMissed', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsOverdue() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isOverdue', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsOverdueDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isOverdue', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsRecurring() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isRecurring', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsRecurringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isRecurring', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsUpcoming() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUpcoming', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByIsUpcomingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUpcoming', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'location', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'location', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      thenByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      thenByNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByParentEventId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'parentEventId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByParentEventIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'parentEventId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByPriority() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'priority', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByPriorityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'priority', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByRecurrenceEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceEndDate', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      thenByRecurrenceEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceEndDate', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByRecurrenceInterval() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceInterval', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      thenByRecurrenceIntervalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrenceInterval', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByRecurrencePattern() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrencePattern', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      thenByRecurrencePatternDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recurrencePattern', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByScheduledDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scheduledDate', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByScheduledDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scheduledDate', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenBySourceModule() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceModule', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenBySourceModuleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceModule', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenBySourceRecordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceRecordId', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenBySourceRecordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceRecordId', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy> thenByWeatherConditions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.asc);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QAfterSortBy>
      thenByWeatherConditionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weatherConditions', Sort.desc);
    });
  }
}

extension EventIsarQueryWhereDistinct
    on QueryBuilder<EventIsar, EventIsar, QDistinct> {
  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByActualCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'actualCost');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByBusinessId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'category');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByCattleTagId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleTagId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByCompletedBy(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completedBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByCompletedDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completedDate');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByCompletionNotes(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completionNotes',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByDaysUntilScheduled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'daysUntilScheduled');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByDescription(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'description', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByEstimatedCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'estimatedCost');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByEventTypeId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventTypeId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByIsAutoGenerated() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isAutoGenerated');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isCompleted');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByIsMissed() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isMissed');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByIsOverdue() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isOverdue');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByIsRecurring() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isRecurring');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByIsUpcoming() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isUpcoming');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByLocation(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'location', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByNotes(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct>
      distinctByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationsEnabled');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByParentEventId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'parentEventId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByPriority() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'priority');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByRecurrenceEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recurrenceEndDate');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByRecurrenceInterval() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recurrenceInterval');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByRecurrencePattern() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recurrencePattern');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByReminderMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reminderMinutes');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByScheduledDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'scheduledDate');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctBySourceModule(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sourceModule', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctBySourceRecordId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sourceRecordId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByTitle(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<EventIsar, EventIsar, QDistinct> distinctByWeatherConditions(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weatherConditions',
          caseSensitive: caseSensitive);
    });
  }
}

extension EventIsarQueryProperty
    on QueryBuilder<EventIsar, EventIsar, QQueryProperty> {
  QueryBuilder<EventIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<EventIsar, double?, QQueryOperations> actualCostProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'actualCost');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<EventIsar, EventCategory, QQueryOperations> categoryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'category');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> cattleTagIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleTagId');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> completedByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completedBy');
    });
  }

  QueryBuilder<EventIsar, DateTime?, QQueryOperations> completedDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completedDate');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> completionNotesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completionNotes');
    });
  }

  QueryBuilder<EventIsar, DateTime?, QQueryOperations> createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<EventIsar, int?, QQueryOperations> daysUntilScheduledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'daysUntilScheduled');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> descriptionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'description');
    });
  }

  QueryBuilder<EventIsar, double?, QQueryOperations> estimatedCostProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'estimatedCost');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> eventTypeIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventTypeId');
    });
  }

  QueryBuilder<EventIsar, bool?, QQueryOperations> isAutoGeneratedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isAutoGenerated');
    });
  }

  QueryBuilder<EventIsar, bool, QQueryOperations> isCompletedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isCompleted');
    });
  }

  QueryBuilder<EventIsar, bool, QQueryOperations> isMissedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isMissed');
    });
  }

  QueryBuilder<EventIsar, bool, QQueryOperations> isOverdueProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isOverdue');
    });
  }

  QueryBuilder<EventIsar, bool?, QQueryOperations> isRecurringProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isRecurring');
    });
  }

  QueryBuilder<EventIsar, bool, QQueryOperations> isUpcomingProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isUpcoming');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> locationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'location');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<EventIsar, bool?, QQueryOperations>
      notificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationsEnabled');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> parentEventIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'parentEventId');
    });
  }

  QueryBuilder<EventIsar, EventPriority, QQueryOperations> priorityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'priority');
    });
  }

  QueryBuilder<EventIsar, DateTime?, QQueryOperations>
      recurrenceEndDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recurrenceEndDate');
    });
  }

  QueryBuilder<EventIsar, int?, QQueryOperations> recurrenceIntervalProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recurrenceInterval');
    });
  }

  QueryBuilder<EventIsar, RecurrencePattern, QQueryOperations>
      recurrencePatternProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recurrencePattern');
    });
  }

  QueryBuilder<EventIsar, List<int>?, QQueryOperations>
      reminderMinutesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reminderMinutes');
    });
  }

  QueryBuilder<EventIsar, DateTime?, QQueryOperations> scheduledDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'scheduledDate');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> sourceModuleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sourceModule');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> sourceRecordIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sourceRecordId');
    });
  }

  QueryBuilder<EventIsar, EventStatus, QQueryOperations> statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations> titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<EventIsar, DateTime?, QQueryOperations> updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<EventIsar, String?, QQueryOperations>
      weatherConditionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weatherConditions');
    });
  }
}
