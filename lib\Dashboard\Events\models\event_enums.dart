

/// Represents different categories of events in cattle management
enum EventCategory {
  health,
  breeding,
  feeding,
  management,
  maintenance,
  financial,
  other
}

/// Represents the current status of an event
enum EventStatus {
  scheduled,
  inProgress,
  completed,
  cancelled,
  overdue,
  missed
}

/// Represents the priority level of an event
enum EventPriority {
  low,
  medium,
  high,
  critical
}

/// Represents different recurrence patterns for events
enum RecurrencePattern {
  daily,
  weekly,
  monthly,
  yearly,
  custom
}

/// Extension methods for EventCategory
extension EventCategoryExtension on EventCategory {
  /// Get display name for the category
  String get displayName {
    switch (this) {
      case EventCategory.health:
        return 'Health';
      case EventCategory.breeding:
        return 'Breeding';
      case EventCategory.feeding:
        return 'Feeding';
      case EventCategory.management:
        return 'Management';
      case EventCategory.maintenance:
        return 'Maintenance';
      case EventCategory.financial:
        return 'Financial';
      case EventCategory.other:
        return 'Other';
    }
  }

  /// Get color hex code for the category
  String get colorHex {
    switch (this) {
      case EventCategory.health:
        return '#FF5722'; // Red-orange for health
      case EventCategory.breeding:
        return '#E91E63'; // Pink for breeding
      case EventCategory.feeding:
        return '#4CAF50'; // Green for feeding
      case EventCategory.management:
        return '#2196F3'; // Blue for management
      case EventCategory.maintenance:
        return '#FF9800'; // Orange for maintenance
      case EventCategory.financial:
        return '#9C27B0'; // Purple for financial
      case EventCategory.other:
        return '#607D8B'; // Blue-grey for other
    }
  }

  /// Get icon name for the category
  String get iconName {
    switch (this) {
      case EventCategory.health:
        return 'medical_services';
      case EventCategory.breeding:
        return 'favorite';
      case EventCategory.feeding:
        return 'restaurant';
      case EventCategory.management:
        return 'business';
      case EventCategory.maintenance:
        return 'build';
      case EventCategory.financial:
        return 'attach_money';
      case EventCategory.other:
        return 'event';
    }
  }
}

/// Extension methods for EventStatus
extension EventStatusExtension on EventStatus {
  /// Get display name for the status
  String get displayName {
    switch (this) {
      case EventStatus.scheduled:
        return 'Scheduled';
      case EventStatus.inProgress:
        return 'In Progress';
      case EventStatus.completed:
        return 'Completed';
      case EventStatus.cancelled:
        return 'Cancelled';
      case EventStatus.overdue:
        return 'Overdue';
      case EventStatus.missed:
        return 'Missed';
    }
  }

  /// Get color hex code for the status
  String get colorHex {
    switch (this) {
      case EventStatus.scheduled:
        return '#2196F3'; // Blue for scheduled
      case EventStatus.inProgress:
        return '#FF9800'; // Orange for in progress
      case EventStatus.completed:
        return '#4CAF50'; // Green for completed
      case EventStatus.cancelled:
        return '#9E9E9E'; // Grey for cancelled
      case EventStatus.overdue:
        return '#F44336'; // Red for overdue
      case EventStatus.missed:
        return '#9C27B0'; // Purple for missed
    }
  }

  /// Check if the status indicates the event is active
  bool get isActive {
    return this == EventStatus.scheduled || this == EventStatus.inProgress;
  }

  /// Check if the status indicates the event is finished
  bool get isFinished {
    return this == EventStatus.completed || this == EventStatus.cancelled;
  }
}

/// Extension methods for EventPriority
extension EventPriorityExtension on EventPriority {
  /// Get display name for the priority
  String get displayName {
    switch (this) {
      case EventPriority.low:
        return 'Low';
      case EventPriority.medium:
        return 'Medium';
      case EventPriority.high:
        return 'High';
      case EventPriority.critical:
        return 'Critical';
    }
  }

  /// Get color hex code for the priority
  String get colorHex {
    switch (this) {
      case EventPriority.low:
        return '#4CAF50'; // Green for low
      case EventPriority.medium:
        return '#FF9800'; // Orange for medium
      case EventPriority.high:
        return '#FF5722'; // Red-orange for high
      case EventPriority.critical:
        return '#F44336'; // Red for critical
    }
  }

  /// Get numeric value for sorting
  int get sortValue {
    switch (this) {
      case EventPriority.low:
        return 1;
      case EventPriority.medium:
        return 2;
      case EventPriority.high:
        return 3;
      case EventPriority.critical:
        return 4;
    }
  }
}

/// Extension methods for RecurrencePattern
extension RecurrencePatternExtension on RecurrencePattern {
  /// Get display name for the recurrence pattern
  String get displayName {
    switch (this) {
      case RecurrencePattern.daily:
        return 'Daily';
      case RecurrencePattern.weekly:
        return 'Weekly';
      case RecurrencePattern.monthly:
        return 'Monthly';
      case RecurrencePattern.yearly:
        return 'Yearly';
      case RecurrencePattern.custom:
        return 'Custom';
    }
  }

  /// Get default interval in days for the pattern
  int get defaultIntervalDays {
    switch (this) {
      case RecurrencePattern.daily:
        return 1;
      case RecurrencePattern.weekly:
        return 7;
      case RecurrencePattern.monthly:
        return 30;
      case RecurrencePattern.yearly:
        return 365;
      case RecurrencePattern.custom:
        return 1; // Default to daily for custom
    }
  }
}