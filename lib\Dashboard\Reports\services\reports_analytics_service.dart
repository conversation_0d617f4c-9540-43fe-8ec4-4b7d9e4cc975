import 'package:flutter/material.dart';
import '../models/report_models.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Breeding/models/breeding_record_isar.dart';
import '../../Weight/models/weight_record_isar.dart';
import '../../Transactions/models/transaction_isar.dart';
import '../../Events/models/event_isar.dart';

/// Pure analytics service for Reports module - no state, just calculations
/// Following the cattle analytics service pattern: single-pass O(n) efficiency
/// All methods are static and purely functional
class ReportsAnalyticsService {

  /// Calculate dashboard analytics from all module data
  /// Single entry point with O(n) efficiency across all data types
  static ReportsAnalyticsResult calculateDashboard({
    required List<CattleIsar> cattle,
    required List<MilkRecordIsar> milkRecords,
    required List<HealthRecordIsar> healthRecords,
    required List<BreedingRecordIsar> breedingRecords,
    required List<WeightRecordIsar> weightRecords,
    required List<TransactionIsar> transactions,
    required List<EventIsar> events,
    required FilterState filter,
  }) {
    if (cattle.isEmpty && milkRecords.isEmpty && healthRecords.isEmpty && 
        breedingRecords.isEmpty && weightRecords.isEmpty && transactions.isEmpty) {
      return ReportsAnalyticsResult.empty;
    }

    // Single pass through all data for maximum efficiency
    final accumulator = _DashboardAnalyticsAccumulator();

    // Process each data type
    for (final animal in cattle) {
      accumulator.processCattle(animal);
    }

    for (final record in milkRecords) {
      accumulator.processMilkRecord(record);
    }

    for (final record in healthRecords) {
      accumulator.processHealthRecord(record);
    }

    for (final record in breedingRecords) {
      accumulator.processBreedingRecord(record);
    }

    for (final record in weightRecords) {
      accumulator.processWeightRecord(record);
    }

    for (final transaction in transactions) {
      accumulator.processTransaction(transaction);
    }

    for (final event in events) {
      accumulator.processEvent(event);
    }

    return accumulator.toResult();
  }

  /// Calculate cattle-specific analytics
  static ReportData calculateCattleReport(List<CattleIsar> cattle, FilterState filter) {
    if (cattle.isEmpty) {
      return ReportData.empty(ReportType.cattle);
    }

    final metrics = <String, ReportMetric>{};
    final chartData = <ChartPoint>[];
    final tableData = <Map<String, String>>[];
    final insights = <String>[];

    // Single-pass analytics calculation
    final accumulator = _CattleAnalyticsAccumulator();
    for (final animal in cattle) {
      accumulator.process(animal);
    }

    // Generate metrics
    metrics['total_cattle'] = ReportMetric.kpi(
      title: 'Total Cattle',
      value: accumulator.totalCattle.toString(),
      icon: Icons.pets,
      color: Colors.brown,
    );

    metrics['active_cattle'] = ReportMetric.kpi(
      title: 'Active Cattle',
      value: accumulator.activeCattle.toString(),
      icon: Icons.check_circle,
      color: Colors.green,
    );

    metrics['average_age'] = ReportMetric.metric(
      title: 'Average Age',
      value: '${accumulator.averageAge.toStringAsFixed(1)} months',
      icon: Icons.calendar_today,
      color: Colors.blue,
    );

    // Generate chart data for status distribution
    accumulator.statusDistribution.forEach((status, count) {
      chartData.add(ChartPoint(
        x: status,
        y: count.toDouble(),
        label: status,
        color: _getStatusColor(status),
      ));
    });

    // Generate table data
    for (final animal in cattle) {
      tableData.add({
        'Tag ID': animal.tagId ?? 'N/A',
        'Status': animal.status?.toString().split('.').last ?? 'Unknown',
        'Age': '${_calculateAgeInMonths(animal)} months',
        'Breed': animal.breed ?? 'N/A',
      });
    }

    // Generate insights
    insights.addAll(_generateCattleInsights(accumulator));

    return ReportData(
      title: 'Cattle Report',
      subtitle: 'Overview of cattle population',
      generated: DateTime.now(),
      startDate: filter.startDate,
      endDate: filter.endDate,
      metrics: metrics,
      chartData: chartData,
      tableData: tableData,
      insights: insights,
      type: ReportType.cattle,
    );
  }

  /// Calculate milk production analytics
  static ReportData calculateMilkReport(List<MilkRecordIsar> milkRecords, FilterState filter) {
    if (milkRecords.isEmpty) {
      return ReportData.empty(ReportType.milk);
    }

    final metrics = <String, ReportMetric>{};
    final chartData = <ChartPoint>[];
    final tableData = <Map<String, String>>[];
    final insights = <String>[];

    // Single-pass analytics calculation
    final accumulator = _MilkAnalyticsAccumulator();
    for (final record in milkRecords) {
      accumulator.process(record);
    }

    // Generate metrics
    metrics['total_production'] = ReportMetric.kpi(
      title: 'Total Production',
      value: '${accumulator.totalProduction.toStringAsFixed(1)}L',
      icon: Icons.local_drink,
      color: Colors.lightBlue,
    );

    metrics['average_daily'] = ReportMetric.kpi(
      title: 'Daily Average',
      value: '${accumulator.averageDaily.toStringAsFixed(1)}L',
      icon: Icons.today,
      color: Colors.blue,
    );

    metrics['total_records'] = ReportMetric.metric(
      title: 'Total Records',
      value: accumulator.totalRecords.toString(),
      icon: Icons.list,
      color: Colors.indigo,
    );

    // Generate chart data for daily production trend
    accumulator.dailyProduction.forEach((date, production) {
      chartData.add(ChartPoint(
        x: date.toIso8601String(),
        y: production,
        label: _formatDate(date),
        color: Colors.lightBlue,
      ));
    });

    // Generate table data
    for (final record in milkRecords) {
      tableData.add({
        'Date': _formatDate(record.date),
        'Cattle': record.cattleTagNumber ?? 'N/A',
        'Quantity': '${record.quantity?.toStringAsFixed(1) ?? '0'}L',
        'Session': record.session ?? 'N/A',
      });
    }

    // Generate insights
    insights.addAll(_generateMilkInsights(accumulator));

    return ReportData(
      title: 'Milk Production Report',
      subtitle: 'Overview of milk production',
      generated: DateTime.now(),
      startDate: filter.startDate,
      endDate: filter.endDate,
      metrics: metrics,
      chartData: chartData,
      tableData: tableData,
      insights: insights,
      type: ReportType.milk,
    );
  }

  /// Calculate financial analytics
  static ReportData calculateFinancialReport(List<TransactionIsar> transactions, FilterState filter) {
    if (transactions.isEmpty) {
      return ReportData.empty(ReportType.financial);
    }

    final metrics = <String, ReportMetric>{};
    final chartData = <ChartPoint>[];
    final tableData = <Map<String, String>>[];
    final insights = <String>[];

    // Single-pass analytics calculation
    final accumulator = _FinancialAnalyticsAccumulator();
    for (final transaction in transactions) {
      accumulator.process(transaction);
    }

    // Generate metrics
    metrics['total_income'] = ReportMetric.kpi(
      title: 'Total Income',
      value: '\$${accumulator.totalIncome.toStringAsFixed(2)}',
      icon: Icons.trending_up,
      color: Colors.green,
    );

    metrics['total_expenses'] = ReportMetric.kpi(
      title: 'Total Expenses',
      value: '\$${accumulator.totalExpenses.toStringAsFixed(2)}',
      icon: Icons.trending_down,
      color: Colors.red,
    );

    metrics['net_profit'] = ReportMetric.kpi(
      title: 'Net Profit',
      value: '\$${accumulator.netProfit.toStringAsFixed(2)}',
      icon: Icons.account_balance,
      color: accumulator.netProfit >= 0 ? Colors.green : Colors.red,
    );

    // Generate chart data for income vs expenses
    chartData.addAll([
      ChartPoint(
        x: 'Income',
        y: accumulator.totalIncome,
        label: 'Income',
        color: Colors.green,
      ),
      ChartPoint(
        x: 'Expenses',
        y: accumulator.totalExpenses,
        label: 'Expenses',
        color: Colors.red,
      ),
    ]);

    // Generate table data
    for (final transaction in transactions) {
      tableData.add({
        'Date': _formatDate(transaction.date),
        'Type': transaction.type ?? 'N/A',
        'Category': transaction.category ?? 'N/A',
        'Amount': '\$${transaction.amount?.toStringAsFixed(2) ?? '0.00'}',
        'Description': transaction.description ?? 'N/A',
      });
    }

    // Generate insights
    insights.addAll(_generateFinancialInsights(accumulator));

    return ReportData(
      title: 'Financial Report',
      subtitle: 'Overview of farm finances',
      generated: DateTime.now(),
      startDate: filter.startDate,
      endDate: filter.endDate,
      metrics: metrics,
      chartData: chartData,
      tableData: tableData,
      insights: insights,
      type: ReportType.financial,
    );
  }

  // Helper methods
  static Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'sold':
        return Colors.orange;
      case 'deceased':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  static int _calculateAgeInMonths(CattleIsar cattle) {
    if (cattle.dateOfBirth == null) return 0;
    final now = DateTime.now();
    final birth = cattle.dateOfBirth!;
    return ((now.difference(birth).inDays) / 30.44).round();
  }

  static String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day}/${date.month}/${date.year}';
  }

  static List<String> _generateCattleInsights(_CattleAnalyticsAccumulator accumulator) {
    final insights = <String>[];
    
    if (accumulator.activeCattle > 0) {
      final activePercentage = (accumulator.activeCattle / accumulator.totalCattle * 100);
      insights.add('${activePercentage.toStringAsFixed(1)}% of cattle are currently active');
    }
    
    if (accumulator.averageAge > 0) {
      insights.add('Average cattle age is ${accumulator.averageAge.toStringAsFixed(1)} months');
    }
    
    return insights;
  }

  static List<String> _generateMilkInsights(_MilkAnalyticsAccumulator accumulator) {
    final insights = <String>[];
    
    if (accumulator.averageDaily > 0) {
      insights.add('Daily milk production averages ${accumulator.averageDaily.toStringAsFixed(1)}L');
    }
    
    if (accumulator.totalProduction > 0) {
      insights.add('Total production recorded: ${accumulator.totalProduction.toStringAsFixed(1)}L');
    }
    
    return insights;
  }

  static List<String> _generateFinancialInsights(_FinancialAnalyticsAccumulator accumulator) {
    final insights = <String>[];

    if (accumulator.netProfit > 0) {
      insights.add('Farm is profitable with net profit of \$${accumulator.netProfit.toStringAsFixed(2)}');
    } else if (accumulator.netProfit < 0) {
      insights.add('Farm has net loss of \$${accumulator.netProfit.abs().toStringAsFixed(2)}');
    }

    if (accumulator.totalIncome > 0 && accumulator.totalExpenses > 0) {
      final profitMargin = (accumulator.netProfit / accumulator.totalIncome * 100);
      insights.add('Profit margin: ${profitMargin.toStringAsFixed(1)}%');
    }

    return insights;
  }
}

/// Analytics result for reports - immutable data class
class ReportsAnalyticsResult {
  final int totalCattle;
  final int activeCattle;
  final double totalMilkProduction;
  final double averageDailyMilk;
  final int totalHealthRecords;
  final int totalBreedingRecords;
  final double totalIncome;
  final double totalExpenses;
  final double netProfit;
  final int totalEvents;

  const ReportsAnalyticsResult({
    required this.totalCattle,
    required this.activeCattle,
    required this.totalMilkProduction,
    required this.averageDailyMilk,
    required this.totalHealthRecords,
    required this.totalBreedingRecords,
    required this.totalIncome,
    required this.totalExpenses,
    required this.netProfit,
    required this.totalEvents,
  });

  static const ReportsAnalyticsResult empty = ReportsAnalyticsResult(
    totalCattle: 0,
    activeCattle: 0,
    totalMilkProduction: 0.0,
    averageDailyMilk: 0.0,
    totalHealthRecords: 0,
    totalBreedingRecords: 0,
    totalIncome: 0.0,
    totalExpenses: 0.0,
    netProfit: 0.0,
    totalEvents: 0,
  );
}

/// Single-pass accumulator for dashboard analytics - O(n) efficiency
class _DashboardAnalyticsAccumulator {
  int totalCattle = 0;
  int activeCattle = 0;
  double totalMilkProduction = 0.0;
  int milkRecordCount = 0;
  int totalHealthRecords = 0;
  int totalBreedingRecords = 0;
  double totalIncome = 0.0;
  double totalExpenses = 0.0;
  int totalEvents = 0;

  void processCattle(CattleIsar cattle) {
    totalCattle++;
    if (cattle.status == CattleStatus.active) {
      activeCattle++;
    }
  }

  void processMilkRecord(MilkRecordIsar record) {
    milkRecordCount++;
    totalMilkProduction += record.quantity ?? 0.0;
  }

  void processHealthRecord(HealthRecordIsar record) {
    totalHealthRecords++;
  }

  void processBreedingRecord(BreedingRecordIsar record) {
    totalBreedingRecords++;
  }

  void processWeightRecord(WeightRecordIsar record) {
    // Weight records don't contribute to dashboard metrics currently
  }

  void processTransaction(TransactionIsar transaction) {
    final amount = transaction.amount ?? 0.0;
    if (transaction.type?.toLowerCase() == 'income') {
      totalIncome += amount;
    } else if (transaction.type?.toLowerCase() == 'expense') {
      totalExpenses += amount;
    }
  }

  void processEvent(EventIsar event) {
    totalEvents++;
  }

  ReportsAnalyticsResult toResult() {
    final averageDailyMilk = milkRecordCount > 0 ? totalMilkProduction / milkRecordCount : 0.0;
    final netProfit = totalIncome - totalExpenses;

    return ReportsAnalyticsResult(
      totalCattle: totalCattle,
      activeCattle: activeCattle,
      totalMilkProduction: totalMilkProduction,
      averageDailyMilk: averageDailyMilk,
      totalHealthRecords: totalHealthRecords,
      totalBreedingRecords: totalBreedingRecords,
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      netProfit: netProfit,
      totalEvents: totalEvents,
    );
  }
}

/// Single-pass accumulator for cattle analytics
class _CattleAnalyticsAccumulator {
  int totalCattle = 0;
  int activeCattle = 0;
  double totalAge = 0.0;
  Map<String, int> statusDistribution = {};

  double get averageAge => totalCattle > 0 ? totalAge / totalCattle : 0.0;

  void process(CattleIsar cattle) {
    totalCattle++;

    if (cattle.status == CattleStatus.active) {
      activeCattle++;
    }

    // Calculate age and add to total
    final ageInMonths = ReportsAnalyticsService._calculateAgeInMonths(cattle);
    totalAge += ageInMonths;

    // Update status distribution
    final status = cattle.status?.toString().split('.').last ?? 'Unknown';
    statusDistribution[status] = (statusDistribution[status] ?? 0) + 1;
  }
}

/// Single-pass accumulator for milk analytics
class _MilkAnalyticsAccumulator {
  int totalRecords = 0;
  double totalProduction = 0.0;
  Map<DateTime, double> dailyProduction = {};

  double get averageDaily => totalRecords > 0 ? totalProduction / totalRecords : 0.0;

  void process(MilkRecordIsar record) {
    totalRecords++;
    final quantity = record.quantity ?? 0.0;
    totalProduction += quantity;

    // Group by date for daily production tracking
    if (record.date != null) {
      final date = DateTime(record.date!.year, record.date!.month, record.date!.day);
      dailyProduction[date] = (dailyProduction[date] ?? 0.0) + quantity;
    }
  }
}

/// Single-pass accumulator for financial analytics
class _FinancialAnalyticsAccumulator {
  double totalIncome = 0.0;
  double totalExpenses = 0.0;

  double get netProfit => totalIncome - totalExpenses;

  void process(TransactionIsar transaction) {
    final amount = transaction.amount ?? 0.0;
    if (transaction.type?.toLowerCase() == 'income') {
      totalIncome += amount;
    } else if (transaction.type?.toLowerCase() == 'expense') {
      totalExpenses += amount;
    }
  }
}
