import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import '../../../constants/app_icons.dart';

part 'transaction_isar.g.dart';

@collection
class TransactionIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String transactionId = '';

  String title = '';

  double amount = 0;

  String description = '';

  String category = '';

  String categoryType = '';

  String paymentMethod = 'Cash';

  @Index()
  DateTime date = DateTime.now();

  int? iconCodePoint;

  String iconFontFamily = 'MaterialIcons';

  DateTime createdAt = DateTime.now();

  DateTime updatedAt = DateTime.now();

  TransactionIsar();

  /// Generate unique business ID for transaction
  static String generateBusinessId() {
    const uuid = Uuid();
    return 'transaction_${uuid.v4()}';
  }

  // Getters for icon conversion with improved icon handling
  @ignore
  IconData get icon {
    // First check if we have a valid stored icon
    if (iconCodePoint != null) {
      return IconData(iconCodePoint!, fontFamily: iconFontFamily);
    }
    
    // Try to get icon by transaction type if no stored icon
    IconData? typeIcon;
    if (categoryType == 'Income') {
      typeIcon = AppIcons.income;
    } else if (categoryType == 'Expense') {
      typeIcon = AppIcons.expense;
    }
    
    // Try to get icon by category name 
    final categoryIcon = AppIcons.getCategoryIcon(category);
    if (categoryIcon != AppIcons.defaultIcon) {
      return categoryIcon;
    }
    
    // If we have a type-based icon, use it
    if (typeIcon != null) {
      return typeIcon;
    }
    
    // Ultimate fallback
    return AppIcons.defaultIcon;
  }

  set icon(IconData value) {
    iconCodePoint = value.codePoint;
    iconFontFamily = value.fontFamily ?? 'MaterialIcons';
  }

  // Add type getter
  String get type => categoryType;

  factory TransactionIsar.create({
    required String transactionId,
    required String title,
    required double amount,
    required String description,
    required String category,
    required String categoryType,
    required DateTime date,
    required IconData icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final transaction = TransactionIsar()
      ..transactionId = transactionId
      ..title = title
      ..amount = amount
      ..description = description
      ..category = category
      ..categoryType = categoryType
      ..date = date
      ..paymentMethod = 'Cash'
      ..iconCodePoint = icon.codePoint
      ..iconFontFamily = icon.fontFamily ?? 'MaterialIcons'
      ..createdAt = createdAt ?? DateTime.now()
      ..updatedAt = updatedAt ?? DateTime.now();

    return transaction;
  }

  String get formattedDate {
    return DateFormat('dd MMM, yyyy').format(date);
  }

  String get formattedAmount {
    return amount.toString();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': transactionId,
      'title': title,
      'amount': amount,
      'description': description,
      'category': category,
      'categoryType': categoryType,
      'paymentMethod': paymentMethod,
      'date': date.toIso8601String(),
      'icon': iconCodePoint,
      'iconFontFamily': iconFontFamily,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory TransactionIsar.fromMap(Map<String, dynamic> map) {
    return TransactionIsar()
      ..transactionId = map['id'] as String
      ..title = map['title'] as String
      ..amount = map['amount'] as double
      ..description = map['description'] as String
      ..category = map['category'] as String
      ..categoryType = map['categoryType'] as String
      ..paymentMethod = map['paymentMethod'] as String? ?? 'Cash'
      ..date = DateTime.parse(map['date'] as String)
      ..iconCodePoint = map['icon'] as int?
      ..iconFontFamily = map['iconFontFamily'] as String? ?? 'MaterialIcons'
      ..createdAt = DateTime.parse(map['createdAt'] as String)
      ..updatedAt = DateTime.parse(map['updatedAt'] as String);
  }

  factory TransactionIsar.fromJson(Map<String, dynamic> json) =>
      TransactionIsar.fromMap(json);

  TransactionIsar copyWith({
    String? transactionId,
    String? title,
    double? amount,
    String? description,
    String? category,
    String? categoryType,
    String? paymentMethod,
    DateTime? date,
    IconData? icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final transaction = TransactionIsar()
      ..transactionId = transactionId ?? this.transactionId
      ..title = title ?? this.title
      ..amount = amount ?? this.amount
      ..description = description ?? this.description
      ..category = category ?? this.category
      ..categoryType = categoryType ?? this.categoryType
      ..paymentMethod = paymentMethod ?? this.paymentMethod
      ..date = date ?? this.date
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? this.updatedAt;

    if (icon != null) {
      transaction.iconCodePoint = icon.codePoint;
      transaction.iconFontFamily = icon.fontFamily ?? 'MaterialIcons';
    } else {
      transaction.iconCodePoint = iconCodePoint;
      transaction.iconFontFamily = iconFontFamily;
    }

    return transaction;
  }
}
