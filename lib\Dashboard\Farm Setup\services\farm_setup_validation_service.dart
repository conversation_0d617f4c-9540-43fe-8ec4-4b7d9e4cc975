import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';

import '../models/breed_category_isar.dart';
import '../models/animal_type_isar.dart';
import '../models/farm_isar.dart';
import '../models/farm_user_isar.dart';
import '../models/user_role_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Farm Setup Validation Service
/// 
/// Handles all validation logic for farm setup operations following established patterns.
/// Pure business logic service with no data access - uses repository for data queries.
/// All validation methods are static and purely functional.
class FarmSetupValidationService {
  static final Logger _logger = Logger('FarmSetupValidationService');

  // Repository access through dependency injection
  static IsarService get _isarService => GetIt.instance<IsarService>();
  static Isar get _isar => _isarService.isar;

  /// Validate farm data
  static Future<void> validateFarm(FarmIsar farm) async {
    if (farm.name == null || farm.name!.isEmpty) {
      throw ValidationException('Farm name is required');
    }

    if (farm.name!.length < 2) {
      throw ValidationException('Farm name must be at least 2 characters long');
    }

    if (farm.name!.length > 100) {
      throw ValidationException('Farm name cannot exceed 100 characters');
    }

    if (farm.ownerName == null || farm.ownerName!.isEmpty) {
      throw ValidationException('Farm owner name is required');
    }

    if (farm.ownerContact == null || farm.ownerContact!.isEmpty) {
      throw ValidationException('Farm owner contact is required');
    }

    // Validate email format if provided
    if (farm.ownerEmail != null && farm.ownerEmail!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(farm.ownerEmail!)) {
        throw ValidationException('Invalid email format');
      }
    }

    // Validate capacity
    if (farm.capacity != null && farm.capacity! < 0) {
      throw ValidationException('Farm capacity cannot be negative');
    }

    if (farm.cattleCount != null && farm.cattleCount! < 0) {
      throw ValidationException('Cattle count cannot be negative');
    }

    if (farm.capacity != null && farm.cattleCount != null && farm.cattleCount! > farm.capacity!) {
      throw ValidationException('Cattle count cannot exceed farm capacity');
    }

    _logger.info('Farm validation passed for: ${farm.name}');
  }

  /// Validate breed category
  static Future<void> validateBreedCategory(BreedCategoryIsar category) async {
    if (category.name == null || category.name!.isEmpty) {
      throw ValidationException('Breed category name is required');
    }

    if (category.name!.length < 2) {
      throw ValidationException('Breed category name must be at least 2 characters long');
    }

    if (category.name!.length > 50) {
      throw ValidationException('Breed category name cannot exceed 50 characters');
    }

    // Check for duplicate names
    final existing = await _isar.breedCategoryIsars
        .filter()
        .nameEqualTo(category.name!)
        .and()
        .not()
        .businessIdEqualTo(category.businessId ?? '')
        .findFirst();

    if (existing != null) {
      throw ValidationException('Breed category with this name already exists');
    }

    _logger.info('Breed category validation passed for: ${category.name}');
  }

  /// Validate animal type
  static Future<void> validateAnimalType(AnimalTypeIsar animalType) async {
    if (animalType.name == null || animalType.name!.isEmpty) {
      throw ValidationException('Animal type name is required');
    }

    if (animalType.name!.length < 2) {
      throw ValidationException('Animal type name must be at least 2 characters long');
    }

    if (animalType.name!.length > 50) {
      throw ValidationException('Animal type name cannot exceed 50 characters');
    }

    // Check for duplicate names
    final existing = await _isar.animalTypeIsars
        .filter()
        .nameEqualTo(animalType.name!)
        .and()
        .not()
        .businessIdEqualTo(animalType.businessId ?? '')
        .findFirst();

    if (existing != null) {
      throw ValidationException('Animal type with this name already exists');
    }

    _logger.info('Animal type validation passed for: ${animalType.name}');
  }

  /// Validate farm user
  static Future<void> validateFarmUser(FarmUserIsar user) async {
    if (user.name == null || user.name!.isEmpty) {
      throw ValidationException('Username is required');
    }

    if (user.name!.length < 3) {
      throw ValidationException('Username must be at least 3 characters long');
    }

    if (user.name!.length > 50) {
      throw ValidationException('Username cannot exceed 50 characters');
    }

    if (user.email == null || user.email!.isEmpty) {
      throw ValidationException('Email is required');
    }

    // Validate email format
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(user.email!)) {
      throw ValidationException('Invalid email format');
    }

    // Check for duplicate username
    final existingUsername = await _isar.farmUserIsars
        .filter()
        .nameEqualTo(user.name!)
        .and()
        .not()
        .businessIdEqualTo(user.businessId ?? '')
        .findFirst();

    if (existingUsername != null) {
      throw ValidationException('Username already exists');
    }

    // Check for duplicate email
    final existingEmail = await _isar.farmUserIsars
        .filter()
        .emailEqualTo(user.email!)
        .and()
        .not()
        .businessIdEqualTo(user.businessId ?? '')
        .findFirst();

    if (existingEmail != null) {
      throw ValidationException('Email already exists');
    }

    _logger.info('Farm user validation passed for: ${user.name}');
  }

  /// Validate user role
  static void validateUserRole(UserRoleIsar role) {
    if (role.name == null || role.name!.isEmpty) {
      throw ValidationException('Role name is required');
    }

    if (role.name!.length < 2) {
      throw ValidationException('Role name must be at least 2 characters long');
    }

    if (role.name!.length > 50) {
      throw ValidationException('Role name cannot exceed 50 characters');
    }

    if (role.permissions.isEmpty) {
      throw ValidationException('Role must have at least one permission');
    }

    _logger.info('User role validation passed for: ${role.name}');
  }

  /// Validate farm configuration
  static void validateFarmConfig(Map<String, dynamic> config) {
    if (config['farmName'] == null || config['farmName'].toString().isEmpty) {
      throw ValidationException('Farm name is required');
    }

    if (config['location'] == null || config['location'].toString().isEmpty) {
      throw ValidationException('Farm location is required');
    }

    final farmName = config['farmName'].toString();
    if (farmName.length < 2) {
      throw ValidationException('Farm name must be at least 2 characters long');
    }

    if (farmName.length > 100) {
      throw ValidationException('Farm name cannot exceed 100 characters');
    }

    _logger.info('Farm configuration validation passed');
  }
}
