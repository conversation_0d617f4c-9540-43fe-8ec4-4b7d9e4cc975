import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../controllers/health_details_controller.dart';

import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_bar.dart';
import '../../../utils/message_utils.dart';

import '../dialogs/health_record_form_dialog.dart';
import '../dialogs/vaccination_form_dialog.dart';
import 'health_details_analytics_tab.dart';
import 'health_details_health_tab.dart';
import 'health_details_vaccine_tab.dart';

class HealthDetailsScreen extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar)? onCattleUpdated;

  const HealthDetailsScreen({
    Key? key,
    required this.cattle,
    this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<HealthDetailsScreen> createState() => _HealthDetailsScreenState();
}

class _HealthDetailsScreenState extends State<HealthDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this); // Analytics, Health, Vaccines
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddHealthDialog(HealthDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => HealthRecordFormDialog(
        cattle: [controller.cattle!],
        onSave: (record) async {
          final success = await controller.addHealthRecord(record);
          if (!mounted) return;
          if (success) {
            HealthMessageUtils.showSuccess(context, HealthMessageUtils.healthRecordCreated());
          }
        },
      ),
    );
  }

  void _showAddVaccineDialog(HealthDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => VaccinationFormDialog(
        cattle: [controller.cattle!],
        cattleId: controller.cattle!.tagId ?? '', // Handle null tagId
        onSave: (record) async {
          final success = await controller.addVaccinationRecord(record);
          if (!mounted) return false;
          if (success) {
            HealthMessageUtils.showSuccess(context, HealthMessageUtils.vaccinationRecorded());
          }
          return success;
        },
      ),
    );
  }

  void _getCurrentTabAction(HealthDetailsController controller) {
    switch (_tabController.index) {
      case 0:
        // Analytics tab - no FAB action
        return;
      case 1:
        _showAddHealthDialog(controller);
        return;
      case 2:
        _showAddVaccineDialog(controller);
        return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<HealthDetailsController>(
      create: (_) => HealthDetailsController()..initialize(widget.cattle), // Create directly, not from GetIt
      child: Consumer<HealthDetailsController>(
        builder: (context, controller, child) {
          // Handle loading state
          if (controller.isLoading) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: '${widget.cattle.name ?? 'Health'} (${widget.cattle.tagId ?? 'No Tag'})',
              ),
              body: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          // Handle error state
          if (controller.error != null) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: '${widget.cattle.name ?? 'Health'} (${widget.cattle.tagId ?? 'No Tag'})',
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading health data',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      controller.error!,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () => controller.refresh(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return Builder(
            builder: (context) {
              // Initialize tab manager here where Provider context is available
              _tabManager ??= UniversalTabManager.threeTabs(
                controller: _tabController,
                tabViews: const [
                  HealthDetailsAnalyticsTab(),
                  HealthDetailsHealthTab(),
                  HealthDetailsVaccineTab(),
                ],
                labels: const ['Analytics', 'Health', 'Vaccines'],
                icons: const [Icons.analytics, Icons.medical_services, Icons.vaccines],
                showFABs: const [false, true, true], // No FAB on analytics, FABs on record tabs
              );

              return Scaffold(
                appBar: AppBarConfig.withBack(
                  context: context,
                  title: '${controller.cattle?.name ?? 'Health'} (${controller.cattle?.tagId ?? 'No Tag'})',
                ),
                body: _tabManager!,
                floatingActionButton: AnimatedBuilder(
                  animation: _tabController,
                  builder: (context, child) {
                    // Only rebuild FAB when tab changes, not the entire screen
                    return _tabManager?.getCurrentFAB(
                      onPressed: () => _getCurrentTabAction(controller), // Pass controller
                      tooltip: 'Add Record',
                      backgroundColor: AppColors.healthHeader,
                    ) ?? const SizedBox.shrink(); // Handle null case
                  },
                ), // Optimized FAB management with AnimatedBuilder
              );
            },
          );
        },
      ),
    );
  }
}


