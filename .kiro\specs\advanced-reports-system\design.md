# Design Document

## Overview

This design document outlines the architecture and implementation approach for modernizing the Cattle Manager App's Reports module. The focus is on creating a clean, efficient, and user-friendly reporting system with modern UI, smooth user experience, and practical PDF/Excel export capabilities.

## Architecture

### Minimal File Structure

```
lib/Dashboard/Reports/
├── screens/
│   └── reports_screen.dart          (Single main screen)
├── widgets/
│   ├── dashboard_cards.dart         (Metric cards)
│   ├── report_charts.dart           (All chart types)
│   └── export_dialog.dart           (Export options)
├── services/
│   ├── reports_service.dart         (Data + Export logic)
│   └── chart_service.dart           (Chart generation)
└── models/
    └── report_models.dart           (All data models)
```

### Design Principles

1. **Minimal Files**: Maximum 8 files total for entire reports module
2. **Compact Code**: Each file under 300 lines, focused single responsibility
3. **Modern Flutter**: Latest widgets, null safety, efficient state management
4. **Zero Dependencies**: Use only existing app dependencies
5. **Performance First**: Lazy loading, efficient rendering, minimal rebuilds

## Components and Interfaces

### 1. Single Reports Screen (reports_screen.dart)

**Purpose**: All-in-one screen with dashboard and report generation

**Compact Design**:
- Tab-based navigation (Dashboard, Reports, Export)
- Integrated filtering in app bar
- Floating action button for quick export
- Responsive layout with LayoutBuilder

**Key Features**:
```dart
class ReportsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) => DefaultTabController(
    length: 3,
    child: Scaffold(
      appBar: AppBar(
        title: Text('Reports'),
        bottom: TabBar(tabs: [
          Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
          Tab(icon: Icon(Icons.bar_chart), text: 'Reports'),
          Tab(icon: Icon(Icons.file_download), text: 'Export'),
        ]),
        actions: [FilterButton(), DateRangeButton()],
      ),
      body: TabBarView(children: [
        DashboardTab(),
        ReportsTab(),
        ExportTab(),
      ]),
      floatingActionButton: ExportFAB(),
    ),
  );
}
```

### 2. Dashboard Cards (dashboard_cards.dart)

**Purpose**: Compact metric display widgets

**Design**: Single file with all card types
```dart
class MetricCard extends StatelessWidget {
  final String title, value;
  final IconData icon;
  final Color color;
  
  @override
  Widget build(BuildContext context) => Card(
    child: ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(value, style: TextStyle(
        fontSize: 24, fontWeight: FontWeight.bold,
      )),
    ),
  );
}
```

### 3. Report Charts (report_charts.dart)

**Purpose**: All chart types in one compact file

**Chart Types**: Line, Bar, Pie charts with minimal code
```dart
class ReportChart extends StatelessWidget {
  final ChartType type;
  final List<ChartData> data;
  final String title;
  
  @override
  Widget build(BuildContext context) => Card(
    child: Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Text(title),
          SizedBox(height: 200, child: _buildChart()),
        ],
      ),
    ),
  );
  
  Widget _buildChart() => switch (type) {
    ChartType.line => LineChart(_lineData()),
    ChartType.bar => BarChart(_barData()),
    ChartType.pie => PieChart(_pieData()),
  };
}
```

### 4. Export Dialog (export_dialog.dart)

**Purpose**: Export options with download and share functionality

**Features**: PDF/Excel export, download to device, share via apps
```dart
class ExportDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) => AlertDialog(
    title: Text('Export & Share Report'),
    content: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Export Format Selection
        ListTile(
          leading: Icon(Icons.picture_as_pdf),
          title: Text('Export as PDF'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(Icons.download),
                onTap: () => _downloadPDF(context),
                tooltip: 'Download PDF',
              ),
              IconButton(
                icon: Icon(Icons.share),
                onTap: () => _sharePDF(context),
                tooltip: 'Share PDF',
              ),
            ],
          ),
        ),
        ListTile(
          leading: Icon(Icons.table_chart),
          title: Text('Export as Excel'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(Icons.download),
                onTap: () => _downloadExcel(context),
                tooltip: 'Download Excel',
              ),
              IconButton(
                icon: Icon(Icons.share),
                onTap: () => _shareExcel(context),
                tooltip: 'Share Excel',
              ),
            ],
          ),
        ),
        Divider(),
        // Quick Actions
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton.icon(
              icon: Icon(Icons.email),
              label: Text('Email'),
              onPressed: () => _emailReport(context),
            ),
            ElevatedButton.icon(
              icon: Icon(Icons.print),
              label: Text('Print'),
              onPressed: () => _printReport(context),
            ),
          ],
        ),
      ],
    ),
  );
}
```

## Services Implementation

### 5. Reports Service (reports_service.dart)

**Modern PDF Layout Features**:
- Professional header with farm logo and details
- Clean typography with proper hierarchy
- Well-organized sections with clear dividers
- Modern table designs with alternating row colors
- Charts integrated seamlessly with data
- Consistent spacing and margins
- Page numbers and footers

**PDF Structure**:
```
┌─────────────────────────────────────┐
│ FARM LOGO    Farm Name              │
│              Report Title           │
│              Generated: Date        │
├─────────────────────────────────────┤
│ EXECUTIVE SUMMARY                   │
│ • Key Metric 1: Value              │
│ • Key Metric 2: Value              │
│ • Key Metric 3: Value              │
├─────────────────────────────────────┤
│ CHARTS & VISUALIZATIONS             │
│ [Clean charts with titles]         │
├─────────────────────────────────────┤
│ DETAILED DATA TABLES                │
│ [Well-formatted tables]            │
├─────────────────────────────────────┤
│ INSIGHTS & RECOMMENDATIONS          │
│ [Key findings and suggestions]      │
└─────────────────────────────────────┘
```

**Purpose**: Data fetching, processing, and export generation

**Key Features**: Download to device storage, share via system apps
```dart
class ReportsService {
  // Data fetching
  static Future<ReportData> getCattleReport(FilterState filter) async {
    // Fetch from existing controllers
    final cattle = await CattleController().getAllCattle();
    return ReportData(
      title: 'Cattle Report',
      metrics: _calculateMetrics(cattle),
      chartData: _generateChartData(cattle),
      tableData: _generateTableData(cattle),
    );
  }
  
  // Download functionality with open option
  static Future<void> downloadPDF(ReportData report) async {
    final file = await _generatePDF(report);
    await _saveToDownloads(file, '${report.title}.pdf');
    _showDownloadSuccessWithOpen(file, 'PDF saved to Downloads');
  }
  
  static Future<void> downloadExcel(ReportData report) async {
    final file = await _generateExcel(report);
    await _saveToDownloads(file, '${report.title}.xlsx');
    _showDownloadSuccessWithOpen(file, 'Excel saved to Downloads');
  }
  
  // Show success with option to open file
  static void _showDownloadSuccessWithOpen(File file, String message) {
    Get.snackbar(
      'Download Complete',
      message,
      duration: Duration(seconds: 5),
      mainButton: TextButton(
        onPressed: () => OpenFile.open(file.path),
        child: Text('OPEN', style: TextStyle(color: Colors.white)),
      ),
    );
  }
  
  // Share functionality
  static Future<void> sharePDF(ReportData report) async {
    final file = await _generatePDF(report);
    await Share.shareXFiles([XFile(file.path)], 
      text: 'Farm Report - ${report.title}');
  }
  
  static Future<void> shareExcel(ReportData report) async {
    final file = await _generateExcel(report);
    await Share.shareXFiles([XFile(file.path)], 
      text: 'Farm Report - ${report.title}');
  }
  
  // Email functionality
  static Future<void> emailReport(ReportData report, ExportType type) async {
    final file = type == ExportType.pdf 
      ? await _generatePDF(report)
      : await _generateExcel(report);
    
    await Share.shareXFiles([XFile(file.path)], 
      subject: 'Farm Report - ${report.title}',
      text: 'Please find attached farm report.');
  }
  
  // Modern PDF generation with professional layout
  static Future<File> _generatePDF(ReportData report) async {
    final pdf = pw.Document();
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: pw.EdgeInsets.all(32),
        build: (context) => [
          _buildModernHeader(report),
          pw.SizedBox(height: 20),
          _buildExecutiveSummary(report),
          pw.SizedBox(height: 20),
          _buildChartsSection(report),
          pw.SizedBox(height: 20),
          _buildDataTablesSection(report),
          pw.SizedBox(height: 20),
          _buildInsightsSection(report),
        ],
        footer: (context) => _buildModernFooter(context),
      ),
    );
    
    return _savePDFFile(pdf, report.title);
  }
  
  // Modern header with farm branding
  static pw.Widget _buildModernHeader(ReportData report) {
    return pw.Container(
      padding: pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('My Farm Name', 
                style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
              pw.Text(report.title, 
                style: pw.TextStyle(fontSize: 18, color: PdfColors.grey700)),
              pw.Text('Generated: ${_formatDate(report.generated)}',
                style: pw.TextStyle(fontSize: 12, color: PdfColors.grey600)),
            ],
          ),
          // Farm logo placeholder
          pw.Container(
            width: 60, height: 60,
            decoration: pw.BoxDecoration(
              color: PdfColors.blue,
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Center(
              child: pw.Text('LOGO', style: pw.TextStyle(color: PdfColors.white)),
            ),
          ),
        ],
      ),
    );
  }
  
  // Well-organized data tables
  static pw.Widget _buildDataTablesSection(ReportData report) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('Detailed Data', 
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          children: [
            // Header row with background
            pw.TableRow(
              decoration: pw.BoxDecoration(color: PdfColors.grey200),
              children: report.tableData.first.keys.map((header) =>
                pw.Padding(
                  padding: pw.EdgeInsets.all(8),
                  child: pw.Text(header, 
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
              ).toList(),
            ),
            // Data rows with alternating colors
            ...report.tableData.asMap().entries.map((entry) {
              final index = entry.key;
              final row = entry.value;
              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: index % 2 == 0 ? PdfColors.white : PdfColors.grey50,
                ),
                children: row.values.map((cell) =>
                  pw.Padding(
                    padding: pw.EdgeInsets.all(8),
                    child: pw.Text(cell),
                  ),
                ).toList(),
              );
            }),
          ],
        ),
      ],
    );
  }
}
```

### 6. Chart Service (chart_service.dart)

**Purpose**: Generate chart data for reports and exports

```dart
class ChartService {
  static List<ChartPoint> generateTrendData(List<dynamic> records) {
    // Process records into chart points
    return records.map((r) => ChartPoint(
      label: _formatDate(r.date),
      value: r.value.toDouble(),
      date: r.date,
    )).toList();
  }
  
  static Widget buildChart(ChartType type, List<ChartPoint> data) {
    return switch (type) {
      ChartType.line => LineChart(_createLineData(data)),
      ChartType.bar => BarChart(_createBarData(data)),
      ChartType.pie => PieChart(_createPieData(data)),
    };
  }
}
```

## Data Models (report_models.dart)

### Compact Models - Single File

```dart
// All models in one file for simplicity
class ReportData {
  final String title;
  final DateTime generated;
  final Map<String, dynamic> metrics;
  final List<ChartPoint> chartData;
  final List<Map<String, String>> tableData;
}

class ChartPoint {
  final String label;
  final double value;
  final DateTime? date;
}

enum ChartType { line, bar, pie }
enum ExportType { pdf, excel }
enum ShareAction { download, share, email, print }

class FilterState {
  final DateTime? startDate, endDate;
  final List<String>? cattleIds;
  
  bool get hasFilters => startDate != null || cattleIds?.isNotEmpty == true;
}

// Extension for quick date ranges
extension DateRanges on DateTime {
  static DateTime get weekAgo => DateTime.now().subtract(Duration(days: 7));
  static DateTime get monthAgo => DateTime.now().subtract(Duration(days: 30));
  static DateTime get yearAgo => DateTime.now().subtract(Duration(days: 365));
}
```

## Error Handling

### Graceful Error Management

```dart
class ReportErrorHandler {
  static void handleReportError(ReportError error, BuildContext context) {
    switch (error.type) {
      case ReportErrorType.dataNotFound:
        _showDataNotFoundDialog(context);
        break;
      case ReportErrorType.exportFailed:
        _showExportFailedDialog(context, error.message);
        break;
      case ReportErrorType.networkError:
        _showNetworkErrorDialog(context);
        break;
      default:
        _showGenericErrorDialog(context, error.message);
    }
  }
}
```

### User-Friendly Error Messages

- Clear, non-technical language
- Suggested actions for resolution
- Retry mechanisms where appropriate
- Fallback options when possible

## Testing Strategy

### Unit Testing
- Test report generation logic
- Validate export functionality
- Test filter operations
- Verify data calculations

### Widget Testing
- Test UI components
- Verify responsive behavior
- Test user interactions
- Validate accessibility

### Integration Testing
- Test end-to-end report generation
- Verify export file quality
- Test cross-module data integration
- Performance testing with large datasets

### User Acceptance Testing
- Test with real farm data
- Validate mobile experience
- Test print functionality
- Verify export quality

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Load report data only when needed
2. **Caching**: Cache frequently accessed reports
3. **Pagination**: Handle large datasets efficiently
4. **Background Processing**: Generate exports in background
5. **Memory Management**: Proper disposal of resources

### Performance Targets

- Dashboard load time: < 1 second
- Report generation: < 3 seconds
- Export generation: < 10 seconds
- Mobile responsiveness: 60 FPS
- Memory usage: < 100MB for typical operations

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1)
- Set up new repository service
- Create basic report models
- Implement data fetching logic
- Set up export service foundation

### Phase 2: Dashboard & Basic Reports (Week 2)
- Build modern dashboard
- Implement basic report types
- Create clean chart components
- Add responsive layouts

### Phase 3: Export & Filtering (Week 3)
- Complete PDF export functionality
- Implement Excel export
- Build smart filtering system
- Add mobile optimizations

### Phase 4: Polish & Testing (Week 4)
- Refine UI/UX details
- Comprehensive testing
- Performance optimization
- Documentation and deployment

This design provides a solid foundation for creating a modern, efficient reporting system that meets your requirements for simplicity, good user experience, and practical export capabilities.