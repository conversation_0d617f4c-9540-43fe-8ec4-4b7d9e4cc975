buildscript {
    ext.kotlin_version = '1.9.10'
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.0'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }

    // Configure namespace for all Android library plugins
    plugins.withId('com.android.library') {
        android {
            if (namespace == null) {
                if (project.name == 'isar_flutter_libs') {
                    namespace = "dev.isar.isar_flutter_libs"
                } else {
                    namespace = "dev.isar.${project.name.replace('-', '_').replace('_', '')}"
                }
            }
            // Force namespace configuration for problematic packages
            if (project.name == 'isar_flutter_libs') {
                namespace = "dev.isar.isar_flutter_libs"
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
