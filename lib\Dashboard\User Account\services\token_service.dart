import 'dart:math';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:logging/logging.dart';

/// Service for generating and validating JWT tokens for email verification and password reset
class TokenService {
  static final TokenService _instance = TokenService._internal();
  factory TokenService() => _instance;
  TokenService._internal();

  final Logger _logger = Logger('TokenService');
  
  // Secret key for JWT signing - In production, this should be stored securely
  static const String _jwtSecret = 'your-super-secret-jwt-key-change-this-in-production';
  
  // Token expiration times
  static const Duration _emailVerificationExpiry = Duration(hours: 24);
  static const Duration _passwordResetExpiry = Duration(hours: 1);

  /// Generate email verification token
  String generateEmailVerificationToken({
    required String email,
    required String userId,
  }) {
    try {
      final jwt = JWT({
        'type': 'email_verification',
        'email': email,
        'userId': userId,
        'iat': DateTime.now().millisecondsSinceEpoch,
        'exp': DateTime.now().add(_emailVerificationExpiry).millisecondsSinceEpoch,
      });

      final token = jwt.sign(SecretKey(_jwtSecret));
      _logger.info('Generated email verification token for: $email');
      return token;
    } catch (e) {
      _logger.severe('Failed to generate email verification token: $e');
      return _generateFallbackToken();
    }
  }

  /// Generate password reset token
  String generatePasswordResetToken({
    required String email,
    required String userId,
  }) {
    try {
      final jwt = JWT({
        'type': 'password_reset',
        'email': email,
        'userId': userId,
        'iat': DateTime.now().millisecondsSinceEpoch,
        'exp': DateTime.now().add(_passwordResetExpiry).millisecondsSinceEpoch,
      });

      final token = jwt.sign(SecretKey(_jwtSecret));
      _logger.info('Generated password reset token for: $email');
      return token;
    } catch (e) {
      _logger.severe('Failed to generate password reset token: $e');
      return _generateFallbackToken();
    }
  }

  /// Validate email verification token
  TokenValidationResult validateEmailVerificationToken(String token) {
    return _validateToken(token, 'email_verification');
  }

  /// Validate password reset token
  TokenValidationResult validatePasswordResetToken(String token) {
    return _validateToken(token, 'password_reset');
  }

  /// Internal method to validate tokens
  TokenValidationResult _validateToken(String token, String expectedType) {
    try {
      final jwt = JWT.verify(token, SecretKey(_jwtSecret));
      final payload = jwt.payload as Map<String, dynamic>;

      // Check token type
      if (payload['type'] != expectedType) {
        _logger.warning('Invalid token type: ${payload['type']}, expected: $expectedType');
        return TokenValidationResult(
          isValid: false,
          error: 'Invalid token type',
        );
      }

      // Check expiration
      final exp = payload['exp'] as int;
      if (DateTime.now().millisecondsSinceEpoch > exp) {
        _logger.warning('Token expired for email: ${payload['email']}');
        return TokenValidationResult(
          isValid: false,
          error: 'Token has expired',
        );
      }

      _logger.info('Token validated successfully for email: ${payload['email']}');
      return TokenValidationResult(
        isValid: true,
        email: payload['email'] as String,
        userId: payload['userId'] as String,
        issuedAt: DateTime.fromMillisecondsSinceEpoch(payload['iat'] as int),
        expiresAt: DateTime.fromMillisecondsSinceEpoch(exp),
      );
    } catch (e) {
      _logger.severe('Token validation failed: $e');
      return TokenValidationResult(
        isValid: false,
        error: 'Invalid token format',
      );
    }
  }

  /// Generate a fallback token when JWT fails (simple random string)
  String _generateFallbackToken() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(32, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Validate fallback token (basic validation for development)
  bool validateFallbackToken(String token) {
    // Basic validation - in production, you'd store these tokens in a database
    return token.length == 32 && RegExp(r'^[a-zA-Z0-9]+$').hasMatch(token);
  }

  /// Extract email from token without full validation (for logging purposes)
  String? extractEmailFromToken(String token) {
    try {
      final jwt = JWT.verify(token, SecretKey(_jwtSecret));
      final payload = jwt.payload as Map<String, dynamic>;
      return payload['email'] as String?;
    } catch (e) {
      return null;
    }
  }

  /// Check if token is expired without full validation
  bool isTokenExpired(String token) {
    try {
      final jwt = JWT.verify(token, SecretKey(_jwtSecret));
      final payload = jwt.payload as Map<String, dynamic>;
      final exp = payload['exp'] as int;
      return DateTime.now().millisecondsSinceEpoch > exp;
    } catch (e) {
      return true; // Consider invalid tokens as expired
    }
  }
}

/// Result class for token validation
class TokenValidationResult {
  final bool isValid;
  final String? email;
  final String? userId;
  final DateTime? issuedAt;
  final DateTime? expiresAt;
  final String? error;

  TokenValidationResult({
    required this.isValid,
    this.email,
    this.userId,
    this.issuedAt,
    this.expiresAt,
    this.error,
  });

  @override
  String toString() {
    if (isValid) {
      return 'TokenValidationResult(valid: true, email: $email, userId: $userId, expiresAt: $expiresAt)';
    } else {
      return 'TokenValidationResult(valid: false, error: $error)';
    }
  }
}
