import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/health_record_isar.dart';
import '../controllers/health_details_controller.dart';

import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../widgets/universal_info_card.dart';

/// Data class for analytics cards
class AnalyticsCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String insight;

  AnalyticsCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.insight,
  });
}


class HealthDetailsAnalyticsTab extends StatefulWidget {
  const HealthDetailsAnalyticsTab({Key? key}) : super(key: key);

  @override
  State<HealthDetailsAnalyticsTab> createState() => _HealthDetailsAnalyticsTabState();
}

class _HealthDetailsAnalyticsTabState extends State<HealthDetailsAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Consumer<HealthDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final healthRecords = controller.healthRecords;
        final vaccinationRecords = controller.vaccinationRecords;

        if (cattle == null) {
          return const Center(child: Text('No cattle data available'));
        }

        // Check if we have any health data to analyze
        if (healthRecords.isEmpty && vaccinationRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Health Analytics',
            message: 'Add health records or vaccinations for ${cattle.name ?? 'this cattle'} to view detailed analytics.',
            tabColor: AppColors.healthHeader,
            tabIndex: 0, // Analytics tab
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Health Performance Metrics
              _buildHealthPerformance(controller),
              const SizedBox(height: 24),

              // Vaccination Analytics
              _buildVaccinationAnalytics(controller),
              const SizedBox(height: 24),

              // Health Trends
              _buildHealthTrends(controller),
            ],
          ),
        );
      },
    );
  }

  /// Build health performance metrics section
  Widget _buildHealthPerformance(HealthDetailsController controller) {
    final cardData = _buildHealthPerformanceCards(controller);
    return _buildAnalyticsSection(
      title: 'Health Performance',
      subtitle: 'Key health metrics and indicators',
      icon: Icons.health_and_safety,
      headerColor: AppColors.healthHeader,
      cardData: cardData,
    );
  }

  /// Build vaccination analytics section
  Widget _buildVaccinationAnalytics(HealthDetailsController controller) {
    final cardData = _buildVaccinationAnalyticsCards(controller);
    return _buildAnalyticsSection(
      title: 'Vaccination Analytics',
      subtitle: 'Vaccination history and compliance',
      icon: Icons.vaccines,
      headerColor: AppColors.healthHeader,
      cardData: cardData,
    );
  }

  /// Build health trends section
  Widget _buildHealthTrends(HealthDetailsController controller) {
    final cardData = _buildHealthTrendsCards(controller);
    return _buildAnalyticsSection(
      title: 'Health Trends',
      subtitle: 'Health patterns and recovery metrics',
      icon: Icons.trending_up,
      headerColor: AppColors.healthHeader,
      cardData: cardData,
    );
  }

  /// Build health performance metric cards
  List<AnalyticsCardData> _buildHealthPerformanceCards(HealthDetailsController controller) {
    final healthRecords = controller.healthRecords;
    final totalRecords = healthRecords.length;
    final activeRecords = healthRecords.where((r) => r.status?.toLowerCase() == 'active').length;
    final resolvedRecords = healthRecords.where((r) => r.status?.toLowerCase() == 'resolved').length;
    final totalCost = healthRecords.where((r) => r.cost != null).fold(0.0, (sum, r) => sum + r.cost!);

    return [
      AnalyticsCardData(
        title: 'Total Records',
        value: totalRecords.toString(),
        subtitle: 'All health records',
        icon: Icons.medical_services,
        color: AppColors.healthKpiColors[0],
        insight: 'Total health records tracked',
      ),
      AnalyticsCardData(
        title: 'Active Issues',
        value: activeRecords.toString(),
        subtitle: 'Ongoing health issues',
        icon: Icons.warning,
        color: activeRecords > 0 ? Colors.orange : Colors.green,
        insight: activeRecords > 0 ? 'Health issues requiring attention' : 'No active health issues',
      ),
      AnalyticsCardData(
        title: 'Resolved',
        value: resolvedRecords.toString(),
        subtitle: 'Successfully treated',
        icon: Icons.check_circle,
        color: Colors.green,
        insight: 'Successfully resolved health issues',
      ),
      AnalyticsCardData(
        title: 'Total Cost',
        value: totalCost > 0 ? '\$${totalCost.toStringAsFixed(2)}' : 'N/A',
        subtitle: 'Health expenses',
        icon: Icons.receipt_long,
        color: AppColors.healthKpiColors[3],
        insight: 'Total health-related expenses',
      ),
    ];
  }

  /// Build vaccination analytics metric cards
  List<AnalyticsCardData> _buildVaccinationAnalyticsCards(HealthDetailsController controller) {
    final vaccinationRecords = controller.vaccinationRecords;
    final totalVaccinations = vaccinationRecords.length;
    final totalVaccineCost = vaccinationRecords.where((r) => r.cost != null).fold(0.0, (sum, r) => sum + r.cost!);
    final uniqueVaccines = vaccinationRecords.map((r) => r.vaccineName).toSet().length;

    return [
      AnalyticsCardData(
        title: 'Total Vaccinations',
        value: totalVaccinations.toString(),
        subtitle: 'All vaccination records',
        icon: Icons.vaccines,
        color: AppColors.healthKpiColors[0],
        insight: 'Total vaccinations administered',
      ),
      AnalyticsCardData(
        title: 'Unique Vaccines',
        value: uniqueVaccines.toString(),
        subtitle: 'Different vaccine types',
        icon: Icons.science,
        color: AppColors.healthKpiColors[1],
        insight: 'Variety of vaccines administered',
      ),
      AnalyticsCardData(
        title: 'Vaccine Cost',
        value: totalVaccineCost > 0 ? '\$${totalVaccineCost.toStringAsFixed(2)}' : 'N/A',
        subtitle: 'Vaccination expenses',
        icon: Icons.receipt_long,
        color: AppColors.healthKpiColors[2],
        insight: 'Total vaccination expenses',
      ),
    ];
  }

  /// Build health trends metric cards
  List<AnalyticsCardData> _buildHealthTrendsCards(HealthDetailsController controller) {
    final healthRecords = controller.healthRecords;
    final recentRecords = healthRecords.where((r) =>
      r.date != null && r.date!.isAfter(DateTime.now().subtract(const Duration(days: 90)))
    ).length;

    return [
      AnalyticsCardData(
        title: 'Recent Issues',
        value: recentRecords.toString(),
        subtitle: 'Last 90 days',
        icon: Icons.schedule,
        color: recentRecords > 3 ? Colors.orange : Colors.green,
        insight: recentRecords > 3 ? 'Higher than normal health issues' : 'Normal health pattern',
      ),
      AnalyticsCardData(
        title: 'Health Score',
        value: _calculateHealthScore(healthRecords),
        subtitle: 'Overall health rating',
        icon: Icons.star,
        color: _getHealthScoreColor(_calculateHealthScore(healthRecords)),
        insight: 'Based on recent health history',
      ),
    ];
  }

  /// Calculate health score based on recent health history
  String _calculateHealthScore(List<HealthRecordIsar> healthRecords) {
    if (healthRecords.isEmpty) return 'N/A';

    final recentRecords = healthRecords.where((r) =>
      r.date != null && r.date!.isAfter(DateTime.now().subtract(const Duration(days: 180)))
    ).toList();

    if (recentRecords.isEmpty) return 'Excellent';

    final activeIssues = recentRecords.where((r) => r.status?.toLowerCase() == 'active').length;
    final totalRecent = recentRecords.length;

    if (activeIssues == 0 && totalRecent <= 2) return 'Excellent';
    if (activeIssues <= 1 && totalRecent <= 4) return 'Good';
    if (activeIssues <= 2 && totalRecent <= 6) return 'Fair';
    return 'Poor';
  }

  /// Get color for health score
  Color _getHealthScoreColor(String score) {
    switch (score) {
      case 'Excellent':
        return Colors.green;
      case 'Good':
        return Colors.lightGreen;
      case 'Fair':
        return Colors.orange;
      case 'Poor':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Build analytics section with consistent styling
  Widget _buildAnalyticsSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<AnalyticsCardData> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(title, subtitle, icon, headerColor),
        const SizedBox(height: 16),
        _buildMetricCardGrid(cardData),
      ],
    );
  }

  /// Build section header
  Widget _buildSectionHeader(String title, String subtitle, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                if (subtitle.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: color.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build a grid of metric cards
  Widget _buildMetricCardGrid(List<AnalyticsCardData> cardData) {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: cardData.map((data) => _buildMetricCard(data)).toList(),
    );
  }

  /// Build individual metric card using universal component
  Widget _buildMetricCard(AnalyticsCardData data) {
    return UniversalInfoCard(
      title: data.title,
      value: data.value,
      subtitle: data.subtitle,
      icon: data.icon,
      color: data.color,
      insight: data.insight,
    );
  }
}