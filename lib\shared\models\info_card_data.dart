import 'package:flutter/material.dart';
import '../../Dashboard/widgets/universal_info_card.dart';

/// Shared InfoCardData model for all analytics tabs
/// 
/// This replaces 6 duplicate implementations across modules and provides
/// seamless integration with UniversalInfoCard for consistent UI.
/// 
/// Usage:
/// ```dart
/// final cardData = InfoCardData(
///   title: 'Total Cattle',
///   value: '150',
///   subtitle: 'active animals',
///   icon: Icons.pets,
///   color: Colors.blue,
///   insight: 'Healthy herd status',
/// );
/// 
/// // Convert to widget
/// Widget card = cardData.toUniversalInfoCard();
/// ```
class InfoCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String insight;
  final VoidCallback? onTap;
  final String? badge;

  const InfoCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.insight,
    this.onTap,
    this.badge,
  });

  /// Convert to UniversalInfoCard.insight factory for consistent UI
  Widget toUniversalInfoCard() {
    return UniversalInfoCard.insight(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle,
      insight: insight,
      onTap: onTap,
    );
  }

  /// Convert to UniversalInfoCard.metric factory with badge support
  Widget toUniversalInfoCardWithBadge() {
    return UniversalInfoCard.metric(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle,
      badge: badge,
      onTap: onTap,
    );
  }

  /// Convert to UniversalInfoCard.kpi factory for key metrics
  Widget toUniversalInfoCardKPI() {
    return UniversalInfoCard.kpi(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle,
      onTap: onTap,
    );
  }

  /// Convert to Map for backward compatibility with existing grid builders
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'value': value,
      'subtitle': subtitle,
      'icon': icon,
      'color': color,
      'insight': insight,
      'badge': badge,
      'onTap': onTap,
    };
  }

  /// Create copy with modified properties
  InfoCardData copyWith({
    String? title,
    String? value,
    String? subtitle,
    IconData? icon,
    Color? color,
    String? insight,
    VoidCallback? onTap,
    String? badge,
  }) {
    return InfoCardData(
      title: title ?? this.title,
      value: value ?? this.value,
      subtitle: subtitle ?? this.subtitle,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      insight: insight ?? this.insight,
      onTap: onTap ?? this.onTap,
      badge: badge ?? this.badge,
    );
  }

  /// Factory constructors for common use cases

  /// Create KPI card for key performance indicators
  factory InfoCardData.kpi({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    String insight = '',
    VoidCallback? onTap,
  }) {
    return InfoCardData(
      title: title,
      value: value,
      subtitle: subtitle,
      icon: icon,
      color: color,
      insight: insight,
      onTap: onTap,
    );
  }

  /// Create metric card with badge
  factory InfoCardData.metric({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    String insight = '',
    String? badge,
    VoidCallback? onTap,
  }) {
    return InfoCardData(
      title: title,
      value: value,
      subtitle: subtitle,
      icon: icon,
      color: color,
      insight: insight,
      badge: badge,
      onTap: onTap,
    );
  }

  /// Create insight card with detailed information
  factory InfoCardData.insight({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String insight,
    VoidCallback? onTap,
  }) {
    return InfoCardData(
      title: title,
      value: value,
      subtitle: subtitle,
      icon: icon,
      color: color,
      insight: insight,
      onTap: onTap,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InfoCardData &&
        other.title == title &&
        other.value == value &&
        other.subtitle == subtitle &&
        other.icon == icon &&
        other.color == color &&
        other.insight == insight &&
        other.badge == badge;
  }

  @override
  int get hashCode {
    return Object.hash(
      title,
      value,
      subtitle,
      icon,
      color,
      insight,
      badge,
    );
  }

  @override
  String toString() {
    return 'InfoCardData(title: $title, value: $value, subtitle: $subtitle, insight: $insight)';
  }
}
