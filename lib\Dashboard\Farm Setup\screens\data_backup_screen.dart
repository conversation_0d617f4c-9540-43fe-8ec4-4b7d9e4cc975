import 'package:flutter/material.dart';
import '../services/farm_setup_repository.dart';
import 'package:get_it/get_it.dart';
import '../models/backup_settings_isar.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/cloud_backup_service.dart';
import '../dialogs/storage_provider_dialog.dart';
import '../../../services/auto_backup_service.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/backup_naming_utils.dart';

class DataBackupScreen extends StatefulWidget {
  const DataBackupScreen({Key? key}) : super(key: key);

  @override
  State<DataBackupScreen> createState() => _DataBackupScreenState();
}

class _DataBackupScreenState extends State<DataBackupScreen> {
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  bool _isLoading = true;
  String? _errorMessage;
  late BackupSettingsIsar _backupSettings;
  bool _backupInProgress = false;
  bool _restoreInProgress = false;
  String? _backupLocation;

  // Cloud backup state
  bool _isCloudAuthenticated = false;
  List<CloudBackupInfo> _cloudBackups = [];
  bool _isLoadingCloudBackups = false;
  bool _isCloudBackupInProgress = false;
  bool _isCloudRestoreInProgress = false;

  // Auto backup status
  AutoBackupStatus? _autoBackupStatus;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _backupSettings = await _farmSetupRepository.getBackupSettings();
      _backupLocation = _backupSettings.backupLocation;

      // If the backup location is 'local' or empty, update it to a real path
      if (_backupLocation == 'local' ||
          _backupLocation == null ||
          _backupLocation!.isEmpty) {
        _backupLocation = await _getDefaultBackupLocation();
        _backupSettings.backupLocation = _backupLocation!;
        await _saveSettings();
      }

      // Check cloud authentication status and load cloud backups
      await _checkCloudAuthentication();
      if (_backupSettings.isCloudStorageEnabled && _isCloudAuthenticated) {
        await _loadCloudBackups();
      }

      // Load auto backup status
      await _loadAutoBackupStatus();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load backup settings: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      await _farmSetupRepository.saveBackupSettings(_backupSettings);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Backup settings saved')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save settings: $e')),
        );
      }
    }
  }

  Future<void> _checkCloudAuthentication() async {
    if (!_backupSettings.isCloudStorageEnabled) {
      setState(() {
        _isCloudAuthenticated = false;
      });
      return;
    }

    try {
      _isCloudAuthenticated = await _farmSetupRepository
          .isCloudAuthenticated(_backupSettings.storageProvider);
    } catch (e) {
      _isCloudAuthenticated = false;
    } finally {
      if (mounted) {
        setState(() {});
      }
    }
  }

  Future<void> _loadCloudBackups() async {
    if (!_backupSettings.isCloudStorageEnabled || !_isCloudAuthenticated) {
      return;
    }

    setState(() => _isLoadingCloudBackups = true);

    try {
      _cloudBackups = await _farmSetupRepository
          .listBackups(_backupSettings.storageProvider);
    } catch (e) {
      _cloudBackups = [];
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load cloud backups: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingCloudBackups = false);
      }
    }
  }

  Future<void> _loadAutoBackupStatus() async {
    try {
      final autoBackupService = GetIt.instance<AutoBackupService>();
      _autoBackupStatus = await autoBackupService.getBackupStatus();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      _autoBackupStatus = null;
    }
  }

  Future<void> _authenticateCloudStorage() async {
    try {
      final success = await _farmSetupRepository.signInToCloud(_backupSettings.storageProvider);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$_backupSettings.storageProviderDisplayName authentication successful!'),
            backgroundColor: AppColors.success,
          ),
        );
        await _checkCloudAuthentication();
        if (_isCloudAuthenticated) {
          await _loadCloudBackups();
        }
        await _loadAutoBackupStatus();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$_backupSettings.storageProviderDisplayName authentication failed'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Authentication error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _openStorageProviderDialog() async {
    final result = await showDialog<BackupSettingsIsar>(
      context: context,
      builder: (context) => StorageProviderDialog(
        currentSettings: _backupSettings,
        farmSetupRepository: _farmSetupRepository,
      ),
    );

    if (result != null) {
      setState(() {
        _backupSettings = result;
      });
      await _checkCloudAuthentication();
      if (_backupSettings.isCloudStorageEnabled && _isCloudAuthenticated) {
        await _loadCloudBackups();
      }
      await _loadAutoBackupStatus();
    }
  }

  Future<bool> _requestStoragePermission() async {
    try {
      // For Android 13+ (API level 33+)
      if (Platform.isAndroid) {
        // First request the basic storage permission
        var storageStatus = await Permission.storage.request();
        if (storageStatus.isGranted) {
          return true;
        }

        // For Android 13+, try media permissions instead
        final mediaStatus = await [
          Permission.photos,
          Permission.videos,
          Permission.audio,
        ].request();

        // Check if all media permissions are granted
        bool allMediaGranted =
            mediaStatus.values.every((status) => status.isGranted);
        if (allMediaGranted) {
          return true;
        }
      } else {
        // For iOS or other platforms
        if (await Permission.storage.request().isGranted) {
          return true;
        }
      }

      // If we get here, show a dialog explaining permissions
      if (mounted) {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Storage Permission Required'),
            content: const Text(
                'This app needs storage access to backup and restore your data. '
                'Please grant storage permission in your device settings.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              TextButton(
                onPressed: () => openAppSettings(),
                child: const Text('Open Settings'),
              ),
            ],
          ),
        );
      }

      return false;
    } catch (e) {
      // If we get a MissingPluginException, we need to restart the app
      if (e.toString().contains('MissingPluginException')) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please restart the app to enable storage access'),
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
      return false;
    }
  }

  Future<String> _getDefaultBackupLocation() async {
    try {
      // For most reliable access, prefer the app-specific directory
      final directory = await getApplicationDocumentsDirectory();
      final backupPath = '$directory.path/CattleManager/Backups';

      // Create the directory if it doesn't exist
      try {
        final dir = Directory(backupPath);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        // Test write access by creating a test file
        final testFile = File('$backupPath/test_access.tmp');
        await testFile.writeAsString('test');
        await testFile.delete(); // Clean up test file

        return backupPath;
      } catch (e) {
        // Fall back to Downloads directory which may be more accessible
        if (Platform.isAndroid) {
          return '/storage/emulated/0/Download/CattleManager/Backups';
        } else {
          rethrow; // Re-throw for other platforms
        }
      }
    } catch (e) {
      // Last resort fallback
      return '/storage/emulated/0/Download/CattleManager/Backups';
    }
  }

  Future<void> _createBackup() async {
    setState(() {
      _backupInProgress = true;
    });

    try {
      if (_backupSettings.storageProvider == BackupStorageProvider.local) {
        if (_backupLocation == null || _backupLocation!.isEmpty) {
          throw Exception('Please select a backup location first');
        }

        // Request storage permission
        final hasPermission = await _requestStoragePermission();
        if (!hasPermission) {
          throw Exception('Storage permission denied');
        }

        // Get the directory for backups
        final backupDir = Directory(_backupLocation!);
        if (!await backupDir.exists()) {
          try {
            await backupDir.create(recursive: true);
          } catch (e) {
            // Try a different location if creation fails
            _backupLocation = await _getDefaultBackupLocation();
            _backupSettings.backupLocation = _backupLocation!;
            await _saveSettings();

            // Try again with the new location
            final newBackupDir = Directory(_backupLocation!);
            if (!await newBackupDir.exists()) {
              await newBackupDir.create(recursive: true);
            }
          }
        }

        // Create a human-readable filename for the backup
        final backupFileName = BackupNamingUtils.generateLocalBackupName();
        final backupFilePath = '${_backupLocation!}/$backupFileName';

        // Perform the actual backup
        final result = await _farmSetupRepository.createBackup();
        final success = result.success;

        if (!success) {
          throw Exception('Backup operation failed');
        }

        // Update the last backup date
        _backupSettings.lastBackupDate = DateTime.now();
        await _farmSetupRepository.saveBackupSettings(_backupSettings);

        // Clean up old local backups to maintain only 3 most recent
        await _cleanupOldLocalBackups();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Backup created: $backupFilePath')),
          );
        }
      } else {
        // Cloud backup
        await _createCloudBackup();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create backup: $e'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      setState(() {
        _backupInProgress = false;
      });
      // Refresh status after backup attempt
      await _loadAutoBackupStatus();
    }
  }

  Future<void> _createCloudBackup() async {
    if (!_backupSettings.isCloudStorageEnabled) {
      throw Exception('Cloud storage is not enabled');
    }

    if (!_isCloudAuthenticated) {
      throw Exception('Not authenticated with cloud provider');
    }

    setState(() {
      _isCloudBackupInProgress = true;
    });

    try {
      final result = await _farmSetupRepository.createBackup();

      if (!result.success) {
        throw Exception(result.message);
      }

      // Update the last backup date
      _backupSettings.lastBackupDate = DateTime.now();
      _backupSettings.lastCloudBackupDate = DateTime.now();
      await _farmSetupRepository.saveBackupSettings(_backupSettings);

      // Refresh cloud backups list
      await _loadCloudBackups();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cloud backup created successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create cloud backup: $e'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCloudBackupInProgress = false;
        });
      }
    }
  }

  Future<void> _selectBackupLocation() async {
    try {
      // Request storage permission
      final hasPermission = await _requestStoragePermission();
      if (!hasPermission) {
        throw Exception('Storage permission denied');
      }

      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();

      if (selectedDirectory != null) {
        setState(() {
          _backupSettings.backupLocation = selectedDirectory;
          _backupLocation = selectedDirectory;
        });
        await _saveSettings();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to select directory: $e')),
        );
      }
    }
  }

  Future<void> _restoreBackup() async {
    setState(() {
      _restoreInProgress = true;
    });

    // We'll collect results here instead of showing snackbars directly
    String? resultMessage;
    bool isError = false;

    try {
      // Show warning dialog
      final confirm = await showDialog<bool>(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Warning'),
          content: const Text(
              'Restoring from backup will overwrite your current data. '
              'This action cannot be undone. Are you sure you want to proceed?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Restore'),
            ),
          ],
        ),
      );

      if (!mounted) return;

      if (confirm != true) {
        return;
      }

      if (_backupSettings.storageProvider == BackupStorageProvider.local) {
        // Local restore
        // Request storage permission
        final hasPermission = await _requestStoragePermission();
        if (!mounted) return;

        if (!hasPermission) {
          throw Exception('Storage permission denied');
        }

        // Pick the backup file
        final result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['isar'],
          dialogTitle: 'Select a backup file',
        );

        if (!mounted) return;

        if (result == null || result.files.single.path == null) {
          throw Exception('No backup file selected');
        }

        final filePath = result.files.single.path!;

        // Perform the actual restore
        final success = await _farmSetupRepository.restoreBackup(filePath);

        if (!mounted) return;

        if (!success) {
          throw Exception('Restore operation failed');
        }

        resultMessage = 'Backup restored successfully. Restart the app to see changes.';
      } else {
        // Cloud restore - show cloud backup selection dialog
        await _showCloudRestoreDialog();
        return; // Don't show result message here as it's handled in the dialog
      }
    } catch (e) {
      resultMessage = 'Failed to restore backup: $e';
      isError = true;
    } finally {
      // Only setState and show result if we're still mounted
      if (mounted) {
        setState(() {
          _restoreInProgress = false;
        });

        // Show result message if we have one
        if (resultMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(resultMessage),
              duration: Duration(seconds: isError ? 3 : 5),
            ),
          );
        }
      }
    }
  }

  Future<void> _showCloudRestoreDialog() async {
    if (_cloudBackups.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No cloud backups available')),
      );
      return;
    }

    final selectedBackup = await showDialog<CloudBackupInfo>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Cloud Backup'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _cloudBackups.length,
            itemBuilder: (context, index) {
              final backup = _cloudBackups[index];
              return ListTile(
                title: Text(backup.name),
                subtitle: Text('$backup.formattedSize • $backup.formattedCreatedTime'),
                onTap: () => Navigator.of(context).pop(backup),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (selectedBackup != null) {
      await _restoreFromCloudBackup(selectedBackup);
    }
  }

  Future<void> _restoreFromCloudBackup(CloudBackupInfo backup) async {
    setState(() {
      _isCloudRestoreInProgress = true;
    });

    try {
      final result = await _farmSetupRepository.restoreFromBackup(
        backup.id,
        _backupSettings.storageProvider,
      );

      if (!result.success) {
        throw Exception(result.message);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cloud backup restored successfully. Restart the app to see changes.'),
            duration: Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to restore cloud backup: $e'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCloudRestoreInProgress = false;
        });
      }
    }
  }

  String formatLastBackupDate(DateTime? date) {
    if (date == null) return 'No backup created yet';
    // Create formatter instance here
    final DateFormat formatter = DateFormat("MMM d, yyyy 'at' h:mm a");
    try {
      // Use the formatter instance
      return formatter.format(date);
    } catch (e) {
      // Consider logging the error e
      return 'Invalid date';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Data Backup')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(_errorMessage!, style: const TextStyle(color: Colors.red)),
              ElevatedButton(
                onPressed: _loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Backup & Restore'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSettings,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Backup Status Overview (Top Priority)
            _buildBackupStatusCard(),
            const SizedBox(height: 20),

            // Configuration Section
            _buildConfigurationSection(),
            const SizedBox(height: 20),

            // Actions Section
            _buildActionsSection(),

            // Cloud Backups History (if applicable)
            if (_backupSettings.isCloudStorageEnabled && _isCloudAuthenticated) ...[
              const SizedBox(height: 20),
              _buildCloudBackupsCard(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBackupStatusCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getBackupStatusIcon(),
                  color: _getBackupStatusColor(),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Backup Status',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getBackupStatusText(),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: _getBackupStatusColor(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusDetails(),
            const SizedBox(height: 16),
            _buildStatusActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusDetails() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          _buildStatusDetailRow(
            'Storage Provider',
            _backupSettings.storageProviderDisplayName,
            _backupSettings.isCloudStorageEnabled ? Icons.cloud : Icons.phone_android,
          ),
          const SizedBox(height: 8),
          _buildStatusDetailRow(
            'Auto Backup',
            _backupSettings.autoBackupEnabled ? 'Enabled' : 'Disabled',
            _backupSettings.autoBackupEnabled ? Icons.check_circle : Icons.cancel,
          ),
          if (_backupSettings.autoBackupEnabled) ...[
            const SizedBox(height: 8),
            _buildStatusDetailRow(
              'Frequency',
              'Every $_backupSettings.autoBackupFrequency days',
              Icons.schedule,
            ),
          ],
          if (_backupSettings.lastBackupDate != null) ...[
            const SizedBox(height: 8),
            _buildStatusDetailRow(
              'Last Backup',
              _formatDate(_backupSettings.lastBackupDate!),
              Icons.backup,
            ),
          ],
          if (_autoBackupStatus?.hasPendingChanges == true) ...[
            const SizedBox(height: 8),
            _buildStatusDetailRow(
              'Status',
              'Backup pending...',
              Icons.pending,
              color: AppColors.warning,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusDetailRow(String label, String value, IconData icon, {Color? color}) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color ?? Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: color ?? Colors.grey[700]),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusActionButtons() {
    if (_backupSettings.isCloudStorageEnabled && !_isCloudAuthenticated) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: _authenticateCloudStorage,
          icon: const Icon(Icons.login),
          label: Text('Authenticate $_backupSettings.storageProviderDisplayName'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
        ),
      );
    }

    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: () => _openStorageProviderDialog(),
        icon: const Icon(Icons.settings),
        label: const Text('Configure Settings'),
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          side: const BorderSide(color: AppColors.primary),
        ),
      ),
    );
  }

  IconData _getBackupStatusIcon() {
    if (_backupSettings.isCloudStorageEnabled && _isCloudAuthenticated) {
      return Icons.cloud_done;
    } else if (_backupSettings.isCloudStorageEnabled) {
      return Icons.cloud_off;
    } else {
      return Icons.phone_android;
    }
  }

  Color _getBackupStatusColor() {
    if (_backupSettings.isCloudStorageEnabled && _isCloudAuthenticated) {
      return AppColors.success;
    } else if (_backupSettings.isCloudStorageEnabled) {
      return AppColors.warning;
    } else {
      return AppColors.info;
    }
  }

  String _getBackupStatusText() {
    if (_backupSettings.isCloudStorageEnabled && _isCloudAuthenticated) {
      return 'Cloud backup active';
    } else if (_backupSettings.isCloudStorageEnabled) {
      return 'Authentication required';
    } else {
      return 'Local backup only';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '$difference.inDays days ago';
    } else {
      return DateFormat('MMM dd, yyyy').format(date);
    }
  }

  /// Clean up old local backups to maintain only the 3 most recent
  Future<void> _cleanupOldLocalBackups() async {
    if (_backupLocation == null || _backupLocation!.isEmpty) {
      return;
    }

    try {
      final backupDir = Directory(_backupLocation!);
      if (!await backupDir.exists()) {
        return;
      }

      // Get all backup files in the directory (both old and new naming patterns)
      final backupFiles = await backupDir
          .list()
          .where((entity) {
            if (entity is! File) return false;
            final fileName = entity.path.split('/').last;
            return BackupNamingUtils.isLocalBackupFile(fileName);
          })
          .cast<File>()
          .toList();

      if (backupFiles.length <= 3) {
        debugPrint('ℹ️ Only $backupFiles.length local backups found, no cleanup needed');
        return;
      }

      // Sort by modification time (newest first)
      backupFiles.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // Keep only the 3 most recent, delete the rest
      final filesToDelete = backupFiles.skip(3).toList();

      debugPrint('ℹ️ Deleting $filesToDelete.length old local backups');

      for (final file in filesToDelete) {
        try {
          await file.delete();
          debugPrint('✅ Deleted old local backup: $file.path');
        } catch (e) {
          debugPrint('❌ Failed to delete old local backup $file.path: $e');
        }
      }

      debugPrint('✅ Local backup cleanup completed. Kept ${backupFiles.length - filesToDelete.length} most recent backups');
    } catch (e) {
      debugPrint('❌ Error during local backup cleanup: $e');
      // Don't throw - cleanup failure shouldn't prevent backup creation
    }
  }

  /// Configuration section containing storage provider and backup settings
  Widget _buildConfigurationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Configuration',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        _buildStorageProviderCard(),
        const SizedBox(height: 16),
        _buildBackupSettingsCard(),
      ],
    );
  }

  /// Actions section containing backup and restore operations
  Widget _buildActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        _buildUnifiedBackupRestoreCard(),
      ],
    );
  }

  /// Unified card for backup and restore operations
  Widget _buildUnifiedBackupRestoreCard() {
    final bool isBackupInProgress = _backupInProgress || _isCloudBackupInProgress;
    final bool isRestoreInProgress = _restoreInProgress || _isCloudRestoreInProgress;
    final bool isAnyOperationInProgress = isBackupInProgress || isRestoreInProgress;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  _backupSettings.isCloudStorageEnabled ? Icons.cloud : Icons.storage,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Backup & Restore Operations',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                // Backup Button
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: isAnyOperationInProgress ? null : _createBackup,
                    icon: isBackupInProgress
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Icon(_backupSettings.isCloudStorageEnabled ? Icons.cloud_upload : Icons.backup),
                    label: Text(isBackupInProgress ? 'Creating...' : 'Create Backup'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Restore Button
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: isAnyOperationInProgress ? null : _restoreBackup,
                    icon: isRestoreInProgress
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Icon(_backupSettings.isCloudStorageEnabled ? Icons.cloud_download : Icons.restore),
                    label: Text(isRestoreInProgress ? 'Restoring...' : 'Restore Data'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.warning,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),

            // Warning for restore
            if (!isAnyOperationInProgress) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.warning, color: AppColors.warning, size: 16),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Restore will overwrite your current data. Create a backup first!',
                        style: TextStyle(
                          color: AppColors.warning,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStorageProviderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.cloud),
                SizedBox(width: 8),
                Text(
                  'Storage Provider',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('Current Provider'),
              subtitle: Text(_backupSettings.storageProviderDisplayName),
              trailing: ElevatedButton.icon(
                icon: const Icon(Icons.settings),
                label: const Text('Configure'),
                onPressed: _openStorageProviderDialog,
              ),
            ),
            if (_backupSettings.isCloudStorageEnabled) ...[
              const Divider(),
              ListTile(
                title: const Text('Authentication Status'),
                subtitle: Text(_isCloudAuthenticated ? 'Authenticated' : 'Not Authenticated'),
                leading: Icon(
                  _isCloudAuthenticated ? Icons.check_circle : Icons.error,
                  color: _isCloudAuthenticated ? Colors.green : Colors.red,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBackupSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings_backup_restore),
                SizedBox(width: 8),
                Text(
                  'Backup Settings',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Auto Backup'),
              subtitle: const Text(
                  'Automatically backup your data regularly'),
              value: _backupSettings.autoBackupEnabled,
              onChanged: (value) {
                setState(() {
                  _backupSettings.autoBackupEnabled = value;
                });
              },
            ),
            if (_backupSettings.autoBackupEnabled)
              ListTile(
                title: const Text('Backup Frequency'),
                subtitle: const Text('How often to create backups'),
                trailing: DropdownButton<int>(
                  value: _backupSettings.autoBackupFrequency,
                  items: [1, 3, 7, 14, 30].map((days) {
                    return DropdownMenuItem(
                      value: days,
                      child:
                          Text('$days ${days == 1 ? 'day' : 'days'}'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _backupSettings.autoBackupFrequency = value;
                      });
                    }
                  },
                ),
              ),
            if (_backupSettings.storageProvider == BackupStorageProvider.local)
              ListTile(
                title: const Text('Backup Location'),
                subtitle: Text(_backupLocation ?? 'Not set'),
                trailing: IconButton(
                  icon: const Icon(Icons.folder_open),
                  onPressed: _selectBackupLocation,
                ),
              ),
          ],
        ),
      ),
    );
  }



  Widget _buildCloudBackupsCard() {
    // Show only the 3 most recent backups
    final recentBackups = _cloudBackups.take(3).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.history),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Recent Cloud Backups',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.info.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
                  ),
                  child: const Text(
                    'Last 3',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.info,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, size: 16, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Only the 3 most recent backups are kept. Older backups are automatically deleted.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoadingCloudBackups)
              const Center(child: CircularProgressIndicator())
            else if (recentBackups.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text('No cloud backups found'),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: recentBackups.length,
                itemBuilder: (context, index) {
                  final backup = recentBackups[index];
                  return ListTile(
                    title: Text(backup.name),
                    subtitle: Text('$backup.formattedSize • $backup.formattedCreatedTime'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.restore),
                          onPressed: () => _restoreFromCloudBackup(backup),
                          tooltip: 'Restore this backup',
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _deleteCloudBackup(backup),
                          tooltip: 'Delete this backup',
                        ),
                      ],
                    ),
                  );
                },
              ),
            const SizedBox(height: 8),
            Center(
              child: TextButton.icon(
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
                onPressed: _loadCloudBackups,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteCloudBackup(CloudBackupInfo backup) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Backup'),
        content: Text('Are you sure you want to delete the backup "$backup.name"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        final success = await _farmSetupRepository.deleteBackup(
          backup.id,
          _backupSettings.storageProvider,
        );

        if (success) {
          await _loadCloudBackups();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Backup deleted successfully')),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to delete backup')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting backup: $e')),
          );
        }
      }
    }
  }
}
