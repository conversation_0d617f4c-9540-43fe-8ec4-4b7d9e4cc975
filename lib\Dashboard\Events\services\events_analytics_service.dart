import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../models/event_enums.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Data class to hold all event analytics results
class EventAnalyticsResult {
  // KPI Metrics
  final int totalEvents;
  final int completedEvents;
  final int scheduledEvents;
  final int overdueEvents;
  final int upcomingEvents;
  final int cancelledEvents;
  final double completionRate;

  // Category distributions
  final Map<String, int> eventsByCategory;
  final Map<String, int> eventsByStatus;
  final Map<String, int> eventsByPriority;

  // Time-based metrics
  final int eventsThisWeek;
  final int eventsThisMonth;
  final int eventsNextWeek;
  final int eventsNextMonth;

  // Performance metrics
  final double averageCompletionTime; // in days
  final int totalEventTypes;
  final int activeEventTypes;

  // Cost metrics
  final double totalEstimatedCosts;
  final double totalActualCosts;
  final double averageEventCost;

  // Automation metrics
  final int autoGeneratedEvents;
  final int manualEvents;
  final double automationRate;
  final Map<String, int> eventsBySourceModule;

  // Attachment metrics
  final int totalAttachments;
  final int eventsWithAttachments;
  final double averageAttachmentsPerEvent;

  // Cattle-specific metrics
  final Map<String, int> eventsByCattle;
  final int cattleWithEvents;
  final double averageEventsPerCattle;

  // Recurrence metrics
  final int recurringEvents;
  final int oneTimeEvents;
  final Map<String, int> eventsByRecurrencePattern;

  const EventAnalyticsResult({
    required this.totalEvents,
    required this.completedEvents,
    required this.scheduledEvents,
    required this.overdueEvents,
    required this.upcomingEvents,
    required this.cancelledEvents,
    required this.completionRate,
    required this.eventsByCategory,
    required this.eventsByStatus,
    required this.eventsByPriority,
    required this.eventsThisWeek,
    required this.eventsThisMonth,
    required this.eventsNextWeek,
    required this.eventsNextMonth,
    required this.averageCompletionTime,
    required this.totalEventTypes,
    required this.activeEventTypes,
    required this.totalEstimatedCosts,
    required this.totalActualCosts,
    required this.averageEventCost,
    required this.autoGeneratedEvents,
    required this.manualEvents,
    required this.automationRate,
    required this.eventsBySourceModule,
    required this.totalAttachments,
    required this.eventsWithAttachments,
    required this.averageAttachmentsPerEvent,
    required this.eventsByCattle,
    required this.cattleWithEvents,
    required this.averageEventsPerCattle,
    required this.recurringEvents,
    required this.oneTimeEvents,
    required this.eventsByRecurrencePattern,
  });

  /// Empty result for when there's no data
  static const empty = EventAnalyticsResult(
    totalEvents: 0,
    completedEvents: 0,
    scheduledEvents: 0,
    overdueEvents: 0,
    upcomingEvents: 0,
    cancelledEvents: 0,
    completionRate: 0.0,
    eventsByCategory: {},
    eventsByStatus: {},
    eventsByPriority: {},
    eventsThisWeek: 0,
    eventsThisMonth: 0,
    eventsNextWeek: 0,
    eventsNextMonth: 0,
    averageCompletionTime: 0.0,
    totalEventTypes: 0,
    activeEventTypes: 0,
    totalEstimatedCosts: 0.0,
    totalActualCosts: 0.0,
    averageEventCost: 0.0,
    autoGeneratedEvents: 0,
    manualEvents: 0,
    automationRate: 0.0,
    eventsBySourceModule: {},
    totalAttachments: 0,
    eventsWithAttachments: 0,
    averageAttachmentsPerEvent: 0.0,
    eventsByCattle: {},
    cattleWithEvents: 0,
    averageEventsPerCattle: 0.0,
    recurringEvents: 0,
    oneTimeEvents: 0,
    eventsByRecurrencePattern: {},
  );
}

/// Pure analytics service for event calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class EventAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static EventAnalyticsResult calculate(
    List<EventIsar> events,
    List<EventTypeIsar> eventTypes,
    List<EventAttachmentIsar> attachments,
    List<CattleIsar> cattle,
  ) {
    if (events.isEmpty) {
      return EventAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _EventAnalyticsAccumulator();

    // Process all event data types
    for (final event in events) {
      accumulator.processEvent(event);
    }

    for (final eventType in eventTypes) {
      accumulator.processEventType(eventType);
    }

    for (final attachment in attachments) {
      accumulator.processAttachment(attachment);
    }

    // Calculate derived metrics
    accumulator.calculateDerivedMetrics(cattle);

    return accumulator.toResult();
  }

  /// Calculate completion rate for a specific cattle
  static double calculateCattleCompletionRate(
    String cattleTagId,
    List<EventIsar> events,
  ) {
    final cattleEvents = events.where((e) => e.cattleTagId == cattleTagId).toList();
    if (cattleEvents.isEmpty) return 0.0;

    final completedEvents = cattleEvents.where((e) => e.status == EventStatus.completed).length;
    return (completedEvents / cattleEvents.length * 100).clamp(0.0, 100.0);
  }

  /// Calculate events for a specific date range
  static List<EventIsar> getEventsInDateRange(
    List<EventIsar> events,
    DateTime startDate,
    DateTime endDate,
  ) {
    return events.where((event) {
      if (event.scheduledDate == null) return false;
      return event.scheduledDate!.isAfter(startDate.subtract(const Duration(days: 1))) &&
             event.scheduledDate!.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }
}

/// Internal accumulator class for efficient single-pass analytics calculation
class _EventAnalyticsAccumulator {
  // Basic counters
  int totalEvents = 0;
  int completedEvents = 0;
  int scheduledEvents = 0;
  int overdueEvents = 0;
  int upcomingEvents = 0;
  int cancelledEvents = 0;

  // Category distributions
  final Map<String, int> eventsByCategory = {};
  final Map<String, int> eventsByStatus = {};
  final Map<String, int> eventsByPriority = {};

  // Time-based metrics
  int eventsThisWeek = 0;
  int eventsThisMonth = 0;
  int eventsNextWeek = 0;
  int eventsNextMonth = 0;

  // Performance metrics
  final List<double> completionTimes = [];
  int totalEventTypes = 0;
  int activeEventTypes = 0;

  // Cost metrics
  double totalEstimatedCosts = 0.0;
  double totalActualCosts = 0.0;

  // Automation metrics
  int autoGeneratedEvents = 0;
  int manualEvents = 0;
  final Map<String, int> eventsBySourceModule = {};

  // Attachment metrics
  int totalAttachments = 0;
  final Set<String> eventsWithAttachments = {};

  // Cattle-specific metrics
  final Map<String, int> eventsByCattle = {};
  final Set<String> cattleWithEvents = {};

  // Recurrence metrics
  int recurringEvents = 0;
  int oneTimeEvents = 0;
  final Map<String, int> eventsByRecurrencePattern = {};

  void processEvent(EventIsar event) {
    totalEvents++;

    // Status counting
    switch (event.status) {
      case EventStatus.completed:
        completedEvents++;
        break;
      case EventStatus.scheduled:
        scheduledEvents++;
        break;
      case EventStatus.cancelled:
        cancelledEvents++;
        break;
      case EventStatus.inProgress:
        scheduledEvents++; // Count in-progress as scheduled for UI purposes
        break;
      case EventStatus.overdue:
        overdueEvents++;
        break;
      case EventStatus.missed:
        overdueEvents++; // Count missed as overdue for analytics
        break;
    }

    // Check if event is actually overdue (status might not be updated)
    if (event.isOverdue && event.status != EventStatus.completed && event.status != EventStatus.cancelled) {
      if (event.status != EventStatus.overdue) {
        overdueEvents++;
        scheduledEvents = scheduledEvents > 0 ? scheduledEvents - 1 : 0;
      }
    }

    // Check if event is upcoming
    if (event.isUpcoming) {
      upcomingEvents++;
    }

    // Category distribution
    final categoryName = event.category.displayName;
    eventsByCategory[categoryName] = (eventsByCategory[categoryName] ?? 0) + 1;

    // Status distribution
    final statusName = event.status.displayName;
    eventsByStatus[statusName] = (eventsByStatus[statusName] ?? 0) + 1;

    // Priority distribution
    final priorityName = event.priority.displayName;
    eventsByPriority[priorityName] = (eventsByPriority[priorityName] ?? 0) + 1;

    // Time-based metrics
    if (event.scheduledDate != null) {
      final now = DateTime.now();
      final eventDate = event.scheduledDate!;

      // This week
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final endOfWeek = startOfWeek.add(const Duration(days: 6));
      if (eventDate.isAfter(startOfWeek) && eventDate.isBefore(endOfWeek.add(const Duration(days: 1)))) {
        eventsThisWeek++;
      }

      // This month
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);
      if (eventDate.isAfter(startOfMonth.subtract(const Duration(days: 1))) && 
          eventDate.isBefore(endOfMonth.add(const Duration(days: 1)))) {
        eventsThisMonth++;
      }

      // Next week
      final nextWeekStart = endOfWeek.add(const Duration(days: 1));
      final nextWeekEnd = nextWeekStart.add(const Duration(days: 6));
      if (eventDate.isAfter(nextWeekStart.subtract(const Duration(days: 1))) && 
          eventDate.isBefore(nextWeekEnd.add(const Duration(days: 1)))) {
        eventsNextWeek++;
      }

      // Next month
      final nextMonthStart = DateTime(now.year, now.month + 1, 1);
      final nextMonthEnd = DateTime(now.year, now.month + 2, 0);
      if (eventDate.isAfter(nextMonthStart.subtract(const Duration(days: 1))) && 
          eventDate.isBefore(nextMonthEnd.add(const Duration(days: 1)))) {
        eventsNextMonth++;
      }
    }

    // Completion time calculation
    if (event.status == EventStatus.completed && 
        event.scheduledDate != null && 
        event.completedDate != null) {
      final completionTime = event.completedDate!.difference(event.scheduledDate!).inDays.toDouble();
      completionTimes.add(completionTime.abs());
    }

    // Cost metrics
    if (event.estimatedCost != null) {
      totalEstimatedCosts += event.estimatedCost!;
    }
    if (event.actualCost != null) {
      totalActualCosts += event.actualCost!;
    }

    // Automation metrics
    if (event.isAutoGenerated == true) {
      autoGeneratedEvents++;
      if (event.sourceModule != null) {
        final moduleName = event.sourceModule!;
        eventsBySourceModule[moduleName] = (eventsBySourceModule[moduleName] ?? 0) + 1;
      }
    } else {
      manualEvents++;
    }

    // Cattle-specific metrics
    if (event.cattleTagId != null) {
      final cattleId = event.cattleTagId!;
      eventsByCattle[cattleId] = (eventsByCattle[cattleId] ?? 0) + 1;
      cattleWithEvents.add(cattleId);
    }

    // Recurrence metrics
    if (event.isRecurring == true) {
      recurringEvents++;
      final patternName = event.recurrencePattern.displayName;
      eventsByRecurrencePattern[patternName] = (eventsByRecurrencePattern[patternName] ?? 0) + 1;
    } else {
      oneTimeEvents++;
    }
  }

  void processEventType(EventTypeIsar eventType) {
    totalEventTypes++;
    if (eventType.isActive == true) {
      activeEventTypes++;
    }
  }

  void processAttachment(EventAttachmentIsar attachment) {
    totalAttachments++;
    if (attachment.eventBusinessId != null) {
      eventsWithAttachments.add(attachment.eventBusinessId!);
    }
  }

  void calculateDerivedMetrics(List<CattleIsar> cattle) {
    // No additional derived metrics needed for now
  }

  EventAnalyticsResult toResult() {
    return EventAnalyticsResult(
      totalEvents: totalEvents,
      completedEvents: completedEvents,
      scheduledEvents: scheduledEvents,
      overdueEvents: overdueEvents,
      upcomingEvents: upcomingEvents,
      cancelledEvents: cancelledEvents,
      completionRate: totalEvents > 0 ? (completedEvents / totalEvents * 100) : 0.0,
      eventsByCategory: Map.unmodifiable(eventsByCategory),
      eventsByStatus: Map.unmodifiable(eventsByStatus),
      eventsByPriority: Map.unmodifiable(eventsByPriority),
      eventsThisWeek: eventsThisWeek,
      eventsThisMonth: eventsThisMonth,
      eventsNextWeek: eventsNextWeek,
      eventsNextMonth: eventsNextMonth,
      averageCompletionTime: completionTimes.isNotEmpty 
          ? completionTimes.reduce((a, b) => a + b) / completionTimes.length 
          : 0.0,
      totalEventTypes: totalEventTypes,
      activeEventTypes: activeEventTypes,
      totalEstimatedCosts: totalEstimatedCosts,
      totalActualCosts: totalActualCosts,
      averageEventCost: totalEvents > 0 
          ? (totalEstimatedCosts + totalActualCosts) / totalEvents 
          : 0.0,
      autoGeneratedEvents: autoGeneratedEvents,
      manualEvents: manualEvents,
      automationRate: totalEvents > 0 ? (autoGeneratedEvents / totalEvents * 100) : 0.0,
      eventsBySourceModule: Map.unmodifiable(eventsBySourceModule),
      totalAttachments: totalAttachments,
      eventsWithAttachments: eventsWithAttachments.length,
      averageAttachmentsPerEvent: totalEvents > 0 ? totalAttachments / totalEvents : 0.0,
      eventsByCattle: Map.unmodifiable(eventsByCattle),
      cattleWithEvents: cattleWithEvents.length,
      averageEventsPerCattle: cattleWithEvents.isNotEmpty 
          ? totalEvents / cattleWithEvents.length 
          : 0.0,
      recurringEvents: recurringEvents,
      oneTimeEvents: oneTimeEvents,
      eventsByRecurrencePattern: Map.unmodifiable(eventsByRecurrencePattern),
    );
  }
}