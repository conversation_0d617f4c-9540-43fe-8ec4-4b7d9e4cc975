import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../screens/event_details_screen.dart';
import '../../../constants/app_colors.dart';

/// Widget for displaying event timeline visualization
/// Shows related events in a chronological timeline format
class EventTimelineWidget extends StatelessWidget {
  final List<EventIsar> events;
  final String? currentEventId;
  final bool showAll;

  const EventTimelineWidget({
    Key? key,
    required this.events,
    this.currentEventId,
    this.showAll = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (events.isEmpty) {
      return _buildEmptyState();
    }

    final displayEvents = showAll ? events : events.take(5).toList();

    return Column(
      children: [
        ...displayEvents.map((event) => _buildTimelineItem(context, event)),
        if (!showAll && events.length > 5) _buildShowMoreButton(context),
      ],
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.timeline,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 8),
          Text(
            'No related events found',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Events for the same cattle will appear here',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual timeline item
  Widget _buildTimelineItem(BuildContext context, EventIsar event) {
    final isCurrentEvent = event.businessId == currentEventId;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: isCurrentEvent ? null : () => _navigateToEvent(context, event),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isCurrentEvent ? _getCategoryColor(event.category).withValues(alpha: 0.1) : Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isCurrentEvent ? _getCategoryColor(event.category) : Colors.grey[200]!,
              width: isCurrentEvent ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // Timeline indicator
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getCategoryColor(event.category),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              
              // Event info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            event.title ?? 'Untitled Event',
                            style: TextStyle(
                              fontWeight: isCurrentEvent ? FontWeight.bold : FontWeight.w600,
                              fontSize: 14,
                              color: isCurrentEvent ? _getCategoryColor(event.category) : Colors.black87,
                            ),
                          ),
                        ),
                        Text(
                          _formatDate(event.scheduledDate),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: isCurrentEvent ? FontWeight.w500 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          _getCategoryIcon(event.category),
                          size: 14,
                          color: _getCategoryColor(event.category),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          event.category.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const Spacer(),
                        _buildStatusChip(event.status),
                      ],
                    ),
                    if (isCurrentEvent) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Current Event',
                        style: TextStyle(
                          fontSize: 10,
                          color: _getCategoryColor(event.category),
                          fontWeight: FontWeight.w500,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Navigation arrow (if not current event)
              if (!isCurrentEvent) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12,
                  color: Colors.grey[400],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build show more button
  Widget _buildShowMoreButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: TextButton.icon(
        onPressed: () => _showAllEvents(context),
        icon: const Icon(Icons.expand_more),
        label: Text('Show ${events.length - 5} more events'),
        style: TextButton.styleFrom(
          foregroundColor: AppColors.eventsHeader,
        ),
      ),
    );
  }

  /// Build status chip
  Widget _buildStatusChip(EventStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStatusColor(status),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(status),
            size: 10,
            color: _getStatusColor(status),
          ),
          const SizedBox(width: 2),
          Text(
            status.displayName,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(status),
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to event details
  void _navigateToEvent(BuildContext context, EventIsar event) {
    if (event.businessId == null) return;
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EventDetailsScreen(
          eventBusinessId: event.businessId!,
        ),
      ),
    );
  }

  /// Show all events in a dialog
  void _showAllEvents(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: AppColors.eventsHeader,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Text(
                      'All Related Events',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              
              // Events list
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: EventTimelineWidget(
                    events: events,
                    currentEventId: currentEventId,
                    showAll: true,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get category color
  Color _getCategoryColor(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return AppColors.healthHeader;
      case EventCategory.breeding:
        return AppColors.breedingHeader;
      case EventCategory.feeding:
        return Colors.green;
      case EventCategory.management:
        return Colors.blue;
      case EventCategory.maintenance:
        return Colors.orange;
      case EventCategory.financial:
        return AppColors.transactionHeader;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return AppColors.eventsHeader;
    }
  }

  /// Get category icon
  IconData _getCategoryIcon(EventCategory category) {
    switch (category) {
      case EventCategory.health:
        return Icons.medical_services;
      case EventCategory.breeding:
        return Icons.favorite;
      case EventCategory.feeding:
        return Icons.restaurant;
      case EventCategory.management:
        return Icons.business;
      case EventCategory.maintenance:
        return Icons.build;
      case EventCategory.financial:
        return Icons.attach_money;
      case EventCategory.other:
      //       default:
 // Unreachable default case removed
        return Icons.event;
    }
  }

  /// Get status color
  Color _getStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.scheduled:
        return Colors.blue;
      case EventStatus.inProgress:
        return Colors.orange;
      case EventStatus.completed:
        return Colors.green;
      case EventStatus.cancelled:
        return Colors.grey;
      case EventStatus.overdue:
        return Colors.red;
      case EventStatus.missed:
        return Colors.red;
    }
  }

  /// Get status icon
  IconData _getStatusIcon(EventStatus status) {
    switch (status) {
      case EventStatus.scheduled:
        return Icons.schedule;
      case EventStatus.inProgress:
        return Icons.play_circle;
      case EventStatus.completed:
        return Icons.check_circle;
      case EventStatus.cancelled:
        return Icons.cancel;
      case EventStatus.overdue:
        return Icons.warning;
      case EventStatus.missed:
        return Icons.error;
    }
  }

  /// Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'No Date';
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate = DateTime(date.year, date.month, date.day);
    
    if (eventDate == today) {
      return 'Today';
    } else if (eventDate == today.add(const Duration(days: 1))) {
      return 'Tomorrow';
    } else if (eventDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else {
      return DateFormat('MMM dd').format(date);
    }
  }
}