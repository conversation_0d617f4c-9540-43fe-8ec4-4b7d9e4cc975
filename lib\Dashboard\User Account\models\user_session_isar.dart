import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'user_isar.dart';

part 'user_session_isar.g.dart';

@collection
class UserSessionIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index(unique: true)
  String? sessionToken;

  @Index()
  String? userBusinessId;

  // Device and location information
  String? deviceInfo;
  String? ipAddress;
  String? userAgent;
  String? location;

  // Session status
  bool isActive = true;
  DateTime? createdAt;
  DateTime? lastAccessedAt;
  DateTime? expiresAt;

  // Relationship to user
  final user = IsarLink<UserIsar>();

  UserSessionIsar();

  factory UserSessionIsar.create({
    required String userBusinessId,
    required String sessionToken,
    String? deviceInfo,
    String? ipAddress,
    String? userAgent,
    String? location,
    Duration? sessionDuration,
  }) {
    final now = DateTime.now();
    final duration = sessionDuration ?? const Duration(days: 30); // Default 30 days
    
    return UserSessionIsar()
      ..businessId = const Uuid().v4()
      ..sessionToken = sessionToken
      ..userBusinessId = userBusinessId
      ..deviceInfo = deviceInfo
      ..ipAddress = ipAddress
      ..userAgent = userAgent
      ..location = location
      ..isActive = true
      ..createdAt = now
      ..lastAccessedAt = now
      ..expiresAt = now.add(duration);
  }

  // Helper methods
  @ignore
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  @ignore
  bool get isValid => isActive && !isExpired;

  void updateLastAccessed() {
    lastAccessedAt = DateTime.now();
  }

  void extendSession({Duration? extension}) {
    final extensionDuration = extension ?? const Duration(days: 30);
    expiresAt = DateTime.now().add(extensionDuration);
    lastAccessedAt = DateTime.now();
  }

  void invalidate() {
    isActive = false;
    lastAccessedAt = DateTime.now();
  }

  // Get remaining session time
  @ignore
  Duration? get remainingTime {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  // Check if session needs renewal (less than 7 days remaining)
  @ignore
  bool get needsRenewal {
    final remaining = remainingTime;
    if (remaining == null) return false;
    return remaining.inDays < 7;
  }
}
