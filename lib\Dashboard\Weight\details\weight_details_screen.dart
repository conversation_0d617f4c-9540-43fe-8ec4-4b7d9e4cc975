import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../controllers/weight_details_controller.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_bar.dart';
import '../../../constants/app_tabs.dart';

import 'weight_details_analytics_tab.dart';
import 'weight_details_records_tab.dart';
import '../dialogs/weight_form_dialog.dart';

class WeightDetailsScreen extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar)? onCattleUpdated;

  const WeightDetailsScreen({
    Key? key,
    required this.cattle,
    this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<WeightDetailsScreen> createState() => _WeightDetailsScreenState();
}

class _WeightDetailsScreenState extends State<WeightDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Handle FAB actions based on current tab
  void _getCurrentTabAction(WeightDetailsController controller) {
    final currentIndex = _tabController.index;

    switch (currentIndex) {
      case 0: // Analytics tab - no action
        break;
      case 1: // Records tab - show add weight record dialog
        _showWeightRecordFormDialog(controller);
        break;
    }
  }

  /// Show weight record form dialog
  void _showWeightRecordFormDialog(WeightDetailsController controller) {
    showDialog(
      context: context,
      builder: (context) => WeightFormDialog(
        cattle: [widget.cattle], // Pass as list for compatibility
        existingRecord: null, // null for add operation
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<WeightDetailsController>(
      create: (_) => WeightDetailsController()..initialize(widget.cattle), // Create directly, not from GetIt
      child: Consumer<WeightDetailsController>(
        builder: (context, controller, child) {
          // Handle loading state
          if (controller.isLoading) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: '${widget.cattle.name ?? 'Weight'} (${widget.cattle.tagId ?? 'No Tag'})',
              ),
              body: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          // Handle error state
          if (controller.error != null) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: '${widget.cattle.name ?? 'Weight'} (${widget.cattle.tagId ?? 'No Tag'})',
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error Loading Weight Data',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      controller.error!,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () => controller.refresh(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return Builder(
            builder: (context) {
              // Initialize tab manager here where Provider context is available
              _tabManager ??= UniversalTabManager.twoTabs(
                controller: _tabController,
                tabViews: [
                  WeightDetailsAnalyticsTab(controller: controller),
                  WeightDetailsRecordsTab(controller: controller),
                ],
                labels: const ['Analytics', 'Records'],
                icons: const [Icons.analytics, Icons.list_alt],
                showFABs: const [false, true], // No FAB on analytics, FAB on records
              );

              return Scaffold(
                appBar: AppBarConfig.withBack(
                  context: context,
                  title: '${controller.cattle?.name ?? 'Weight'} (${controller.cattle?.tagId ?? 'No Tag'})',
                ),
                body: _tabManager!,
                floatingActionButton: AnimatedBuilder(
                  animation: _tabController,
                  builder: (context, child) {
                    // Only rebuild FAB when tab changes, not the entire screen
                    return _tabManager?.getCurrentFAB(
                      onPressed: () => _getCurrentTabAction(controller), // Pass controller
                      tooltip: 'Add Weight Record',
                      backgroundColor: AppColors.weightHeader,
                    ) ?? const SizedBox.shrink(); // Handle null case
                  },
                ), // Optimized FAB management with AnimatedBuilder
              );
            },
          );
        },
      ),
    );
  }
}
