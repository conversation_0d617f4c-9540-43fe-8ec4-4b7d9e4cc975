import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';
import 'google_drive_auth_service.dart';
import '../../../utils/backup_naming_utils.dart';

class GoogleDriveService {
  final Logger _logger = Logger('GoogleDriveService');
  late drive.DriveApi _driveApi;
  late GoogleDriveAuthService _authService;
  bool _isInitialized = false;

  static final GoogleDriveService _instance = GoogleDriveService._internal();

  factory GoogleDriveService() {
    return _instance;
  }

  GoogleDriveService._internal() {
    _authService = GoogleDriveAuthService();
  }

  Future<bool> initialize() async {
    if (_isInitialized) {
      _logger.info('Google Drive service already initialized');
      return true;
    }

    try {
      _logger.info('Initializing Google Drive service...');

      // Initialize the auth service first (silent authentication only)
      final authInitialized = await _authService.initialize();
      if (!authInitialized) {
        _logger.info('Google Drive auth service not initialized (no cached credentials) - user will need to sign in manually');
        // DO NOT automatically attempt sign-in here to avoid triggering authentication prompts
        // The user must explicitly choose to authenticate
        return false;
      }

      // Get the authenticated client
      final authClient = _authService.authClient;
      if (authClient == null) {
        _logger.warning('No authenticated client available');
        return false;
      }

      _driveApi = drive.DriveApi(authClient);
      _isInitialized = true;
      _logger.info('Google Drive service initialized successfully');
      return true;
    } catch (e) {
      _logger.severe('Error initializing Google Drive service: $e');
      return false;
    }
  }

  /// Explicitly authenticate with Google Drive (user-initiated)
  /// This should only be called when the user explicitly chooses to authenticate
  Future<bool> authenticateUser() async {
    try {
      _logger.info('User-initiated Google Drive authentication...');
      final signInSuccess = await _authService.signIn();
      if (signInSuccess) {
        // Re-initialize the service with the new authentication
        _isInitialized = false;
        return await initialize();
      }
      return false;
    } catch (e) {
      _logger.severe('Error during user authentication: $e');
      return false;
    }
  }

  Future<String?> uploadBackup(String farmId, Map<String, dynamic> data, {String? farmName}) async {
    if (!_isInitialized) {
      _logger.warning('Google Drive service not initialized - user must authenticate first');
      return null;
    }
    try {
      // Use the new simplified naming convention
      final fileName = BackupNamingUtils.generateCloudBackupName(farmName ?? 'Farm');
      final content = jsonEncode(data);

      final file = drive.File()
        ..name = fileName
        ..mimeType = 'application/json';

      final media = drive.Media(
        Stream.fromIterable([utf8.encode(content)]),
        content.length,
        contentType: 'application/json',
      );

      final result = await _driveApi.files.create(file, uploadMedia: media);
      return result.id;
    } catch (e) {
      debugPrint('Error uploading backup to Google Drive: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> downloadBackup(String fileId) async {
    if (!_isInitialized) {
      _logger.warning('Google Drive service not initialized - user must authenticate first');
      return null;
    }

    try {
      final content = await _driveApi.files.get(fileId,
          downloadOptions: drive.DownloadOptions.fullMedia) as drive.Media;

      final bytes = await content.stream.toList();
      final jsonString = utf8.decode(bytes.expand((x) => x).toList());
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error downloading backup from Google Drive: $e');
      return null;
    }
  }

  Future<List<drive.File>> listBackups() async {
    if (!_isInitialized) {
      _logger.warning('Google Drive service not initialized - user must authenticate first');
      return [];
    }

    try {
      final result = await _driveApi.files.list(
        q: "(name contains 'cattle_manager_backup' or name contains 'CattleManager_') and mimeType = 'application/json'",
        orderBy: 'createdTime desc',
      );

      return result.files ?? [];
    } catch (e) {
      debugPrint('Error listing backups from Google Drive: $e');
      return [];
    }
  }

  Future<bool> deleteBackup(String fileId) async {
    if (!_isInitialized) {
      _logger.warning('Google Drive service not initialized - user must authenticate first');
      return false;
    }

    try {
      await _driveApi.files.delete(fileId);
      return true;
    } catch (e) {
      debugPrint('Error deleting backup from Google Drive: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _authService.signOut();
      _isInitialized = false;
      _logger.info('Google Drive service signed out successfully');
    } catch (e) {
      _logger.warning('Error signing out from Google Drive: $e');
    }
  }

  void dispose() {
    _isInitialized = false;
  }

  bool get isInitialized => _isInitialized;
}
