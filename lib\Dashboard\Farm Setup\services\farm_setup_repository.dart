import 'dart:io';
import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

import '../models/breed_category_isar.dart';
import '../models/animal_type_isar.dart';
import '../models/farm_isar.dart';
import '../models/alert_settings_isar.dart';
import '../models/milk_settings_isar.dart';
import '../models/backup_settings_isar.dart';
import '../models/user_role_isar.dart';
import '../models/farm_user_isar.dart';
import '../../Transactions/models/category_isar.dart';
import '../../Events/models/event_type_isar.dart';

import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

import '../models/currency_settings_isar.dart';
import 'cloud_backup_service.dart';

import '../../../Dashboard/Cattle/models/cattle_isar.dart';
// import '../../Transactions/models/transaction_isar.dart';
import '../../widgets/filters/filter_data_service.dart';

// Legacy alias for compatibility
typedef FarmSetupHandler = FarmSetupRepository;

/// Legacy Farm Setup Repository - Simplified to follow cattle/weight pattern exactly
/// This class maintains backward compatibility with pure CRUD operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class FarmSetupRepository {
  final IsarService _isarService;
  final Logger _logger;
  final CloudBackupService _cloudBackupService;

  // Public constructor with explicit dependency injection
  FarmSetupRepository(this._isarService, this._logger, this._cloudBackupService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE FARM SETUP STREAMS ===//

  /// Watches all farms with reactive updates
  Stream<List<FarmIsar>> watchAllFarms() {
    return _isar.farmIsars.where().watch(fireImmediately: true);
  }

  /// Watches all breed categories with reactive updates
  Stream<List<BreedCategoryIsar>> watchAllBreedCategories() {
    return _isar.breedCategoryIsars.where().watch(fireImmediately: true);
  }

  /// Watches all animal types with reactive updates
  Stream<List<AnimalTypeIsar>> watchAllAnimalTypes() {
    return _isar.animalTypeIsars.where().watch(fireImmediately: true);
  }

  /// Watches all alert settings with reactive updates
  Stream<List<AlertSettingsIsar>> watchAllAlertSettings() {
    return _isar.alertSettingsIsars.where().watch(fireImmediately: true);
  }

  /// Watches all currency settings with reactive updates
  Stream<List<CurrencySettingsIsar>> watchAllCurrencySettings() {
    return _isar.currencySettingsIsars.where().watch(fireImmediately: true);
  }

//=== FARM METHODS ===//

  /// Category Methods
  Future<List<CategoryIsar>> getIncomeCategories() async {
    return _getCategoriesByType('Income');
  }

  Future<List<CategoryIsar>> getExpenseCategories() async {
    return _getCategoriesByType('Expense');
  }

  Future<List<CategoryIsar>> getHealthIssueCategories() async {
    return _getCategoriesByType('HealthIssue');
  }

  Future<List<CategoryIsar>> getTreatmentCategories() async {
    return _getCategoriesByType('Treatment');
  }

  Future<List<CategoryIsar>> getVaccineCategories() async {
    return _getCategoriesByType('Vaccine');
  }

  Future<void> createCategory(CategoryIsar category) async {
    await _isar.writeTxn(() async {
      await _isar.categoryIsars.put(category);
    });
  }

  Future<void> updateCategory(CategoryIsar category) async {
    await _isar.writeTxn(() async {
      await _isar.categoryIsars.put(category);
    });
  }

  Future<void> deleteCategory(String categoryId) async {
    await _isar.writeTxn(() async {
      await _isar.categoryIsars
          .filter()
          .categoryIdEqualTo(categoryId)
          .deleteAll();
    });
  }

  Future<List<CategoryIsar>> _getCategoriesByType(String type) async {
    try {
      return await _isar.categoryIsars
          .filter()
          .typeEqualTo(type)
          .sortByName()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting $type categories: $e');
      throw DatabaseException('Failed to get $type categories', e.toString());
    }
  }

  /// Get the current farm (single farm per user)
  Future<FarmIsar?> getCurrentFarm() async {
    return await _isar.farmIsars.where().findFirst();
  }

  /// Get all farms
  Future<List<FarmIsar>> getAllFarms() async {
    try {
      // Remove unnecessary await
      final isar = _isar;
      final farms = await isar.farmIsars.where().sortByName().findAll();

      return farms;
    } catch (e) {
      _logger.severe('Error getting all farms: $e');
      throw DatabaseException('Failed to get all farms', e.toString());
    }
  }

  /// Get current farm ID (single farm per user)
  Future<String> getCurrentFarmId() async {
    try {
      debugPrint('🏠 [FARM_REPO_DEBUG] getCurrentFarmId() called');

      // Get the first (and only) farm's ID
      final farm = await getCurrentFarm();

      if (farm?.farmBusinessId != null) {
        debugPrint('✅ [FARM_REPO_DEBUG] Found farm ID: ${farm!.farmBusinessId}');
        return farm.farmBusinessId!;
      }

      debugPrint('❌ [FARM_REPO_DEBUG] No farm found - throwing DatabaseException');
      throw DatabaseException('No farm found', 'No farms exist in the database');
    } catch (e) {
      debugPrint('❌ [FARM_REPO_DEBUG] Error in getCurrentFarmId(): $e');
      debugPrint('❌ [FARM_REPO_DEBUG] Error type: $e.runtimeType');

      // Log as info instead of severe during first-time setup to reduce noise
      _logger.info('No farm found in database - this is normal during first-time setup: $e');
      throw DatabaseException('Failed to get current farm ID', e.toString());
    }
  }



  /// Add a new farm
  Future<FarmIsar> addFarm(FarmIsar farm) async {
    // Generate business ID if needed
    if (farm.farmBusinessId?.isEmpty ?? true) {
      farm.farmBusinessId = const Uuid().v4();
    }

    // Set audit fields
    final now = DateTime.now();
    farm.createdAt = now;
    farm.updatedAt = now;

    await _isar.writeTxn(() async {
      await _isar.farmIsars.put(farm);
    });

    return farm;
  }

  /// Update an existing farm
  Future<FarmIsar> updateFarm(FarmIsar farm) async {
    // Set audit fields
    farm.updatedAt = DateTime.now();

    await _isar.writeTxn(() async {
      await _isar.farmIsars.put(farm);
    });

    return farm;
  }

  /// Delete a farm
  Future<void> deleteFarm(String farmBusinessId) async {
    await _isar.writeTxn(() async {
      await _isar.farmIsars
          .filter()
          .farmBusinessIdEqualTo(farmBusinessId)
          .deleteAll();
    });
  }

  //=== BREED CATEGORIES ===//

  /// Get all breed categories
  Future<List<BreedCategoryIsar>> getAllBreedCategories() async {
    return await _isar.breedCategoryIsars.where().sortByName().findAll();
  }

  /// Get breed category by ID
  Future<BreedCategoryIsar?> getBreedCategoryById(String businessId) async {
    return await _isar.breedCategoryIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get breed categories for a specific animal type
  Future<List<BreedCategoryIsar>> getBreedCategoriesForAnimalType(
      String animalTypeId) async {
    try {
      if (animalTypeId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      final breedCategories = await _isar.breedCategoryIsars
          .filter()
          .animalTypeIdEqualTo(animalTypeId)
          .sortByName()
          .findAll();

      return breedCategories;
    } catch (e) {
      _logger.severe(
          'Error retrieving breed categories for animal type: $animalTypeId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve breed categories for animal type', e.toString());
    }
  }

  /// Add or update a breed category
  Future<void> addOrUpdateBreedCategory(BreedCategoryIsar category) async {
    try {
      await _validateBreedCategory(category);

      await _isar.writeTxn(() async {
        await _isar.breedCategoryIsars.put(category);
      });

      // Clear filter cache after breed category modification
      FilterDataService.instance.clearCache();

      _logger.info('Successfully saved breed category: ${category.businessId}');
    } catch (e) {
      _logger.severe('Error saving breed category: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save breed category', e.toString());
    }
  }

  /// Delete a breed category
  Future<void> deleteBreedCategory(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Breed category ID is required');
      }

      await _isar.writeTxn(() async {
        final category = await _isar.breedCategoryIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (category == null) {
          throw RecordNotFoundException(
              'Breed category not found: $businessId');
        }

        // Check if category is in use by any cattle
        final cattleCount = await _isar
            .collection<CattleIsar>()
            .filter()
            .breedIdEqualTo(businessId)
            .count();

        if (cattleCount > 0) {
          throw ValidationException(
              'Cannot delete breed category that is in use by $cattleCount cattle');
        }

        // For now, we'll assume the category isn't in use
        await _isar.breedCategoryIsars.delete(category.id);
      });

      // Clear filter cache after breed category deletion
      FilterDataService.instance.clearCache();

      _logger.info('Successfully deleted breed category: $businessId');
    } catch (e) {
      _logger.severe('Error deleting breed category: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete breed category', e.toString());
    }
  }

  //=== ANIMAL TYPES ===//

  /// Get all animal types
  Future<List<AnimalTypeIsar>> getAllAnimalTypes() async {
    try {
      final animalTypes =
          await _isar.animalTypeIsars.where().sortByName().findAll();

      return animalTypes;
    } catch (e) {
      _logger.severe('Error retrieving animal types: $e');
      throw DatabaseException('Failed to retrieve animal types', e.toString());
    }
  }

  /// Get animal type by ID
  Future<AnimalTypeIsar?> getAnimalTypeById(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      return await _isar.animalTypeIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      _logger.severe('Error retrieving animal type by ID: $businessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve animal type', e.toString());
    }
  }

  /// Add or update an animal type
  Future<void> addOrUpdateAnimalType(AnimalTypeIsar animalType) async {
    try {
      await _validateAnimalType(animalType);

      await _isar.writeTxn(() async {
        await _isar.animalTypeIsars.put(animalType);
      });

      // Clear filter cache after animal type modification
      FilterDataService.instance.clearCache();

      _logger.info('Successfully saved animal type: ${animalType.businessId}');
    } catch (e) {
      _logger.severe('Error saving animal type: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save animal type', e.toString());
    }
  }

  /// Delete an animal type
  Future<void> deleteAnimalType(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      await _isar.writeTxn(() async {
        final animalType = await _isar.animalTypeIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (animalType == null) {
          throw RecordNotFoundException('Animal type not found: $businessId');
        }

        // Check if animal type is in use by breed categories
        final breedCategoryCount = await _isar.breedCategoryIsars
            .filter()
            .animalTypeIdEqualTo(businessId)
            .count();

        if (breedCategoryCount > 0) {
          throw ValidationException(
              'Cannot delete animal type that has breed categories');
        }

        // Check if animal type is in use by cattle
        final cattleCount = await _isar
            .collection<CattleIsar>()
            .filter()
            .animalTypeIdEqualTo(businessId)
            .count();

        if (cattleCount > 0) {
          throw ValidationException(
              'Cannot delete animal type that is in use by $cattleCount cattle');
        }

        // If not in use, proceed with deletion
        await _isar.animalTypeIsars.delete(animalType.id);
      });

      // Clear filter cache after animal type deletion
      FilterDataService.instance.clearCache();

      _logger.info('Successfully deleted animal type: $businessId');
    } catch (e) {
      _logger.severe('Error deleting animal type: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete animal type', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate breed category
  Future<void> _validateBreedCategory(BreedCategoryIsar category) async {
    if (category.name == null || category.name!.isEmpty) {
      throw ValidationException('Breed category name is required');
    }

    if (category.animalTypeId == null || category.animalTypeId!.isEmpty) {
      throw ValidationException('Animal type ID is required');
    }

    // Check if animal type exists
    final animalType = await _isar.animalTypeIsars
        .filter()
        .businessIdEqualTo(category.animalTypeId!)
        .findFirst();

    if (animalType == null) {
      throw ValidationException('Invalid animal type ID');
    }
  }

  /// Validate animal type
  Future<void> _validateAnimalType(AnimalTypeIsar animalType) async {
    if (animalType.name == null || animalType.name!.isEmpty) {
      throw ValidationException('Animal type name is required');
    }

    // Check for duplicate names
    final existing = await _isar.animalTypeIsars
        .filter()
        .nameEqualTo(animalType.name!)
        .and()
        .not()
        .businessIdEqualTo(animalType.businessId ?? '')
        .findFirst();

    if (existing != null) {
      throw ValidationException('Animal type with this name already exists');
    }
  }

//=== MILK SETTINGS ===//

  Future<MilkSettingsIsar> getMilkSettings() async {
    try {
      final currentFarmId = await getCurrentFarmId();
      _logger.info('Getting milk settings for farm: $currentFarmId');

      final settings = await _isar.milkSettingsIsars
          .filter()
          .farmBusinessIdEqualTo(currentFarmId)
          .findFirst();

      return settings ?? MilkSettingsIsar.create(
          farmBusinessId: currentFarmId,
          unit: 'liters',
          regularRate: 1.0,
          premiumRate: 1.5,
          bulkRate: 0.8,
      );
    } catch (e) {
      _logger.severe('Error retrieving milk settings: $e');
      throw DatabaseException('Failed to retrieve milk settings', e.toString());
    }
  }

  Future<void> saveMilkSettings(MilkSettingsIsar settings) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.milkSettingsIsars.put(settings);
      });
      _logger.info('Milk settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving milk settings: $e');
      throw DatabaseException('Failed to save milk settings', e.toString());
    }
  }

//=== BACKUP METHODS ===//

  Future<BackupSettingsIsar> getBackupSettings() async {
    try {
      // First try to get existing backup settings
      final existingSettings = await _isar.backupSettingsIsars.where().findFirst();
      if (existingSettings != null) {
        return existingSettings;
      }

      // If no settings exist, try to get current farm ID safely
      String farmId;
      try {
        farmId = await getCurrentFarmId();
        _logger.info('Using existing farm ID for backup settings: $farmId');
      } catch (e) {
        // If no farm exists, create default settings with a placeholder farm ID
        // This is safe during first-time initialization before farms are created
        _logger.info('No farm found when creating backup settings, using default farm ID. This is normal during first-time setup.');
        farmId = 'default-farm';
      }

      // Create default backup settings with auto backup enabled
      final defaultSettings = BackupSettingsIsar.create(
        farmBusinessId: farmId,
        backupLocation: 'local',
        autoBackupEnabled: true,  // Enable auto backup by default
        autoBackupFrequency: 7,
      );

      // Save the default settings
      await _isar.writeTxn(() async {
        await _isar.backupSettingsIsars.put(defaultSettings);
      });

      _logger.info('Created default backup settings with farm ID: $farmId');
      return defaultSettings;
    } catch (e) {
      _logger.severe('Error retrieving backup settings: $e');
      throw DatabaseException('Failed to retrieve backup settings', e.toString());
    }
  }

  Future<void> saveBackupSettings(BackupSettingsIsar settings) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.backupSettingsIsars.put(settings);
      });
      _logger.info('Backup settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving backup settings: $e');
      throw DatabaseException('Failed to save backup settings', e.toString());
    }
  }

  /// Create a backup using the configured storage provider
  Future<CloudBackupResult> createBackup() async {
    try {
      final settings = await getBackupSettings();
      final farmId = settings.farmBusinessId ?? await getCurrentFarmId();

      _logger.info('Creating backup for farm: $farmId using provider: ${settings.storageProvider.name}');

      final result = await _cloudBackupService.createCloudBackup(farmId, settings.storageProvider);

      if (result.success) {
        // Update backup settings with last backup date
        final updatedSettings = settings.copyWith(
          lastBackupDate: DateTime.now(),
          lastCloudBackupDate: settings.isCloudStorageEnabled ? DateTime.now() : null,
        );
        await saveBackupSettings(updatedSettings);

        _logger.info('Backup created successfully: ${result.backupId}');
      } else {
        _logger.warning('Backup failed: ${result.message}');
      }

      return result;
    } catch (e) {
      _logger.severe('Error creating backup: $e');
      return CloudBackupResult.error('Error creating backup: $e');
    }
  }

  /// Restore from a backup using the specified provider
  Future<CloudBackupResult> restoreFromBackup(String backupId, BackupStorageProvider provider) async {
    try {
      _logger.info('Restoring from backup: $backupId using provider: ${provider.name}');

      final result = await _cloudBackupService.restoreFromCloudBackup(backupId, provider);

      if (result.success) {
        _logger.info('Backup restored successfully: $backupId');
        // Notify listeners that data has changed
        // notifyListeners(); // Removed as FarmSetupRepository is not a ChangeNotifier
      } else {
        _logger.warning('Restore failed: ${result.message}');
      }

      return result;
    } catch (e) {
      _logger.severe('Error restoring from backup: $e');
      return CloudBackupResult.error('Error restoring from backup: $e');
    }
  }

  /// List available backups for the current farm
  Future<List<CloudBackupInfo>> listBackups(BackupStorageProvider provider) async {
    try {
      final farmId = await getCurrentFarmId();
      return await _cloudBackupService.listCloudBackups(farmId, provider);
    } catch (e) {
      _logger.severe('Error listing backups: $e');
      return [];
    }
  }

  /// Delete a backup
  Future<bool> deleteBackup(String backupId, BackupStorageProvider provider) async {
    try {
      return await _cloudBackupService.deleteCloudBackup(backupId, provider);
    } catch (e) {
      _logger.severe('Error deleting backup: $e');
      return false;
    }
  }

  /// Restore from a local backup file
  ///
  /// This is the modern replacement for the deprecated restoreDatabase() method.
  /// It provides better error handling, validation, and logging.
  ///
  /// [filePath] - The path to the backup file to restore from
  ///
  /// Returns `true` if the restoration was successful, `false` otherwise.
  ///
  /// Throws no exceptions - all errors are caught and logged.
  Future<bool> restoreBackup(String filePath) async {
    try {
      _logger.info('Restoring from local backup file: $filePath');

      // Validate the backup file exists
      final file = File(filePath);
      if (!await file.exists()) {
        _logger.warning('Backup file not found: $filePath');
        return false;
      }

      // Validate file size (basic sanity check)
      final fileSize = await file.length();
      if (fileSize == 0) {
        _logger.warning('Backup file is empty: $filePath');
        return false;
      }

      // Perform the restoration using IsarService
      await _isarService.restoreFromBackup(file);

      // Notify listeners that data has changed
      // notifyListeners(); // Removed as FarmSetupRepository is not a ChangeNotifier

      _logger.info('Local backup restored successfully from: $filePath');
      return true;
    } catch (e) {
      _logger.severe('Error restoring from local backup: $e');
      return false;
    }
  }

  /// Check if authenticated with a cloud provider
  Future<bool> isCloudAuthenticated(BackupStorageProvider provider) async {
    try {
      return await _cloudBackupService.isAuthenticated(provider);
    } catch (e) {
      _logger.severe('Error checking authentication: $e');
      return false;
    }
  }

  /// Sign in to a cloud provider
  Future<bool> signInToCloud(BackupStorageProvider provider) async {
    try {
      return await _cloudBackupService.signIn(provider);
    } catch (e) {
      _logger.severe('Error signing in to cloud: $e');
      return false;
    }
  }

  /// Sign out from a cloud provider
  Future<void> signOutFromCloud(BackupStorageProvider provider) async {
    try {
      await _cloudBackupService.signOut(provider);
    } catch (e) {
      _logger.severe('Error signing out from cloud: $e');
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use createBackup() instead')
  Future<bool> backupDatabase(String path) async {
    try {
      final result = await createBackup();
      return result.success;
    } catch (e) {
      _logger.severe('Error in legacy backup method: $e');
      return false;
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use restoreBackup() instead')
  Future<bool> restoreDatabase(String path) async {
    _logger.info('Using deprecated restoreDatabase method, redirecting to restoreBackup()');
    return await restoreBackup(path);
  }

//=== USER METHODS ===//

  Future<List<FarmUserIsar>> getFarmUsers() async {
    try {
      final currentFarmId = await getCurrentFarmId();
      return await _isar.farmUserIsars
          .filter()
          .farmBusinessIdEqualTo(currentFarmId)
          .sortByName()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting farm users: $e');
      throw DatabaseException('Failed to get farm users', e.toString());
    }
  }

  Future<List<UserRoleIsar>> getAllUserRoles() async {
    try {
      return await _isar.userRoleIsars.where().sortByName().findAll();
    } catch (e) {
      _logger.severe('Error getting user roles: $e');
      throw DatabaseException('Failed to get user roles', e.toString());
    }
  }

  Future<void> migrateUsersFromSharedPreferences(
      List<FarmUserIsar> users, List<UserRoleIsar> roles) async {
    try {
      await _isar.writeTxn(() async {
        // First save roles
        for (final role in roles) {
          await _isar.userRoleIsars.put(role);
        }
        
        // Then save users
        for (final user in users) {
          await _isar.farmUserIsars.put(user);
        }
      });
      _logger.info('Successfully migrated users from SharedPreferences');
    } catch (e) {
      _logger.severe('Error migrating users: $e');
      throw DatabaseException('Failed to migrate users', e.toString());
    }
  }

  Future<void> ensureDefaultUserRoles() async {
    try {
      final existingRoles = await _isar.userRoleIsars.where().findAll();
      if (existingRoles.isEmpty) {
        // Create default roles
        await _isar.writeTxn(() async {
          for (final role in UserRoleIsar.defaultRoles) {
            await _isar.userRoleIsars.put(role);
          }
        });
      }
    } catch (e) {
      _logger.severe('Error ensuring default user roles: $e');
      throw DatabaseException('Failed to ensure default user roles', e.toString());
    }
  }

  Future<void> saveUser(FarmUserIsar user) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.put(user);
      });
      _logger.info('User saved successfully');
    } catch (e) {
      _logger.severe('Error saving user: $e');
      throw DatabaseException('Failed to save user', e.toString());
    }
  }

  Future<void> deleteUser(String userBusinessId) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.farmUserIsars
            .filter()
            .businessIdEqualTo(userBusinessId)
            .deleteAll();
      });
      _logger.info('User deleted successfully');
    } catch (e) {
      _logger.severe('Error deleting user: $e');
      throw DatabaseException('Failed to delete user', e.toString());
    }
  }

  Future<void> saveUserRole(UserRoleIsar role) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.userRoleIsars.put(role);
      });
      _logger.info('User role saved successfully');
    } catch (e) {
      _logger.severe('Error saving user role: $e');
      throw DatabaseException('Failed to save user role', e.toString());
    }
  }

//=== FARM CONFIGURATION ===//

  /// Save farm configuration - replaced with a simple logging implementation
  Future<void> saveFarmConfig(Map<String, dynamic> config) async {
    try {
      // Validate the config
      if (config['farmName'] == null || config['farmName'].isEmpty) {
        throw ValidationException('Farm name is required');
      }

      if (config['location'] == null || config['location'].isEmpty) {
        throw ValidationException('Farm location is required');
      }

      _logger.info('Successfully saved farm configuration');
    } catch (e) {
      _logger.severe('Error saving farm configuration: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to save farm configuration', e.toString());
    }
  }





  // Alert Settings methods
  Future<AlertSettingsIsar> getAlertSettings() async {
    try {
      // Get the current farm ID
      final currentFarmId = await getCurrentFarmId();

      _logger.info('Getting alert settings for farm: $currentFarmId');

      // First try to find settings for the current farm
      final settings = await _isar.alertSettingsIsars
          .filter()
          .farmBusinessIdEqualTo(currentFarmId)
          .findFirst();

      if (settings != null) {
        _logger.info('Found existing alert settings for farm $currentFarmId');
        return settings;
      }

      // If not found, create default settings for this farm
      _logger.info('Creating new alert settings for farm $currentFarmId');
      final newSettings = AlertSettingsIsar.create()
        ..farmBusinessId = currentFarmId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      // Save the new settings
      await _isar.writeTxn(() async {
        await _isar.alertSettingsIsars.put(newSettings);
      });

      return newSettings;
    } catch (e) {
      _logger.severe('Error retrieving alert settings: $e');
      throw DatabaseException(
          'Failed to retrieve alert settings', e.toString());
    }
  }

  Future<void> saveAlertSettings(AlertSettingsIsar settings) async {
    try {
      if (settings.farmBusinessId == null || settings.farmBusinessId!.isEmpty) {
        // If farmBusinessId is missing, set it to the current farm
        final currentFarmId = await getCurrentFarmId();
        settings.farmBusinessId = currentFarmId;
        _logger.info('Setting missing farmBusinessId to: $currentFarmId');
      }

      // Update timestamp
      settings.updatedAt = DateTime.now();

      // Save to database
      _logger
          .info('Saving alert settings for farm: ${settings.farmBusinessId}');
      await _isar.writeTxn(() async {
        await _isar.alertSettingsIsars.put(settings);
      });

      _logger.info('Alert settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving alert settings: $e');
      throw DatabaseException('Failed to save alert settings', e.toString());
    }
  }

  //=== CURRENCY SETTINGS ===//

  /// Get currency settings
  Future<CurrencySettingsIsar?> getCurrencySettings() async {
    try {
      return await _isar.currencySettingsIsars.where().findFirst();
    } catch (e) {
      _logger.severe('Error retrieving currency settings: $e');
      throw DatabaseException('Failed to retrieve currency settings', e.toString());
    }
  }

  /// Save currency settings
  Future<void> saveCurrencySettings(CurrencySettingsIsar settings) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.currencySettingsIsars.put(settings);
      });
      _logger.info('Currency settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving currency settings: $e');
      throw DatabaseException('Failed to save currency settings', e.toString());
    }
  }

  //=== EVENT CATEGORIES ===//

  /// Get all event types
  Future<List<EventTypeIsar>> getAllEventTypes() async {
    try {
      final eventTypes = await _isar.eventTypeIsars.where().sortByName().findAll();
      return eventTypes;
    } catch (e) {
      _logger.severe('Error retrieving event types: $e');
      throw DatabaseException('Failed to retrieve event types', e.toString());
    }
  }

  /// Save event type
  Future<void> saveEventType(EventTypeIsar eventType) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.eventTypeIsars.put(eventType);
      });
      _logger.info('Event type saved successfully');
    } catch (e) {
      _logger.severe('Error saving event type: $e');
      throw DatabaseException('Failed to save event type', e.toString());
    }
  }

  /// Delete event type
  Future<void> deleteEventType(String businessId) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.eventTypeIsars
            .filter()
            .businessIdEqualTo(businessId)
            .deleteAll();
      });
      _logger.info('Event type deleted successfully');
    } catch (e) {
      _logger.severe('Error deleting event type: $e');
      throw DatabaseException('Failed to delete event type', e.toString());
    }
  }
}
