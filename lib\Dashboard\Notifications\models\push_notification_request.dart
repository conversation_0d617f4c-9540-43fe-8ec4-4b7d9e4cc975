/// Request model for push notifications
class PushNotificationRequest {
  final String title;
  final String body;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? sound;
  final String? channelId;
  final String? channelName;
  final String? channelDescription;
  final int? badge;
  final String? collapseKey;
  final int? ttl; // Time to live in seconds
  final String? priority;
  final bool? contentAvailable;
  final bool? mutableContent;
  final String? category;
  final String? threadId;
  final List<String>? targetTokens;
  final String? topic;
  final String? condition;
  
  const PushNotificationRequest({
    required this.title,
    required this.body,
    this.data,
    this.imageUrl,
    this.sound,
    this.channelId,
    this.channelName,
    this.channelDescription,
    this.badge,
    this.collapseKey,
    this.ttl,
    this.priority,
    this.contentAvailable,
    this.mutableContent,
    this.category,
    this.threadId,
    this.targetTokens,
    this.topic,
    this.condition,
  });
  
  /// Create a high priority push notification request
  factory PushNotificationRequest.highPriority({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
    List<String>? targetTokens,
  }) {
    return PushNotificationRequest(
      title: title,
      body: body,
      data: data,
      imageUrl: imageUrl,
      priority: 'high',
      sound: 'default',
      channelId: 'high_priority',
      channelName: 'High Priority Notifications',
      channelDescription: 'Important notifications that require immediate attention',
      contentAvailable: true,
      mutableContent: true,
      ttl: 3600, // 1 hour
      targetTokens: targetTokens,
    );
  }
  
  /// Create a normal priority push notification request
  factory PushNotificationRequest.normal({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
    List<String>? targetTokens,
  }) {
    return PushNotificationRequest(
      title: title,
      body: body,
      data: data,
      imageUrl: imageUrl,
      priority: 'normal',
      sound: 'default',
      channelId: 'normal',
      channelName: 'General Notifications',
      channelDescription: 'General app notifications',
      ttl: 86400, // 24 hours
      targetTokens: targetTokens,
    );
  }
  
  /// Create a silent push notification request (for background updates)
  factory PushNotificationRequest.silent({
    required Map<String, dynamic> data,
    List<String>? targetTokens,
  }) {
    return PushNotificationRequest(
      title: '',
      body: '',
      data: data,
      priority: 'normal',
      contentAvailable: true,
      channelId: 'silent',
      channelName: 'Background Updates',
      channelDescription: 'Silent notifications for background data updates',
      ttl: 3600, // 1 hour
      targetTokens: targetTokens,
    );
  }
  
  /// Create a health alert push notification request
  factory PushNotificationRequest.healthAlert({
    required String cattleName,
    required String healthIssue,
    required String severity,
    String? cattleId,
    List<String>? targetTokens,
  }) {
    final isHighPriority = severity.toLowerCase() == 'critical' || severity.toLowerCase() == 'high';
    
    return PushNotificationRequest(
      title: isHighPriority ? '🚨 Health Alert' : '📋 Health Update',
      body: '$cattleName requires attention for $healthIssue',
      data: {
        'type': 'health_alert',
        'cattleId': cattleId ?? '',
        'cattleName': cattleName,
        'healthIssue': healthIssue,
        'severity': severity,
        'timestamp': DateTime.now().toIso8601String(),
      },
      priority: isHighPriority ? 'high' : 'normal',
      sound: isHighPriority ? 'alert' : 'default',
      channelId: isHighPriority ? 'health_alerts' : 'health_updates',
      channelName: isHighPriority ? 'Health Alerts' : 'Health Updates',
      channelDescription: isHighPriority 
          ? 'Critical health alerts requiring immediate attention'
          : 'General health updates and notifications',
      category: 'health',
      ttl: isHighPriority ? 1800 : 3600, // 30 min for alerts, 1 hour for updates
      targetTokens: targetTokens,
    );
  }
  
  /// Create a breeding reminder push notification request
  factory PushNotificationRequest.breedingReminder({
    required String cattleName,
    required String reminderType,
    required bool isOverdue,
    String? cattleId,
    List<String>? targetTokens,
  }) {
    return PushNotificationRequest(
      title: isOverdue ? '⏰ Overdue Breeding Reminder' : '🐄 Breeding Reminder',
      body: '$cattleName is ${isOverdue ? 'overdue for' : 'due for'} $reminderType',
      data: {
        'type': 'breeding_reminder',
        'cattleId': cattleId ?? '',
        'cattleName': cattleName,
        'reminderType': reminderType,
        'isOverdue': isOverdue,
        'timestamp': DateTime.now().toIso8601String(),
      },
      priority: isOverdue ? 'high' : 'normal',
      sound: 'default',
      channelId: 'breeding',
      channelName: 'Breeding Reminders',
      channelDescription: 'Breeding cycle reminders and notifications',
      category: 'breeding',
      ttl: 3600, // 1 hour
      targetTokens: targetTokens,
    );
  }
  
  /// Create a milk production push notification request
  factory PushNotificationRequest.milkProduction({
    required String cattleName,
    required double currentProduction,
    required double productionChange,
    String? cattleId,
    List<String>? targetTokens,
  }) {
    String emoji = '🥛';
    if (productionChange > 0) {
      emoji = '📈';
    } else if (productionChange < 0) {
      emoji = '📉';
    }
    
    return PushNotificationRequest(
      title: '$emoji Milk Production Update',
      body: '$cattleName produced ${currentProduction.toStringAsFixed(1)}L of milk',
      data: {
        'type': 'milk_production',
        'cattleId': cattleId ?? '',
        'cattleName': cattleName,
        'currentProduction': currentProduction,
        'productionChange': productionChange,
        'timestamp': DateTime.now().toIso8601String(),
      },
      priority: 'normal',
      sound: 'default',
      channelId: 'milk_production',
      channelName: 'Milk Production',
      channelDescription: 'Milk production updates and alerts',
      category: 'production',
      ttl: 7200, // 2 hours
      targetTokens: targetTokens,
    );
  }
  
  /// Convert to JSON for API requests
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'notification': {
        'title': title,
        'body': body,
      },
    };
    
    if (data != null) {
      json['data'] = data;
    }
    
    if (imageUrl != null) {
      json['notification']['image'] = imageUrl;
    }
    
    if (sound != null) {
      json['notification']['sound'] = sound;
    }
    
    if (badge != null) {
      json['notification']['badge'] = badge;
    }
    
    // Android specific
    final androidConfig = <String, dynamic>{};
    if (channelId != null) {
      androidConfig['channel_id'] = channelId;
    }
    if (priority != null) {
      androidConfig['priority'] = priority;
    }
    if (ttl != null) {
      androidConfig['ttl'] = '${ttl}s';
    }
    if (collapseKey != null) {
      androidConfig['collapse_key'] = collapseKey;
    }
    if (androidConfig.isNotEmpty) {
      json['android'] = androidConfig;
    }
    
    // iOS specific
    final apnsConfig = <String, dynamic>{};
    if (contentAvailable != null) {
      apnsConfig['content_available'] = contentAvailable;
    }
    if (mutableContent != null) {
      apnsConfig['mutable_content'] = mutableContent;
    }
    if (category != null) {
      apnsConfig['category'] = category;
    }
    if (threadId != null) {
      apnsConfig['thread_id'] = threadId;
    }
    if (apnsConfig.isNotEmpty) {
      json['apns'] = {'payload': {'aps': apnsConfig}};
    }
    
    // Targeting
    if (targetTokens != null && targetTokens!.isNotEmpty) {
      if (targetTokens!.length == 1) {
        json['to'] = targetTokens!.first;
      } else {
        json['registration_ids'] = targetTokens;
      }
    } else if (topic != null) {
      json['to'] = '/topics/$topic';
    } else if (condition != null) {
      json['condition'] = condition;
    }
    
    return json;
  }
  
  /// Create from JSON
  factory PushNotificationRequest.fromJson(Map<String, dynamic> json) {
    final notification = json['notification'] ?? {};
    final android = json['android'] ?? {};
    final apns = json['apns']?['payload']?['aps'] ?? {};
    
    return PushNotificationRequest(
      title: notification['title'] ?? '',
      body: notification['body'] ?? '',
      data: json['data']?.cast<String, dynamic>(),
      imageUrl: notification['image'],
      sound: notification['sound'],
      channelId: android['channel_id'],
      badge: notification['badge'],
      collapseKey: android['collapse_key'],
      ttl: android['ttl'] != null ? int.tryParse(android['ttl'].toString().replaceAll('s', '')) : null,
      priority: android['priority'],
      contentAvailable: apns['content_available'],
      mutableContent: apns['mutable_content'],
      category: apns['category'],
      threadId: apns['thread_id'],
      targetTokens: json['registration_ids']?.cast<String>() ?? 
                   (json['to'] != null && !json['to'].toString().startsWith('/topics/') 
                       ? [json['to']] 
                       : null),
      topic: json['to']?.toString().startsWith('/topics/') == true 
             ? json['to'].toString().substring(8) 
             : null,
      condition: json['condition'],
    );
  }
}
