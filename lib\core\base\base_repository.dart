import 'dart:async';
import 'package:isar/isar.dart';
import '../../services/database/isar_service.dart';

/// Abstract base repository for all module repositories
/// Defines the standard interface and patterns that all repositories must follow
/// 
/// Key principles:
/// - Pure reactive repositories with Stream-only operations
/// - Logic-free, simple CRUD operations
/// - Maximum purity: no error handling, no logging - exceptions bubble up naturally
/// - Explicit dependency injection through constructor
/// - Consistent naming conventions across all modules
abstract class BaseRepository<T> {
  final IsarService _isarService;

  /// Public constructor with explicit dependency injection
  BaseRepository(this._isarService);

  /// Getter for Isar instance
  Isar get isar => _isarService.isar;

  //=== REACTIVE STREAMS ===//

  /// Watches all records with reactive updates
  /// The controller is responsible for all filtering and sorting
  /// Must be implemented by concrete repositories
  Stream<List<T>> watchAll();

  /// Optional: Watch a single record by ID
  /// Override in concrete repositories if needed
  Stream<T?> watchById(String id) {
    throw UnimplementedError('watchById not implemented for this repository');
  }

  //=== CRUD OPERATIONS ===//

  /// Save (add or update) a record using Isar's native upsert
  /// Must be implemented by concrete repositories
  Future<void> save(T record);

  /// Delete a record by its Isar ID
  /// Must be implemented by concrete repositories
  Future<void> delete(int id);

  /// Optional: Delete by business ID
  /// Override in concrete repositories if needed
  Future<void> deleteByBusinessId(String businessId) {
    throw UnimplementedError('deleteByBusinessId not implemented for this repository');
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all records (for analytics and report generation)
  /// Returns a Future<List> for one-time data fetching
  /// Must be implemented by concrete repositories
  Future<List<T>> getAll();

  /// Optional: Get records by date range for filtered operations
  /// Override in concrete repositories if needed
  Future<List<T>> getByDateRange(DateTime startDate, DateTime endDate) {
    throw UnimplementedError('getByDateRange not implemented for this repository');
  }

  /// Optional: Get records by specific IDs for filtered operations
  /// Override in concrete repositories if needed
  Future<List<T>> getByIds(List<String> ids) {
    throw UnimplementedError('getByIds not implemented for this repository');
  }

  //=== BACKWARD COMPATIBILITY METHODS ===//

  /// Add record - alias for save for backward compatibility
  Future<void> add(T record) async {
    await save(record);
  }

  /// Update record - alias for save for backward compatibility
  Future<void> update(T record) async {
    await save(record);
  }
}

/// Repository interface for modules that need cattle-specific filtering
/// Extends BaseRepository with cattle-related query methods
abstract class CattleRelatedRepository<T> extends BaseRepository<T> {
  CattleRelatedRepository(super.isarService);

  /// Get records for specific cattle (for analytics and filtering)
  /// Must be implemented by concrete repositories that have cattle relationships
  Future<List<T>> getByCattleIds(List<String> cattleIds);

  /// Get records for a single cattle (for analytics and filtering)
  Future<List<T>> getByCattleId(String cattleId) async {
    return await getByCattleIds([cattleId]);
  }
}

/// Repository interface for modules that need farm-specific filtering
/// Extends BaseRepository with farm-related query methods
abstract class FarmRelatedRepository<T> extends BaseRepository<T> {
  FarmRelatedRepository(super.isarService);

  /// Get records for specific farm (for analytics and filtering)
  /// Must be implemented by concrete repositories that have farm relationships
  Future<List<T>> getByFarmId(String farmId);
}

/// Repository interface for modules that support both cattle and farm filtering
/// Combines both CattleRelatedRepository and FarmRelatedRepository interfaces
abstract class CattleFarmRepository<T> extends BaseRepository<T> {
  CattleFarmRepository(super.isarService);

  /// Get records for specific cattle (for analytics and filtering)
  Future<List<T>> getByCattleIds(List<String> cattleIds);

  /// Get records for a single cattle (for analytics and filtering)
  Future<List<T>> getByCattleId(String cattleId) async {
    return await getByCattleIds([cattleId]);
  }

  /// Get records for specific farm (for analytics and filtering)
  Future<List<T>> getByFarmId(String farmId);
}
