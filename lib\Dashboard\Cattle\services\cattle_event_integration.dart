import '../models/cattle_isar.dart';
// import '../../Events/services/event_service.dart'; // File removed

import 'package:flutter/foundation.dart';
/// CattleEventIntegration - DISABLED
/// This class is disabled because EventService and related enums have been removed
class CattleEventIntegration {
  CattleEventIntegration();

  /// Create cattle-related events - DISABLED
  Future<void> createCattleEvents(CattleIsar cattle) async {
    debugPrint('CattleEventIntegration: createCattleEvents disabled - EventService removed');
    return;
  }

  /// Update cattle events - DISABLED
  Future<void> updateCattleEvents(CattleIsar cattle) async {
    debugPrint('CattleEventIntegration: updateCattleEvents disabled - EventService removed');
    return;
  }

  /// Delete cattle events - DISABLED
  Future<void> deleteCattleEvents(String cattleId, String businessId) async {
    debugPrint('CattleEventIntegration: deleteCattleEvents disabled - EventService removed');
    return;
  }

  /*
  // All methods below are commented out due to missing EventService and related enums

      // Create health check event
      await _createHealthCheckEvent(cattle);

      // Create feeding schedule event
      await _createFeedingScheduleEvent(cattle);
    } catch (e) {
      debugPrint('Error creating cattle events: $e');
    }
  }

  /// Create registration event
  Future<void> _createRegistrationEvent(CattleIsar cattle) async {
    final event = EventIsar()
      ..title = 'Cattle Registration - $cattle.tagId'
      ..description = 'Registration of cattle $cattle.tagId - $cattle.breedId'
      ..eventType = EventType.cattle
      ..priority = EventPriority.low
      ..status = EventStatus.completed
      ..startDateTime = cattle.dateOfBirth!
      ..endDateTime = cattle.dateOfBirth!.add(const Duration(hours: 1))
      ..cattleIds = [cattle.businessId!]
      ..businessId = cattle.businessId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create weighing event
  Future<void> _createWeighingEvent(CattleIsar cattle) async {
    final event = EventIsar()
      ..title = 'Weighing - $cattle.tagId'
      ..description = 'Weigh cattle $cattle.tagId - Current weight: $cattle.weightkg'
      ..eventType = EventType.cattle
      ..priority = EventPriority.medium
      ..status = EventStatus.pending
      ..startDateTime = DateTime.now()
      ..endDateTime = DateTime.now().add(const Duration(hours: 1))
      ..cattleIds = [cattle.businessId!]
      ..businessId = cattle.businessId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now()
      ..isRecurring = true
      ..recurrenceRule = 'FREQ=MONTHLY';

    await _eventService.createEvent(event);
  }

  /// Create vaccination schedule events
  Future<void> _createVaccinationScheduleEvents(CattleIsar cattle) async {
    try {
      final birthDate = cattle.dateOfBirth!;
      
      // Create vaccination events based on age
      final vaccinationSchedule = [
        {'age': 3, 'type': 'Initial Vaccination'},
        {'age': 6, 'type': 'Booster Vaccination'},
        {'age': 12, 'type': 'Annual Vaccination'},
      ];

      for (final schedule in vaccinationSchedule) {
        final ageInMonths = schedule['age'] as int;
        final vaccinationType = schedule['type'] as String;
        final vaccinationDate = birthDate.add(Duration(days: ageInMonths * 30));

        final event = EventIsar()
          ..title = '$vaccinationType - $cattle.tagId'
          ..description = '$vaccinationType for cattle $cattle.tagId'
          ..eventType = EventType.cattle
          ..priority = EventPriority.high
          ..status = EventStatus.pending
          ..startDateTime = vaccinationDate
          ..endDateTime = vaccinationDate.add(const Duration(hours: 1))
          ..cattleIds = [cattle.businessId!]
          ..businessId = cattle.businessId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        await _eventService.createEvent(event);
      }
    } catch (e) {
      debugPrint('Error creating vaccination schedule events: $e');
    }
  }

  /// Create health check event
  Future<void> _createHealthCheckEvent(CattleIsar cattle) async {
    try {
      final nextCheckup = DateTime.now().add(const Duration(days: 30));

      final event = EventIsar()
        ..title = 'Health Checkup - $cattle.tagId'
        ..description = 'Routine health checkup for cattle $cattle.tagId'
        ..eventType = EventType.cattle
        ..priority = EventPriority.medium
        ..status = EventStatus.pending
        ..startDateTime = nextCheckup
        ..endDateTime = nextCheckup.add(const Duration(hours: 1))
        ..cattleIds = [cattle.businessId!]
        ..businessId = cattle.businessId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now()
        ..isRecurring = true
        ..recurrenceRule = 'FREQ=MONTHLY';

      await _eventService.createEvent(event);
    } catch (e) {
      debugPrint('Error creating health check event: $e');
    }
  }

  /// Create feeding schedule event
  Future<void> _createFeedingScheduleEvent(CattleIsar cattle) async {
    try {
      final feedingTimes = ['06:00', '12:00', '18:00'];
      
      for (final time in feedingTimes) {
        final timeParts = time.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        
        final feedingTime = DateTime(
          DateTime.now().year,
          DateTime.now().month,
          DateTime.now().day,
          hour,
          minute,
        );

        final event = EventIsar()
          ..title = 'Feeding - $cattle.tagId'
          ..description = 'Daily feeding schedule for cattle $cattle.tagId'
          ..eventType = EventType.cattle
          ..priority = EventPriority.medium
          ..status = EventStatus.pending
          ..startDateTime = feedingTime
          ..endDateTime = feedingTime.add(const Duration(minutes: 30))
          ..cattleIds = [cattle.businessId!]
          ..businessId = cattle.businessId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..isRecurring = true
          ..recurrenceRule = 'FREQ=DAILY';

        await _eventService.createEvent(event);
      }
    } catch (e) {
      debugPrint('Error creating feeding schedule event: $e');
    }
  }

  /// Create birthday event
  Future<void> createBirthdayEvent(CattleIsar cattle) async {
    try {
      if (cattle.dateOfBirth == null) return;

      final birthday = DateTime(
        DateTime.now().year,
        cattle.dateOfBirth!.month,
        cattle.dateOfBirth!.day,
      );

      final event = EventIsar()
        ..title = 'Birthday - $cattle.tagId'
        ..description = 'Birthday celebration for cattle $cattle.tagId - Age: ${_calculateAge(cattle.dateOfBirth!)}'
        ..eventType = EventType.cattle
        ..priority = EventPriority.low
        ..status = EventStatus.pending
        ..startDateTime = birthday
        ..endDateTime = birthday.add(const Duration(hours: 1))
        ..cattleIds = [cattle.businessId!]
        ..businessId = cattle.businessId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now()
        ..isRecurring = true
        ..recurrenceRule = 'FREQ=YEARLY';

      await _eventService.createEvent(event);
    } catch (e) {
      debugPrint('Error creating birthday event: $e');
    }
  }

  /// Create weaning event
  Future<void> createWeaningEvent(CattleIsar cattle) async {
    try {
      if (cattle.dateOfBirth == null) return;

      final weaningDate = cattle.dateOfBirth!.add(const Duration(days: 180));

      final event = EventIsar()
        ..title = 'Weaning - $cattle.tagId'
        ..description = 'Weaning scheduled for cattle $cattle.tagId'
        ..eventType = EventType.cattle
        ..priority = EventPriority.high
        ..status = EventStatus.pending
        ..startDateTime = weaningDate
        ..endDateTime = weaningDate.add(const Duration(hours: 2))
        ..cattleIds = [cattle.businessId!]
        ..businessId = cattle.businessId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventService.createEvent(event);
    } catch (e) {
      debugPrint('Error creating weaning event: $e');
    }
  }

  /// Create castration event
  Future<void> createCastrationEvent(CattleIsar cattle) async {
    try {
      if (cattle.dateOfBirth == null) return;

      final castrationDate = cattle.dateOfBirth!.add(const Duration(days: 90));

      final event = EventIsar()
        ..title = 'Castration - $cattle.tagId'
        ..description = 'Castration scheduled for cattle $cattle.tagId'
        ..eventType = EventType.cattle
        ..priority = EventPriority.high
        ..status = EventStatus.pending
        ..startDateTime = castrationDate
        ..endDateTime = castrationDate.add(const Duration(hours: 1))
        ..cattleIds = [cattle.businessId!]
        ..businessId = cattle.businessId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventService.createEvent(event);
    } catch (e) {
      debugPrint('Error creating castration event: $e');
    }
  }

  /// Update cattle events
  Future<void> updateCattleEvents(CattleIsar cattle) async {
    try {
      // Update existing events based on cattle changes
      final events = await _eventService.getEventsByCattleId(cattle.businessId!);

      for (final event in events) {
        if (event.eventType == EventType.cattle) {
          await _updateEventFromCattle(event, cattle);
        }
      }
    } catch (e) {
      debugPrint('Error updating cattle events: $e');
    }
  }

  /// Update event from cattle
  Future<void> _updateEventFromCattle(
    EventIsar event,
    CattleIsar cattle,
  ) async {
    if (event.title?.contains('Weighing') == true && cattle.weight != null) {
      event.startDateTime = DateTime.now();
      event.endDateTime = DateTime.now().add(const Duration(hours: 1));
      event.description = 'Weigh cattle $cattle.tagId - Current weight: $cattle.weightkg';
    } else if (event.title?.contains('Birthday') == true && cattle.dateOfBirth != null) {
      final birthday = DateTime(
        DateTime.now().year,
        cattle.dateOfBirth!.month,
        cattle.dateOfBirth!.day,
      );
      event.startDateTime = birthday;
      event.endDateTime = birthday.add(const Duration(hours: 1));
      event.description = 'Birthday celebration for cattle $cattle.tagId - Age: ${_calculateAge(cattle.dateOfBirth!)}';
    }

    event.updatedAt = DateTime.now();
    await _eventService.updateEvent(event);
  }

  /// Delete cattle events
  Future<void> deleteCattleEvents(String cattleId) async {
    try {
      final events = await _eventService.getEventsByCattleId(cattleId);

      final cattleEvents = events.where((event) =>
        event.eventType == EventType.cattle
      ).toList();

      for (final event in cattleEvents) {
        await _eventService.deleteEvent(event.id);
      }
    } catch (e) {
      debugPrint('Error deleting cattle events: $e');
    }
  }

  /// Calculate age in years and months
  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final years = now.year - birthDate.year;
    final months = now.month - birthDate.month;
    
    if (months < 0) {
      return '${years - 1} years ${months + 12} months';
    }
    
    return '$years years $months months';
  }

  /// Create herd management event
  Future<void> createHerdManagementEvent({
    required String businessId,
    required DateTime managementDate,
    required String managementType,
    required List<String> cattleIds,
  }) async {
    try {
      final event = EventIsar()
        ..title = 'Herd Management - $managementType'
        ..description = '$managementType for selected cattle'
        ..eventType = EventType.cattle
        ..priority = EventPriority.medium
        ..status = EventStatus.pending
        ..startDateTime = managementDate
        ..endDateTime = managementDate.add(const Duration(hours: 4))
        ..cattleIds = cattleIds
        ..businessId = businessId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await _eventService.createEvent(event);
    } catch (e) {
      debugPrint('Error creating herd management event: $e');
    }
  }
  */
}